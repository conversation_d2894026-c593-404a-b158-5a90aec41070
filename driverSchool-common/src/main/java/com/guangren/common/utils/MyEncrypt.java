package com.guangren.common.utils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;



public class MyEncrypt {
	private String Algorithm = "AES"; // 定义 加密算法,可用DES,DESede,Blowfish
	private String key = "er$%#95684";
	
	private static MyEncrypt instance = new MyEncrypt();
	
	public static MyEncrypt getInstance(){
		return instance;
	}
		
	public String getAlgorithm() {
		return Algorithm;
	}
	public void setAlgorithm(String algorithm) {
		Algorithm = algorithm;
	}
	public String getKey() {
		return key;
	}
	public void setKey(String key) {
		this.key = key;
	}
	/**
	 * 解码
	 * @param value	待解密字符串
	 */
	public  String decrypt(String value){
		try {
			byte[] b = decryptMode(getKeyBytes(key), Base64.decodeBase64(value));
			return new String(b);
		} catch (Exception e) {
			return null;
		}
	}
	/**
	 * 加密
	 * @param value	待加密字符串
	 */
	public  String encrypt(String value){
		String str = null;
		try {
			str = byte2Base64(encryptMode(getKeyBytes(key), value.getBytes()));
		} catch (Exception e) {
			// TODO: handle exception
		}
		return str;
	}
	/**
	 * 计算24位长的密码byte值,首先对原始密钥做MD5算hash值，再用前8位数据对应补全后8位
	 */
	private  byte[] getKeyBytes(String strKey) throws Exception {
		if (null == strKey || strKey.length() < 1)
			throw new Exception("key is null or empty!");
		java.security.MessageDigest alg = java.security.MessageDigest.getInstance("MD5");
		alg.update(strKey.getBytes());
		byte[] bkey = alg.digest();
		int start = bkey.length;
		byte[] bkey24 = new byte[24];
		for (int i = 0; i < start; i++) {
			bkey24[i] = bkey[i];
		}
		for (int i = start; i < 24; i++) {
			bkey24[i] = bkey[i - start];
		}
		return bkey24;

	}
	/**
	 * 
	 * @param keybyte	加密密钥，长度为24字节
	 * @param src		被加密的数据缓冲区（源）
	 * @return
	 */
	private  byte[] encryptMode(byte[] keybyte, byte[] src) {
		try {
			// 生成密钥
			SecretKey deskey = new SecretKeySpec(keybyte, Algorithm); // 加密
			Cipher c1 = Cipher.getInstance(Algorithm);
			c1.init(Cipher.ENCRYPT_MODE, deskey);
			return c1.doFinal(src);
		} catch (java.security.NoSuchAlgorithmException e1) {
			e1.printStackTrace();
		} catch (javax.crypto.NoSuchPaddingException e2) {
			e2.printStackTrace();
		} catch (java.lang.Exception e3) {
			e3.printStackTrace();
		}
		return null;
	}
	/**
	 * 
	 * @param keybyte	加密密钥，长度为24字节
	 * @param src		加密后的缓冲区
	 * @return	
	 */
	private  byte[] decryptMode(byte[] keybyte, byte[] src) {
		try { // 生成密钥
			SecretKey deskey = new SecretKeySpec(keybyte, Algorithm);
			// 解密
			Cipher c1 = Cipher.getInstance(Algorithm);
			c1.init(Cipher.DECRYPT_MODE, deskey);
			return c1.doFinal(src);
		} catch (java.security.NoSuchAlgorithmException e1) {
			e1.printStackTrace();
		} catch (javax.crypto.NoSuchPaddingException e2) {
			e2.printStackTrace();
		} catch (java.lang.Exception e3) {
			e3.printStackTrace();
		}
		return null;
	}
	// 转换成base64编码
	private static String byte2Base64(byte[] b) {
		return Base64.encodeBase64String(b);
	}
	
	public static void main(String [] args){
		String s = "{\"VIRTUAL_ACCOUNT_ARRAY\":[{\"VIRTUAL_ACCOUNT_NO\":\"000041\",\"VIRTUAL_ACCOUNT_NAME\":\"刘小翠0222\",\"STUDENTS_NAME\":\"刘小翠\",\"STUDENTS_ID\":\"513006198902230222\",\"STUDENTS_AMT\":\"2000.00\"},{\"VIRTUAL_ACCOUNT_NO\":\"000042\",\"VIRTUAL_ACCOUNT_NAME\":\"王治治8841\",\"STUDENTS_NAME\":\"王治治\",\"STUDENTS_ID\":\"450881199106018841\",\"STUDENTS_AMT\":\"2000.00\"}],\"JX_CILENT_NO\":\"**********\",\"ORDER_ID\":\"123456\",\"VIRTUAL_PAY_ACCOUNT_NO\":\"000043\",\"SUPERVISE_AMT\":\"5000.00\",\"JX_SUPERVISE_ACCOUNT\":\"120010190010076896\"}";
		String str = MyEncrypt.getInstance().encrypt(s);
		System.out.println("加密：=="+str);
		String str2 = MyEncrypt.getInstance().decrypt(str);
		System.out.println("解密：=="+str2);
	}
}
