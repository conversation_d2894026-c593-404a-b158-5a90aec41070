ALTER TABLE `t_school` 
DROP COLUMN `b2ctid`,
DROP COLUMN `b2cmid`,
DROP COLUMN `tid`,
DROP COLUMN `mid`;


insert into sys_config (`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values ('监管资金导入手续费', 'supervise.import.commission', '0.0036', 'N', 'admin', '2023-08-02 09:53:58', 'admin', '2023-08-05 09:42:17', '');

insert into sys_config (`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values ('驾协银联公对公主商户号', 'supervise.unionPay.b2bmid', '89844038641388S', 'N', 'admin', '2023-08-05 10:01:50', 'admin', '2023-08-05 11:07:24', '');
--insert into sys_config (`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values ('驾协银联公对公监管商户号', 'supervise.unionPay.sub.b2bmid', '', 'N', 'admin', '2023-08-05 10:01:50', 'admin', '2023-08-05 11:07:24', '');
insert into sys_config (`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values ('驾协银联公对公终端号', 'supervise.unionPay.b2btid', '388S0001', 'N', 'admin', '2023-08-05 10:01:50', 'admin', '2023-08-05 11:07:24', '');

insert into sys_config (`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values ('驾协银联公对私主商户号', 'supervise.unionPay.b2cmid', '89844038641388R', 'N', 'admin', '2023-08-05 10:01:50', 'admin', '2023-08-05 11:07:24', '');
--insert into sys_config (`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values ('驾协银联公对私监管商户号', 'supervise.unionPay.sub.b2cmid', '', 'N', 'admin', '2023-08-05 10:01:50', 'admin', '2023-08-05 11:07:24', '');
insert into sys_config (`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values ('驾协银联公对私终端号', 'supervise.unionPay.b2ctid', '388R0001', 'N', 'admin', '2023-08-05 10:01:50', 'admin', '2023-08-05 11:07:24', '');

--insert into sys_config (`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values ('驾协银联客户号', 'supervise.unionPay.clientNo', 'JX5J536', 'N', 'admin', '2023-08-05 11:04:34', 'admin', '2023-08-07 16:00:00', '');
--insert into sys_config (`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values ('驾协银联签名key', 'supervise.unionPay.signKey', '6a42d7feb8a35a4e9f16a9ee327cfac8', 'N', 'admin', '2023-08-05 11:04:34', 'admin', '2023-08-07 16:00:15', '');
insert into sys_config (`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values ('监管资金手续费计算方式', 'supervise.commission.type', '1', 'N', 'admin', '2023-08-05 11:04:34', 'admin', '2023-08-07 16:00:15', '0-支付整额方式,手续费另算，1-入账整额方式，手续费包括在费用中');

insert into sys_config (`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values ('驾协银联扫码主商户号', 'supervise.unionPay.scancodemid', '89844038641388Q', 'N', 'admin', '2023-08-05 10:01:50', 'admin', '2023-08-05 11:07:24', '');
--insert into sys_config (`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values ('驾协银联扫码监管商户号', 'supervise.unionPay.sub.scancodemid', '', 'N', 'admin', '2023-08-05 10:01:50', 'admin', '2023-08-05 11:07:24', '');
insert into sys_config (`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values ('驾协银联扫码终端号', 'supervise.unionPay.scancodetid', '388Q0001', 'N', 'admin', '2023-08-05 10:01:50', 'admin', '2023-08-05 11:07:24', '');


ALTER TABLE `t_bank_transaction_record` 
ADD COLUMN `trans_type` INT(1) NULL AFTER `sub_order_id`,
ADD COLUMN `b2bmid` VARCHAR(45) NULL AFTER `trans_type`,
ADD COLUMN `b2btid` VARCHAR(45) NULL AFTER `b2bmid`,
ADD COLUMN `subb2bmid` VARCHAR(45) NULL AFTER `b2btid`,
ADD COLUMN `b2cmid` VARCHAR(45) NULL AFTER `subb2bmid`,
ADD COLUMN `b2ctid` VARCHAR(45) NULL AFTER `b2cmid`,
ADD COLUMN `subb2cmid` VARCHAR(45) NULL AFTER `b2ctid`,
CHANGE COLUMN `amound` `amount` DECIMAL(10,2) NULL DEFAULT 0 COMMENT '交易金额' ;


ALTER TABLE `t_supervise_batch_submit` 
ADD COLUMN `supervise_fee` DECIMAL(20,0) NULL DEFAULT 0 AFTER `commission`,
CHANGE COLUMN `total_fee` `total_fee` DECIMAL(20,0) NULL DEFAULT 0 COMMENT '总额' ;


ALTER TABLE `t_bank_transaction_record` 
ADD COLUMN `id` VARCHAR(45) NULL FIRST;

ALTER TABLE `t_bank_transaction_record` 
CHANGE COLUMN `id` `id` VARCHAR(45) NOT NULL ,
ADD PRIMARY KEY (`id`);


ALTER TABLE `t_school_student` 
CHANGE COLUMN `residue_supervise_amt` `residue_supervise_amt` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '剩余待释放监管金额' ;


ALTER TABLE `t_province_student` 
CHANGE COLUMN `stunum` `stunum` VARCHAR(16) NOT NULL COMMENT '学员编号' FIRST;


ALTER TABLE `t_province_study_time` 
ADD COLUMN `id` VARCHAR(45) NOT NULL FIRST,
DROP PRIMARY KEY,
ADD PRIMARY KEY USING BTREE (`id`);

ALTER TABLE `t_school_deal_flow` 
CHANGE COLUMN `id` `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键' ;

ALTER TABLE `t_supervise_flow` 
CHANGE COLUMN `id` `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键' ;


--上面已更新数据库

ALTER TABLE `t_province_study_time` 
ADD COLUMN `id` VARCHAR(45) NOT NULL FIRST,
DROP PRIMARY KEY,
ADD PRIMARY KEY USING BTREE (`id`);

ALTER TABLE `t_school_student_drop_out` 
CHANGE COLUMN `student_id` `student_id` VARCHAR(45) NOT NULL COMMENT '学员id' ,
CHANGE COLUMN `status` `status` INT(1) NULL DEFAULT 0 COMMENT '退学状态：0=等待学员确认，1=学员已确认，2=等待驾协确认，3=驾协退回，4=驾协已确认' ,
CHANGE COLUMN `is_done` `is_done` INT(1) NULL DEFAULT 0 COMMENT '流程状态：0=进行中，1=流程结束' ;

ALTER TABLE `t_supervise_batch_submit_detail` 
ADD COLUMN `commission` DECIMAL(20,2) NULL DEFAULT 0 AFTER `supervise_fee`;


ALTER TABLE `t_school_deal_flow` 
ADD COLUMN `bank_info` VARCHAR(45) NULL COMMENT '支付银行信息',
ADD COLUMN `bank_card_no` VARCHAR(45) NULL COMMENT '支付卡信息号',
ADD COLUMN `target_order_id` VARCHAR(45) NULL COMMENT '目标平台单号',
ADD COLUMN `bank_order_id` VARCHAR(45) NULL COMMENT '交易流水号';


ALTER TABLE `t_bank_transaction_record` 
ADD COLUMN `bank_info` VARCHAR(45) NULL COMMENT '支付银行信息',
ADD COLUMN `bank_card_no` VARCHAR(45) NULL COMMENT '支付卡信息号',
ADD COLUMN `target_order_id` VARCHAR(45) NULL COMMENT '目标平台单号';

ALTER TABLE `t_bank_transaction_record` 
ADD COLUMN `scancodemid` VARCHAR(45) NULL COMMENT '二维码扫码主商户Id' AFTER `target_order_id`,
ADD COLUMN `scancodetid` VARCHAR(45) NULL COMMENT '扫码终端号' AFTER `scancodemid`,
ADD COLUMN `subscancodemid` VARCHAR(45) NULL COMMENT '扫码子商户号' AFTER `scancodetid`,
CHANGE COLUMN `b2bmid` `b2bmid` VARCHAR(45) NULL DEFAULT NULL COMMENT '企业网关主商户Id' ,
CHANGE COLUMN `b2btid` `b2btid` VARCHAR(45) NULL DEFAULT NULL COMMENT '企业网关终端Id' ,
CHANGE COLUMN `subb2bmid` `subb2bmid` VARCHAR(45) NULL DEFAULT NULL COMMENT '企业网关子商户主ID' ,
CHANGE COLUMN `b2cmid` `b2cmid` VARCHAR(45) NULL DEFAULT NULL COMMENT '个人网关主商户ID' ,
CHANGE COLUMN `b2ctid` `b2ctid` VARCHAR(45) NULL DEFAULT NULL COMMENT '个人网关终端号' ,
CHANGE COLUMN `subb2cmid` `subb2cmid` VARCHAR(45) NULL DEFAULT NULL COMMENT '个人网关子商户Id' ;



ALTER TABLE `t_school` 
ADD COLUMN `is_supervise` INT(1) NULL DEFAULT 1 COMMENT '是否整个学校不监管，默认为1，都需要监管' AFTER `supervise_amt`;


ALTER TABLE `t_supervise_batch_submit_detail` 
ADD COLUMN `total_fee` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '总费用' AFTER `commission`;

ALTER TABLE `t_supervise_batch_submit_detail` 
CHANGE COLUMN `commission` `commission` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '手续费' ;

ALTER TABLE `t_supervise_batch_submit_detail` 
CHANGE COLUMN `commission` `commission` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '手续费' ,
CHANGE COLUMN `total_fee` `total_fee` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '总费用' ;


ALTER TABLE `t_supervise_pay` 
ADD COLUMN `commission_fee` DECIMAL(20,2) NULL DEFAULT 0 AFTER `total_release_fee`;


ALTER TABLE `t_supervise_pay` 
CHANGE COLUMN `study_fee` `study_fee` DECIMAL(20,2) NOT NULL DEFAULT 0  COMMENT '总学费' ,
CHANGE COLUMN `supervise_fee` `supervise_fee` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '监管金额' ,
CHANGE COLUMN `total_real_fee` `total_real_fee` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '累计的实际交费金额，每交费一次加一次' ,
CHANGE COLUMN `rest_study_fee` `rest_study_fee` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '剩余学费 （study_fee-total_real_fee）' ,
CHANGE COLUMN `total_release_fee` `total_release_fee` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '总的释放金额' ,
CHANGE COLUMN `commission_fee` `commission_fee` DECIMAL(20,2) NULL DEFAULT '0.00' ,
CHANGE COLUMN `rest_supervise_fee` `rest_supervise_fee` DECIMAL(20,2) NOT NULL COMMENT '剩余释放的金额' ;


ALTER TABLE `t_supervise_batch_submit_detail` 
CHANGE COLUMN `supervise_fee` `supervise_fee` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '一个人多少钱' ,
CHANGE COLUMN `commission` `commission` DECIMAL(20,2) NULL DEFAULT '0.00' COMMENT '手续费' ,
CHANGE COLUMN `total_fee` `total_fee` DECIMAL(20,2) NULL DEFAULT '0.00' COMMENT '总费用' ;


ALTER TABLE `t_supervise_batch_submit` 
CHANGE COLUMN `total_fee` `total_fee` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '总额' ,
CHANGE COLUMN `commission` `commission` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '手续费' ,
CHANGE COLUMN `supervise_fee` `supervise_fee` DECIMAL(20,2) NULL DEFAULT 0 ;


ALTER TABLE `t_supervise_exception` 
CHANGE COLUMN `release_fee` `release_fee` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '释放金额' ;


ALTER TABLE `t_supervise_exception_day` 
CHANGE COLUMN `release_fee` `release_fee` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '释放金额' ;


ALTER TABLE `t_supervise_flow` 
CHANGE COLUMN `last_balance` `last_balance` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '上期结余' ,
CHANGE COLUMN `the_amt` `the_amt` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '本期发生金额' ,
CHANGE COLUMN `the_balance` `the_balance` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '本期结余' ;

ALTER TABLE `t_supervise_pay_detail` 
CHANGE COLUMN `fee` `fee` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '支付金额' ;

ALTER TABLE `t_supervise_release_record` 
CHANGE COLUMN `supervise_fee` `supervise_fee` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '监管金额' ,
CHANGE COLUMN `release_fee` `release_fee` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '释放金额' ;


ALTER TABLE `t_school_student` 
CHANGE COLUMN `residue_supervise_amt` `residue_supervise_amt` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '剩余待释放监管金额' ;


ALTER TABLE `t_school_deal_flow` 
CHANGE COLUMN `last_balance` `last_balance` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '上期结余' ,
CHANGE COLUMN `the_amt` `the_amt` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '本期发生金额' ,
CHANGE COLUMN `the_balance` `the_balance` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '本期结余' ;


ALTER TABLE `t_school` 
CHANGE COLUMN `supervise_amt` `supervise_amt` DECIMAL(20,2) NULL DEFAULT 0 COMMENT '监管总金额' ;


ALTER TABLE `t_school` 
ADD COLUMN `sign_key` VARCHAR(45) NULL COMMENT '签名key' AFTER `is_supervise`;



insert into sys_dict_type values ('139', '银联个人网银标识', 'unionBank_person_chnlNo', '0', 'admin', '2023-07-01 14:54:56', '', NULL, NULL);
insert into sys_dict_type values ('140', '银联企业网银标识', 'unionBank_enterprise_chnlNo', '0', 'admin', '2023-07-01 14:54:56', '', NULL, NULL);

insert into sys_dict_data values ('262', '1', '平安银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('263', '2', '建设银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('264', '3', '中信银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('265', '4', '中信银行信用卡', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('266', '5', '光大银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('267', '6', '民生银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('268', '7', '交通银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('269', '8', '邮储银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('270', '9', '浙商银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('271', '10', '北京银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('272', '11', '上海银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('273', '12', '宁波银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('274', '13', '青岛银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('275', '14', '华夏银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('276', '15', '农业银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('277', '16', '广西北部湾', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('278', '17', '桂林银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('279', '18', '兰州银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('280', '19', '齐鲁银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('281', '20', '兴业银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('282', '21', '招商银行', '***********', 'unionBank_person_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);

insert into sys_dict_data values ('283', '1', '平安银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('284', '2', '招商银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('285', '3', '中国光大银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('286', '4', '民生银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('287', '5', '邮储银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('288', '6', '建设银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('289', '7', '北京银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('290', '8', '浦发银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('291', '9', '上海银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('292', '10', '中信银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('293', '11', '宁波银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('294', '12', '青岛银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('295', '13', '华夏银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('296', '14', '工商银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('297', '15', '交通银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('298', '16', '农业银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('299', '17', '浙商银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('300', '18', '浙江省农村信用社联合社', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('301', '19', '桂林银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('302', '20', '杭州银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('303', '21', '广西北部湾银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('304', '22', '齐鲁银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('305', '23', '中国银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('306', '24', '贵阳银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('307', '25', '兰州银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('308', '26', '日照银行', '0**********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('309', '27', '柳州银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('310', '28', '长沙银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('311', '29', '云南红塔银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('312', '30', '宁波鄞州农村商业银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('313', '31', '山东省农村信用社', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('314', '32', '兴业银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('315', '33', '广发银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);
insert into sys_dict_data values ('316', '34', '台州银行', '***********', 'unionBank_enterprise_chnlNo', NULL, NULL, 'N', '0', 'admin', '2023-07-05 14:39:43', '', NULL, NULL);


--退学学员菜单和7天退学学员菜单


