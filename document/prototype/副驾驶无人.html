<!DOCTYPE html>
<html>
  <head>
    <title>副驾驶无人</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/副驾驶无人/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/副驾驶无人/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Shape) -->
      <div id="u0" class="ax_shape">
        <img id="u0_img" class="img " src="images/非学员人脸/u0.png"/>
        <!-- Unnamed () -->
        <div id="u1" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u2" class="ax_table">

        <!-- Unnamed (Table Cell) -->
        <div id="u3" class="ax_table_cell">
          <img id="u3_img" class="img " src="images/运营数据（移动，运营）/u105.png"/>
          <!-- Unnamed () -->
          <div id="u4" class="text">
            <p><span>时间点</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u5" class="ax_table_cell">
          <img id="u5_img" class="img " src="images/运营数据（移动，运营）/u105.png"/>
          <!-- Unnamed () -->
          <div id="u6" class="text">
            <p><span>图片</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u7" class="ax_table_cell">
          <img id="u7_img" class="img " src="images/运营数据（移动，运营）/u105.png"/>
          <!-- Unnamed () -->
          <div id="u8" class="text">
            <p><span>摄像机</span><span></span><span>名称</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u9" class="ax_table_cell">
          <img id="u9_img" class="img " src="images/运营数据（移动，运营）/u105.png"/>
          <!-- Unnamed () -->
          <div id="u10" class="text">
            <p><span>所属训练场</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u11" class="ax_table_cell">
          <img id="u11_img" class="img " src="images/运营数据（移动，运营）/u105.png"/>
          <!-- Unnamed () -->
          <div id="u12" class="text">
            <p><span>所属分校</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u13" class="ax_table_cell">
          <img id="u13_img" class="img " src="images/非学员人脸/u13.png"/>
          <!-- Unnamed () -->
          <div id="u14" class="text">
            <p><span>所属驾校</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u15" class="ax_table_cell">
          <img id="u15_img" class="img " src="images/运营数据（移动，运营）/u105.png"/>
          <!-- Unnamed () -->
          <div id="u16" class="text">
            <p><span>2023-04-06 14:23:14</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17" class="ax_table_cell">
          <img id="u17_img" class="img " src="images/运营数据（移动，运营）/u105.png"/>
          <!-- Unnamed () -->
          <div id="u18" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u19" class="ax_table_cell">
          <img id="u19_img" class="img " src="images/运营数据（移动，运营）/u105.png"/>
          <!-- Unnamed () -->
          <div id="u20" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u21" class="ax_table_cell">
          <img id="u21_img" class="img " src="images/运营数据（移动，运营）/u105.png"/>
          <!-- Unnamed () -->
          <div id="u22" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u23" class="ax_table_cell">
          <img id="u23_img" class="img " src="images/运营数据（移动，运营）/u105.png"/>
          <!-- Unnamed () -->
          <div id="u24" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u25" class="ax_table_cell">
          <img id="u25_img" class="img " src="images/非学员人脸/u13.png"/>
          <!-- Unnamed () -->
          <div id="u26" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u27" class="ax_table_cell">
          <img id="u27_img" class="img " src="images/非学员人脸/u27.png"/>
          <!-- Unnamed () -->
          <div id="u28" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u29" class="ax_table_cell">
          <img id="u29_img" class="img " src="images/非学员人脸/u27.png"/>
          <!-- Unnamed () -->
          <div id="u30" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u31" class="ax_table_cell">
          <img id="u31_img" class="img " src="images/非学员人脸/u27.png"/>
          <!-- Unnamed () -->
          <div id="u32" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u33" class="ax_table_cell">
          <img id="u33_img" class="img " src="images/非学员人脸/u27.png"/>
          <!-- Unnamed () -->
          <div id="u34" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u35" class="ax_table_cell">
          <img id="u35_img" class="img " src="images/非学员人脸/u27.png"/>
          <!-- Unnamed () -->
          <div id="u36" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u37" class="ax_table_cell">
          <img id="u37_img" class="img " src="images/非学员人脸/u37.png"/>
          <!-- Unnamed () -->
          <div id="u38" class="text">
            <p><span></span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u39" class="ax_text_field">
        <input id="u39_input" type="text" value="开始时间"/>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u40" class="ax_text_field">
        <input id="u40_input" type="text" value="结束时间"/>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u41" class="ax_droplist">
        <select id="u41_input">
          <option value="所属驾校">所属驾校</option>
        </select>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u42" class="ax_droplist">
        <select id="u42_input">
          <option value="所属分校">所属分校</option>
        </select>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u43" class="ax_droplist">
        <select id="u43_input">
          <option value="所属训练场">所属训练场</option>
        </select>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u44" class="ax_html_button">
        <input id="u44_input" type="submit" value="查询"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u45" class="ax_html_button">
        <input id="u45_input" type="submit" value="导出"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u46" class="ax_html_button">
        <input id="u46_input" type="submit" value="删除"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u47" class="ax_h2">
        <img id="u47_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u48" class="text">
          <p><span>导出</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u49" class="ax_shape">
        <img id="u49_img" class="img " src="images/非学员人脸/u49.png"/>
        <!-- Unnamed () -->
        <div id="u50" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u51" class="ax_paragraph">
        <img id="u51_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u52" class="text">
          <p><span>只能导出小于5000条数据，超过5000条请缩小查询范围</span></p>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u53" class="ax_html_button">
        <input id="u53_input" type="submit" value="开始导出"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u54" class="ax_html_button">
        <input id="u54_input" type="submit" value="取消"/>
      </div>
    </div>
  </body>
</html>
