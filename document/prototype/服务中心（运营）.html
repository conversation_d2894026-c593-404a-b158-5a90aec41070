<!DOCTYPE html>
<html>
  <head>
    <title>服务中心（运营）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/服务中心（运营）/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/服务中心（运营）/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Shape) -->
      <div id="u0" class="ax_shape">
        <img id="u0_img" class="img " src="images/服务中心（运营）/u0.png"/>
        <!-- Unnamed () -->
        <div id="u1" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u2" class="ax_table">

        <!-- Unnamed (Table Cell) -->
        <div id="u3" class="ax_table_cell">
          <img id="u3_img" class="img " src="images/服务中心（运营）/u3.png"/>
          <!-- Unnamed () -->
          <div id="u4" class="text">
            <p><span>标题</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u5" class="ax_table_cell">
          <img id="u5_img" class="img " src="images/服务中心（运营）/u5.png"/>
          <!-- Unnamed () -->
          <div id="u6" class="text">
            <p><span>回</span><span>复</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u7" class="ax_table_cell">
          <img id="u7_img" class="img " src="images/服务中心（运营）/u7.png"/>
          <!-- Unnamed () -->
          <div id="u8" class="text">
            <p><span>排序</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u9" class="ax_table_cell">
          <img id="u9_img" class="img " src="images/运营数据（移动，运营）/u101.png"/>
          <!-- Unnamed () -->
          <div id="u10" class="text">
            <p><span>是否显示</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u11" class="ax_table_cell">
          <img id="u11_img" class="img " src="images/服务中心（运营）/u11.png"/>
          <!-- Unnamed () -->
          <div id="u12" class="text">
            <p><span>更新</span><span></span><span>时间</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u13" class="ax_table_cell">
          <img id="u13_img" class="img " src="images/服务中心（运营）/u3.png"/>
          <!-- Unnamed () -->
          <div id="u14" class="text">
            <p><span>报名流程是什么</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u15" class="ax_table_cell">
          <img id="u15_img" class="img " src="images/服务中心（运营）/u5.png"/>
          <!-- Unnamed () -->
          <div id="u16" class="text">
            <p><span>首先</span><span>......</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17" class="ax_table_cell">
          <img id="u17_img" class="img " src="images/服务中心（运营）/u7.png"/>
          <!-- Unnamed () -->
          <div id="u18" class="text">
            <p><span>1</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u19" class="ax_table_cell">
          <img id="u19_img" class="img " src="images/运营数据（移动，运营）/u101.png"/>
          <!-- Unnamed () -->
          <div id="u20" class="text">
            <p><span>是</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u21" class="ax_table_cell">
          <img id="u21_img" class="img " src="images/服务中心（运营）/u11.png"/>
          <!-- Unnamed () -->
          <div id="u22" class="text">
            <p><span>2023-01-02&nbsp; 14：23：14</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u23" class="ax_table_cell">
          <img id="u23_img" class="img " src="images/服务中心（运营）/u23.png"/>
          <!-- Unnamed () -->
          <div id="u24" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u25" class="ax_table_cell">
          <img id="u25_img" class="img " src="images/服务中心（运营）/u25.png"/>
          <!-- Unnamed () -->
          <div id="u26" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u27" class="ax_table_cell">
          <img id="u27_img" class="img " src="images/服务中心（运营）/u27.png"/>
          <!-- Unnamed () -->
          <div id="u28" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u29" class="ax_table_cell">
          <img id="u29_img" class="img " src="images/服务中心（运营）/u29.png"/>
          <!-- Unnamed () -->
          <div id="u30" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u31" class="ax_table_cell">
          <img id="u31_img" class="img " src="images/服务中心（运营）/u31.png"/>
          <!-- Unnamed () -->
          <div id="u32" class="text">
            <p><span></span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u33" class="ax_html_button">
        <input id="u33_input" type="submit" value="增加"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u34" class="ax_html_button">
        <input id="u34_input" type="submit" value="修改"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u35" class="ax_html_button">
        <input id="u35_input" type="submit" value="删除"/>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u36" class="ax_text_field">
        <input id="u36_input" type="text" value="输入标题搜索"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u37" class="ax_html_button">
        <input id="u37_input" type="submit" value="查询"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u38" class="ax_shape">
        <img id="u38_img" class="img " src="images/资讯内容（运营）/u44.png"/>
        <!-- Unnamed () -->
        <div id="u39" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u40" class="ax_h2">
        <img id="u40_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u41" class="text">
          <p><span>新增与修改</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u42" class="ax_paragraph">
        <img id="u42_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u43" class="text">
          <p><span>标题</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u44" class="ax_text_field">
        <input id="u44_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u45" class="ax_paragraph">
        <img id="u45_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u46" class="text">
          <p><span>回复</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u47" class="ax_image">
        <img id="u47_img" class="img " src="images/学车指引（运营）/u5.png"/>
        <!-- Unnamed () -->
        <div id="u48" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u49" class="ax_html_button">
        <input id="u49_input" type="submit" value="保存"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u50" class="ax_paragraph">
        <img id="u50_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u51" class="text">
          <p><span>排序</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u52" class="ax_text_field">
        <input id="u52_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u53" class="ax_paragraph">
        <img id="u53_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u54" class="text">
          <p><span>是否显示</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u55" class="ax_droplist">
        <select id="u55_input">
          <option value="是">是</option>
          <option value="否">否</option>
        </select>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u56" class="ax_paragraph">
        <img id="u56_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u57" class="text">
          <p><span>数字越小越排前面</span></p>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u58" class="ax_html_button">
        <input id="u58_input" type="submit" value="审核"/>
      </div>
    </div>
  </body>
</html>
