<!DOCTYPE html>
<html>
  <head>
    <title>填写资料</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/填写资料/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/填写资料/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Shape) -->
      <div id="u0" class="ax_shape">
        <img id="u0_img" class="img " src="images/填写资料/u0.png"/>
        <!-- Unnamed () -->
        <div id="u1" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u2" class="ax_shape">
        <img id="u2_img" class="img " src="images/选择驾驶证/u2.png"/>
        <!-- Unnamed () -->
        <div id="u3" class="text">
          <p><span>在线报名</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u4" class="ax_shape">
        <img id="u4_img" class="img " src="images/选择驾驶证/u4.png"/>
        <!-- Unnamed () -->
        <div id="u5" class="text">
          <p><span>分配学员号</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u6" class="ax_shape">
        <img id="u6_img" class="img " src="images/选择驾驶证/u2.png"/>
        <!-- Unnamed () -->
        <div id="u7" class="text">
          <p><span>报名审核</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u8" class="ax_shape">
        <img id="u8_img" class="img " src="images/选择驾驶证/u2.png"/>
        <!-- Unnamed () -->
        <div id="u9" class="text">
          <p><span>签定合同</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u10" class="ax_shape">
        <img id="u10_img" class="img " src="images/选择驾驶证/u2.png"/>
        <!-- Unnamed () -->
        <div id="u11" class="text">
          <p><span>在线交费</span></p>
        </div>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u12" class="ax_horizontal_line">
        <img id="u12_start" class="img " src="resources/images/transparent.gif" alt="u12_start"/>
        <img id="u12_end" class="img " src="resources/images/transparent.gif" alt="u12_end"/>
        <img id="u12_line" class="img " src="images/选择驾驶证/u12_line.png" alt="u12_line"/>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u13" class="ax_horizontal_line">
        <img id="u13_start" class="img " src="resources/images/transparent.gif" alt="u13_start"/>
        <img id="u13_end" class="img " src="resources/images/transparent.gif" alt="u13_end"/>
        <img id="u13_line" class="img " src="images/选择驾驶证/u12_line.png" alt="u13_line"/>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u14" class="ax_horizontal_line">
        <img id="u14_start" class="img " src="resources/images/transparent.gif" alt="u14_start"/>
        <img id="u14_end" class="img " src="resources/images/transparent.gif" alt="u14_end"/>
        <img id="u14_line" class="img " src="images/选择驾驶证/u12_line.png" alt="u14_line"/>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u15" class="ax_horizontal_line">
        <img id="u15_start" class="img " src="resources/images/transparent.gif" alt="u15_start"/>
        <img id="u15_end" class="img " src="resources/images/transparent.gif" alt="u15_end"/>
        <img id="u15_line" class="img " src="images/选择驾驶证/u12_line.png" alt="u15_line"/>
      </div>

      <!-- Unnamed (Horizontal Line) -->
      <div id="u16" class="ax_horizontal_line">
        <img id="u16_start" class="img " src="resources/images/transparent.gif" alt="u16_start"/>
        <img id="u16_end" class="img " src="resources/images/transparent.gif" alt="u16_end"/>
        <img id="u16_line" class="img " src="images/填写资料/u16_line.png" alt="u16_line"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u17" class="ax_paragraph">
        <img id="u17_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u18" class="text">
          <p><span>学员姓名：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u19" class="ax_text_field">
        <input id="u19_input" type="text" value="请输入姓名"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u20" class="ax_paragraph">
        <img id="u20_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u21" class="text">
          <p><span>证件类型</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u22" class="ax_droplist">
        <select id="u22_input">
          <option value="身份证">身份证</option>
        </select>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u23" class="ax_paragraph">
        <img id="u23_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u24" class="text">
          <p><span>证件号码</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u25" class="ax_text_field">
        <input id="u25_input" type="text" value="请输入证件号码"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u26" class="ax_paragraph">
        <img id="u26_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u27" class="text">
          <p><span>身份证</span><span>省市区</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u28" class="ax_droplist">
        <select id="u28_input">
          <option value="选择市">选择市</option>
        </select>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u29" class="ax_droplist">
        <select id="u29_input">
          <option value="选择省">选择省</option>
        </select>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u30" class="ax_droplist">
        <select id="u30_input">
          <option value="选择区">选择区</option>
        </select>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u31" class="ax_paragraph">
        <img id="u31_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u32" class="text">
          <p><span>身份证</span><span>详细地址</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u33" class="ax_text_field">
        <input id="u33_input" type="text" value="请输入证件详细地址"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u34" class="ax_paragraph">
        <img id="u34_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u35" class="text">
          <p><span>现居住地</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u36" class="ax_droplist">
        <select id="u36_input">
          <option value="选择市">选择市</option>
        </select>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u37" class="ax_droplist">
        <select id="u37_input">
          <option value="选择省">选择省</option>
        </select>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u38" class="ax_droplist">
        <select id="u38_input">
          <option value="选择区">选择区</option>
        </select>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u39" class="ax_paragraph">
        <img id="u39_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u40" class="text">
          <p><span>现居</span><span></span><span>详细地址</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u41" class="ax_text_field">
        <input id="u41_input" type="text" value="请输入居住地详细地址"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u42" class="ax_paragraph">
        <img id="u42_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u43" class="text">
          <p><span>手机号</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u44" class="ax_text_field">
        <input id="u44_input" type="text" value="请输入手机号码"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u45" class="ax_paragraph">
        <img id="u45_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u46" class="text">
          <p><span>图片验证码</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u47" class="ax_image">
        <img id="u47_img" class="img " src="images/填写资料/u47.png"/>
        <!-- Unnamed () -->
        <div id="u48" class="text">
          <p><span>4589</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u49" class="ax_text_field">
        <input id="u49_input" type="text" value="图片验证码"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u50" class="ax_paragraph">
        <img id="u50_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u51" class="text">
          <p><span>手机验证码</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u52" class="ax_text_field">
        <input id="u52_input" type="text" value="手机验证码"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u53" class="ax_paragraph">
        <img id="u53_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u54" class="text">
          <p><span>驾照类型</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u55" class="ax_h2">
        <img id="u55_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u56" class="text">
          <p><span>C1</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u57" class="ax_paragraph">
        <img id="u57_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u58" class="text">
          <p><span>业务类型</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u59" class="ax_droplist">
        <select id="u59_input">
          <option value="初领">初领</option>
        </select>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u60" class="ax_paragraph">
        <img id="u60_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u61" class="text">
          <p><span>驾</span><span>校名称</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u62" class="ax_paragraph">
        <img id="u62_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u63" class="text">
          <p><span>广仁驾校南城门店</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u64" class="ax_paragraph">
        <img id="u64_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u65" class="text">
          <p><span>照片</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u66" class="ax_image">
        <img id="u66_img" class="img " src="images/填写资料/u66.png"/>
        <!-- Unnamed () -->
        <div id="u67" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u68" class="ax_html_button">
        <input id="u68_input" type="submit" value="提交"/>
      </div>
    </div>
  </body>
</html>
