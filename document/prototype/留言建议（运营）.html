<!DOCTYPE html>
<html>
  <head>
    <title>留言建议（运营）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/留言建议（运营）/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/留言建议（运营）/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Table) -->
      <div id="u0" class="ax_table">

        <!-- Unnamed (Table Cell) -->
        <div id="u1" class="ax_table_cell">
          <img id="u1_img" class="img " src="images/留言建议（运营）/u1.png"/>
          <!-- Unnamed () -->
          <div id="u2" class="text">
            <p><span>留言类型</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3" class="ax_table_cell">
          <img id="u3_img" class="img " src="images/留言建议（运营）/u1.png"/>
          <!-- Unnamed () -->
          <div id="u4" class="text">
            <p><span>内容</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u5" class="ax_table_cell">
          <img id="u5_img" class="img " src="images/留言建议（运营）/u1.png"/>
          <!-- Unnamed () -->
          <div id="u6" class="text">
            <p><span>图片</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u7" class="ax_table_cell">
          <img id="u7_img" class="img " src="images/留言建议（运营）/u1.png"/>
          <!-- Unnamed () -->
          <div id="u8" class="text">
            <p><span>联系方式</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u9" class="ax_table_cell">
          <img id="u9_img" class="img " src="images/留言建议（运营）/u1.png"/>
          <!-- Unnamed () -->
          <div id="u10" class="text">
            <p><span>48小时允许联系</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u11" class="ax_table_cell">
          <img id="u11_img" class="img " src="images/留言建议（运营）/u11.png"/>
          <!-- Unnamed () -->
          <div id="u12" class="text">
            <p><span>提交时间</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u13" class="ax_table_cell">
          <img id="u13_img" class="img " src="images/留言建议（运营）/u1.png"/>
          <!-- Unnamed () -->
          <div id="u14" class="text">
            <p><span>产品建议</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u15" class="ax_table_cell">
          <img id="u15_img" class="img " src="images/留言建议（运营）/u1.png"/>
          <!-- Unnamed () -->
          <div id="u16" class="text">
            <p><span>无法看学时</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17" class="ax_table_cell">
          <img id="u17_img" class="img " src="images/留言建议（运营）/u1.png"/>
          <!-- Unnamed () -->
          <div id="u18" class="text">
            <p><span>查看</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u19" class="ax_table_cell">
          <img id="u19_img" class="img " src="images/留言建议（运营）/u1.png"/>
          <!-- Unnamed () -->
          <div id="u20" class="text">
            <p><span>18925062547</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u21" class="ax_table_cell">
          <img id="u21_img" class="img " src="images/留言建议（运营）/u1.png"/>
          <!-- Unnamed () -->
          <div id="u22" class="text">
            <p><span>是</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u23" class="ax_table_cell">
          <img id="u23_img" class="img " src="images/留言建议（运营）/u11.png"/>
          <!-- Unnamed () -->
          <div id="u24" class="text">
            <p><span>2023-01-02&nbsp; 14：23：14</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u25" class="ax_table_cell">
          <img id="u25_img" class="img " src="images/留言建议（运营）/u25.png"/>
          <!-- Unnamed () -->
          <div id="u26" class="text">
            <p><span>功能异常</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u27" class="ax_table_cell">
          <img id="u27_img" class="img " src="images/留言建议（运营）/u25.png"/>
          <!-- Unnamed () -->
          <div id="u28" class="text">
            <p><span>报名异常</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u29" class="ax_table_cell">
          <img id="u29_img" class="img " src="images/留言建议（运营）/u25.png"/>
          <!-- Unnamed () -->
          <div id="u30" class="text">
            <p><span>查看</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u31" class="ax_table_cell">
          <img id="u31_img" class="img " src="images/留言建议（运营）/u25.png"/>
          <!-- Unnamed () -->
          <div id="u32" class="text">
            <p><span><EMAIL></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u33" class="ax_table_cell">
          <img id="u33_img" class="img " src="images/留言建议（运营）/u25.png"/>
          <!-- Unnamed () -->
          <div id="u34" class="text">
            <p><span>否</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u35" class="ax_table_cell">
          <img id="u35_img" class="img " src="images/留言建议（运营）/u35.png"/>
          <!-- Unnamed () -->
          <div id="u36" class="text">
            <p><span>2023-01-02&nbsp; 14：23：14</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u37" class="ax_html_button">
        <input id="u37_input" type="submit" value="删除"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u38" class="ax_html_button">
        <input id="u38_input" type="submit" value="导出"/>
      </div>
    </div>
  </body>
</html>
