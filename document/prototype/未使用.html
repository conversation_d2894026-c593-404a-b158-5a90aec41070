<!DOCTYPE html>
<html>
  <head>
    <title>未使用</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/未使用/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/未使用/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Table) -->
      <div id="u0" class="ax_table">

        <!-- Unnamed (Table Cell) -->
        <div id="u1" class="ax_table_cell">
          <img id="u1_img" class="img " src="images/未使用/u1.png"/>
          <!-- Unnamed () -->
          <div id="u2" class="text">
            <p><span>批次</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3" class="ax_table_cell">
          <img id="u3_img" class="img " src="images/未使用/u3.png"/>
          <!-- Unnamed () -->
          <div id="u4" class="text">
            <p><span>号码</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u5" class="ax_table_cell">
          <img id="u5_img" class="img " src="images/未使用/u5.png"/>
          <!-- Unnamed () -->
          <div id="u6" class="text">
            <p><span>导入时间</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u7" class="ax_table_cell">
          <img id="u7_img" class="img " src="images/未使用/u1.png"/>
          <!-- Unnamed () -->
          <div id="u8" class="text">
            <p><span>20231024001</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u9" class="ax_table_cell">
          <img id="u9_img" class="img " src="images/未使用/u3.png"/>
          <!-- Unnamed () -->
          <div id="u10" class="text">
            <p><span>1892506156</span><span>2</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u11" class="ax_table_cell">
          <img id="u11_img" class="img " src="images/未使用/u5.png"/>
          <!-- Unnamed () -->
          <div id="u12" class="text">
            <p><span>2023-10-12 14:15:14</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u13" class="ax_table_cell">
          <img id="u13_img" class="img " src="images/未使用/u1.png"/>
          <!-- Unnamed () -->
          <div id="u14" class="text">
            <p><span>20231024001</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u15" class="ax_table_cell">
          <img id="u15_img" class="img " src="images/未使用/u3.png"/>
          <!-- Unnamed () -->
          <div id="u16" class="text">
            <p><span>1852564584</span><span>6</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17" class="ax_table_cell">
          <img id="u17_img" class="img " src="images/未使用/u5.png"/>
          <!-- Unnamed () -->
          <div id="u18" class="text">
            <p><span>2023-10-12 14:15:14</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u19" class="ax_table_cell">
          <img id="u19_img" class="img " src="images/未使用/u1.png"/>
          <!-- Unnamed () -->
          <div id="u20" class="text">
            <p><span>20231024001</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u21" class="ax_table_cell">
          <img id="u21_img" class="img " src="images/未使用/u3.png"/>
          <!-- Unnamed () -->
          <div id="u22" class="text">
            <p><span>1852564584</span><span>7</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u23" class="ax_table_cell">
          <img id="u23_img" class="img " src="images/未使用/u5.png"/>
          <!-- Unnamed () -->
          <div id="u24" class="text">
            <p><span>2023-10-12 14:15:14</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u25" class="ax_table_cell">
          <img id="u25_img" class="img " src="images/未使用/u25.png"/>
          <!-- Unnamed () -->
          <div id="u26" class="text">
            <p><span>2023102400</span><span>2</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u27" class="ax_table_cell">
          <img id="u27_img" class="img " src="images/未使用/u27.png"/>
          <!-- Unnamed () -->
          <div id="u28" class="text">
            <p><span>1852564584</span><span>8</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u29" class="ax_table_cell">
          <img id="u29_img" class="img " src="images/未使用/u29.png"/>
          <!-- Unnamed () -->
          <div id="u30" class="text">
            <p><span>2023-10-12 14:15:14</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u31" class="ax_html_button">
        <input id="u31_input" type="submit" value="导入"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u32" class="ax_html_button">
        <input id="u32_input" type="submit" value="删除"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u33" class="ax_html_button">
        <input id="u33_input" type="submit" value="导出"/>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u34" class="ax_text_field">
        <input id="u34_input" type="text" value="批次"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u35" class="ax_html_button">
        <input id="u35_input" type="submit" value="查询"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u36" class="ax_paragraph">
        <img id="u36_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u37" class="text">
          <p><span>开卡详情里面未开卡的数据以及新导入的数据</span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u38" class="ax_table">

        <!-- Unnamed (Table Cell) -->
        <div id="u39" class="ax_table_cell">
          <img id="u39_img" class="img " src="images/未使用/u39.png"/>
          <!-- Unnamed () -->
          <div id="u40" class="text">
            <p><span>号码</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u41" class="ax_table_cell">
          <img id="u41_img" class="img " src="images/未使用/u39.png"/>
          <!-- Unnamed () -->
          <div id="u42" class="text">
            <p><span>1892506156</span><span>2</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u43" class="ax_table_cell">
          <img id="u43_img" class="img " src="images/未使用/u43.png"/>
          <!-- Unnamed () -->
          <div id="u44" class="text">
            <p><span>1892506156</span><span>2</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u45" class="ax_paragraph">
        <img id="u45_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u46" class="text">
          <p><span>批次号自动生成，同一批导入批次和时间相同</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u47" class="ax_h2">
        <img id="u47_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u48" class="text">
          <p><span>导</span><span>入</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u49" class="ax_paragraph">
        <img id="u49_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u50" class="text">
          <p><span>前端优先呈现该表格号码，如果该表格没有号码，则呈现移动号码</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u51" class="ax_text_field">
        <input id="u51_input" type="text" value="号码"/>
      </div>
    </div>
  </body>
</html>
