<!DOCTYPE html>
<html>
  <head>
    <title>监控回放</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/监控回放/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/监控回放/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Shape) -->
      <div id="u0" class="ax_shape">
        <img id="u0_img" class="img " src="images/实时监控/u0.png"/>
        <!-- Unnamed () -->
        <div id="u1" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Vertical Line) -->
      <div id="u2" class="ax_vertical_line">
        <img id="u2_start" class="img " src="resources/images/transparent.gif" alt="u2_start"/>
        <img id="u2_end" class="img " src="resources/images/transparent.gif" alt="u2_end"/>
        <img id="u2_line" class="img " src="images/实时监控/u56_line.png" alt="u2_line"/>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u3" class="ax_image">
        <img id="u3_img" class="img " src="images/实时监控/u57.png"/>
        <!-- Unnamed () -->
        <div id="u4" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u5" class="ax_image">
        <img id="u5_img" class="img " src="images/实时监控/u57.png"/>
        <!-- Unnamed () -->
        <div id="u6" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u7" class="ax_image">
        <img id="u7_img" class="img " src="images/实时监控/u57.png"/>
        <!-- Unnamed () -->
        <div id="u8" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u9" class="ax_image">
        <img id="u9_img" class="img " src="images/实时监控/u57.png"/>
        <!-- Unnamed () -->
        <div id="u10" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u11" class="ax_image">
        <img id="u11_img" class="img " src="images/实时监控/u57.png"/>
        <!-- Unnamed () -->
        <div id="u12" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u13" class="ax_image">
        <img id="u13_img" class="img " src="images/实时监控/u57.png"/>
        <!-- Unnamed () -->
        <div id="u14" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u15" class="ax_image">
        <img id="u15_img" class="img " src="images/实时监控/u57.png"/>
        <!-- Unnamed () -->
        <div id="u16" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u17" class="ax_image">
        <img id="u17_img" class="img " src="images/实时监控/u57.png"/>
        <!-- Unnamed () -->
        <div id="u18" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u19" class="ax_image">
        <img id="u19_img" class="img " src="images/实时监控/u57.png"/>
        <!-- Unnamed () -->
        <div id="u20" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u21" class="ax_paragraph">
        <img id="u21_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u22" class="text">
          <p><span>宫格切换</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u23" class="ax_paragraph">
        <img id="u23_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u24" class="text">
          <p><span>驾校：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u25" class="ax_droplist">
        <select id="u25_input">
        </select>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u26" class="ax_paragraph">
        <img id="u26_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u27" class="text">
          <p><span>分校</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u28" class="ax_droplist">
        <select id="u28_input">
        </select>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u29" class="ax_paragraph">
        <img id="u29_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u30" class="text">
          <p><span>训练场</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u31" class="ax_droplist">
        <select id="u31_input">
        </select>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u32" class="ax_paragraph">
        <img id="u32_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u33" class="text">
          <p><span>摄像头</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u34" class="ax_droplist">
        <select id="u34_input">
        </select>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u35" class="ax_paragraph">
        <img id="u35_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u36" class="text">
          <p><span>开始时间</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u37" class="ax_text_field">
        <input id="u37_input" type="text" value="选择时间"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u38" class="ax_paragraph">
        <img id="u38_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u39" class="text">
          <p><span>结事</span><span></span><span>时间</span><span></span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u40" class="ax_text_field">
        <input id="u40_input" type="text" value="选择时间"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u41" class="ax_html_button">
        <input id="u41_input" type="submit" value="查询"/>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u42" class="ax_table">

        <!-- Unnamed (Table Cell) -->
        <div id="u43" class="ax_table_cell">
          <img id="u43_img" class="img " src="images/学时数据（驾校，分校，报名点，运营，监管查看）/u137.png"/>
          <!-- Unnamed () -->
          <div id="u44" class="text">
            <p><span>开始时间</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u45" class="ax_table_cell">
          <img id="u45_img" class="img " src="images/学时数据（驾校，分校，报名点，运营，监管查看）/u137.png"/>
          <!-- Unnamed () -->
          <div id="u46" class="text">
            <p><span>结束时间</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u47" class="ax_table_cell">
          <img id="u47_img" class="img " src="images/监控回放/u47.png"/>
          <!-- Unnamed () -->
          <div id="u48" class="text">
            <p><span>操作</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u49" class="ax_table_cell">
          <img id="u49_img" class="img " src="images/学时数据（驾校，分校，报名点，运营，监管查看）/u137.png"/>
          <!-- Unnamed () -->
          <div id="u50" class="text">
            <p><span>09：00</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u51" class="ax_table_cell">
          <img id="u51_img" class="img " src="images/学时数据（驾校，分校，报名点，运营，监管查看）/u137.png"/>
          <!-- Unnamed () -->
          <div id="u52" class="text">
            <p><span>10：00</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u53" class="ax_table_cell">
          <img id="u53_img" class="img " src="images/监控回放/u47.png"/>
          <!-- Unnamed () -->
          <div id="u54" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u55" class="ax_table_cell">
          <img id="u55_img" class="img " src="images/监控回放/u55.png"/>
          <!-- Unnamed () -->
          <div id="u56" class="text">
            <p><span>10：00</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u57" class="ax_table_cell">
          <img id="u57_img" class="img " src="images/监控回放/u55.png"/>
          <!-- Unnamed () -->
          <div id="u58" class="text">
            <p><span>11：00</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u59" class="ax_table_cell">
          <img id="u59_img" class="img " src="images/监控回放/u59.png"/>
          <!-- Unnamed () -->
          <div id="u60" class="text">
            <p><span>播放</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u61" class="ax_paragraph">
        <img id="u61_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u62" class="text">
          <p><span>播</span><span>放</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u63" class="ax_paragraph">
        <img id="u63_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u64" class="text">
          <p><span>查询结果：</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u65" class="ax_paragraph">
        <img id="u65_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u66" class="text">
          <p><span>点击多个文件可以多个宫格播放，不支持快进，回退</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
