<!DOCTYPE html>
<html>
  <head>
    <title>用户管理（运营）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/用户管理（运营）/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/用户管理（运营）/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Shape) -->
      <div id="u0" class="ax_shape">
        <img id="u0_img" class="img " src="images/用户管理（运营）/u0.png"/>
        <!-- Unnamed () -->
        <div id="u1" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u2" class="ax_table">

        <!-- Unnamed (Table Cell) -->
        <div id="u3" class="ax_table_cell">
          <img id="u3_img" class="img " src="images/设备列表/u11.png"/>
          <!-- Unnamed () -->
          <div id="u4" class="text">
            <p><span>用户名</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u5" class="ax_table_cell">
          <img id="u5_img" class="img " src="images/设备列表/u11.png"/>
          <!-- Unnamed () -->
          <div id="u6" class="text">
            <p><span>真实姓名</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u7" class="ax_table_cell">
          <img id="u7_img" class="img " src="images/设备列表/u11.png"/>
          <!-- Unnamed () -->
          <div id="u8" class="text">
            <p><span>联系方式</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u9" class="ax_table_cell">
          <img id="u9_img" class="img " src="images/设备列表/u11.png"/>
          <!-- Unnamed () -->
          <div id="u10" class="text">
            <p><span>所属</span><span></span><span>角色</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u11" class="ax_table_cell">
          <img id="u11_img" class="img " src="images/用户管理（运营）/u11.png"/>
          <!-- Unnamed () -->
          <div id="u12" class="text">
            <p><span>状态</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u13" class="ax_table_cell">
          <img id="u13_img" class="img " src="images/设备列表/u11.png"/>
          <!-- Unnamed () -->
          <div id="u14" class="text">
            <p><span>chengping</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u15" class="ax_table_cell">
          <img id="u15_img" class="img " src="images/设备列表/u11.png"/>
          <!-- Unnamed () -->
          <div id="u16" class="text">
            <p><span>陈兵</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17" class="ax_table_cell">
          <img id="u17_img" class="img " src="images/设备列表/u11.png"/>
          <!-- Unnamed () -->
          <div id="u18" class="text">
            <p><span>18695625123</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u19" class="ax_table_cell">
          <img id="u19_img" class="img " src="images/设备列表/u11.png"/>
          <!-- Unnamed () -->
          <div id="u20" class="text">
            <p><span>管理员</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u21" class="ax_table_cell">
          <img id="u21_img" class="img " src="images/用户管理（运营）/u11.png"/>
          <!-- Unnamed () -->
          <div id="u22" class="text">
            <p><span>启用</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u23" class="ax_table_cell">
          <img id="u23_img" class="img " src="images/设备列表/u43.png"/>
          <!-- Unnamed () -->
          <div id="u24" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u25" class="ax_table_cell">
          <img id="u25_img" class="img " src="images/设备列表/u43.png"/>
          <!-- Unnamed () -->
          <div id="u26" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u27" class="ax_table_cell">
          <img id="u27_img" class="img " src="images/设备列表/u43.png"/>
          <!-- Unnamed () -->
          <div id="u28" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u29" class="ax_table_cell">
          <img id="u29_img" class="img " src="images/设备列表/u43.png"/>
          <!-- Unnamed () -->
          <div id="u30" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u31" class="ax_table_cell">
          <img id="u31_img" class="img " src="images/用户管理（运营）/u31.png"/>
          <!-- Unnamed () -->
          <div id="u32" class="text">
            <p><span></span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u33" class="ax_html_button">
        <input id="u33_input" type="submit" value="新增"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u34" class="ax_html_button">
        <input id="u34_input" type="submit" value="修改"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u35" class="ax_html_button">
        <input id="u35_input" type="submit" value="删除"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u36" class="ax_html_button">
        <input id="u36_input" type="submit" value="重置密码"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u37" class="ax_h2">
        <img id="u37_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u38" class="text">
          <p><span>新增</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u39" class="ax_shape">
        <img id="u39_img" class="img " src="images/用户管理（运营）/u39.png"/>
        <!-- Unnamed () -->
        <div id="u40" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u41" class="ax_paragraph">
        <img id="u41_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u42" class="text">
          <p><span>真实姓名</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u43" class="ax_text_field">
        <input id="u43_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u44" class="ax_paragraph">
        <img id="u44_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u45" class="text">
          <p><span>登录用户名</span><span></span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u46" class="ax_text_field">
        <input id="u46_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u47" class="ax_paragraph">
        <img id="u47_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u48" class="text">
          <p><span>密码</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u49" class="ax_text_field">
        <input id="u49_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u50" class="ax_paragraph">
        <img id="u50_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u51" class="text">
          <p><span>字母，数字，特殊字符组合，不少8位</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u52" class="ax_paragraph">
        <img id="u52_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u53" class="text">
          <p><span>确认</span><span>密码</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u54" class="ax_text_field">
        <input id="u54_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u55" class="ax_paragraph">
        <img id="u55_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u56" class="text">
          <p><span>联系方式</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u57" class="ax_text_field">
        <input id="u57_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u58" class="ax_paragraph">
        <img id="u58_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u59" class="text">
          <p><span>是否启用</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u60" class="ax_droplist">
        <select id="u60_input">
          <option value="启用">启用</option>
          <option value="禁用">禁用</option>
        </select>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u61" class="ax_html_button">
        <input id="u61_input" type="submit" value="保存"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u62" class="ax_h2">
        <img id="u62_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u63" class="text">
          <p><span>修改</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u64" class="ax_shape">
        <img id="u64_img" class="img " src="images/机构用户（运营，驾校，分校，报名点，移动，监管）/u108.png"/>
        <!-- Unnamed () -->
        <div id="u65" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u66" class="ax_paragraph">
        <img id="u66_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u67" class="text">
          <p><span>真实姓名</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u68" class="ax_text_field">
        <input id="u68_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u69" class="ax_paragraph">
        <img id="u69_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u70" class="text">
          <p><span>登录用户名</span><span></span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u71" class="ax_text_field">
        <input id="u71_input" type="text" value="liucheng" disabled readonly/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u72" class="ax_paragraph">
        <img id="u72_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u73" class="text">
          <p><span>联系方式</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u74" class="ax_text_field">
        <input id="u74_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u75" class="ax_paragraph">
        <img id="u75_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u76" class="text">
          <p><span>是否启用</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u77" class="ax_droplist">
        <select id="u77_input">
          <option value="启用">启用</option>
          <option value="禁用">禁用</option>
        </select>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u78" class="ax_html_button">
        <input id="u78_input" type="submit" value="保存"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u79" class="ax_h2">
        <img id="u79_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u80" class="text">
          <p><span>重置密码</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u81" class="ax_shape">
        <img id="u81_img" class="img " src="images/机构用户（运营，驾校，分校，报名点，移动，监管）/u125.png"/>
        <!-- Unnamed () -->
        <div id="u82" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u83" class="ax_paragraph">
        <img id="u83_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u84" class="text">
          <p><span>密码</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u85" class="ax_text_field">
        <input id="u85_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u86" class="ax_paragraph">
        <img id="u86_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u87" class="text">
          <p><span>字母，数字，特殊字符组合，不少8位</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u88" class="ax_paragraph">
        <img id="u88_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u89" class="text">
          <p><span>确认</span><span>密码</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u90" class="ax_text_field">
        <input id="u90_input" type="text" value=""/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u91" class="ax_html_button">
        <input id="u91_input" type="submit" value="保存"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u92" class="ax_paragraph">
        <img id="u92_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u93" class="text">
          <p><span>用户名不可修改</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
