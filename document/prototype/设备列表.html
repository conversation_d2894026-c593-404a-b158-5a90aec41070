<!DOCTYPE html>
<html>
  <head>
    <title>设备列表</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/设备列表/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/设备列表/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Shape) -->
      <div id="u0" class="ax_shape">
        <img id="u0_img" class="img " src="images/设备列表/u0.png"/>
        <!-- Unnamed () -->
        <div id="u1" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Table) -->
      <div id="u2" class="ax_table">

        <!-- Unnamed (Table Cell) -->
        <div id="u3" class="ax_table_cell">
          <img id="u3_img" class="img " src="images/设备列表/u3.png"/>
          <!-- Unnamed () -->
          <div id="u4" class="text">
            <p><span>设备</span><span>序列号</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u5" class="ax_table_cell">
          <img id="u5_img" class="img " src="images/设备列表/u3.png"/>
          <!-- Unnamed () -->
          <div id="u6" class="text">
            <p><span>设备名称</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u7" class="ax_table_cell">
          <img id="u7_img" class="img " src="images/设备列表/u3.png"/>
          <!-- Unnamed () -->
          <div id="u8" class="text">
            <p><span>设备类型</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u9" class="ax_table_cell">
          <img id="u9_img" class="img " src="images/设备列表/u3.png"/>
          <!-- Unnamed () -->
          <div id="u10" class="text">
            <p><span>在线</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u11" class="ax_table_cell">
          <img id="u11_img" class="img " src="images/设备列表/u11.png"/>
          <!-- Unnamed () -->
          <div id="u12" class="text">
            <p><span>同步时间</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u13" class="ax_table_cell">
          <img id="u13_img" class="img " src="images/设备列表/u13.png"/>
          <!-- Unnamed () -->
          <div id="u14" class="text">
            <p><span>所属训练场</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u15" class="ax_table_cell">
          <img id="u15_img" class="img " src="images/设备列表/u15.png"/>
          <!-- Unnamed () -->
          <div id="u16" class="text">
            <p><span>所属分校</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17" class="ax_table_cell">
          <img id="u17_img" class="img " src="images/设备列表/u17.png"/>
          <!-- Unnamed () -->
          <div id="u18" class="text">
            <p><span>所属驾校</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u19" class="ax_table_cell">
          <img id="u19_img" class="img " src="images/设备列表/u19.png"/>
          <!-- Unnamed () -->
          <div id="u20" class="text">
            <p><span>E285964-1</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u21" class="ax_table_cell">
          <img id="u21_img" class="img " src="images/设备列表/u19.png"/>
          <!-- Unnamed () -->
          <div id="u22" class="text">
            <p><span>右上角</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u23" class="ax_table_cell">
          <img id="u23_img" class="img " src="images/设备列表/u19.png"/>
          <!-- Unnamed () -->
          <div id="u24" class="text">
            <p><span>IPC</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u25" class="ax_table_cell">
          <img id="u25_img" class="img " src="images/设备列表/u19.png"/>
          <!-- Unnamed () -->
          <div id="u26" class="text">
            <p><span>在线</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u27" class="ax_table_cell">
          <img id="u27_img" class="img " src="images/设备列表/u27.png"/>
          <!-- Unnamed () -->
          <div id="u28" class="text">
            <p><span>2023-04-04 02：00：00</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u29" class="ax_table_cell">
          <img id="u29_img" class="img " src="images/设备列表/u29.png"/>
          <!-- Unnamed () -->
          <div id="u30" class="text">
            <p><span>袁屋边</span><span>训练场</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u31" class="ax_table_cell">
          <img id="u31_img" class="img " src="images/设备列表/u31.png"/>
          <!-- Unnamed () -->
          <div id="u32" class="text">
            <p><span>南城分校</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u33" class="ax_table_cell">
          <img id="u33_img" class="img " src="images/设备列表/u33.png"/>
          <!-- Unnamed () -->
          <div id="u34" class="text">
            <p><span>东南驾校</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u35" class="ax_table_cell">
          <img id="u35_img" class="img " src="images/设备列表/u35.png"/>
          <!-- Unnamed () -->
          <div id="u36" class="text">
            <p><span>E285964-</span><span>2</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u37" class="ax_table_cell">
          <img id="u37_img" class="img " src="images/设备列表/u35.png"/>
          <!-- Unnamed () -->
          <div id="u38" class="text">
            <p><span>左上角</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u39" class="ax_table_cell">
          <img id="u39_img" class="img " src="images/设备列表/u35.png"/>
          <!-- Unnamed () -->
          <div id="u40" class="text">
            <p><span>IPC</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u41" class="ax_table_cell">
          <img id="u41_img" class="img " src="images/设备列表/u35.png"/>
          <!-- Unnamed () -->
          <div id="u42" class="text">
            <p><span>离线</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u43" class="ax_table_cell">
          <img id="u43_img" class="img " src="images/设备列表/u43.png"/>
          <!-- Unnamed () -->
          <div id="u44" class="text">
            <p><span>2023-04-04 02：00：00</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u45" class="ax_table_cell">
          <img id="u45_img" class="img " src="images/设备列表/u45.png"/>
          <!-- Unnamed () -->
          <div id="u46" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u47" class="ax_table_cell">
          <img id="u47_img" class="img " src="images/设备列表/u47.png"/>
          <!-- Unnamed () -->
          <div id="u48" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u49" class="ax_table_cell">
          <img id="u49_img" class="img " src="images/设备列表/u49.png"/>
          <!-- Unnamed () -->
          <div id="u50" class="text">
            <p><span></span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u51" class="ax_html_button">
        <input id="u51_input" type="submit" value="手动同步监控系统数据"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u52" class="ax_html_button">
        <input id="u52_input" type="submit" value="分配训练场"/>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u53" class="ax_text_field">
        <input id="u53_input" type="text" value="设备名称"/>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u54" class="ax_droplist">
        <select id="u54_input">
          <option value="选择驾校">选择驾校</option>
        </select>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u55" class="ax_droplist">
        <select id="u55_input">
          <option value="选择分校">选择分校</option>
        </select>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u56" class="ax_html_button">
        <input id="u56_input" type="submit" value="查询"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u57" class="ax_html_button">
        <input id="u57_input" type="submit" value="导出"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u58" class="ax_shape">
        <img id="u58_img" class="img " src="images/设备列表/u58.png"/>
        <!-- Unnamed () -->
        <div id="u59" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u60" class="ax_h2">
        <img id="u60_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u61" class="text">
          <p><span>手动同步监控系统数据</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u62" class="ax_paragraph">
        <img id="u62_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u63" class="text">
          <p><span>数据正在同步，请稍候.....</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u64" class="ax_shape">
        <img id="u64_img" class="img " src="images/设备列表/u58.png"/>
        <!-- Unnamed () -->
        <div id="u65" class="text">
          <p><span>同步成功！</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u66" class="ax_h2">
        <img id="u66_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u67" class="text">
          <p><span>分配训练场</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u68" class="ax_shape">
        <img id="u68_img" class="img " src="images/设备列表/u68.png"/>
        <!-- Unnamed () -->
        <div id="u69" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u70" class="ax_paragraph">
        <img id="u70_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u71" class="text">
          <p><span>所属驾校</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u72" class="ax_droplist">
        <select id="u72_input">
        </select>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u73" class="ax_paragraph">
        <img id="u73_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u74" class="text">
          <p><span>所属</span><span>分校</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u75" class="ax_droplist">
        <select id="u75_input">
        </select>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u76" class="ax_paragraph">
        <img id="u76_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u77" class="text">
          <p><span>所属</span><span>训练场</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u78" class="ax_droplist">
        <select id="u78_input">
        </select>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u79" class="ax_html_button">
        <input id="u79_input" type="submit" value="保存"/>
      </div>
    </div>
  </body>
</html>
