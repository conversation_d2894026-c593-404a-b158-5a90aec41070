$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,g,h,[i],j,_(k,l,m,n,o,p,q,_(),r,_(s,t,u,v,w,_(x,y,z,A),B,null,C,v,D,v,E,F,G,null,H,I,J,K,L,M,N,I),O,_(),P,_(),Q,_(R,[_(S,T,U,V,m,W,X,W,Y,Z,r,_(ba,_(bb,bc,bd,be),bf,_(bg,bh,bi,bj)),O,_(),R,[_(S,bk,U,V,bl,Z,m,bm,X,bn,Y,Z,r,_(ba,_(bb,bc,bd,be),bf,_(bg,bh,bi,bj)),O,_())],bo,_(bp,bq)),_(S,br,U,V,m,bs,X,bs,Y,Z,r,_(ba,_(bb,bt,bd,bu),bf,_(bg,bv,bi,bw)),O,_()),_(S,bx,U,V,m,W,X,bn,Y,Z,r,_(ba,_(bb,by,bd,bz),bf,_(bg,bA,bi,bB)),O,_(),R,[_(S,bC,U,V,bl,Z,m,bm,X,bn,Y,Z,r,_(ba,_(bb,by,bd,bz),bf,_(bg,bA,bi,bB)),O,_())],bo,_(bp,bD)),_(S,bE,U,V,m,W,X,bn,Y,Z,r,_(ba,_(bb,bF,bd,bG),bf,_(bg,bH,bi,bB)),O,_(),R,[_(S,bI,U,V,bl,Z,m,bm,X,bn,Y,Z,r,_(ba,_(bb,bF,bd,bG),bf,_(bg,bH,bi,bB)),O,_())],bo,_(bp,bD)),_(S,bJ,U,V,m,bK,X,bK,Y,Z,r,_(ba,_(bb,bt,bd,bL),bf,_(bg,bv,bi,bM)),O,_()),_(S,bN,U,V,m,bs,X,bs,Y,Z,r,_(ba,_(bb,bt,bd,bO),bf,_(bg,bv,bi,bw)),O,_()),_(S,bP,U,V,m,W,X,bn,Y,Z,r,_(ba,_(bb,by,bd,bQ),bf,_(bg,bc,bi,bB)),O,_(),R,[_(S,bR,U,V,bl,Z,m,bm,X,bn,Y,Z,r,_(ba,_(bb,by,bd,bQ),bf,_(bg,bc,bi,bB)),O,_())],bo,_(bp,bD)),_(S,bS,U,V,m,bT,X,bT,Y,Z,r,_(ba,_(bb,bt,bd,bU),bf,_(bg,bV,bi,bw)),O,_())])),bW,_(),bX,_(bY,_(bZ,ca),cb,_(bZ,cc),cd,_(bZ,ce),cf,_(bZ,cg),ch,_(bZ,ci),cj,_(bZ,ck),cl,_(bZ,cm),cn,_(bZ,co),cp,_(bZ,cq),cr,_(bZ,cs),ct,_(bZ,cu),cv,_(bZ,cw)));}; 
var b="url",c="驾校端登录页.html",d="generationDate",e=new Date(1709167784991.47),f="isCanvasEnabled",g=false,h="variables",i="OnLoadVariable",j="page",k="packageId",l="9a05baa25c8347a2bbebfb3f27476228",m="type",n="Axure:Page",o="name",p="驾校端登录页",q="notes",r="style",s="baseStyle",t="627587b6038d43cca051c114ac41ad32",u="pageAlignment",v="near",w="fill",x="fillType",y="solid",z="color",A=0xFFFFFFFF,B="image",C="imageHorizontalAlignment",D="imageVerticalAlignment",E="imageRepeat",F="auto",G="favicon",H="sketchFactor",I="0",J="colorStyle",K="appliedColor",L="fontName",M="Applied Font",N="borderWidth",O="adaptiveStyles",P="interactionMap",Q="diagram",R="objects",S="id",T="84d661e558b446df9add9d77eaff4577",U="label",V="",W="buttonShape",X="styleType",Y="visible",Z=true,ba="location",bb="x",bc=40,bd="y",be=30,bf="size",bg="width",bh=990,bi="height",bj=430,bk="9805ae85d1f448f1a3660505e24a4a65",bl="isContained",bm="richTextPanel",bn="paragraph",bo="images",bp="normal~",bq="images/驾校端登录页/u0.png",br="ae00122fd51d4fee8ca23b1b4cdcb2f1",bs="textBox",bt=380,bu=249,bv=200,bw=25,bx="********************************",by=306,bz=254,bA=53,bB=16,bC="6d58a1f060fb4461b3fa38bab19056c4",bD="resources/images/transparent.gif",bE="15231275c0064ddfa270b0abc4511032",bF=266,bG=207,bH=92,bI="4746d8097fd24a74a286a95350fd0d70",bJ="c92a50911f7f4b6484ae9e81931637ec",bK="comboBox",bL=205,bM=22,bN="********************************",bO=295,bP="2a45011c02f7443495f2b05b57f4ebeb",bQ=300,bR="03f7417158e94213a4556dbca634cccd",bS="1268839bb925471d8c24036d3679bdf2",bT="button",bU=345,bV=100,bW="masters",bX="objectPaths",bY="84d661e558b446df9add9d77eaff4577",bZ="scriptId",ca="u0",cb="9805ae85d1f448f1a3660505e24a4a65",cc="u1",cd="ae00122fd51d4fee8ca23b1b4cdcb2f1",ce="u2",cf="********************************",cg="u3",ch="6d58a1f060fb4461b3fa38bab19056c4",ci="u4",cj="15231275c0064ddfa270b0abc4511032",ck="u5",cl="4746d8097fd24a74a286a95350fd0d70",cm="u6",cn="c92a50911f7f4b6484ae9e81931637ec",co="u7",cp="********************************",cq="u8",cr="2a45011c02f7443495f2b05b57f4ebeb",cs="u9",ct="03f7417158e94213a4556dbca634cccd",cu="u10",cv="1268839bb925471d8c24036d3679bdf2",cw="u11";
return _creator();
})());