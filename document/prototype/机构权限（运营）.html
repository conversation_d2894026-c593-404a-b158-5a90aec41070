<!DOCTYPE html>
<html>
  <head>
    <title>机构权限（运营）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/机构权限（运营）/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/机构权限（运营）/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Shape) -->
      <div id="u0" class="ax_shape">
        <img id="u0_img" class="img " src="images/机构权限（运营）/u0.png"/>
        <!-- Unnamed () -->
        <div id="u1" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u2" class="ax_html_button">
        <input id="u2_input" type="submit" value="移动方"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u3" class="ax_html_button">
        <input id="u3_input" type="submit" value="监管方"/>
      </div>

      <!-- Unnamed (Vertical Line) -->
      <div id="u4" class="ax_vertical_line">
        <img id="u4_start" class="img " src="resources/images/transparent.gif" alt="u4_start"/>
        <img id="u4_end" class="img " src="resources/images/transparent.gif" alt="u4_end"/>
        <img id="u4_line" class="img " src="images/机构权限（运营）/u4_line.png" alt="u4_line"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u5" class="ax_html_button">
        <input id="u5_input" type="submit" value="驾校方"/>
      </div>

      <!-- Unnamed (Tree) -->
      <div id="u6" class="ax_tree_node treeroot">
        <div id="u6_children" class="u6_children">

          <!-- Unnamed (Tree Node) -->
          <div id="u7" class="ax_tree_node treenode">

            <!-- Unnamed (Image) -->
            <div id="u8" class="ax_image">
              <img id="u8_img" class="img " src="images/实时监控/u4_selected.png"/>
              <!-- Unnamed () -->
              <div id="u9" class="text">
                <p><span></span></p>
              </div>
            </div>
            <!-- Unnamed (Shape) -->
            <div id="u10" class="" selectiongroup="u6_tree_group">
              <img id="u10_img" class="img " src="resources/images/transparent.gif"/>
              <!-- Unnamed () -->
              <div id="u11" class="text">
                <p><span>权限列表</span></p>
              </div>
            </div>
            <div id="u7_children" class="u7_children">

              <!-- Unnamed (Tree Node) -->
              <div id="u12" class="ax_tree_node treenode">

                <!-- Unnamed (Image) -->
                <div id="u13" class="ax_image">
                  <img id="u13_img" class="img " src="images/实时监控/u4_selected.png"/>
                  <!-- Unnamed () -->
                  <div id="u14" class="text">
                    <p><span></span></p>
                  </div>
                </div>
                <!-- Unnamed (Shape) -->
                <div id="u15" class="" selectiongroup="u6_tree_group">
                  <img id="u15_img" class="img " src="resources/images/transparent.gif"/>
                  <!-- Unnamed () -->
                  <div id="u16" class="text">
                    <p><span>学员管理</span></p>
                  </div>
                </div>
                <div id="u12_children" class="u12_children">

                  <!-- Unnamed (Tree Node) -->
                  <div id="u17" class="ax_tree_node treenode">

                    <!-- Unnamed (Image) -->
                    <div id="u18" class="ax_image">
                      <img id="u18_img" class="img " src="images/实时监控/u4_selected.png"/>
                      <!-- Unnamed () -->
                      <div id="u19" class="text">
                        <p><span></span></p>
                      </div>
                    </div>
                    <!-- Unnamed (Shape) -->
                    <div id="u20" class="" selectiongroup="u6_tree_group">
                      <img id="u20_img" class="img " src="resources/images/transparent.gif"/>
                      <!-- Unnamed () -->
                      <div id="u21" class="text">
                        <p><span>学员管理</span></p>
                      </div>
                    </div>
                    <div id="u17_children" class="u17_children">

                      <!-- Unnamed (Tree Node) -->
                      <div id="u22" class="ax_tree_node treenode">
                        <!-- Unnamed (Shape) -->
                        <div id="u23" class="" selectiongroup="u6_tree_group">
                          <img id="u23_img" class="img " src="resources/images/transparent.gif"/>
                          <!-- Unnamed () -->
                          <div id="u24" class="text">
                            <p><span>新增</span></p>
                          </div>
                        </div>
                      </div>

                      <!-- Unnamed (Tree Node) -->
                      <div id="u25" class="ax_tree_node treenode">
                        <!-- Unnamed (Shape) -->
                        <div id="u26" class="" selectiongroup="u6_tree_group">
                          <img id="u26_img" class="img " src="resources/images/transparent.gif"/>
                          <!-- Unnamed () -->
                          <div id="u27" class="text">
                            <p><span>查看</span></p>
                          </div>
                        </div>
                      </div>

                      <!-- Unnamed (Tree Node) -->
                      <div id="u28" class="ax_tree_node treenode">
                        <!-- Unnamed (Shape) -->
                        <div id="u29" class="" selectiongroup="u6_tree_group">
                          <img id="u29_img" class="img " src="resources/images/transparent.gif"/>
                          <!-- Unnamed () -->
                          <div id="u30" class="text">
                            <p><span>修改</span></p>
                          </div>
                        </div>
                      </div>

                      <!-- Unnamed (Tree Node) -->
                      <div id="u31" class="ax_tree_node treenode">
                        <!-- Unnamed (Shape) -->
                        <div id="u32" class="" selectiongroup="u6_tree_group">
                          <img id="u32_img" class="img " src="resources/images/transparent.gif"/>
                          <!-- Unnamed () -->
                          <div id="u33" class="text">
                            <p><span>删除</span></p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Unnamed (Tree Node) -->
                  <div id="u34" class="ax_tree_node treenode">
                    <!-- Unnamed (Shape) -->
                    <div id="u35" class="" selectiongroup="u6_tree_group">
                      <img id="u35_img" class="img " src="resources/images/transparent.gif"/>
                      <!-- Unnamed () -->
                      <div id="u36" class="text">
                        <p><span>学时数据</span></p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Unnamed (Tree Node) -->
              <div id="u37" class="ax_tree_node treenode">
                <!-- Unnamed (Shape) -->
                <div id="u38" class="" selectiongroup="u6_tree_group">
                  <img id="u38_img" class="img " src="resources/images/transparent.gif"/>
                  <!-- Unnamed () -->
                  <div id="u39" class="text">
                    <p><span>车辆管理</span></p>
                  </div>
                </div>
              </div>

              <!-- Unnamed (Tree Node) -->
              <div id="u40" class="ax_tree_node treenode">
                <!-- Unnamed (Shape) -->
                <div id="u41" class="" selectiongroup="u6_tree_group">
                  <img id="u41_img" class="img " src="resources/images/transparent.gif"/>
                  <!-- Unnamed () -->
                  <div id="u42" class="text">
                    <p><span>教练管理</span></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u43" class="ax_html_button">
        <input id="u43_input" type="submit" value="保存"/>
      </div>
    </div>
  </body>
</html>
