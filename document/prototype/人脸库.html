<!DOCTYPE html>
<html>
  <head>
    <title>人脸库</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/人脸库/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/人脸库/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Shape) -->
      <div id="u0" class="ax_shape">
        <img id="u0_img" class="img " src="images/人脸库/u0.png"/>
        <!-- Unnamed () -->
        <div id="u1" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u2" class="ax_image">
        <img id="u2_img" class="img " src="images/人脸库/u2.png"/>
        <!-- Unnamed () -->
        <div id="u3" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u4" class="ax_paragraph">
        <img id="u4_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u5" class="text">
          <p><span>刘</span><span>小厦</span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u6" class="ax_image">
        <img id="u6_img" class="img " src="images/人脸库/u2.png"/>
        <!-- Unnamed () -->
        <div id="u7" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u8" class="ax_paragraph">
        <img id="u8_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u9" class="text">
          <p><span>成明</span></p>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u10" class="ax_html_button">
        <input id="u10_input" type="submit" value="从学员导入"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u11" class="ax_html_button">
        <input id="u11_input" type="submit" value="上传人脸"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u12" class="ax_html_button">
        <input id="u12_input" type="submit" value="上一页"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u13" class="ax_html_button">
        <input id="u13_input" type="submit" value="下一页"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u14" class="ax_paragraph">
        <img id="u14_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u15" class="text">
          <p><span>X</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u16" class="ax_paragraph">
        <img id="u16_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u17" class="text">
          <p><span>X</span></p>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u18" class="ax_html_button">
        <input id="u18_input" type="submit" value="下发到监控系统"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u19" class="ax_html_button">
        <input id="u19_input" type="submit" value="所有人脸下发到监控系统"/>
      </div>

      <!-- Unnamed (Checkbox) -->
      <div id="u20" class="ax_checkbox">
        <label for="u20_input">
          <!-- Unnamed () -->
          <div id="u21" class="text">
            <p><span></span></p>
          </div>
        </label>
        <input id="u20_input" type="checkbox" value="checkbox"/>
      </div>

      <!-- Unnamed (Checkbox) -->
      <div id="u22" class="ax_checkbox">
        <label for="u22_input">
          <!-- Unnamed () -->
          <div id="u23" class="text">
            <p><span></span></p>
          </div>
        </label>
        <input id="u22_input" type="checkbox" value="checkbox"/>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u24" class="ax_image">
        <img id="u24_img" class="img " src="images/人脸库/u2.png"/>
        <!-- Unnamed () -->
        <div id="u25" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u26" class="ax_paragraph">
        <img id="u26_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u27" class="text">
          <p><span>刘</span><span>小厦</span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u28" class="ax_image">
        <img id="u28_img" class="img " src="images/人脸库/u2.png"/>
        <!-- Unnamed () -->
        <div id="u29" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u30" class="ax_paragraph">
        <img id="u30_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u31" class="text">
          <p><span>成明</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u32" class="ax_paragraph">
        <img id="u32_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u33" class="text">
          <p><span>X</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u34" class="ax_paragraph">
        <img id="u34_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u35" class="text">
          <p><span>X</span></p>
        </div>
      </div>

      <!-- Unnamed (Checkbox) -->
      <div id="u36" class="ax_checkbox">
        <label for="u36_input">
          <!-- Unnamed () -->
          <div id="u37" class="text">
            <p><span></span></p>
          </div>
        </label>
        <input id="u36_input" type="checkbox" value="checkbox"/>
      </div>

      <!-- Unnamed (Checkbox) -->
      <div id="u38" class="ax_checkbox">
        <label for="u38_input">
          <!-- Unnamed () -->
          <div id="u39" class="text">
            <p><span></span></p>
          </div>
        </label>
        <input id="u38_input" type="checkbox" value="checkbox"/>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u40" class="ax_image">
        <img id="u40_img" class="img " src="images/人脸库/u2.png"/>
        <!-- Unnamed () -->
        <div id="u41" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u42" class="ax_paragraph">
        <img id="u42_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u43" class="text">
          <p><span>刘</span><span>小厦</span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u44" class="ax_image">
        <img id="u44_img" class="img " src="images/人脸库/u2.png"/>
        <!-- Unnamed () -->
        <div id="u45" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u46" class="ax_paragraph">
        <img id="u46_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u47" class="text">
          <p><span>成明</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u48" class="ax_paragraph">
        <img id="u48_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u49" class="text">
          <p><span>X</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u50" class="ax_paragraph">
        <img id="u50_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u51" class="text">
          <p><span>X</span></p>
        </div>
      </div>

      <!-- Unnamed (Checkbox) -->
      <div id="u52" class="ax_checkbox">
        <label for="u52_input">
          <!-- Unnamed () -->
          <div id="u53" class="text">
            <p><span></span></p>
          </div>
        </label>
        <input id="u52_input" type="checkbox" value="checkbox"/>
      </div>

      <!-- Unnamed (Checkbox) -->
      <div id="u54" class="ax_checkbox">
        <label for="u54_input">
          <!-- Unnamed () -->
          <div id="u55" class="text">
            <p><span></span></p>
          </div>
        </label>
        <input id="u54_input" type="checkbox" value="checkbox"/>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u56" class="ax_image">
        <img id="u56_img" class="img " src="images/人脸库/u2.png"/>
        <!-- Unnamed () -->
        <div id="u57" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u58" class="ax_paragraph">
        <img id="u58_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u59" class="text">
          <p><span>刘</span><span>小厦</span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u60" class="ax_image">
        <img id="u60_img" class="img " src="images/人脸库/u2.png"/>
        <!-- Unnamed () -->
        <div id="u61" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u62" class="ax_paragraph">
        <img id="u62_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u63" class="text">
          <p><span>成明</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u64" class="ax_paragraph">
        <img id="u64_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u65" class="text">
          <p><span>X</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u66" class="ax_paragraph">
        <img id="u66_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u67" class="text">
          <p><span>X</span></p>
        </div>
      </div>

      <!-- Unnamed (Checkbox) -->
      <div id="u68" class="ax_checkbox">
        <label for="u68_input">
          <!-- Unnamed () -->
          <div id="u69" class="text">
            <p><span></span></p>
          </div>
        </label>
        <input id="u68_input" type="checkbox" value="checkbox"/>
      </div>

      <!-- Unnamed (Checkbox) -->
      <div id="u70" class="ax_checkbox">
        <label for="u70_input">
          <!-- Unnamed () -->
          <div id="u71" class="text">
            <p><span></span></p>
          </div>
        </label>
        <input id="u70_input" type="checkbox" value="checkbox"/>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u72" class="ax_image">
        <img id="u72_img" class="img " src="images/人脸库/u2.png"/>
        <!-- Unnamed () -->
        <div id="u73" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u74" class="ax_paragraph">
        <img id="u74_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u75" class="text">
          <p><span>刘</span><span>小厦</span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u76" class="ax_image">
        <img id="u76_img" class="img " src="images/人脸库/u2.png"/>
        <!-- Unnamed () -->
        <div id="u77" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u78" class="ax_paragraph">
        <img id="u78_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u79" class="text">
          <p><span>成明</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u80" class="ax_paragraph">
        <img id="u80_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u81" class="text">
          <p><span>X</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u82" class="ax_paragraph">
        <img id="u82_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u83" class="text">
          <p><span>X</span></p>
        </div>
      </div>

      <!-- Unnamed (Checkbox) -->
      <div id="u84" class="ax_checkbox">
        <label for="u84_input">
          <!-- Unnamed () -->
          <div id="u85" class="text">
            <p><span></span></p>
          </div>
        </label>
        <input id="u84_input" type="checkbox" value="checkbox"/>
      </div>

      <!-- Unnamed (Checkbox) -->
      <div id="u86" class="ax_checkbox">
        <label for="u86_input">
          <!-- Unnamed () -->
          <div id="u87" class="text">
            <p><span></span></p>
          </div>
        </label>
        <input id="u86_input" type="checkbox" value="checkbox"/>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u88" class="ax_image">
        <img id="u88_img" class="img " src="images/人脸库/u2.png"/>
        <!-- Unnamed () -->
        <div id="u89" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u90" class="ax_paragraph">
        <img id="u90_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u91" class="text">
          <p><span>刘</span><span>小厦</span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u92" class="ax_image">
        <img id="u92_img" class="img " src="images/人脸库/u2.png"/>
        <!-- Unnamed () -->
        <div id="u93" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u94" class="ax_paragraph">
        <img id="u94_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u95" class="text">
          <p><span>成明</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u96" class="ax_paragraph">
        <img id="u96_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u97" class="text">
          <p><span>X</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u98" class="ax_paragraph">
        <img id="u98_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u99" class="text">
          <p><span>X</span></p>
        </div>
      </div>

      <!-- Unnamed (Checkbox) -->
      <div id="u100" class="ax_checkbox">
        <label for="u100_input">
          <!-- Unnamed () -->
          <div id="u101" class="text">
            <p><span></span></p>
          </div>
        </label>
        <input id="u100_input" type="checkbox" value="checkbox"/>
      </div>

      <!-- Unnamed (Checkbox) -->
      <div id="u102" class="ax_checkbox">
        <label for="u102_input">
          <!-- Unnamed () -->
          <div id="u103" class="text">
            <p><span></span></p>
          </div>
        </label>
        <input id="u102_input" type="checkbox" value="checkbox"/>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u104" class="ax_text_field">
        <input id="u104_input" type="text" value="输入姓名搜索"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u105" class="ax_html_button">
        <input id="u105_input" type="submit" value="查询"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u106" class="ax_shape">
        <img id="u106_img" class="img " src="images/人脸库/u106.png"/>
        <!-- Unnamed () -->
        <div id="u107" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u108" class="ax_h2">
        <img id="u108_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u109" class="text">
          <p><span>从学员导入</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u110" class="ax_paragraph">
        <img id="u110_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u111" class="text">
          <p><span>将现有已经审核的学员的头像导入人脸库，如果人脸存在相同名称和身份</span></p><p><span>证号，请选择覆盖还是忽略？</span></p>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u112" class="ax_html_button">
        <input id="u112_input" type="submit" value="覆盖"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u113" class="ax_html_button">
        <input id="u113_input" type="submit" value="忽略"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u114" class="ax_html_button">
        <input id="u114_input" type="submit" value="取消"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u115" class="ax_shape">
        <img id="u115_img" class="img " src="images/人脸库/u115.png"/>
        <!-- Unnamed () -->
        <div id="u116" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u117" class="ax_paragraph">
        <img id="u117_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u118" class="text">
          <p><span>正在导入，请稍候.......</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u119" class="ax_shape">
        <img id="u119_img" class="img " src="images/人脸库/u115.png"/>
        <!-- Unnamed () -->
        <div id="u120" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u121" class="ax_paragraph">
        <img id="u121_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u122" class="text">
          <p><span>导入成功，完成118个学员头像导入，20个学员头像被忽略。</span></p>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u123" class="ax_html_button">
        <input id="u123_input" type="submit" value="确定"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u124" class="ax_h2">
        <img id="u124_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u125" class="text">
          <p><span>上传人脸</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u126" class="ax_shape">
        <img id="u126_img" class="img " src="images/人脸库/u126.png"/>
        <!-- Unnamed () -->
        <div id="u127" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u128" class="ax_paragraph">
        <img id="u128_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u129" class="text">
          <p><span>姓名</span><span>*</span><span>： </span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u130" class="ax_text_field">
        <input id="u130_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u131" class="ax_paragraph">
        <img id="u131_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u132" class="text">
          <p><span>身份证号</span><span>*</span><span>： </span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u133" class="ax_text_field">
        <input id="u133_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u134" class="ax_paragraph">
        <img id="u134_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u135" class="text">
          <p><span>人脸</span><span></span><span>*</span><span>： </span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u136" class="ax_image">
        <img id="u136_img" class="img " src="images/人脸库/u136.png"/>
        <!-- Unnamed () -->
        <div id="u137" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u138" class="ax_html_button">
        <input id="u138_input" type="submit" value="保存"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u139" class="ax_shape">
        <img id="u139_img" class="img " src="images/人脸库/u139.png"/>
        <!-- Unnamed () -->
        <div id="u140" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u141" class="ax_paragraph">
        <img id="u141_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u142" class="text">
          <p><span>已经存在相同的姓名和身份证号的人脸，需要替换吗？</span></p>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u143" class="ax_html_button">
        <input id="u143_input" type="submit" value="确定"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u144" class="ax_html_button">
        <input id="u144_input" type="submit" value="取消"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u145" class="ax_h2">
        <img id="u145_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u146" class="text">
          <p><span>下发</span><span></span><span>人脸</span><span>到设备</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u147" class="ax_shape">
        <img id="u147_img" class="img " src="images/人脸库/u147.png"/>
        <!-- Unnamed () -->
        <div id="u148" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u149" class="ax_paragraph">
        <img id="u149_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u150" class="text">
          <p><span>所选人脸将下发到大华ICC１.２.０系统中？</span></p>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u151" class="ax_html_button">
        <input id="u151_input" type="submit" value="确定"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u152" class="ax_html_button">
        <input id="u152_input" type="submit" value="取消"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u153" class="ax_h2">
        <img id="u153_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u154" class="text">
          <p><span>所有</span><span></span><span>人脸</span><span>到设备</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u155" class="ax_shape">
        <img id="u155_img" class="img " src="images/人脸库/u155.png"/>
        <!-- Unnamed () -->
        <div id="u156" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u157" class="ax_paragraph">
        <img id="u157_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u158" class="text">
          <p><span>所有</span><span></span><span>人脸</span><span>数据</span><span>将下发到大华ICC１.２.０系统中？</span></p>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u159" class="ax_html_button">
        <input id="u159_input" type="submit" value="确定"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u160" class="ax_html_button">
        <input id="u160_input" type="submit" value="取消"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u161" class="ax_paragraph">
        <img id="u161_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u162" class="text">
          <p><span>图片大小100*100px,小于1M，jpg或者png图片</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
