<!DOCTYPE html>
<html>
  <head>
    <title>驾校图片（运营）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <link href="resources/css/jquery-ui-themes.css" type="text/css" rel="stylesheet"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/驾校图片（运营）/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-1.7.1.min.js"></script>
    <script src="resources/scripts/jquery-ui-1.8.10.custom.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="data/document.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/ie.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="files/驾校图片（运营）/data.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (Table) -->
      <div id="u0" class="ax_table">

        <!-- Unnamed (Table Cell) -->
        <div id="u1" class="ax_table_cell">
          <img id="u1_img" class="img " src="images/运营数据（移动，运营）/u99.png"/>
          <!-- Unnamed () -->
          <div id="u2" class="text">
            <p><span>驾校名称</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u3" class="ax_table_cell">
          <img id="u3_img" class="img " src="images/学员管理（驾校，分校，报名点，运营，监管查看）/u157.png"/>
          <!-- Unnamed () -->
          <div id="u4" class="text">
            <p><span>主标题</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u5" class="ax_table_cell">
          <img id="u5_img" class="img " src="images/运营数据（移动，运营）/u91.png"/>
          <!-- Unnamed () -->
          <div id="u6" class="text">
            <p><span>副标题</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u7" class="ax_table_cell">
          <img id="u7_img" class="img " src="images/运营数据（移动，运营）/u99.png"/>
          <!-- Unnamed () -->
          <div id="u8" class="text">
            <p><span>图片</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u9" class="ax_table_cell">
          <img id="u9_img" class="img " src="images/运营数据（移动，运营）/u99.png"/>
          <!-- Unnamed () -->
          <div id="u10" class="text">
            <p><span>是否显示</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u11" class="ax_table_cell">
          <img id="u11_img" class="img " src="images/运营数据（移动，运营）/u99.png"/>
          <!-- Unnamed () -->
          <div id="u12" class="text">
            <p><span>是否审核</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u13" class="ax_table_cell">
          <img id="u13_img" class="img " src="images/运营数据（移动，运营）/u99.png"/>
          <!-- Unnamed () -->
          <div id="u14" class="text">
            <p><span>排序</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u15" class="ax_table_cell">
          <img id="u15_img" class="img " src="images/驾校图片（运营）/u15.png"/>
          <!-- Unnamed () -->
          <div id="u16" class="text">
            <p><span>更新</span><span></span><span>时间</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u17" class="ax_table_cell">
          <img id="u17_img" class="img " src="images/运营数据（移动，运营）/u15.png"/>
          <!-- Unnamed () -->
          <div id="u18" class="text">
            <p><span>广仁驾校</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u19" class="ax_table_cell">
          <img id="u19_img" class="img " src="images/学员管理（驾校，分校，报名点，运营，监管查看）/u29.png"/>
          <!-- Unnamed () -->
          <div id="u20" class="text">
            <p><span>78+直营门店</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u21" class="ax_table_cell">
          <img id="u21_img" class="img " src="images/运营数据（移动，运营）/u7.png"/>
          <!-- Unnamed () -->
          <div id="u22" class="text">
            <p><span>全市78家直营门门，专业指导</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u23" class="ax_table_cell">
          <img id="u23_img" class="img " src="images/运营数据（移动，运营）/u15.png"/>
          <!-- Unnamed () -->
          <div id="u24" class="text">
            <p><span>查看</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u25" class="ax_table_cell">
          <img id="u25_img" class="img " src="images/运营数据（移动，运营）/u15.png"/>
          <!-- Unnamed () -->
          <div id="u26" class="text">
            <p><span>是</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u27" class="ax_table_cell">
          <img id="u27_img" class="img " src="images/运营数据（移动，运营）/u15.png"/>
          <!-- Unnamed () -->
          <div id="u28" class="text">
            <p><span>是</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u29" class="ax_table_cell">
          <img id="u29_img" class="img " src="images/运营数据（移动，运营）/u15.png"/>
          <!-- Unnamed () -->
          <div id="u30" class="text">
            <p><span>0</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u31" class="ax_table_cell">
          <img id="u31_img" class="img " src="images/驾校图片（运营）/u31.png"/>
          <!-- Unnamed () -->
          <div id="u32" class="text">
            <p><span>2023-05-24 17:42:14</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u33" class="ax_table_cell">
          <img id="u33_img" class="img " src="images/驾校图片（运营）/u33.png"/>
          <!-- Unnamed () -->
          <div id="u34" class="text">
            <p><span>广仁驾校</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u35" class="ax_table_cell">
          <img id="u35_img" class="img " src="images/学员管理（驾校，分校，报名点，运营，监管查看）/u189.png"/>
          <!-- Unnamed () -->
          <div id="u36" class="text">
            <p><span>350+星级教练</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u37" class="ax_table_cell">
          <img id="u37_img" class="img " src="images/驾校图片（运营）/u37.png"/>
          <!-- Unnamed () -->
          <div id="u38" class="text">
            <p><span>350+星级教练，专业</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u39" class="ax_table_cell">
          <img id="u39_img" class="img " src="images/驾校图片（运营）/u33.png"/>
          <!-- Unnamed () -->
          <div id="u40" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u41" class="ax_table_cell">
          <img id="u41_img" class="img " src="images/驾校图片（运营）/u33.png"/>
          <!-- Unnamed () -->
          <div id="u42" class="text">
            <p><span></span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u43" class="ax_table_cell">
          <img id="u43_img" class="img " src="images/驾校图片（运营）/u33.png"/>
          <!-- Unnamed () -->
          <div id="u44" class="text">
            <p><span>是</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u45" class="ax_table_cell">
          <img id="u45_img" class="img " src="images/驾校图片（运营）/u33.png"/>
          <!-- Unnamed () -->
          <div id="u46" class="text">
            <p><span>1</span></p>
          </div>
        </div>

        <!-- Unnamed (Table Cell) -->
        <div id="u47" class="ax_table_cell">
          <img id="u47_img" class="img " src="images/驾校图片（运营）/u47.png"/>
          <!-- Unnamed () -->
          <div id="u48" class="text">
            <p><span>2023-05-24 17:42:14</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u49" class="ax_html_button">
        <input id="u49_input" type="submit" value="增加"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u50" class="ax_html_button">
        <input id="u50_input" type="submit" value="修改"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u51" class="ax_html_button">
        <input id="u51_input" type="submit" value="删除"/>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u52" class="ax_html_button">
        <input id="u52_input" type="submit" value="审核"/>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u53" class="ax_droplist">
        <select id="u53_input">
          <option value="驾校名称选择">驾校名称选择</option>
        </select>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u54" class="ax_html_button">
        <input id="u54_input" type="submit" value="查询"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u55" class="ax_h2">
        <img id="u55_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u56" class="text">
          <p><span>增加/修改</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u57" class="ax_shape">
        <img id="u57_img" class="img " src="images/驾校图片（运营）/u57.png"/>
        <!-- Unnamed () -->
        <div id="u58" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u59" class="ax_paragraph">
        <img id="u59_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u60" class="text">
          <p><span>所属驾校</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u61" class="ax_droplist">
        <select id="u61_input">
        </select>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u62" class="ax_paragraph">
        <img id="u62_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u63" class="text">
          <p><span>主标题</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u64" class="ax_text_field">
        <input id="u64_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u65" class="ax_paragraph">
        <img id="u65_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u66" class="text">
          <p><span>版面有限，不要超过</span><span>15</span><span>个字符，超过小程序端用...显示</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u67" class="ax_paragraph">
        <img id="u67_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u68" class="text">
          <p><span>副</span><span>标题</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u69" class="ax_text_field">
        <input id="u69_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u70" class="ax_paragraph">
        <img id="u70_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u71" class="text">
          <p><span>版面有限，不要超过</span><span>2</span><span>5</span><span>个字符，超过小程序端用...显示</span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u72" class="ax_image">
        <img id="u72_img" class="img " src="images/学车指引（运营）/u5.png"/>
        <!-- Unnamed () -->
        <div id="u73" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u74" class="ax_paragraph">
        <img id="u74_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u75" class="text">
          <p><span>详细内容</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u76" class="ax_paragraph">
        <img id="u76_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u77" class="text">
          <p><span>不填写将不做跳转</span></p>
        </div>
      </div>

      <!-- Unnamed (HTML Button) -->
      <div id="u78" class="ax_html_button">
        <input id="u78_input" type="submit" value="保存"/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u79" class="ax_paragraph">
        <img id="u79_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u80" class="text">
          <p><span>排序</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Text Field) -->
      <div id="u81" class="ax_text_field">
        <input id="u81_input" type="text" value=""/>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u82" class="ax_paragraph">
        <img id="u82_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u83" class="text">
          <p><span>是否显示</span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Droplist) -->
      <div id="u84" class="ax_droplist">
        <select id="u84_input">
          <option value="是">是</option>
          <option value="否">否</option>
        </select>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u85" class="ax_paragraph">
        <img id="u85_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u86" class="text">
          <p><span>数字越小越排前面</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u87" class="ax_paragraph">
        <img id="u87_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u88" class="text">
          <p><span>图片</span><span></span><span>*</span><span>：</span></p>
        </div>
      </div>

      <!-- Unnamed (Image) -->
      <div id="u89" class="ax_image">
        <img id="u89_img" class="img " src="images/驾校图片（运营）/u89.png"/>
        <!-- Unnamed () -->
        <div id="u90" class="text">
          <p><span></span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u91" class="ax_paragraph">
        <img id="u91_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u92" class="text">
          <p><span>215*150px或者正比例放大的图片，不超过2M</span></p>
        </div>
      </div>

      <!-- Unnamed (Shape) -->
      <div id="u93" class="ax_paragraph">
        <img id="u93_img" class="img " src="resources/images/transparent.gif"/>
        <!-- Unnamed () -->
        <div id="u94" class="text">
          <p><span>图片上传之后可以截剪</span></p>
        </div>
      </div>
    </div>
  </body>
</html>
