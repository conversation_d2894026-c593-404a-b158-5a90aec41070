package com.guangren.hardware.service.impl;

import com.guangren.business.vo.CarSurveyImportVo;
import com.guangren.common.constant.UserConstants;
import com.guangren.common.core.text.Convert;
import com.guangren.common.exception.BusinessException;
import com.guangren.common.utils.DateUtils;
import com.guangren.common.utils.StringUtils;
import com.guangren.common.utils.bean.BeanUtils;
import com.guangren.common.utils.poi.ExcelUtil;
import com.guangren.common.utils.uuid.IdUtils;
import com.guangren.hardware.domain.CarSurvey;
import com.guangren.hardware.mapper.CarSurveyMapper;
import com.guangren.hardware.service.ICarSurveyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 车辆黒白名单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-05-10
 */
@Service
public class CarSurveyServiceImpl implements ICarSurveyService 
{
    @Autowired
    private CarSurveyMapper carSurveyMapper;

    /**
     * 查询车辆黒白名单
     * 
     * @param id 车辆黒白名单主键
     * @return 车辆黒白名单
     */
    @Override
    public CarSurvey selectCarSurveyById(String id)
    {
        return carSurveyMapper.selectCarSurveyById(id);
    }

    /**
     * 查询车辆黒白名单列表
     * 
     * @param carSurvey 车辆黒白名单
     * @return 车辆黒白名单
     */
    @Override
    public List<CarSurvey> selectCarSurveyList(CarSurvey carSurvey)
    {
        return carSurveyMapper.selectCarSurveyList(carSurvey);
    }

    /**
     * 新增车辆黒白名单
     * 
     * @param carSurvey 车辆黒白名单
     * @return 结果
     */
    @Override
    public int insertCarSurvey(CarSurvey carSurvey)
    {
        carSurvey.setId(IdUtils.fastSimpleUUID());
        carSurvey.setCarCode("0");
        carSurvey.setCarColor("0");
        carSurvey.setCarNumColor("0");
        carSurvey.setCarBrand("0");
        carSurvey.setCarType("0");
        return carSurveyMapper.insertCarSurvey(carSurvey);
    }

    /**
     * 修改车辆黒白名单
     * 
     * @param carSurvey 车辆黒白名单
     * @return 结果
     */
    @Override
    public int updateCarSurvey(CarSurvey carSurvey)
    {
        return carSurveyMapper.updateCarSurvey(carSurvey);
    }



    /**
     * 验证参数唯一性
     *
     * @param carSurvey 车辆黒白名单
     * @return 车辆黒白名单
     */
    @Override
    public String checkUnique(CarSurvey carSurvey)
    {
        String id = carSurvey.getId() == null ? "-1"  : carSurvey.getId();
        CarSurvey info=carSurveyMapper.checkUnique(carSurvey);
        if(StringUtils.isNotNull(info) && !info.getId().equals(id)){
            return UserConstants.COMMOM_NOT_UNIQUE;
        }
        return UserConstants.COMMOM_UNIQUE;
    }


    /**
     * 批量删除车辆黒白名单
     * 
     * @param ids 需要删除的车辆黒白名单主键
     * @return 结果
     */
    @Override
    public int deleteCarSurveyByIds(String ids)
    {
        return carSurveyMapper.deleteCarSurveyByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除车辆黒白名单信息
     * 
     * @param id 车辆黒白名单主键
     * @return 结果
     */
    @Override
    public int deleteCarSurveyById(String id)
    {
        return carSurveyMapper.deleteCarSurveyById(id);
    }

    /**
     * 导入车辆黑白名单
     */
    @Override
    public List<CarSurveyImportVo> importCarSurvey(MultipartFile excel) {
        if (excel == null || excel.isEmpty()) return new ArrayList<>();
        try {
            // 获取列表
            ExcelUtil<CarSurveyImportVo> util = new ExcelUtil<>(CarSurveyImportVo.class);
            List<CarSurveyImportVo> carList = util.importExcel(excel.getInputStream());
            if (StringUtils.isNull(carList) || carList.size() == 0) {
                throw new BusinessException("导入数据不能为空！");
            }
            // 进行遍历
            List<CarSurveyImportVo> resultList = new ArrayList<>();

            for (int i = 0; i < carList.size(); i++) {
                int row = i + 1;
                String info = "第" + row + "条数据，";
                CarSurveyImportVo carSurveyImportVo = carList.get(i);
                if (carSurveyImportVo == null) {
                    throw new BusinessException("导入数据不能为空！");
                }

                CarSurvey carSurvey = new CarSurvey();

                if (StringUtils.isNull(carSurveyImportVo.getListType())) {
                    throw new BusinessException(info +"黑白名单不能为空！");
                }

                if (!carSurveyImportVo.getListType().equals("0") && !carSurveyImportVo.getListType().equals("2")) {
                    throw new BusinessException(info +"黑白名单不能填写【白名单】或【黑名单】以外的字符！");
                }

                if (StringUtils.isNull(carSurveyImportVo.getType())) {
                    throw new BusinessException(info +"生效时间类型不能为空！");
                }

                if (!carSurveyImportVo.getType().equals("0") && !carSurveyImportVo.getType().equals("1")) {
                    throw new BusinessException(info +"生效时间类型不能填写【永久】或【限期】以外的字符！");
                }

                //永久 & 设置具体天
                if (carSurveyImportVo.getType().equals("0")) {
                    carSurvey.setValidStartTime(DateUtils.parseDateToStr("yyyy-MM-dd",new Date()));
                    String validEndTime = DateUtils.plusDay(36500, DateUtils.parseDateToStr("yyyy-MM-dd", carSurveyImportVo.getValidEndTime()));
                    carSurvey.setValidEndTime(validEndTime);
                }else if (carSurveyImportVo.getType().equals("1")){
                    if (carSurveyImportVo.getValidEndTime() == null) {
                        throw new BusinessException(info +"生效时间类型为【限期】时，有效结束时间不能为空！");
                    }
                    if (carSurveyImportVo.getValidStartTime() == null) {
                        carSurvey.setValidStartTime(DateUtils.parseDateToStr("yyyy-MM-dd",new Date()));
                    }else {
                        carSurvey.setValidStartTime(DateUtils.parseDateToStr("yyyy-MM-dd",carSurveyImportVo.getValidStartTime()));
                    }
                    carSurvey.setValidEndTime(DateUtils.parseDateToStr("yyyy-MM-dd", carSurveyImportVo.getValidEndTime()));
                }
                carSurveyImportVo.setCarNum(carSurveyImportVo.getCarNum().trim());
                carSurvey.setCarCode("0");
                carSurvey.setCarColor("0");
                carSurvey.setCarNumColor("0");
                carSurvey.setCarBrand("0");
                carSurvey.setCarType("0");
                carSurvey.setParkType(1);
                carSurvey.setParkinglotCode("1656565771289071617");
                carSurvey.setDeptId("1");
                carSurvey.setDeptName("东莞驾校综合人员管理");
                BeanUtils.copyBeanProp(carSurvey, carSurveyImportVo);
                int total = this.insertCarSurvey(carSurvey);
                if (total > 0) {
                    resultList.add(carSurveyImportVo);
                }
            }
            return resultList;
        } catch(Exception e){
            throw new BusinessException(e.getMessage());
        }
    }
}
