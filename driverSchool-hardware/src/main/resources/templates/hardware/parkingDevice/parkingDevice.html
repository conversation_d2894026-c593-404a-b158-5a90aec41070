<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('车场设备道闸列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>车场区或设备名称：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>编码：</label>
                                <input type="text" name="code"/>
                            </li>
                            <li>
                                <label>上级ID：</label>
                                <input type="text" name="parentId"/>
                            </li>
                            <li>
                                <label>添加时间：</label>
                                <input type="text" class="time-input" placeholder="请选择添加时间" name="createdTime"/>
                            </li>
                            <li>
                                <label>修改时间：</label>
                                <input type="text" class="time-input" placeholder="请选择修改时间" name="updatedTime"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.treeTable.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="hardware:parkingDevice:add">
                    <i class="fa fa-plus"></i> 新增
                </a>
                <a class="btn btn-primary" onclick="$.operate.edit()" shiro:hasPermission="hardware:parkingDevice:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-info" id="expandAllBtn">
                    <i class="fa fa-exchange"></i> 展开/折叠
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-tree-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var addFlag = [[${@permission.hasPermi('hardware:parkingDevice:add')}]];
        var editFlag = [[${@permission.hasPermi('hardware:parkingDevice:edit')}]];
        var removeFlag = [[${@permission.hasPermi('hardware:parkingDevice:remove')}]];
        var prefix = ctx + "hardware/parkingDevice";

        $(function() {
            var options = {
                code: "id",
                parentCode: "parentId",
                expandColumn: "1",
                uniqueId: "id",
                url: prefix + "/list",
                createUrl: prefix + "/add/{id}",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove/{id}",
                modalName: "车场设备道闸",
                showPageGo: true,
                columns: [{
                    field: 'selectItem',
                    radio: true
                },
                {
                    field: 'name',
                    title: '车场区或设备名称',
                    align: 'left'
                },
                {
                    field: 'code',
                    title: '编码',
                    align: 'left'
                },
                {
                    field: 'parentId',
                    title: '上级ID',
                    align: 'left'
                },
                {
                    field: 'createdTime',
                    title: '添加时间',
                    align: 'left'
                },
                {
                    field: 'updatedTime',
                    title: '修改时间',
                    align: 'left'
                },
                {
                    title: '操作',
                    align: 'center',
                    align: 'left',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-info  btn-xs ' + addFlag + '" href="javascript:void(0)" onclick="$.operate.add(\'' + row.id + '\')"><i class="fa fa-plus"></i>新增</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.treeTable.init(options);
        });
    </script>
</body>
</html>