<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('人像库列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>库名称：</label>
                                <input type="text" name="groupName"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="hardware:faceGroup:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="hardware:faceGroup:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-success multiple disabled"  id="faceControlChannel">
                    <i class="fa fa-camera"></i> 布控通道
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('hardware:faceGroup:edit')}]];
        var removeFlag = [[${@permission.hasPermi('hardware:faceGroup:remove')}]];
        var prefix = ctx + "hardware/faceGroup";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "人像库",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'groupName',
                    title: '库名称'
                },
                {
                    field: 'groupType',
                    title: '人像库类型',
                    formatter: function(value, row, index) {
                        if(value == 1){
                            return "白名单库";
                        }else{
                            return "-";
                        }
                    }
                },
                {
                    field: 'deviceCodeList',
                    title: '人脸设备编码集合'
                },
                {
                    field: 'remark',
                    title: '备注'
                }
               ]
            };
            $.table.init(options);

            $("#select_item_all").click(function () {
                $("tbody input[name='select_item']").prop("checked",this.checked);
            });

            //打开
            $("#faceControlChannel").click(function () {
                table.set();
                var rows = $.table.selectRows();
                if (rows.length < 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                $.modal.confirm("确定要布控通道选择的人像库吗?", function () {
                    $.operate.submitPro(prefix + "/faceControlChannel", "post", "json", {ids: $.table.selectColumns("id").join(",")}, "人像库正在布控通道中。。。",channelCallBack);
                });
            })
        });

        function channelCallBack() {
            $("#select_item_all").prop("checked",false);
        }
    </script>
</body>
</html>