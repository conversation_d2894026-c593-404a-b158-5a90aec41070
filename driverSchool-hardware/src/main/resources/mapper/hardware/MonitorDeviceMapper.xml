<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guangren.hardware.mapper.MonitorDeviceMapper">
    
    <resultMap type="MonitorDevice" id="MonitorDeviceResult">
        <result property="id"    column="id"    />
        <result property="channelSeq"    column="channel_seq"    />
        <result property="channelCode"    column="channel_code"    />
        <result property="channelSn"    column="channel_sn"    />
        <result property="channelName"    column="channel_name"    />
        <result property="channelType"    column="channel_type"    />
        <result property="cameraType"    column="camera_type"    />
        <result property="ip"    column="ip"    />
        <result property="isOnline"    column="is_online"    />
        <result property="chExt"    column="ch_ext"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updatedTime"    column="updated_time"    />
        <result property="schoolId"    column="school_id"    />
        <result property="branchId"    column="branch_id"    />
        <result property="trainingGroundId"    column="training_ground_id"/>
        <association property="school" column="school_id" select="com.guangren.business.mapper.SchoolMapper.selectSchoolById"/>
        <association property="branch" column="branch_id" select="com.guangren.business.mapper.SchoolBranchMapper.selectSchoolBranchById"/>
        <association property="trainingGround" column="training_ground_id" select="com.guangren.business.mapper.SchoolTrainingGroundMapper.selectSchoolTrainingGroundById"/>
    </resultMap>

    <resultMap type="com.guangren.hardware.domain.MonitorVo" id="MonitorVoResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="monitorId"    column="monitorId"    />
        <result property="uniqueId"    column="uniqueId"    />
        <result property="type"    column="type"    />
        <result property="status"    column="status"    />
        <result property="parentName" column="parent_name" />
    </resultMap>

    <sql id="selectMonitorDeviceVo">
        select id, channel_seq, channel_code, channel_sn, channel_name, channel_type, camera_type, ip, is_online, ch_ext, created_time, updated_time, school_id, branch_id, training_ground_id from t_monitor_device
    </sql>

    <select id="selectMonitorDeviceList" parameterType="MonitorDevice" resultMap="MonitorDeviceResult">
        <include refid="selectMonitorDeviceVo"/>
        <where>  
            <if test="channelSeq != null  and channelSeq != ''"> and channel_seq = #{channelSeq}</if>
            <if test="channelCode != null  and channelCode != ''"> and channel_code = #{channelCode}</if>
            <if test="channelSn != null  and channelSn != ''"> and channel_sn = #{channelSn}</if>
            <if test="channelName != null  and channelName != ''"> and channel_name like concat('%', #{channelName}, '%')</if>
            <if test="channelType != null  and channelType != ''"> and channel_type = #{channelType}</if>
            <if test="cameraType != null  and cameraType != ''"> and camera_type = #{cameraType}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="isOnline != null "> and is_online = #{isOnline}</if>
            <if test="chExt != null  and chExt != ''"> and ch_ext = #{chExt}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="updatedTime != null "> and updated_time = #{updatedTime}</if>
            <if test="schoolId != null  and schoolId != ''"> and school_id = #{schoolId}</if>
            <if test="branchId != null  and branchId != ''"> and branch_id = #{branchId}</if>
            <if test="trainingGroundId != null  and trainingGroundId != ''"> and training_ground_id = #{trainingGroundId}</if>


            <if test="params.schoolId != null  and params.schoolId != ''">
                and school_id = #{params.schoolId}
            </if>
            <if test="params.banchId != null  and params.banchId != ''">
                and branch_id = #{params.banchId}
            </if>


        </where>
    </select>
    
    <select id="selectMonitorDeviceById" parameterType="String" resultMap="MonitorDeviceResult">
        <include refid="selectMonitorDeviceVo"/>
        where id = #{id}
    </select>

    <select id="checkUnique" parameterType="MonitorDevice" resultMap="MonitorDeviceResult">
        <include refid="selectMonitorDeviceVo"/>
        <where>
            <if test="channelSeq != null  and channelSeq != ''"> and channel_seq = #{channelSeq}</if>
            <if test="channelCode != null  and channelCode != ''"> and channel_code = #{channelCode}</if>
            <if test="channelSn != null  and channelSn != ''"> and channel_sn = #{channelSn}</if>
            <if test="channelName != null  and channelName != ''"> and channel_name like concat('%', #{channelName}, '%')</if>
            <if test="channelType != null  and channelType != ''"> and channel_type = #{channelType}</if>
            <if test="cameraType != null  and cameraType != ''"> and camera_type = #{cameraType}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="isOnline != null "> and is_online = #{isOnline}</if>
            <if test="chExt != null  and chExt != ''"> and ch_ext = #{chExt}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="updatedTime != null "> and updated_time = #{updatedTime}</if>
            <if test="schoolId != null  and schoolId != ''"> and school_id = #{schoolId}</if>
            <if test="branchId != null  and branchId != ''"> and branch_id = #{branchId}</if>
            <if test="trainingGroundId != null  and trainingGroundId != ''"> and training_ground_id = #{trainingGroundId}</if>
        </where>
        limit 1
    </select>

    <select id="treeMonitorData" parameterType="MonitorDevice" resultMap="MonitorVoResult">
        select a.id,0 as monitorId,a.`name`,UUID() as "uniqueId",1 as "type",0 as "parent_id",null as "status" from t_school a
        union
        select a.id,0 as monitorId,a.`name`,UUID() as "uniqueId",1 as "type",IFNULL(a.school_id,0) as "parent_id",null as "status"  from t_school_branch a
        union
        select a.id,0 as monitorId,a.`name`,UUID() as "uniqueId",1 as "type",IFNULL(a.branch_id ,0) as "parent_id",null as "status" from t_school_training_ground a
        union
        select b.id ,b.id as monitorId,b.channel_name as name, b.id as "uniqueId",2 as "type",b.training_ground_id as "parent_id",b.is_online as "status"   from t_monitor_device b
        <where>
            <if test="params.schoolId != null  and params.schoolId != ''">
                and school_id = #{params.schoolId}
            </if>
            <if test="params.banchId != null  and params.banchId != ''">
                and branch_id = #{params.banchId}
            </if>
        </where>
    </select>


    <select id="getMonitorPage" parameterType="map"  resultMap="MonitorDeviceResult">
        <include refid="selectMonitorDeviceVo"/>
        <where>
            <if test="id != null  and id != ''"> and id = #{id}</if>
            <if test="channelSeq != null  and channelSeq != ''"> and channel_seq = #{channelSeq}</if>
            <if test="channelCode != null  and channelCode != ''"> and channel_code = #{channelCode}</if>
            <if test="channelSn != null  and channelSn != ''"> and channel_sn = #{channelSn}</if>
            <if test="channelName != null  and channelName != ''"> and channel_name like concat('%', #{channelName}, '%')</if>
            <if test="channelType != null  and channelType != ''"> and channel_type = #{channelType}</if>
            <if test="cameraType != null  and cameraType != ''"> and camera_type = #{cameraType}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="isOnline != null "> and is_online = #{isOnline}</if>
            <if test="schoolId != null  and schoolId != ''"> and school_id = #{schoolId}</if>
            <if test="branchId != null  and branchId != ''"> and branch_id = #{branchId}</if>
            <if test="trainingGroundId != null  and trainingGroundId != ''"> and training_ground_id = #{trainingGroundId}</if>
            <if test="order_by != null and order_by == 'updated_time'"> order by updated_time DESC </if>

            <if test="schoolId != null  and schoolId != ''">
                and school_id = #{schoolId}
            </if>
            <if test="branchId != null  and branchId != ''">
                and branch_id = #{branchId}
            </if>
        </where>
        limit #{page},#{pageNo}
    </select>

    <select id="getMonitorPageCount" resultType="java.lang.Integer">
        select count(1) from t_monitor_device
        <where>
            <if test="id != null  and id != ''"> and id = #{id}</if>
            <if test="channelSeq != null  and channelSeq != ''"> and channel_seq = #{channelSeq}</if>
            <if test="channelCode != null  and channelCode != ''"> and channel_code = #{channelCode}</if>
            <if test="channelSn != null  and channelSn != ''"> and channel_sn = #{channelSn}</if>
            <if test="channelName != null  and channelName != ''"> and channel_name like concat('%', #{channelName}, '%')</if>
            <if test="channelType != null  and channelType != ''"> and channel_type = #{channelType}</if>
            <if test="cameraType != null  and cameraType != ''"> and camera_type = #{cameraType}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="isOnline != null "> and is_online = #{isOnline}</if>
            <if test="chExt != null  and chExt != ''"> and ch_ext = #{chExt}</if>
            <if test="schoolId != null  and schoolId != ''"> and school_id = #{schoolId}</if>
            <if test="branchId != null  and branchId != ''"> and branch_id = #{branchId}</if>
            <if test="trainingGroundId != null  and trainingGroundId != ''"> and training_ground_id = #{trainingGroundId}</if>
            <if test="order_by != null and order_by == 'updated_time'"> order by updated_time DESC </if>
            <if test="schoolId != null  and schoolId != ''">
                and school_id = #{schoolId}
            </if>
            <if test="branchId != null  and branchId != ''">
                and branch_id = #{branchId}
            </if>
        </where>
    </select>

    <select id="selectMonitorByChannelCode"  parameterType="String" resultMap="MonitorDeviceResult">
        <include refid="selectMonitorDeviceVo"/>
        where channel_code = #{channelCode}
    </select>

    <insert id="insertMonitorDevice" parameterType="MonitorDevice">
        insert into t_monitor_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="channelSeq != null">channel_seq,</if>
            <if test="channelCode != null">channel_code,</if>
            <if test="channelSn != null">channel_sn,</if>
            <if test="channelName != null and channelName != ''">channel_name,</if>
            <if test="channelType != null">channel_type,</if>
            <if test="cameraType != null">camera_type,</if>
            <if test="ip != null">ip,</if>
            <if test="isOnline != null">is_online,</if>
            <if test="chExt != null">ch_ext,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="schoolId != null">school_id,</if>
            <if test="branchId != null">branch_id,</if>
            <if test="trainingGroundId != null">training_ground_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="channelSeq != null">#{channelSeq},</if>
            <if test="channelCode != null">#{channelCode},</if>
            <if test="channelSn != null">#{channelSn},</if>
            <if test="channelName != null and channelName != ''">#{channelName},</if>
            <if test="channelType != null">#{channelType},</if>
            <if test="cameraType != null">#{cameraType},</if>
            <if test="ip != null">#{ip},</if>
            <if test="isOnline != null">#{isOnline},</if>
            <if test="chExt != null">#{chExt},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="schoolId != null">#{schoolId},</if>
            <if test="branchId != null">#{branchId},</if>
            <if test="trainingGroundId != null">#{trainingGroundId},</if>
         </trim>
    </insert>

    <update id="updateMonitorDevice" parameterType="MonitorDevice">
        update t_monitor_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="channelSeq != null">channel_seq = #{channelSeq},</if>
            <if test="channelCode != null">channel_code = #{channelCode},</if>
            <if test="channelSn != null">channel_sn = #{channelSn},</if>
            <if test="channelName != null and channelName != ''">channel_name = #{channelName},</if>
            <if test="channelType != null">channel_type = #{channelType},</if>
            <if test="cameraType != null">camera_type = #{cameraType},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="isOnline != null">is_online = #{isOnline},</if>
            <if test="chExt != null">ch_ext = #{chExt},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="schoolId != null">school_id = #{schoolId},</if>
            <if test="branchId != null">branch_id = #{branchId},</if>
            <if test="trainingGroundId != null">training_ground_id = #{trainingGroundId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorDeviceById" parameterType="String">
        delete from t_monitor_device where id = #{id}
    </delete>

    <delete id="deleteMonitorDeviceByIds" parameterType="String">
        delete from t_monitor_device where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteMonitorDeviceByChannelCode" parameterType="String">
        delete from t_monitor_device where channel_code in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>