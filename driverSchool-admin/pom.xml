<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>driverSchool</artifactId>
        <groupId>com.guangren</groupId>
        <version>4.7.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>driverSchool-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- SpringBoot集成thymeleaf模板 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <!-- spring-boot-devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 表示依赖不会传递 -->
        </dependency>
        
        <dependency>
		    <groupId>org.springframework.boot</groupId>
		    <artifactId>spring-boot-starter-cache</artifactId>
		</dependency>
        

        <!-- swagger3-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>

        <!-- 防止进入swagger页面报类型转换错误，排除3.0.0中的引用，手动增加1.6.2版本 -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.6.2</version>
        </dependency>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.guangren</groupId>
            <artifactId>driverSchool-framework</artifactId>
        </dependency>

        <!-- 定时任务-->
        <dependency>
            <groupId>com.guangren</groupId>
            <artifactId>driverSchool-quartz</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.guangren</groupId>
            <artifactId>driverSchool-generator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.guangren</groupId>
            <artifactId>driverSchool-org</artifactId>
        </dependency>

        <dependency>
            <groupId>com.guangren</groupId>
            <artifactId>yidong</artifactId>
        </dependency>
        <!--
        <dependency>
            <groupId>com.guangren</groupId>
            <artifactId>driverSchool-hardware</artifactId>
        </dependency>-->
        
        <dependency>
	        <groupId>com.baomidou</groupId>
	        <artifactId>mybatis-plus-boot-starter</artifactId>
	    </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
        </dependency>
        
        <dependency>
		   <groupId>org.projectlombok</groupId>
		   <artifactId>lombok</artifactId>
		   <optional>true</optional>
		</dependency>
		
		<dependency>
		   <groupId>yidong</groupId>
		   <artifactId>masmgc.sdk.mms</artifactId>
		   <version>1.0.3</version>
		</dependency>

		<dependency>
		   <groupId>yidong</groupId>
		   <artifactId>masmgc.sdk.sms</artifactId>
		   <version>1.0.3</version>
		</dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.tess4j</groupId>
            <artifactId>tess4j</artifactId>
        </dependency>
        
        <dependency>  
		   <groupId>com.google.zxing</groupId>  
		   <artifactId>core</artifactId>  
		   <version>3.3.3</version>  
		</dependency>  
		<dependency>  
		   <groupId>com.google.zxing</groupId>  
		   <artifactId>javase</artifactId>  
		   <version>3.3.3</version>  
		</dependency>
        <!-- Apache POI for working with Microsoft documents -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.1.RELEASE</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>   
                <groupId>org.apache.maven.plugins</groupId>   
                <artifactId>maven-war-plugin</artifactId>   
                <version>3.0.0</version>   
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>   
            </plugin>   
            <!-- YUI Compressor (CSS/JS压缩) 
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>yuicompressor-maven-plugin</artifactId>
                <version>1.5.1</version>
                <executions>
                    <execution>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>compress</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <jswarn>false</jswarn>
                    <nosuffix>true</nosuffix>
                    <linebreakpos>50000</linebreakpos>
                    <sourceDirectory>src/main/resources/static</sourceDirectory>
                    <force>true</force>
                    <includes>
                        <include>**/*.js</include>
                        <include>**/*.css</include>
                    </includes>
                    <excludes>
                        <exclude>**/*.min.js</exclude>
                        <exclude>**/*.min.css</exclude>
                        <exclude>**/fileinput.js</exclude>
                        <exclude>**/bootstrap-table/**</exclude>
                    </excludes>
                </configuration>
            </plugin> -->
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>

</project>