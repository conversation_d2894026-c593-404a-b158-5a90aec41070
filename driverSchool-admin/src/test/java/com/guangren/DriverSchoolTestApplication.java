package com.guangren;

import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.guangren.business.domain.SchoolContract;
import com.guangren.business.domain.SchoolRegistration;
import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.service.ISchoolContractService;
import com.guangren.business.service.ISchoolRegistrationService;
import com.guangren.business.service.ISchoolStudentService;
import com.guangren.business.service.impl.UnionBankService;
import com.guangren.web.controller.client.MiniAppController;
import com.guangren.web.controller.out.StudyCenterApiController;
import com.guangren.web.controller.out.schedule.SynSchedule;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

@Slf4j
@SpringBootTest(classes = DriverSchoolApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class DriverSchoolTestApplication {


    @Autowired
    UnionBankService unionBankService;

    @Autowired
    private MiniAppController miniAppController;

    @Resource
    private StudyCenterApiController studyCenterApiController;

    @Autowired
    private ISchoolStudentService studentService;
    @Autowired
    private ISchoolRegistrationService schoolRegistrationService;
    @Autowired
    private ISchoolContractService schoolContractService;

    @Test
    public void test() throws Exception{
        String identity="******************";
        boolean validCard = IdcardUtil.isValidCard(identity);
        System.out.println("validCard = " + validCard);
        System.out.println("birth:"+IdcardUtil.getBirthByIdCard(identity));
        System.out.println("gender:"+IdcardUtil.getGenderByIdCard(identity));
    }

    @Test
    public void test2() {
        JSONObject data = new JSONObject();
        JSONObject data2 = new JSONObject();
        JSONArray dataArr = new JSONArray();
        JSONObject params = new JSONObject();

        data.put("orgCode", "东莞市宏图驾校培训有限公司");
        data.put("studentName", "王的春");
        data.put("nationality", "壮族");
        data.put("gender", "女");
        data.put("idCard", "42092219731015461X");
        data.put("businessType", "增驾");
        data.put("licenseType", "C3");
//        File file = new File("/Users/<USER>/Desktop/东莞驾培工作文件/**********-111640.png");
//        File file1 = new File("/Users/<USER>/Desktop/东莞驾培工作文件/login-background.jpg");
//        String base64 = ImageUtil.imageToBase64(file);
//        String base641 = ImageUtil.imageToBase64(file1);
//        String replace = base64.replace("data:image/png;base64,", "");
//        String replace1 = base641.replace("data:image/png;base64,", "");
        //data.put("image", replace);
        data.put("oldLicenseNo", "123456");
        data.put("oldLicenseUseDate", "2023-07-28");
        data.put("oldDriveCarType", "C1");
        //data.put("oldLicenseImage", replace1);


        data2.put("orgCode", "东莞市宏图驾校培训有限公司");
        data2.put("studentName", "张三");
        data2.put("nationality", "汉族");
        data2.put("idCard", "12345");
        data2.put("businessType", "增驾");

        params.put("dataType", "student");
        params.put("action", "update");
        dataArr.add(data);
        dataArr.add(data2);
        params.put("data", dataArr);
        JSONObject resp = studyCenterApiController.studyCenterApi(params.toString(), "1", 7);
        System.out.println("resp = " + resp);
    }
}
