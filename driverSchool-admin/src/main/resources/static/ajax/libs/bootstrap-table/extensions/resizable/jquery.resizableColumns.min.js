/**
 * jquery-resizable-columns - Resizable table columns for jQuery
 * @date Sat May 16 2015 19:03:57 GMT+0100 (BST)
 * @version v0.1.0
 * @link http://dobtco.github.io/jquery-resizable-columns/
 * @license MIT
 */
!function t(e,i,n){function s(a,o){if(!i[a]){if(!e[a]){var h="function"==typeof require&&require;if(!o&&h)return h(a,!0);if(r)return r(a,!0);var l=new Error("Cannot find module '"+a+"'");throw l.code="MODULE_NOT_FOUND",l}var d=i[a]={exports:{}};e[a][0].call(d.exports,function(t){var i=e[a][1][t];return s(i?i:t)},d,d.exports,t,e,i,n)}return i[a].exports}for(var r="function"==typeof require&&require,a=0;a<n.length;a++)s(n[a]);return s}({1:[function(t,e,i){"use strict";function n(t){return t&&t.__esModule?t:{"default":t}}var s=t("./class"),r=n(s),a=t("./constants");$.fn.resizableColumns=function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;e>n;n++)i[n-1]=arguments[n];return this.each(function(){var e=$(this),n=e.data(a.DATA_API);if(n){if("string"==typeof t)return n[t].apply(n,i)}else n=new r["default"](e,t),e.data(a.DATA_API,n)})},$.resizableColumns=r["default"]},{"./class":2,"./constants":3}],2:[function(t,e,i){"use strict";function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var s=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}(),r=t("./constants"),a=function(){function t(e,i){n(this,t),this.ns=".rc"+this.count++,this.options=$.extend({},t.defaults,i),this.$window=$(window),this.$ownerDocument=$(e[0].ownerDocument),this.$table=e,this.refreshHeaders(),this.restoreColumnWidths(),this.syncHandleWidths(),this.bindEvents(this.$window,"resize",this.syncHandleWidths.bind(this)),this.options.start&&this.bindEvents(this.$table,r.EVENT_RESIZE_START,this.options.start),this.options.resize&&this.bindEvents(this.$table,r.EVENT_RESIZE,this.options.resize),this.options.stop&&this.bindEvents(this.$table,r.EVENT_RESIZE_STOP,this.options.stop)}return s(t,[{key:"refreshHeaders",value:function(){var t=this.options.selector;"function"==typeof t&&(t=t.call(this,this.$table)),this.$tableHeaders=this.$table.find(t),this.assignPercentageWidths(),this.createHandles()}},{key:"createHandles",value:function(){var t=this,e=this.$handleContainer;null!=e&&e.remove(),this.$handleContainer=$("<div class='"+r.CLASS_HANDLE_CONTAINER+"' />"),this.$table.before(this.$handleContainer),this.$tableHeaders.each(function(e,i){var n=t.$tableHeaders.eq(e),s=t.$tableHeaders.eq(e+1);if(0!==s.length&&!n.is(r.SELECTOR_UNRESIZABLE)&&!s.is(r.SELECTOR_UNRESIZABLE)){$("<div class='"+r.CLASS_HANDLE+"' />").data(r.DATA_TH,$(i)).appendTo(t.$handleContainer)}}),this.bindEvents(this.$handleContainer,["mousedown","touchstart"],"."+r.CLASS_HANDLE,this.onPointerDown.bind(this))}},{key:"assignPercentageWidths",value:function(){var t=this;this.$tableHeaders.each(function(e,i){var n=$(i);t.setWidth(n[0],n.outerWidth()/t.$table.width()*100)})}},{key:"syncHandleWidths",value:function(){var t=this,e=this.$handleContainer;e.width(this.$table.width()),e.find("."+r.CLASS_HANDLE).each(function(e,i){var n=$(i),s=t.options.resizeFromBody?t.$table.height():t.$table.find("thead").height(),a=n.data(r.DATA_TH).outerWidth()+(n.data(r.DATA_TH).offset().left-t.$handleContainer.offset().left);n.css({left:a,height:s})})}},{key:"saveColumnWidths",value:function(){var t=this;this.$tableHeaders.each(function(e,i){var n=$(i);t.options.store&&!n.is(r.SELECTOR_UNRESIZABLE)&&t.options.store.set(t.generateColumnId(n),t.parseWidth(i))})}},{key:"restoreColumnWidths",value:function(){var t=this;this.$tableHeaders.each(function(e,i){var n=$(i);if(t.options.store&&!n.is(r.SELECTOR_UNRESIZABLE)){var s=t.options.store.get(t.generateColumnId(n));null!=s&&t.setWidth(i,s)}})}},{key:"onPointerDown",value:function(t){if(1===t.which){this.operation&&this.onPointerUp(t);var e=$(t.currentTarget);if(!e.is(r.SELECTOR_UNRESIZABLE)){var i=e.index(),n=this.$tableHeaders.eq(i).not(r.SELECTOR_UNRESIZABLE),s=this.$tableHeaders.eq(i+1).not(r.SELECTOR_UNRESIZABLE),a=this.parseWidth(n[0]),o=this.parseWidth(s[0]);this.operation={$leftColumn:n,$rightColumn:s,$currentGrip:e,startX:this.getPointerX(t),widths:{left:a,right:o},newWidths:{left:a,right:o}},this.bindEvents(this.$ownerDocument,["mousemove","touchmove"],this.onPointerMove.bind(this)),this.bindEvents(this.$ownerDocument,["mouseup","touchend"],this.onPointerUp.bind(this)),this.$handleContainer.add(this.$table).addClass(r.CLASS_TABLE_RESIZING),n.add(s).add(e).addClass(r.CLASS_COLUMN_RESIZING),this.triggerEvent(r.EVENT_RESIZE_START,[n,s,a,o],t),t.preventDefault()}}}},{key:"onPointerMove",value:function(t){var e=this.operation;if(this.operation){var i=(this.getPointerX(t)-e.startX)/this.$table.width()*100;if(0!==i){var n=e.$leftColumn[0],s=e.$rightColumn[0],a=void 0,o=void 0;return i>0?(a=this.constrainWidth(e.widths.left+(e.widths.right-e.newWidths.right)),o=this.constrainWidth(e.widths.right-i)):0>i&&(a=this.constrainWidth(e.widths.left+i),o=this.constrainWidth(e.widths.right+(e.widths.left-e.newWidths.left))),n&&this.setWidth(n,a),s&&this.setWidth(s,o),e.newWidths.left=a,e.newWidths.right=o,this.triggerEvent(r.EVENT_RESIZE,[e.$leftColumn,e.$rightColumn,a,o],t)}}}},{key:"onPointerUp",value:function(t){var e=this.operation;if(this.operation)return this.unbindEvents(this.$ownerDocument,["mouseup","touchend","mousemove","touchmove"]),this.$handleContainer.add(this.$table).removeClass(r.CLASS_TABLE_RESIZING),e.$leftColumn.add(e.$rightColumn).add(e.$currentGrip).removeClass(r.CLASS_COLUMN_RESIZING),this.syncHandleWidths(),this.saveColumnWidths(),this.operation=null,this.triggerEvent(r.EVENT_RESIZE_STOP,[e.$leftColumn,e.$rightColumn,e.newWidths.left,e.newWidths.right],t)}},{key:"destroy",value:function(){var t=this.$table,e=this.$handleContainer.find("."+r.CLASS_HANDLE);return this.unbindEvents(this.$window.add(this.$ownerDocument).add(this.$table).add(e)),e.removeData(r.DATA_TH),t.removeData(r.DATA_API),this.$handleContainer.remove(),this.$handleContainer=null,this.$tableHeaders=null,this.$table=null,t}},{key:"bindEvents",value:function(t,e,i,n){"string"==typeof e?e+=this.ns:e=e.join(this.ns+" ")+this.ns,arguments.length>3?t.on(e,i,n):t.on(e,i)}},{key:"unbindEvents",value:function(t,e){"string"==typeof e?e+=this.ns:e=null!=e?e.join(this.ns+" ")+this.ns:this.ns,t.off(e)}},{key:"triggerEvent",value:function(t,e,i){var n=$.Event(t);return n.originalEvent&&(n.originalEvent=$.extend({},i)),this.$table.trigger(n,[this].concat(e||[]))}},{key:"generateColumnId",value:function(t){return this.$table.data(r.DATA_COLUMNS_ID)+"-"+t.data(r.DATA_COLUMN_ID)}},{key:"parseWidth",value:function(t){return t?parseFloat(t.style.width.replace("%","")):0}},{key:"setWidth",value:function(t,e){e=e.toFixed(2),e=e>0?e:0,t.style.width=e+"%"}},{key:"constrainWidth",value:function(t){return void 0!=this.options.minWidth&&(t=Math.max(this.options.minWidth,t)),void 0!=this.options.maxWidth&&(t=Math.min(this.options.maxWidth,t)),t}},{key:"getPointerX",value:function(t){return 0===t.type.indexOf("touch")?(t.originalEvent.touches[0]||t.originalEvent.changedTouches[0]).pageX:t.pageX}}]),t}();i["default"]=a,a.defaults={selector:function(t){return t.find("thead").length?r.SELECTOR_TH:r.SELECTOR_TD},store:window.store,syncHandlers:!0,resizeFromBody:!0,maxWidth:null,minWidth:.01},a.count=0,e.exports=i["default"]},{"./constants":3}],3:[function(t,e,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var n="resizableColumns";i.DATA_API=n;var s="resizable-columns-id";i.DATA_COLUMNS_ID=s;var r="resizable-column-id";i.DATA_COLUMN_ID=r;var a="th";i.DATA_TH=a;var o="rc-table-resizing";i.CLASS_TABLE_RESIZING=o;var h="rc-column-resizing";i.CLASS_COLUMN_RESIZING=h;var l="rc-handle";i.CLASS_HANDLE=l;var d="rc-handle-container";i.CLASS_HANDLE_CONTAINER=d;var u="column:resize:start";i.EVENT_RESIZE_START=u;var c="column:resize";i.EVENT_RESIZE=c;var f="column:resize:stop";i.EVENT_RESIZE_STOP=f;var E="tr:first > th:visible";i.SELECTOR_TH=E;var v="tr:first > td:visible";i.SELECTOR_TD=v;var $="[data-noresize]";i.SELECTOR_UNRESIZABLE=$},{}],4:[function(t,e,i){"use strict";function n(t){return t&&t.__esModule?t:{"default":t}}Object.defineProperty(i,"__esModule",{value:!0});{var s=t("./class"),r=n(s),a=t("./adapter");n(a)}i["default"]=r["default"],e.exports=i["default"]},{"./adapter":1,"./class":2}]},{},[4]);
