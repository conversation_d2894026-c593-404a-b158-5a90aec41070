@charset "utf-8";

/*
Author：张浩华
Date：2011.11.25 0:12
Version：SimpleTree 1.0
*/

.st_tree{ 
	padding:40px 40px;
}

/* 超链接 */
.st_tree a{ 
	color:#333; 
	text-decoration:none; 
}

/* 鼠标经过的超链接 */
.st_tree a:hover{ 
	color:#dd6b4d; 
/*	text-decoration:underline;*/
}

/* 菜单 */
.st_tree ul{ 
	padding:0 0 0 18px; 
	margin:0; 
}

/* 菜单项 */
.st_tree ul li{ 
	font-size:16px; 
	color:#fff; 
	line-height:16px; 
	cursor:pointer;
	list-style:none; 
/* 	background:url(imgs/st_node.png);  */
	background-repeat:no-repeat; 
	padding:0 0 20px 40px;
}

.usable_icon{
	background: url(imgs/camera-icon-sel.png) no-repeat;
}

.disabled_icon{
	background:url(imgs/st_node.png);
}

.st_tree ul ul li:hover {
/* 	background: url(imgs/camera-icon-sel.png) no-repeat; */
}

/* 子菜单 */
.st_tree ul li ul{}

/* 子菜单项 */
.st_tree ul li ul li{}

/* 子菜单的父节点 */
.st_tree .folder{ 
	list-style-image:url(imgs/st_icon.png); 
	background:url(imgs/st_folder_open.png); 
	background-repeat:no-repeat; 
	padding:20px 0 -1px 30px; 
}

/* 展开的父节点 */
.st_tree .open{ 
	list-style-image:url(imgs/st_icon_open.png); 
	background:url(imgs/st_folder.png); 
	background-repeat:no-repeat; 
	padding:0 0 20px 40px; 
}

.st_tree .hover{
/*	background-color:#ffa;*/
}