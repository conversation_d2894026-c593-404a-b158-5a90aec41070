// 选中的播放器ID
var selectPlayerId = "";
// 选中的播放器下表
var selectIndex = null;
// 播放器数组
var playerArray = new Array();
// 是否开启全屏
var isFullScreen = false;

$(function () {

    // 窗口发生变化监听事件
    window.onresize = function () {
        if (selectIndex) {
            var playerDiv = $("#player" + selectIndex).find("div.fullscreen");
            if (playerDiv.length == 0) {
                isFullScreen = false;
                $(".monitor-controller-div").css("visibility", "hidden");
                return;
            }
            // 开启全屏
            if (playerDiv.width() == window.screen.width
                || playerDiv.height() == window.screen.height) {
                isFullScreen = true;
                $(".monitor-controller-div").css("visibility", "visible");
                $("#player" + selectIndex).find(".monitor-controller-div").css(
                    "opacity", "0.3");
            }
            // 关闭全屏
            else {
                isFullScreen = false;
                $(".monitor-controller-div").css("visibility", "hidden");
            }
        }
    }
})

/**
 * 点击播放视频源
 *
 * @param id
 * @param index
 * @param m3u8Url
 * @param transcodingStatus
 * @returns
 */
function playRecord(id, index, m3u8Url, isOnline) {
    // 进行选中
    selectDiv(id, index);
    // 判断状态
    if (isOnline == 0) {
        $.modal.msgError('设备不在线');
        return false;
    }
    // 获取播放器
    return initPlayerRecord(id, index, m3u8Url);
}

/**
 * 创建并获取播放器
 *
 * @param index
 * @param m3u8Url
 * @returns
 */
function initPlayerRecord(id, index, m3u8Url) {
    var player = playerArray[index];
    if (player) {
        //$.modal.msgError('视频已经存在...');
        return player;
    }

    try {
        // 初始化播放器
        var options = null;
        // 根据宫格加载播放器宽高
        if ($("#pageNo").val() == 1) {
            options = {
                id: "player" + index,
                width: '100%',
                height: '720px',
                source: m3u8Url,
                autoplay: false,
                isLive:false,
            };
        }

        if ($("#pageNo").val() == 4) {
            options = {
                id: "player" + index,
                width: '708px',
                height: '363px',
                source: m3u8Url,
                autoplay: false,
                isLive:false,
            };
        }
        if ($("#pageNo").val() == 9) {
            options = {
                id: "player" + index,
                width: '468px',
                height: '242px',
                source: m3u8Url,
                autoplay: false,
                isLive:false,
            };
        }
        player = new Aliplayer(options,function (player) {
            // 播放，并保存播放器
            playerArray[index] = player;
            player.play();
            $("#img" + index).hide();
            $("#player" + index).show();
            // 添加监控控制栏
            // addMonitorController(id, index);
        });
        return player;
    } catch (err) {
        $.modal.msgError("播放失败:"+err);
    }
}


/**
 * 创建一个播放窗口
 *
 * @param id
 * @param index
 * @param m3u8Url
 * @param transcodingStatus
 * @param title
 * @returns
 */
function createDiv(id, index, m3u8Url, isOnline) {
    var item = "";
    // item += '<a  href="javascript:play(\'' + id + '\', \'' + index + '\', \'' + m3u8Url + '\', \'' + transcodingStatus + '\')">';
    item += '<div id="item' + index + '" class="item' + $("#pageNo").val() + '" onclick="playRecord(\'' + id + '\', \'' + index + '\', \'' + m3u8Url + '\', \'' + isOnline + '\')">';
    if ($("#pageNo").val() == 1) {
        item += '<img id="img' + index + '" src="/static/img/monitor/camera-1.png"/>';
    }
    if ($("#pageNo").val() == 4) {
        item += '<img id="img' + index + '" src="/static/img/monitor/camera-4.png"/>';
    }
    if ($("#pageNo").val() == 9) {
        item += '<img id="img' + index + '" src="/static/img/monitor/camera-9.png"/>';
    }
    item += '<div id="player' + index + '"></div>';
    item += '</div>';
    // item += '</a>';
    return item;
}

/**
 * 添加选中样式
 *
 * @param id
 * @returns
 */
function selectDiv(id, index) {
    $(".item" + $("#pageNo").val()).removeClass("isSelect");
    $("#item" + index).find(".item" + $("#pageNo").val()).addClass("isSelect");
    selectPlayerId = id;
    selectIndex = index;
}
