// 选中的播放器ID
var selectPlayerId = "";
// 选中的播放器下表
var selectIndex = null;
// 播放器数组
var playerArray = new Array();
// 是否开启全屏
var isFullScreen = false;

$(function () {

    // 窗口发生变化监听事件
    window.onresize = function () {
        if (selectIndex) {
            var playerDiv = $("#player" + selectIndex).find("div.fullscreen");
            if (playerDiv.length == 0) {
                isFullScreen = false;
                $(".monitor-controller-div").css("visibility", "hidden");
                return;
            }
            // 开启全屏
            if (playerDiv.width() == window.screen.width
                || playerDiv.height() == window.screen.height) {
                isFullScreen = true;
                $(".monitor-controller-div").css("visibility", "visible");
                $("#player" + selectIndex).find(".monitor-controller-div").css(
                    "opacity", "0.3");
            }
            // 关闭全屏
            else {
                isFullScreen = false;
                $(".monitor-controller-div").css("visibility", "hidden");
            }
        }
    }
})

/**
 * 点击播放视频源
 *
 * @param id
 * @param index
 * @param m3u8Url
 * @param transcodingStatus
 * @returns
 */
function play(id, index, m3u8Url, isOnline) {
    // 进行选中
    selectDiv(id, index);
    // 判断状态
    if (isOnline == 0) {
        $.modal.msgError('设备不在线');
        return false;
    }

    var formData = new FormData();
    formData.append("id", id);
    $.ajax({
        url: "/hardware/monitorDevice/getOne",
        type: 'post',
        cache: false,
        data: formData,
        processData: false,
        contentType: false,
        dataType: "json",
        success: function (result) {
            if (result.code == 0) {
                if (result.data !=null){
                    m3u8Url= result.data.m3u8StreamUrl;
                    if (m3u8Url == null || m3u8Url == "" || m3u8Url == "null") {
                        $.modal.msgError("设备连接异常");
                        return false;
                    }
                    // 获取播放器
                    return initPlayer(id, index, m3u8Url);
                }
            } else {
                $.modal.msgError('启动流失败');
            }
        },
        error: function (error) {
            $.modal.msgError('操作失败');
        }
    });

}


function playRecord(id, index, m3u8Url, isOnline) {
    // 进行选中
    selectDiv(id, index);
    // 判断状态
    if (isOnline == 0) {
        $.modal.msgError('设备不在线');
        return false;
    }
    // 获取播放器
    return initPlayerRecord(id, index, m3u8Url);
}

/**
 * 创建并获取播放器
 *
 * @param index
 * @param m3u8Url
 * @returns
 */
function initPlayer(id, index, m3u8Url) {
    var player = playerArray[index];
    /*if (player) {
        $.modal.msgError('视频已经存在...');
        return player;
    }*/

    try {
        // 初始化播放器
        var options = null;
        // 根据宫格加载播放器宽高
        if ($("#pageNo").val() == 1) {
            options = {
                id: "player" + index,
                width: '100%',
                height: '720px',
                source: m3u8Url,
                autoplay: true,
                isLive:true,
            };
        }

        if ($("#pageNo").val() == 4) {
            options = {
                id: "player" + index,
                width: '698px',
                height: '363px',
                source: m3u8Url,
                autoplay: true,
                isLive:true,
            };
        }
        if ($("#pageNo").val() == 9) {
            options = {
                id: "player" + index,
                width: '458px',
                height: '242px',
                source: m3u8Url,
                autoplay: true,
                isLive:true,
            };
        }
        player = new Aliplayer(options,function (player) {
            // 播放，并保存播放器
            playerArray[index] = player;
            player.play();

            $("#img" + index).hide();
            $("#player" + index).show();
            // 添加监控控制栏
           // addMonitorController(id, index);
        });
        return player;
    } catch (err) {
        $.modal.msgError("播放失败:"+err);
    }
}


function initPlayerRecord(id, index, m3u8Url) {
    var player = playerArray[index];
    if (player) {
        $.modal.msgError('视频已经存在...');
        return player;
    }

    try {
        // 初始化播放器
        var options = null;
        // 根据宫格加载播放器宽高
        if ($("#pageNo").val() == 1) {
            options = {
                id: "player" + index,
                width: '100%',
                height: '720px',
                source: m3u8Url,
                autoplay: true,
                isLive:false,
            };
        }

        if ($("#pageNo").val() == 4) {
            options = {
                id: "player" + index,
                width: '708px',
                height: '363px',
                source: m3u8Url,
                autoplay: true,
                isLive:false,
            };
        }
        if ($("#pageNo").val() == 9) {
            options = {
                id: "player" + index,
                width: '468px',
                height: '242px',
                source: m3u8Url,
                autoplay: true,
                isLive:false,
            };
        }
        player = new Aliplayer(options,function (player) {
            // 播放，并保存播放器
            playerArray[index] = player;
            player.play();
            $("#img" + index).hide();
            $("#player" + index).show();
            // 添加监控控制栏
            // addMonitorController(id, index);
        });
        return player;
    } catch (err) {
        $.modal.msgError("播放失败:"+err);
    }
}

/**
 * 鼠标进入
 *
 * @param div
 * @returns
 */
function mouseover(div) {
    if (isFullScreen) {
        $(div).css("opacity", "1.0");
    }
}

/**
 * 鼠标出来
 *
 * @param div
 * @returns
 */
function mouseout(div) {
    if (isFullScreen) {
        $(div).css("opacity", "0.3");
    }
}

/**
 * 添加监控画面
 *
 * @param index
 * @returns
 */
function addMonitorController(id, index) {
    var $video = $("#item" + index).find("video");
    var item = "";
    item += '<div class="monitor-controller-div" onmouseover="mouseover(this);" onmouseout="mouseout(this);">';

    item += '<div class="direction_div">';
    item += '<a href="javascript:up(\'' + id + '\', \'' + index + '\');"><img class="camera_img_up" src="/img/monitor/btn-top.png" /></a>';
    item += '</div>';

    item += '<div class="direction_div">';
    item += '<a href="javascript:down(\'' + id + '\', \'' + index + '\');"><img class="camera_img_bottom" src="/img/monitor/btn-bottom.png" /></a>';
    item += '</div>';

    item += '<div class="direction_div">';
    item += '<a href="javascript:left(\'' + id + '\', \'' + index + '\');"><img class="camera_img_left" src="/img/monitor/btn-left.png" /></a>';
    item += '</div>';

    item += '<div class="direction_div">';
    item += '<a href="javascript:right(\'' + id + '\', \'' + index + '\');"><img class="camera_img_right" src="/img/monitor/btn-right.png" /></a>';
    item += '</div>';

    // item += '<div class="direction_div">';
    // item += '<a href="javascript:stop(\'' + id + '\', \'' + index + '\');"><img class="camera_img_right" src="/iot/images/btn-refresh.png"/></a>';
    // item += '</div>';

    item += '<div class="monitor-btn-bottom">';
    item += '<button onclick="zoomIn(\'' + id + '\', \'' + index + '\');">-</button>';
    item += '<button onclick="zoomOut(\'' + id + '\', \'' + index + '\');">+</button>';
    item += '</div>';

    item += '</div>';
    $video.after($(item));
}

/**
 * 切换播放画面
 *
 * @param id
 * @param m3u8Url
 * @param transcodingStatus
 * @param title
 * @returns
 */

function cut(id,isOnline) {
    // 切换窗口前的判断
    if (!selectPlayerId) {
        $.modal.msgError('请选中窗口再进行切换');
        return false;
    }
    if (!selectIndex) {
        $.modal.msgError('请选中窗口再进行切换');
        return false;
    }
    if (isOnline == 0) {
        $.modal.msgError('设备不在线');
        return false;
    }

    var m3u8Url="";
    var formData = new FormData();
    formData.append("id", id);
    $.ajax({
        url: "/hardware/monitorDevice/getOne",
        type: 'post',
        cache: false,
        data: formData,
        processData: false,
        contentType: false,
        dataType: "json",
        success: function (result) {
            if (result.code == 0) {
                if (result.data !=null){
                    m3u8Url= result.data.m3u8StreamUrl;
                    if (m3u8Url == null || m3u8Url == "" || m3u8Url == "null") {
                        $.modal.msgError('设备连接异常');
                        return false;
                    }
                    // 销毁当前播放器和窗口
                    var player = playerArray[selectIndex];
                    if (player) {
                        player.pause();
                        playerArray[selectIndex] = null;
                    }
                    // 重新创建播放器和窗口
                    var divItem = $("#item" + selectIndex);
                    var item = createDiv(id, selectIndex, m3u8Url, isOnline);
                    divItem.after($(item));
                    divItem.remove();
                    play(id, selectIndex, m3u8Url, isOnline);

                }
            } else {
                $.modal.msgError('启动流失败');
            }
        },
        error: function (error) {
            $.modal.msgError('操作失败');
        }
    });

}

/**
 * 创建一个播放窗口
 *
 * @param id
 * @param index
 * @param m3u8Url
 * @param transcodingStatus
 * @param title
 * @returns
 */
function createDiv(id, index, m3u8Url, isOnline) {
    var item = "";
    // item += '<a  href="javascript:play(\'' + id + '\', \'' + index + '\', \'' + m3u8Url + '\', \'' + transcodingStatus + '\')">';
    item += '<div id="item' + index + '" class="item' + $("#pageNo").val() + '" onclick="play(\'' + id + '\', \'' + index + '\', \'' + m3u8Url + '\', \'' + isOnline + '\')">';
    if ($("#pageNo").val() == 1) {
        item += '<img id="img' + index + '" src="/static/img/monitor/camera-1.png"/>';
    }
    if ($("#pageNo").val() == 4) {
        item += '<img id="img' + index + '" src="/static/img/monitor/camera-4.png"/>';
    }
    if ($("#pageNo").val() == 9) {
        item += '<img id="img' + index + '" src="/static/img/monitor/camera-9.png"/>';
    }
    item += '<div id="player' + index + '"></div>';
    item += '</div>';
    // item += '</a>';
    return item;
}

/**
 * 添加选中样式
 *
 * @param id
 * @returns
 */
function selectDiv(id, index) {
    $(".item" + $("#pageNo").val()).removeClass("isSelect");
    $("#item" + index).find(".item" + $("#pageNo").val()).addClass("isSelect");
    selectPlayerId = id;
    selectIndex = index;
}

/**
 * 向左转动
 *
 * @returns
 */
function left(id, index) {
    sendRequest("left", id, index);
}

/**
 * 向右转动
 *
 * @returns
 */
function right(id, index) {
    sendRequest("right", id, index);
}

/**
 * 向上转动
 *
 * @returns
 */
function up(id, index) {
    sendRequest("up", id, index);
}

/**
 * 向下转动
 *
 * @returns
 */
function down(id, index) {
    sendRequest("down", id, index);
}

/**
 * 停止转动
 *
 * @returns
 */
function stop(id, index) {
    sendRequest("stop", id, index);
}

/**
 * 拉近
 *
 * @returns
 */
function zoomIn(id, index) {
    sendRequest("zoomIn", id, index);
}

/**
 * 拉远
 *
 * @returns
 */
function zoomOut(id, index) {
    sendRequest("zoomOut", id, index);
}

/**
 * 发送命令
 */
function sendRequest(type, id, index) {
    // 加载层
    var layerIndex = null;

    if (id && index) {
        selectDiv(id, index);
    }
    if (!selectPlayerId) {
        $.modal.msgError('请选中播放器');
        return false;
    }
    if (!isFullScreen) {
        layerIndex = layer.load(2);
    }
    var formData = new FormData();
    formData.append("id", selectPlayerId);
    formData.append("type", type);
    $.ajax({
        url: "operation",
        type: 'post',
        cache: false,
        data: formData,
        processData: false,
        contentType: false,
        dataType: "json",
        success: function (result) {
            if (layerIndex) {
                layer.close(layerIndex);
            }
            if (result.code == 200) {
                layer.msg('操作成功', {time: 1000});
            } else {
                $.modal.msgError('设备不支持，操作失败');
            }
        },
        error: function (error) {
            if (layerIndex) {
                layer.close(layerIndex);
            }
            $.modal.msgError('操作失败');
        }
    });
}