.monitor-div {
	margin-top: 5px;
}

.isSelect {
	border: 2px solid red !important;
}

.monitor-div .item9 {
	float: left;
	width: 32.9%;
	height: 242px;
	/* 	margin-left: 1px; */
	margin-right: 1px;
	cursor: pointer;
	overflow: hidden;
	border: 2px solid #ebebeb;
}

.monitor-div .item4 {
	float: left;
	width: 49.6%;
	height: 363px;
	border: 2px solid #ebebeb;
	margin-right: 1px;
	cursor: pointer;
	overflow: hidden;
}

.monitor-div .item1 {
	float: left;
	width: 100%;
	height: 720px;
	border: 2px solid #ebebeb;
	margin-right: 1px;
	cursor: pointer;
	overflow: hidden;
}


.monitor-div img {
	width: 100%;
	/* 	margin-top: -28px; */
}


.dvr-controls[data-dvr-controls] {
	display: none !important;
}

.drawer-container[data-volume] {
	display: none !important;
}

.media-control-center-panel[data-media-control] {
	display: none !important;
}

/** 右侧的按钮框 */
.monitor-controller-div {
	visibility: hidden;
	position: absolute;
	right: 0px;
	height: 100%;
	background: rgba(255, 255, 255, 0.11);
}

.monitor-controller-div>.direction_div {
	margin-top: 20px;
	margin-left: 10px;
	margin-right: 10px;
}

.monitor-controller-div>.direction_div:first-child {
	margin-top: 350px;
}

.monitor-btn-bottom {
	margin-top: 20px;
	position: relative;
	color: #666;
}

/** 按钮 */
.monitor-btn-bottom button {
	display: block;
	width: 38px;
	height: 38px;
	border-radius: 50%;
	border: 0;
	font-size: 24px;
	line-height: 38px;
	text-align: center;
	color: #fff;
	background-color: #dd6b4d;
	opacity: .8;
	margin-top: 20px;
	margin-left: 10px;
	margin-right: 10px;
}

.monitor-btn-bottom button:hover {
	opacity: 1;
}

.record-play-list {
	margin-bottom: 10px;
}

.record-play-list:hover {
	color: #dd6b4d;
}

.record-play-list:hover .record-play-img img{
	border: 1px solid #dd6b4d;
}

.record-play-img {
	display:inline-block;
	width: 240px;
	height: 130px;
	overflow: hidden;
	margin-left: 20px;
/* 	margin-right: 10px; */
}

.record-play-img>img{
	width: 230px;
}

.record-play-p {
	display:inline-block;
}

.record-play-t {
	font-size: 20px;
}

.record-play-times {
	font-size: 16px;
/* 	color: #666; */
}

.record-play-time {
	font-size: 16px;
}

.prism-live-display, /*live */
.prism-cc-btn, /*cc*/
.prism-setting-btn, /*设置*/
.volume-icon, /*音量图标*/
.mute,
.prism-info-display,
.prism-detect-info,
.prism-volume {
    display: none !important;
 }
.prism-info-display.prism-info-left-bottom{
    display: none;
}

