@charset "utf-8";
/* CSS Document */
* {
	margin: 0;
	padding: 0;
}

dl, dd {
	margin: 0;
	padding: 0;
}

a, a:hover {
	cursor: pointer;
	text-decoration: none;
}

ul li {
	list-style: none;
}

button {
	cursor: pointer;
}

/*index*/
html body {
	background-color: #ebebeb;
	font-family: "微软雅黑";
	font-size: 16px;
}

.login-bg {
	background: url(../images/login-bg.png) no-repeat;
	background-size: cover;
}

.login-box {
	width: 60%;
	min-width: 1080px;
	height: 640px;
	background-color: #fff;
	box-shadow: 0px 2px 16px 0px rgba(67, 75, 81, 0.5);
	border-radius: 16px;
	margin: 140px auto;
}

.login {
	float: left;
	width: 100%;
	height: 350px;
	margin-top: 160px;
}

.login-l {
	float: left;
	text-align: center;
	width: -moz-calc(50% - 1px);
	width: -webkit-calc(50% - 1px);
	width: calc(50% - 1px);
	height: 100%;
	border-right: 1px solid #eee;
}

.login-r {
	float: right;
	text-align: center;
	width: 50%;
	height: 100%;
}

.login-right-bg {
	width: 358px;
	height: 314px;
	margin: 18px auto;
}

.login-form {
	width: 340px;
	margin: 0 auto;
}

.login-form h3 {
	font-size: 36px;
	color: #dd6b4d;
}

.user input, .password input {
	height: 40px;
	width: 340px;
	font-size: 16px;
	text-indent: 50px;
	outline: none;
	border: 1px solid #cbcccb;
	border-radius: 4px;
}

.code input {
	float: left;
	height: 40px;
	width: 160px;
	font-size: 16px;
	text-indent: 10px;
	outline: none;
	border: 1px solid #cbcccb;
	border-radius: 4px;
}

.code-img {
	float: right;
	width: 160px;
	background-color: #ccc;
	height: 40px;
}

.user, .password, .code {
	position: relative;
	width: 340px;
	height: 40px;
	margin-top: 30px;
}

.ipt-icon img {
	position: absolute;
	left: 10px;
	top: 8px;
}

.login-btn {
	float: left;
	width: 340px;
}

.login-btn button {
	width: 134px;
	height: 40px;
	border: 0;
	background-color: #dd6b4d;
	border-radius: 4px;
	margin-top: 40px;
	float: right;
	color: #fff;
}

.login-btn button:hover {
	opacity: .8;
}

.checkbox {
	float: left;
	margin-top: 50px;
	color: #333;
	cursor: pointer;
}

.my_protocol .input_agreement_protocol {
	appearance: none;
	-webkit-appearance: none;
	outline: none;
	display: none;
}

.my_protocol .input_agreement_protocol+span {
	width: 16px;
	height: 16px;
	background-color: red;
	display: inline-block;
	background: url(../images/icon_checkbox.png) no-repeat;
	background-position-x: 0px;
	background-position-y: -25px;
	position: relative;
	top: 3px;
	cursor: pointer;
}

.my_protocol .input_agreement_protocol:checked+span {
	background-position: 0 0px
}

.login-copyright {
	float: left;
	font-size: 18px;
	color: #333;
	margin-top: 40px;
	width: 100%;
	text-align: center;
}

.nav-box {
	position: fixed;
	height: 80px;
	width: 100%;
	z-index: 999;
	background-color: #183661;
}

.nav-logo {
	float: left;
	width: 256px;
	color: #fff;
	font-size: 24px;
	padding-left: 20px;
	line-height: 80px;
}

.nav {
	height: 80px;
	overflow: hidden;
}

.nav-tab {
	position: relative;
	float: left;
	height: 80px;
	overflow: hidden;
}

.nav-tab-list {
	position: relative;
	float: left;
	width: 120px;
	height: 80px;
	display: inline-block;
	vertical-align: middle;
	font-size: 16px;
}

.border-1 {
	background: url(../images/border-1px.png) no-repeat 0 20px;
}

.nav-tab-list a {
	position: relative;
	display: inline-block;
	color: #fff;
	text-align: center;
	width: 120px;
	height: 80px;
	padding-top: 6px;
}

.nav-tab-list a:hover {
	color: #fff;
}

.nav-tab-list a span {
	position: absolute;
	margin: 14px auto 3px;
	width: 32px;
	height: 30px;
	left: 50%;
	right: 50%;
	margin-left: -16px;
}

.nav-tab-list .icon1 {
	background: url(../images/jiajuanfang-icon.png) no-repeat center center;
}

.nav-tab-list .icon2 {
	background: url(../images/gonggongfuwu-icon.png) no-repeat center center;
}

.nav-tab-list .icon3 {
	background: url(../images/nengyuanguanli-icon.png) no-repeat center
		center;
}

.nav-tab-list .icon4 {
	background: url(../images/wangluoshebei-icon.png) no-repeat center
		center;
}

.nav-tab-list .icon5 {
	background: url(../images/xitong-icon.png) no-repeat center center;
}

.nav-tab-list .icon6 {
	background: url(../images/zichanguanli-icon.png) no-repeat center center;
}

.nav-tab-list p {
	clear: both;
	/*	margin-top: 44px;*/
}

.nav-tab-on {
	background-color: #112440;
}

.nav-avator {
	position: absolute;
	right: 0;
	margin-right: 20px;
}

.nav-avator-arrow {
	content: '';
	width: 0;
	height: 0;
	border-style: solid dashed dashed;
	border-color: #fff transparent transparent;
	overflow: hidden;
	cursor: pointer;
	transition: all .2s;
	-webkit-transition: all .2s;
	position: absolute;
	top: 50%;
	right: -20px;
	margin-top: -2px;
	border-width: 6px;
	border-top-color: rgba(255, 255, 255, 1);
}

/*
.nav-avator-arrow {
	position: absolute;
	display: block;
	width: 12px;
	height: 9px;
	background: url(../images/arrow-b.png) no-repeat center center;
	top: 38px;
	right: -20px;
}
*/
.nav-avator .nav-img {
	width: 44px;
	height: 44px;
	border-radius: 50%;
	display: inline-block;
	vertical-align: middle;
}

.nav-avator-list {
	position: relative;
	display: inline-block;
	width: 120px;
}

.nav-avator-list a {
	position: relative;
	display: inline-block;
	color: #fff;
	margin-top: 18px;
}

.nav-avaotr-menu {
	display: none;
	position: absolute;
	left: 0;
	top: 80px;
	min-height: 100%;
	line-height: 32px;
	padding: 10px 10px;;
	box-shadow: 0 2px 4px rgba(0, 0, 0, .12);
	border: 1px solid #d2d2d2;
	background-color: #fff;
	z-index: 100;
	white-space: nowrap;
	cursor: pointer;
}

.nav-avator-list:hover .nav-avator-arrow {
	margin-top: -9px;
	border-style: dashed dashed solid;
	border-color: transparent transparent #fff;
}

/*
.nav-avator .nav-avator-list:hover .nav-avaotr-menu {
	display: block;
	top: 80px;
	left: 0;
	z-index: 999;
}
*/
.nav-side {
	position: fixed;
	width: 198px;
	height: 100%;
	background-color: #fff;
	left: 0;
	top: 80px;
	z-index: 999;
}

.nav-side-menu {
	position: relative;
	width: 198px;
	margin-top: 20px;
}

.nav-side-list {
	position: relative;
	margin: 0 auto;
	width: 158px;
	height: 36px;
	border-radius: 4px;
	text-align: center;
	cursor: pointer;
}

.side-on {
	background: url( ../images/sidebar-sel.png);
	background: -moz-linear-gradient(10deg, #dd6b4d 50%, #ecbe50 100%);
	background: -webkit-linear-gradient(10deg, #dd6b4d 50%, #ecbe50 100%);
	background: -ms-linear-gradient(10deg, #dd6b4d 50%, #ecbe50 100%);
	/*	FILTER: progid:DXImageTransform.Microsoft.Gradient(startColorStr=#dd6b4d, endColorStr=#ecbe50); */
	filter: progid:DXImageTransform.Microsoft.Gradient(startColorStr='#541CD4',
		endColorStr='#009BDF', gradientType='0');
}

.side-on span p {
	color: #fff;
}

.nav-side-list span {
	color: #333;
	line-height: 36px;
}

.nav-side-list span p {
	float: left;
	position: absolute;
	margin-left: 50px;
}

.side-icon {
	float: left;
	position: absolute;
	width: 20px;
	height: 20px;
	top: 8px;
	left: 20px;
	display: block;
}

.icon-shexiangtou {
	background: url(../images/shexiangtou-nor.png) no-repeat center center;
}

.side-on .icon-shexiangtou {
	background: url(../images/shexiangtou-sel.png) no-repeat center center;
}

.icon-menjin {
	background: url(../images/menjin-nor.png) no-repeat center center;
}

.side-on .icon-menjin {
	background: url(../images/menjin-sel.png) no-repeat center center;
}

.icon-chewei {
	background: url(../images/chewei-noe.png) no-repeat center center;
}

.side-on .icon-chewei {
	background: url(../images/chewei-sel.png) no-repeat center center;
}

.icon-shuijin {
	background: url(../images/shuijin-nor.png) no-repeat center center;
}

.side-on .icon-shuijin {
	background: url(../images/shuijin-sel.png) no-repeat center center;
}

.icon-yanwu {
	background: url(../images/yanwu-nor.png) no-repeat center center;
}

.side-on .icon-yanwu {
	background: url(../images/yanwu-sel.png) no-repeat center center;
}

.icon-zhinengmen {
	background: url(../images/zhinengmen-nor.png) no-repeat center center;
}

.side-on .icon-zhinengmen {
	background: url(../images/zhinengmen-sel.png) no-repeat center center;
}

.icon-shexiangtou {
	background: url(../images/shexiangtou-nor.png) no-repeat center center;
}

.copyright {
	position: fixed;
	bottom: 0;
	width: -moz-calc(100% - 198px);
	width: -webkit-calc(100% - 198px);
	width: calc(100% - 198px);
	color: #666;
	padding: 10px 0;
	background-color: #fff;
	text-align: center;
	border-top: 1px solid #ebebeb;
	box-shadow: 10px 10px 10px #ccc;
}

.content {
	position: absolute;
	top: 80px;
	left: 198px;
	/*	bottom: 40px;*/
	width: -moz-calc(100% - 198px);
	width: -webkit-calc(100% - 198px);
	width: calc(100% - 198px);
	overflow: auto;
	box-sizing: border-box;
}

.side-content {
	float: left;
	width: 100%;
}

.side-content li {
	display: none;
	float: left;
	width: 100%;
}

.side-content .show {
	display: block;
}

.right-btn {
	float: right;
	margin-right: 20px;
	margin-top: 20px;
}

.right-btn button {
	width: 100px;
	height: 38px;
	color: #fff;
	-moz-border-radius: 4px;
	-webkit-border-radius: 4px;
	border-radius: 4px;
	border: none;
	margin-left: 20px;
}

.right-btn button:hover {
	opacity: .8;
}

.btn-shebei {
	background-color: #dd6b4d;
}

.btn-jiankong {
	background-color: #4b6ff2;
}

.gaikuang-box {
	width: -moz-calc(100% - 40px);
	width: -webkit-calc(100% - 40px);
	width: calc(100% - 40px);
	min-width: 1200px;
	float: left;
	background-color: #fff;
	margin: 20px 20px 0;
}

.gaikuang-box h4 {
	font-size: 18px;
	font-weight: bold;
	color: #444;
	padding: 10px;
	border-bottom: 1px solid #ebebeb;
}

.gaikuang {
	display: flex;
	margin: 10px;
}

.gaikuang .shebei-total, .gaikuang .online-total, .gaikuang .time-total
	{
	width: 33.3%;
	margin: 10px;
	height: 260px;
	border-radius: 4px;
}

.shebei-total {
	position: relative;
	background: -moz-linear-gradient(0deg, rgb(111, 142, 251) 0%,
		rgb(196, 201, 247) 100%);
	background: -webkit-linear-gradient(0deg, rgb(111, 142, 251) 0%,
		rgb(196, 201, 247) 100%);
	background: -ms-linear-gradient(0deg, rgb(111, 142, 251) 0%,
		rgb(196, 201, 247) 100%);
}

.shebei-total:hover {
	box-shadow: 2px 4px 16px rgb(111, 142, 251);
}

.p-header {
	font-size: 18px;
	color: #fff;
	padding: 20px 20px 40px;
}

.nubmer {
	position: relative;
	font-size: 48px;
	color: #fff;
	margin-left: 120px;
}

.nubmer:hover {
	color: #fff;
}

.p-ge {
	position: absolute;
	width: 40px;
	bottom: 10px;
	display: inline-block;
	font-size: 18px;
	float: left;
}

.p-time {
	color: #fff;
	font-size: 18px;
	position: absolute;
	bottom: 20px;
	left: 20px;
}

.shebei-img {
	width: 74px;
	height: 70px;
	position: absolute;
	display: block;
	right: 20px;
	bottom: 20px;
	background: url(../images/shebei-total.png) no-repeat;
}

.online-img {
	width: 90px;
	height: 68px;
	position: absolute;
	display: block;
	right: 20px;
	bottom: 20px;
	background: url(../images/online-total.png) no-repeat;
}

.time-img {
	width: 80px;
	height: 80px;
	position: absolute;
	display: block;
	right: 20px;
	bottom: 20px;
	background: url(../images/time-total.png) no-repeat;
}

.online-total {
	position: relative;
	background: -moz-linear-gradient(0deg, rgb(251, 99, 53) 0%,
		rgb(252, 208, 192) 100%);
	background: -webkit-linear-gradient(0deg, rgb(251, 99, 53) 0%,
		rgb(252, 208, 192) 100%);
	background: -ms-linear-gradient(0deg, rgb(251, 99, 53) 0%,
		rgb(252, 208, 192) 100%);
}

.online-total:hover {
	box-shadow: 2px 4px 16px rgb(251, 99, 53);
}

.time-total {
	position: relative;
	background: -moz-linear-gradient(0deg, rgb(255, 172, 69) 0%,
		rgb(253, 229, 167) 100%);
	background: -webkit-linear-gradient(0deg, rgb(255, 172, 69) 0%,
		rgb(253, 229, 167) 100%);
	background: -ms-linear-gradient(0deg, rgb(255, 172, 69) 0%,
		rgb(253, 229, 167) 100%);
}

.time-total:hover {
	box-shadow: 2px 4px 16px rgb(255, 172, 69);
}

.qingkuang-box {
	float: left;
	width: -moz-calc(100% - 40px);
	width: -webkit-calc(100% - 40px);
	width: calc(100% - 40px);
	margin: 20px;
}

.qingkuang {
	float: left;
	margin-right: 20px;
	position: relative;
	width: -moz-calc(33.33% - 10px);
	width: -webkit-calc(33.33% - 10px);
	width: calc(33.33% - 10px);
	min-width: 374px;
	background-color: #fff;
	height: 345px;
}

.qingkuang:hover {
	box-shadow: 2px 4px 16px #999;
}

.qingkuang h4 {
	font-size: 18px;
	font-weight: bold;
	color: #444;
	padding: 10px;
	border-bottom: 1px solid #ebebeb;
}

.chart {
	width: 100%;
	margin-top: 80px;;
	text-align: center;
}

.chart img {
	margin: 0 auto;
}

.over-time {
	position: absolute;
	width: 100%;
	bottom: 20px;
	color: #999999;
	text-align: center;
}

.top-a-left {
	font-size: 18px;
	color: #dd6b4d;
}

.top-a-right {
	font-size: 18px;
}

.shebei-box {
	/*		height: 600px; */
	
}

.search-box {
	margin: 20px;
}

.search-ipt {
	margin-right: 20px;
}

.layui-inline .search-btn {
	background-color: #dd6b4d;
	padding: 0 40px;
}

.table-box {
	margin: 20px;
	width: -moz-calc(100% - 40px);
	width: -webkit-calc(100% - 40px);
	width: calc(100% - 40px);
}

.layui-form .layui-table {
	width: 100%;
	max-width: 1642px;
}

.fenye {
	float: right;
	margin-bottom: 40px;
}

.fenye .layui-laypage-current {
	background-color: #dd6b4d;
	color: #fff;
}

.fenye .layui-laypage a:hover {
	color: #dd6b4d;
}

.layui-table .gray-color {
	background-color: #f2f2f2;
	width: 200px;
}

.gaikuang .zhuma, .gaikuang .fuma, .gaikuang .yidong {
	width: 33.3%;
	margin: 10px;
	height: 200px;
	border: 1px solid #dd6b4d;
}

.p-jiankong {
	font-size: 18px;
	color: #666;
	padding: 20px;
}

.p-jiankong-a {
	float: right;
	color: #dd6b4d;
}

.p-jiankong-a:hover {
	color: #dd6b4d;
}

.a-gaoqing {
	float: left;
	font-size: 18px;
	color: #333;
	margin-left: 80px;
	padding-top: 60px;
}

.video-box {
	position: relative;
	float: left;
	width: 100%;
	/* 	top: 10px; */
	margin-top: 5px;
	/*min-width: 1700px;*/
}

.video-left {
	float: right;
	width: -moz-calc(100% - 414px);
	width: -webkit-calc(100% - 414px);
	width: calc(100% - 414px);
	margin-left: 5px;
}

.video-right {
	float: left;
	width: 397px;
	margin-right: 5px;
}

.camera-box {
	background-color: #fff;
	padding: 10px;
}

.camera-box span {
	margin-left: 20px;
}

.viedo {
	margin-top: 20px;
}

.viedo div {
	float: left;
	width: 33.2%;
	height: 245px;
	background-color: #ccc;
}

.margin-r {
	margin-right: 1px;
}

.margin-b {
	margin-bottom: 1px;
}

.video-list {
	background-color: #fff;
	height: 797px;
	overflow: auto;
}

.video-btn-box {
	margin-top: 5px;
	height: 250px;
	text-align: center;
	background-color: #fff;
}

.video-btn-bottom img {
	padding-top: 20px;
}

.search-box .layui-form-select dl dd.layui-this {
	background-color: #dd6b4d;
	color: #fff;
}

.btn-group {
	float: left;
}

.layui-btn-group .layui-btn.add-btn {
	background-color: #6f8efb;
}

.layui-btn-group .layui-btn.edit-btn {
	background-color: #43cf7c;
}

.layui-btn-group .layui-btn.upload-btn {
	background-color: #00baad;
}

.layui-btn-group .layui-btn.download-btn {
	background-color: #dd6b4d;
}

.video-btn {
	padding-top: 10px;
	cursor: pointer;
	position: relative;
}

.change_camera_direction {
	text-align: center;
	width: 180px;
	height: 180px;
	background-color: rgba(221, 107, 77, .2);
	border-radius: 50%;
	margin: 0 auto;
}

.direction_content {
	width: 100%;
	height: 100%;
	position: relative;
}

.direction_div {
	position: relative;
	width: 38px;
	height: 38px;
}

.direction_div:hover {
	background-color: #dd6b4d;
	border-radius: 4px;
}

.direction_div img {
	width: 100%;
	height: 100%;
	opacity: .7;
}
/*left*/
.left_direction {
	top: 18%;
	left: 6%;
}
/*bottom*/
.bottom_direction {
	top: 31%;
	left: 39%;
}
/*right*/
.right_direction {
	top: -24%;
	left: 73%;
}
/*top*/
.top_direction {
	top: 5%;
	left: 39%;
}

.center_direction {
	top: -45%;
	left: 39%;
}

.progress {
	position: relative;
	width: 200px;
	margin: 10px auto;
}

.progress_bg {
	height: 10px;
	border: 1px solid #ddd;
	border-radius: 5px;
	overflow: hidden;
	background-color: #f2f2f2;
}

.progress_bar {
	background: #dd6b4d;
	width: 0;
	height: 10px;
	border-radius: 5px;
}

.progress_btn {
	width: 20px;
	height: 20px;
	border-radius: 10px;
	position: absolute;
	background: #fff;
	left: 0px;
	margin-left: -10px;
	top: -5px;
	cursor: pointer;
	border: 1px #ddd solid;
	box-sizing: border-box;
}

.progress_btn:hover {
	border-color: #F7B824;
}

.video-btn-bottom {
	margin-top: 20px;
	position: relative;
	color: #666;
}

.min {
	font-size: 14px;
	position: absolute;
	left: 120px;
	top: -14px;
}

.max {
	font-size: 14px;
	position: absolute;
	right: 120px;
	top: -14px;
}

/** 滚动条样式 */
::-webkit-scrollbar-track {
	background-color: #F5F5F5;
}

::-webkit-scrollbar {
	width: 10px;
	height: 10px;
	background-color: #F5F5F5;
}

::-webkit-scrollbar-thumb {
	border-radius: 6px;
	background-color: #999;
}

/** 监控按钮 */
.video-btn-bottom button {
	width: 38px;
	height: 38px;
	border-radius: 50%;
	border: 0;
	font-size: 24px;
	line-height: 38px;
	text-align: center;
	color: #fff;
	background-color: #dd6b4d;
	opacity: .8;
}

.video-btn-bottom button:hover {
	opacity: 1;
}

/*视频回放css*/
.replya-big-box {
	position: relative;
	float: left;
	top: 20px;
	/* 	margin-left: 200px; */
	width: 100%;
	/* 	width: calc(100% - 200px); */
	/* 	width: -webkit-calc(100% - 200px); */
	/* 	width: -moz-calc(100% - 200px); */
}

.replay-title {
	/*	float: left;*/
	width: calc(100% - 40px);
	width: -webkit-calc(100% - 40px);
	width: -moz-calc(100% - 40px);
	height: 60px;
	margin-left: 20px;
	background-color: #fff;
	border-radius: 4px;
}

.replay-title p {
	float: left;
	font-size: 18px;
	margin-left: 20px;
	line-height: 60px;
	font-weight: bold;
	color: #444;
}

.replya-search {
	height: 40px;
	margin-right: 20px;
	margin-top: 10px;
	float: right;
	border-bottom: 1px solid #d9d9d9;
}

.replay-box {
	width: 100%;
	margin-top: 20px;
}

.replya-search input {
	float: left;
	width: 360px;
	height: 40px;
	text-indent: 5px;
	border: none;
}

.replya-search-btn {
	float: right;
	height: 40px;
	width: 40px;
	border: none;
	background-color: none;
	background: url(/iot/images/search-icon.png) no-repeat;
	background-position: 6px 6px;
}

.replya-search-btn:hover {
	border-radius: 4px;
	background: url(/iot/images/search-white-icon.png) no-repeat #dd6b4d;
	background-position: 6px 6px;
}

.replya-search ::-webkit-input-placeholder { /* WebKit browsers */
	color: #ccc;
	font-size: 16px;
}

.replya-search ::-moz-placeholder { /* Mozilla Firefox 19+ */
	color: #ccc;
	font-size: 16px;
}

.replya-search :-ms-input-placeholder { /* Internet Explorer 10+ */
	color: #ccc;
	font-size: 16px;
}

.replay-item-box {
	float: left;
	width: calc(100% - 30px);
	width: -webkit-calc(100% - 30px);
	width: -moz-calc(100% - 30px);
	margin: 0 20px 10px;
}

.replay-item {
	position: relative;
	float: left;
	margin-right: 10px;
	width: calc(20% - 10px);
	width: -webkit-calc(20% - 10px);
	width: -moz-calc(20% - 10px);
	height: 310px;
	background-color: #fff;
	border-radius: 8px;
	cursor: pointer;
}

.replay-mask {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 250px;
	border-top-left-radius: 8px;
	border-top-right-radius: 8px;
	background-color: #000;
	opacity: 0;
}

.replay-icon {
	display: none;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 250px;
	text-align: center;
	line-height: 250px;
}

.replay-icon .layui-icon {
	font-size: 48px;
	color: #fff;
	font-weight: bold;
	z-index: 999;
}

.replay-item:hover {
	box-shadow: 2px 6px 6px #ccc;
}

.replay-item:hover .replay-mask {
	opacity: .2;
}

.replay-item:hover .replay-icon {
	display: block;
}

.replay-item:hover .replay-item-title {
	background-color: #fff;
	color: #dd6b4d;
	z-index: 999;
}

.replay-img {
	position: relative;
	height: 250px;
	background-color: #ccc;
	border-top-left-radius: 8px;
	border-top-right-radius: 8px;
	overflow: hidden;
}

.replay-img img {
	height: 100%;
}

.replay-time {
	position: absolute;
	background-color: #000;
	opacity: .7;
	padding: 2px 6px;
	border-radius: 2px;
	right: 10px;
	bottom: 10px;
	color: #fff;
}

.replay-time a {
	color: #fff;
}

.replay-item-title {
	height: 60px;
	/*	background-color: #fff;*/
	border-bottom-left-radius: 8px;
	border-bottom-right-radius: 8px;
}

.replay-item-title h4 {
	font-size: 16px;
	font-weight: bold;
	padding: 6px 0 0 6px;
}

.replay-item-title p {
	font-size: 14px;
	color: #999;
	padding: 4px 0 0 6px;
}

.replay-page {
	float: right;
	margin-right: 20px;
}

.replay-box .layui-laypage .layui-laypage-curr .layui-laypage-em {
	background-color: #dd6b4d;
}

.replay-box .layui-laypage .layui-laypage-refresh i:hover {
	color: #dd6b4d;
}

.replay-box .layui-laypage a:hover {
	color: #dd6b4d;
}

.replay-page-number {
	margin-left: 20px;
}

.replay-page-button {
	margin-top: -20px;
	margin-right: 20px;
}

/*视频回放播放页面css*/
.replay-play-box {
	padding: 0 20px;
}

.replay-play {
	float: left;
/* 	background-color: #ccc; */
	width: calc(100% - 500px);
/* 	height: 720px; */
}

.replay-play-title p {
	font-size: 24px;
	padding-left: 20px;
	color: #333;
}

.replay-play-list-box {
	float: right;
	width: 480px;
	height: 680px;
	background-color: #fff;
	overflow: auto;
}

.replay-play-list {
/* 	height: 180px; */
	width: 100%;
	cursor: pointer;
}

.replay-play-list:hover {
	/*	background-color:rgba(0,0,0,0.10);*/
	color: #dd6b4d;
}

.replay-play-list:hover .replya-play-img img {
	border: 1px solid #dd6b4d;
}

.replya-play-img {
	float: left;
	width: 240px;
	height: 150px;
	overflow: hidden;
}

.replya-play-img img {
	margin: 10px;
	width: 220px;
/* 	height: 160px; */
/* 	overflow: hidden; */
}

.replya-play-p {
	float: left;
	width: 230px;
}

.replya-play-p p {
/* 	padding: 10px; */
	padding: 8px;
}

.replya-play-t {
	margin-top: 10px;
	font-size: 20px;
}

.replya-play-times {
	font-size: 16px;
	color: #666;
}

.replya-play-time {
	font-size: 16px;
}

.replay-play-list-h {
	margin: 20px 0;
}

.replay-play-list-h h4 {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	border-bottom: 2px solid #dd6b4d;
	padding-left: 10px;
	padding-bottom: 10px;
}

.replay-play-btn p {
	font-size: 18px;
	color: #666;
	float: right;
	margin-top: 20px;
	margin-right: 20px;
	cursor: pointer;
}

.replay-play-btn p:hover {
	color: #dd6b4d;
}