function chart01() {
    var chartDom = document.getElementById('jg-chart01');
    var myChart = echarts.init(chartDom);
    var v0 = 0;
    var v1 = 0;


    var superviseSchoolCount = $("#superviseSchoolCount").val();
    var noSuperviseSchoolCount= $("#noSuperviseSchoolCount").val();

    if ($.common.isNotEmpty(superviseSchoolCount)){
        v0=superviseSchoolCount;
    }

    if ($.common.isNotEmpty(noSuperviseSchoolCount)){
        v1=noSuperviseSchoolCount;
    }

    // 指定图表的配置项和数据
    var dataCake=[
        { value: v0 , name: '受监管驾校' },
        { value: v1, name: '未受监管驾校' }
    ];

    var option;
    option = {
        tooltip: {
            trigger:'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        title: {
            text: '受监管驾校占比',
            x:'center',
            y:'bottom',
            itemGap: 10,
            textStyle: {
                fontSize: 12,
                color: '#333'
            },
            top:'85',
            left:'30'
        },

        color:['#FF9452','#31D9AB'],
        legend: {
            orient: 'vertical',
            data: ['受监管驾校', '未受监管驾校'],
            itemGap: 30,
            right: 5,
            y: 'center',
            itemWidth: 12,  // 设置图例图形的宽
            itemHeight: 12,
            selectedMode: false,
            formatter:function(name){
                let target;
                for(let i=0;i<dataCake.length;i++){
                    if(dataCake[i].name===name){
                        target=dataCake[i].value
                    }
                }
                let arr=[name+':',""+target]
                return arr.join("")
            },
        },

        series: [
            {
                name: '',
                type: 'pie',
                radius: ['50%', '70%'],
                center: ['30%', '40%'],
                avoidLabelOverlap: false,
                label: {
                    normal: {
                        show: false,
                        position: 'center'
                    },
                },
                labelLine: {
                    show: false
                },
                data: [
                    {value: v0, name: '受监管驾校',label:{
                            normal:{
                                show:true,
                                formatter: '{d}%',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bolder',
                                },
                            }}
                    },
                    {value: v1, name: '未受监管驾校'}
                ]
            }
        ]
    };

    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }

}


function chart02() {
    var chartDom = document.getElementById('jg-chart02');
    var myChart = echarts.init(chartDom);
    var v0 = 0;
    var v1 = 0;

    // 指定图表的配置项和数据
    var dataCake=[
        { value: v0 , name: '受监管训练场数' },
        { value: v1, name: '未受监管训练场数' }
    ];

    var option;
    option = {
        tooltip: {
            trigger:'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        title: {
            text: '受监管训练场数占比',
            x:'center',
            y:'bottom',
            itemGap: 10,
            textStyle: {
                fontSize: 12,
                color: '#333'
            },
            top:'85',
            left:'10'
        },

        color:['#126AFC','#FF9452'],
        legend: {
            orient: 'vertical',
            data: ['受监管训练场数', '未受监管训练场数'],
            itemGap: 30,
            right: 5,
            y: 'center',
            itemWidth: 12,  // 设置图例图形的宽
            itemHeight: 12,
            selectedMode: false,
            formatter:function(name){
                let target;
                for(let i=0;i<dataCake.length;i++){
                    if(dataCake[i].name===name){
                        target=dataCake[i].value
                    }
                }
                let arr=[name+':',""+target]
                return arr.join("")
            },
        },

        series: [
            {
                name: '',
                type: 'pie',
                radius: ['50%', '70%'],
                center: ['20%', '40%'],
                avoidLabelOverlap: false,
                label: {
                    normal: {
                        show: false,
                        position: 'center'
                    },
                },
                labelLine: {
                    show: false
                },
                data: [
                    {value: v0, name: '受监管训练场数',label:{
                            normal:{
                                show:true,
                                formatter: '{d}%',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bolder',
                                },
                            }}
                    },
                    {value: v1, name: '未受监管训练场数'}
                ]
            }
        ]
    };

    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }

}


function chart03() {
    var chartDom = document.getElementById('jg-chart03');
    var myChart = echarts.init(chartDom);
    var v0 = 0;
    var v1 = 0;


    var superviseStudentCount = $("#superviseStudentCount").val();
    var noSuperviseStudentCount= $("#noSuperviseStudentCount").val();

    if ($.common.isNotEmpty(superviseStudentCount)){
        v0=superviseStudentCount;
    }

    if ($.common.isNotEmpty(noSuperviseStudentCount)){
        v1=noSuperviseStudentCount;
    }

    // 指定图表的配置项和数据
    var dataCake=[
        { value: v0 , name: '受监管学员' },
        { value: v1, name: '未受监管学员' }
    ];

    var option;
    option = {
        tooltip: {
            trigger:'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        title: {
            text: '受监管学员占比',
            x:'center',
            y:'bottom',
            itemGap: 10,
            textStyle: {
                fontSize: 12,
                color: '#333'
            },
            top:'85',
            left:'30'
        },

        color:['#8878FF','#FF9452'],
        legend: {
            orient: 'vertical',
            data: ['受监管学员', '未受监管学员'],
            itemGap: 30,
            right: 5,
            y: 'center',
            itemWidth: 12,  // 设置图例图形的宽
            itemHeight: 12,
            selectedMode: false,
            formatter:function(name){
                let target;
                for(let i=0;i<dataCake.length;i++){
                    if(dataCake[i].name===name){
                        target=dataCake[i].value
                    }
                }
                let arr=[name+':',""+target]
                return arr.join("")
            },
        },

        series: [
            {
                name: '',
                type: 'pie',
                radius: ['50%', '70%'],
                center: ['30%', '40%'],
                avoidLabelOverlap: false,
                label: {
                    normal: {
                        show: false,
                        position: 'center'
                    },
                },
                labelLine: {
                    show: false
                },
                data: [
                    {value: v0, name: '受监管学员',label:{
                            normal:{
                                show:true,
                                formatter: '{d}%',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bolder',
                                },
                            }}
                    },
                    {value: v1, name: '未受监管学员'}
                ]
            }
        ]
    };

    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }

}


function chart04() {
    var chartDom = document.getElementById('jg-chart04');
    var myChart = echarts.init(chartDom);
    var option;
    option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        title: {
            text: '以上数据仅作参考，可能存在误差。本平台将持续优化数据模型，提升准确性。',
            x:'center',
            y:'bottom',
            itemGap: 10,
            textStyle: {
                fontSize: 12,
                color: '#333'
            },
        },
        legend: {
            right: 5,
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            containLabel: true
        },

        xAxis: {
            type: 'category',
            data: ['全市(合计)', '宏天驾校', '张师傅驾校 ', '广仁驾校', '永成驾校']
        },
        yAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
            axisLabel: {
                formatter: '{value} %'
              }
        },
        //color:'#448AFC',
        series: [
            {
                name: '科目二异常状态占比',
                data: [11.36, 32.14, 9.09, 5.00, 1.00],
                type: 'bar',
                tooltip: {
                    valueFormatter: function (value) {
                      return value + ' %';
                    }
                },
                itemStyle: {
                    color: '#4A90E2'
                },
                showBackground: true,
                backgroundStyle: {
                    color: 'rgba(180, 180, 180, 0.2)'
                },
                barWidth:30
            },
            {
                name: '科目三异常状态占比',
                data: [15.15, 21.43, 18.18, 10.00, 10.00],
                type: 'bar',
                tooltip: {
                    valueFormatter: function (value) {
                      return value + ' %';
                    }
                  },
                showBackground: true,
                backgroundStyle: {
                    color: 'rgba(180, 180, 180, 0.2)'
                },
                barWidth:30
            }
        ]
    };
    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }

}


function chart05() {
    var chartDom = document.getElementById('jg-chart05');
    var myChart = echarts.init(chartDom);
    var option;


    option = {
        title: {
            text: '以上数据仅作参考，可能存在误差。本平台将持续优化数据模型，提升准确性。',
            x:'center',
            y:'bottom',
            itemGap: 10,
            textStyle: {
                fontSize: 12,
                color: '#333'
            },
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            right: 5,
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            containLabel: true
        },
        color:'#FFB971',
        xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01]
        },
        yAxis: {
            type: 'category',
            data: ['宏天驾校(石码)', '宏天驾校(石龙)', '宏天驾校(企石', '广仁驾校(主山平', '广仁驾校(西平)']
        },
        series: [
            {
                name: '异常状态占比',
                type: 'bar',
                data: [30, 89, 104, 183, 294]
            },
        ]
    };

    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }

}