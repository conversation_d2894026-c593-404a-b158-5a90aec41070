
//地图加载
var map = new AMap.Map("container", {
    resizeEnable: false,
    zoom:11,
});

var clickHandler = function(e) {
    loadMapAddress(e.lnglat.getLat(),e.lnglat.getLng())
};
map.setDefaultCursor('crosshair');
// 绑定事件
map.on('click', clickHandler);

$('#location').click(function () {
    //有值的情况下
    if ($('#gpsAddress').val()) {
        var province = $("#province option:checked").val();
        var city = $("#city option:checked").val();
        if ($.common.isEmpty(province) ||  $.common.isEmpty(city)) {
            $.modal.alertError("至少选择两级行政区域");
            return;
        }
        addressAjax()
    }else{
        $.modal.alertError("请输入具体的坐标地址");
    }
    loadMapAddress($("#latitude").val(),$("#latitude").val())
});

//监听经纬度input框值改变时间
$('#longitude').blur(function () {
    if(!$.common.isEmpty($("#longitude").val())&& !$.common.isEmpty($("#latitude").val())){
        loadMapAddress($("#latitude").val(),$("#longitude").val());
    }
});
$('#latitude').blur(function () {
    if(!$.common.isEmpty($("#longitude").val())&& !$.common.isEmpty($("#latitude").val())){
        loadMapAddress($("#latitude").val(),$("#longitude").val());
    }
});

$('#gpsAddress').blur(function (){
    if(!$.common.isEmpty($("#gpsAddress").val())){
        var province = $("#province option:checked").val();
        var city = $("#city option:checked").val();
        if ($.common.isEmpty(province) ||  $.common.isEmpty(city)) {
            return false;
        }
        addressAjax();
        loadMapAddress($("#latitude").val(),$("#latitude").val())
    }
})

function addressAjax() {
    var province = $("#province option:checked").val();
    var city = $("#city option:checked").val();
    var address =province+city+$('#gpsAddress').val();
    $.ajax({
        url:ctx + "business/area/showProjectAddress",
        type:'post',
        data:{
            city :city,
            address:address
        },
        success:function (data) {
            if (data.code==0){
                console.log(data)
                city =data.data.cityname;
                if (city){
                    map.setCity(city);
                }
                if (data.data.la) {
                    la = Number(data.data.la);
                    lg = Number(data.data.lg);
                    center =[la,lg];
                    //第一次加载
                    if (la){
                        loadMapAddress(lg,la) ;
                    }
                }

            } else{
                $.modal.alertError(data.msg);
                //清空经纬度
                $("#longitude").val("")
                $("#latitude").val("")
            }
        },
        error:function (msg) {
            $.modal.alertError(msg);
        }
    });
}
function  loadMapAddress(la,lg) {
    console.log("la======"+la);
    console.log("lg==="+lg);
    $("#longitude").val(lg);
    $("#latitude").val(la);
    currentla =la;
    currentlg =lg ;
    map.clearMap();
    var marker = new AMap.Marker({
        icon: "https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png",
        position: [currentlg, currentla],
        offset: new AMap.Pixel(-13, -30)
    });
    map.add(marker);
    map.setCenter([currentlg,currentla]);
    //已知点坐标
    var lnglatXY = new AMap.LngLat(currentlg, currentla);
    var MGeocoder;
    //加载地理编码插件
    map.plugin(["AMap.Geocoder"], function() {
        MGeocoder = new AMap.Geocoder({
            radius: 1000,
            extensions: "all"
        });
        //返回地理编码结果
        MGeocoder.on("complete", geocoder_CallBack);
        //逆地理编码
        MGeocoder.getAddress(lnglatXY);
    });
}

//回调函数
function geocoder_CallBack(data) {
    //返回地址描述
    if (data.info == "OK") {
        address = data.regeocode.formattedAddress;
        var infoWindow = new AMap.InfoWindow({
            isCustom: true,  //使用自定义窗体
            content: createInfoWindow(),
            offset: new AMap.Pixel(16, -45)
        });
        infoWindow.open(map, [currentlg,currentla]);
        circleName=null;
        circleId=null ;
        var region = address.match(/.+?(省|市|特别行政区|自治区|自治州|街道|镇|县|区)/g);
        $('#province').val(region[0]);
        $('#city').val(region[1]);
        $('#town').val(region[2]);
        map.setFitView();
    }else{
        map = new AMap.Map("container", {
            resizeEnable: false,
            zoom:11,
        });
        map.setFitView();
    }
    $('#gpsAddress').val(address.split(region[2])[1]);
}
//构建自定义信息窗体
function createInfoWindow() {
    //实例化信息窗体
    var title = '<span style="font-size:11px;color:#F00;">地址</span>',
        content = [];
    content.push(address);
    content.join("<br/>");
    var info = document.createElement("div");
    info.className = "custom-info input-card content-window-card";
    //可以通过下面的方式修改自定义窗体的宽高
    //info.style.width = "400px";
    // 定义顶部标题
    var top = document.createElement("div");
    var titleD = document.createElement("div");
    var closeX = document.createElement("img");
    top.className = "info-top";
    titleD.innerHTML = title;
    closeX.src = "https://webapi.amap.com/images/close2.gif";
    closeX.onclick = closeInfoWindow;

    top.appendChild(titleD);
    top.appendChild(closeX);
    info.appendChild(top);

    // 定义中部内容
    var middle = document.createElement("div");
    middle.className = "info-middle";
    middle.style.backgroundColor = 'white';
    middle.innerHTML = content;
    info.appendChild(middle);

    // 定义底部内容
    var bottom = document.createElement("div");
    bottom.className = "info-bottom";
    bottom.style.position = 'relative';
    bottom.style.top = '0px';
    bottom.style.margin = '0 auto';
    var sharp = document.createElement("img");
    sharp.src = "https://webapi.amap.com/images/sharp.png";
    bottom.appendChild(sharp);
    info.appendChild(bottom);
    return info;
}
//关闭信息窗体
function closeInfoWindow() {
    map.clearInfoWindow();
    map.remove(circle);
}