
function tableScroll(tableid, hei, speed, len) {
    clearTimeout(MyMarhq);
    $('#' + tableid).parent().find('.tableid_').remove()
    $('#' + tableid).parent().prepend(
        '<table class="tableid_"><thead>' + $('#' + tableid + ' thead').html() + '</thead></table>'
    ).css({
        'position': 'relative',
        'overflow': 'hidden',
        'height': hei + 'px'
    })
    $(".tableid_").find('th').each(function(i) {
        $(this).css('width', $('#' + tableid).find('th:eq(' + i + ')').width());
    });
    $(".tableid_").css({
        'position': 'absolute',
        'top': 0,
        'left': 0,
        'z-index': 9
    })
    $('#' + tableid).css({
        'position': 'absolute',
        'top': 0,
        'left': 0,
        'z-index': 1
    })

    if ($('#' + tableid).find('tbody tr').length > len) {
        $('#' + tableid).find('tbody').html($('#' + tableid).find('tbody').html() + $('#' + tableid).find('tbody').html());
        $(".tableid_").css('top', 0);
        $('#' + tableid).css('top', 0);
        var tblTop = 0;
        var outerHeight = $('#' + tableid).find('tbody').find("tr").outerHeight();

        function Marqueehq() {
            if (tblTop <= -outerHeight * $('#' + tableid).find('tbody').find("tr").length) {
                tblTop = 0;
            } else {
                tblTop -= 1;
            }
            $('#' + tableid).css('margin-top', tblTop + 'px');
            clearTimeout(MyMarhq);
            MyMarhq = setTimeout(function() {
                Marqueehq()
            }, speed);
        }

        MyMarhq = setTimeout(Marqueehq, speed);
        $('#' + tableid).find('tbody').hover(function() {
            clearTimeout(MyMarhq);
        }, function() {
            clearTimeout(MyMarhq);
            if ($('#' + tableid).find('tbody tr').length > len) {
                MyMarhq = setTimeout(Marqueehq, speed);
            }
        })
    }

}


function jkChart01() {
    var chartDom = document.getElementById('jk-chart01');
    var myChart = echarts.init(chartDom);
    var v0 = 5;
    var v1 = 2;

    // 指定图表的配置项和数据
    var dataCake=[
        { value: v0 , name: '在线设备' },
        { value: v1, name: '离线设备' }
    ];

    var option;
    option = {
        tooltip: {
            trigger:'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        title: {
            text: '在线设备占比',
            x:'center',
            y:'bottom',
            itemGap: 10,
            textStyle: {
                fontSize: 14,
                fontWeight: 'bolder',
                color: '#333'
            },
            left:'20'
        },

        color:['#31D9AB','#FF9452'],
        legend: {
            orient: 'vertical',
            data: ['在线设备', '离线设备'],
            itemGap: 30,
            right: 20,
            y: 'center',
            itemWidth: 12,  // 设置图例图形的宽
            itemHeight: 12,
            selectedMode: false,
            formatter:function(name){
                let target;
                for(let i=0;i<dataCake.length;i++){
                    if(dataCake[i].name===name){
                        target=dataCake[i].value
                    }
                }
                let arr=[name+':',""+target]
                return arr.join("")
            },
        },

        series: [
            {
                name: '',
                type: 'pie',
                radius: ['50%', '70%'],
                center: ['22%', '40%'],
                avoidLabelOverlap: false,
                label: {
                    normal: {
                        show: false,
                        position: 'center'
                    },
                },
                labelLine: {
                    show: false
                },
                data: [
                    {value: v0, name: '在线设备',label:{
                            normal:{
                                show:true,
                                formatter : function (data){
                                    return  data.percent.toFixed(0) + "%";
                                },
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bolder',
                                },
                            }}
                    },
                    {value: v1, name: '离线设备'}
                ]
            }
        ]
    };

    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }

}


function jkChart02() {
    var chartDom = document.getElementById('jk-chart02');
    var myChart = echarts.init(chartDom);
    var v0 = 0;
    var v1 = 0;



    // 指定图表的配置项和数据
    var dataCake=[
        { value: v0 , name: '在线设备' },
        { value: v1, name: '离线设备' }
    ];

    var option;
    option = {
        tooltip: {
            trigger:'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        title: {
            text: '在线设备占比',
            x:'center',
            y:'bottom',
            itemGap: 10,
            textStyle: {
                fontSize: 14,
                fontWeight: 'bolder',
                color: '#333'
            },
            left:'20'
        },

        color:['#31D9AB','#FF9452'],
        legend: {
            orient: 'vertical',
            data: ['在线设备', '离线设备'],
            itemGap: 30,
            right: 20,
            y: 'center',
            itemWidth: 12,  // 设置图例图形的宽
            itemHeight: 12,
            selectedMode: false,
            formatter:function(name){
                let target;
                for(let i=0;i<dataCake.length;i++){
                    if(dataCake[i].name===name){
                        target=dataCake[i].value
                    }
                }
                let arr=[name+':',""+target]
                return arr.join("")
            },
        },

        series: [
            {
                name: '',
                type: 'pie',
                radius: ['50%', '70%'],
                center: ['22%', '40%'],
                avoidLabelOverlap: false,
                label: {
                    normal: {
                        show: false,
                        position: 'center'
                    },
                },
                labelLine: {
                    show: false
                },
                data: [
                    {value: v0, name: '在线设备',label:{
                            normal:{
                                show:true,
                                formatter : function (data){
                                    return  data.percent.toFixed(0) + "%";
                                },
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bolder',
                                },
                            }}
                    },
                    {value: v1, name: '离线设备'}
                ]
            }
        ]
    };

    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }
}


function jkChart03() {
    var chartDom = document.getElementById('jk-chart03');
    var myChart = echarts.init(chartDom);
    var v0 = 0;
    var v1 = 0;

    var option = {
        title: {
            text: '不明入场车辆趋势图',
            subtext: '单位:辆',
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        color:['#A3EED9'],
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                boundaryGap: false,
                data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
            }
        ],
        yAxis: [
            {
                type: 'value'
            }
        ],
        series: [
            {
                name: '',
                type: 'line',
                smooth:true,
                //设置折线颜色和粗细
                lineStyle: {
                    width: 2,
                    color: "#44E2F0",
                },
                //设置面积区域为渐变效果
                areaStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(
                            0, 0, 0, 1,
                            [
                                { offset: 0, color: 'rgba(53,238,188,1)' },                   //区域渐变色
                                { offset: 1, color: 'rgba(53,238,188,0.00)' },
                            ]
                        ) //改变区域颜色
                    }
                },
                data: [120, 132, 101, 134, 90, 230, 0,0,0,0,0,0]
            },
        ]
    };

    if (option && typeof option === "object"){
        myChart.setOption(option, true);
    }

}


function jkChart04() {
    var chartDom = document.getElementById('jk-chart04');
    var myChart = echarts.init(chartDom);
    var v0 = 0;
    var v1 = 0;

    var option = {
        title: {
            text: '不明入场人员趋势图',
            subtext: '单位:人',
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        color:['#FFA168'],
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                boundaryGap: false,
                data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
            }
        ],
        yAxis: [
            {
                type: 'value'
            }
        ],
        series: [
            {
                name: '',
                type: 'line',
                smooth:true,
                //设置折线颜色和粗细
                lineStyle: {
                    width: 2,
                    color: "#FFA168",
                },
                //设置面积区域为渐变效果
                areaStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(
                            0, 0, 0, 1,
                            [
                                { offset: 0, color: 'rgba(255,161,104,1)' },                   //区域渐变色
                                { offset: 1, color: 'rgba(255,161,104,0.00)' },
                            ]
                        ) //改变区域颜色
                    }
                },
                data: [120, 132, 101, 134, 90, 130, 0,0,0,0,0,0]
            },
        ]
    };

    if (option && typeof option === "object"){
        myChart.setOption(option, true);
    }

}


function jkChart05() {
    var chartDom = document.getElementById('jk-chart05');
    var myChart = echarts.init(chartDom);
    var v0 = 0;
    var v1 = 0;

    var option = {
        title: {
            text: '所有预警事件数量趋势图',
            subtext: '单位:件',
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        color:['#A3EED9'],
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                boundaryGap: false,
                data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
            }
        ],
        yAxis: [
            {
                type: 'value'
            }
        ],
        series: [
            {
                name: '',
                type: 'line',
                smooth:true,
                //设置折线颜色和粗细
                lineStyle: {
                    width: 2,
                    color: "#44E2F0",
                },
                //设置面积区域为渐变效果
                areaStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(
                            0, 0, 0, 1,
                            [
                                { offset: 0, color: 'rgba(53,238,188,1)' },                   //区域渐变色
                                { offset: 1, color: 'rgba(53,238,188,0.00)' },
                            ]
                        ) //改变区域颜色
                    }
                },
                data: [120, 132, 101, 134, 90, 230, 0,0,0,0,0,0]
            },
        ]
    };

    if (option && typeof option === "object"){
        myChart.setOption(option, true);
    }

}
