//驾校选择查询驾校情况
$("#schoolId").on("change", function () {
    var schoolId= $("#schoolId").val();
    console.log(schoolId);
    if ($.common.isNotEmpty(schoolId)){
        var formData = new FormData();
        formData.append("schoolId", schoolId);
        $.ajax({
            url: ctx + "business/sta/selectSchoolData",
            data: formData,
            type: "post",
            processData: false,
            contentType: false,
            success: function(result) {
                if (result.code == web_status.SUCCESS) {
                    if (result.data !=null){
                        $("#branchCount").text(result.data.branchCount);
                        $("#registrationCount").text(result.data.registrationCount);
                        $("#studentCount").text(result.data.studentCount);
                        $("#trainingGroundCount").text(result.data.trainingGroundCount);
                    }else{
                        $.modal.alertWarning("未查询数据")
                    }
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                }  else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            }
        })
    }else{
        var topWindow = $(window.parent.document);
        var currentId = $('.page-tabs-content', topWindow).find('.active').attr('data-id');
        var target = $('.RuoYi_iframe[data-id="' + currentId + '"]', topWindow);
        var url = target.attr('src');
        target.attr('src', url).ready();
    }


});


// laydate time-input 时间控件绑定
if ($(".sta-month-input").length > 0) {
    layui.use('laydate', function () {
        var com = layui.laydate;
        $(".sta-month-input").each(function (index, item) {
            var time = $(item);
            // 控制回显格式
            var format = time.attr("data-format") || 'yyyy-MM-dd';
            // 控制日期控件按钮
            var buttons = time.attr("data-btn") || 'clear|now|confirm', newBtnArr = [];
            // 日期控件选择完成后回调处理
            var callback = time.attr("data-callback") || {};
            if (buttons) {
                if (buttons.indexOf("|") > 0) {
                    var btnArr = buttons.split("|"), btnLen = btnArr.length;
                    for (var j = 0; j < btnLen; j++) {
                        if ("clear" === btnArr[j] || "now" === btnArr[j] || "confirm" === btnArr[j]) {
                            newBtnArr.push(btnArr[j]);
                        }
                    }
                } else {
                    if ("clear" === buttons || "now" === buttons || "confirm" === buttons) {
                        newBtnArr.push(buttons);
                    }
                }
            } else {
                newBtnArr = ['clear', 'now', 'confirm'];
            }
            com.render({
                elem: item,
                theme: 'molv',
                trigger: 'click',
                type: 'month',
                format: format,
                btns: newBtnArr,
                done: function (value, data) {
                    if (typeof window[callback] != 'undefined'
                        && window[callback] instanceof Function) {
                        window[callback](value, data);
                    }
                    var formData = new FormData();
                    formData.append("month", value);
                    $.ajax({
                        url: ctx + "business/sta/selectArticleMonthData",
                        data: formData,
                        type: "post",
                        processData: false,
                        contentType: false,
                        success: function(result) {
                            if (result.code == web_status.SUCCESS) {
                                if (result.data !=null){
                                    $("#articleNum").text(result.data.articleCheckedCount + result.data.articelUncheckedCount);
                                    $("#articleCheckedCount").text(result.data.articleCheckedCount);
                                    $("#articelUncheckedCount").text(result.data.articelUncheckedCount);

                                    $("#messageNum").text(result.data.messageHandledCount + result.data.messageUnhandledCount);
                                    $("#messageHandledCount").text(result.data.messageHandledCount);
                                    $("#messageUnhandledCount").text(result.data.messageUnhandledCount);

                                    $("#miniappNum").text(result.data.miniappStudentVisitCount + result.data.miniappArticelVisitCount);
                                    $("#miniappStudentVisitCount").text(result.data.miniappStudentVisitCount);
                                    $("#miniappArticelVisitCount").text(result.data.miniappArticelVisitCount);
                                    jgChart07();
                                    jgChart08();
                                    jgChart09();

                                }else{
                                    $.modal.alertWarning("未查询数据")
                                }

                            } else if (result.code == web_status.WARNING) {
                                $.modal.alertWarning(result.msg)
                            }  else {
                                $.modal.alertError(result.msg);
                            }
                            $.modal.closeLoading();
                        }
                    })

                }
            });
        });
    });
}


function jgChart01() {
    var chartDom = document.getElementById('jg-chart01');
    var myChart = echarts.init(chartDom);
    var option;

    var superviseStudentCount=$("#superviseStudentCount").text();
    var noSuperviseStudentCount=$("#noSuperviseStudentCount").val();

    var v0 = 0;
    var v1 = 0;
    if (superviseStudentCount != null){
        v0= superviseStudentCount;
    }
    if (noSuperviseStudentCount != null){
        v1= noSuperviseStudentCount;
    }

    var dataCake=[
        { value: v0 , name: '受资金监管人' },
        { value: v1, name: '未受资金监管人' }
    ];

    if ($.common.isNotEmpty(superviseStudentCount)  && $.common.isNotEmpty(noSuperviseStudentCount)) {
        option = {
            backgroundColor:'rgba(240,242,245)',
            tooltip: {
                trigger: 'item',
                formatter: "{b}: {c}",
                position:function(p){   //其中p为当前鼠标的位置
                    return [p[0] + 10, p[1] - 10];
                }
            },
            legend: {
                orient: 'vertical',
                y:'bottom',
                left: '50%',
                itemHeight: 12,
                itemWidth: 12,
                selectedMode: false,
                formatter:function(name){
                    let target;
                    for(let i=0;i<dataCake.length;i++){
                        if(dataCake[i].name===name){
                            target=dataCake[i].value
                        }
                    }
                    let arr=[name+':',""+target]
                    return arr.join("")
                }
            },
            series: [
                {
                    type: 'pie',
                    radius: ['55%', '80%'],
                    center: ['25%','50%'],
                    avoidLabelOverlap: false,
                    label: {
                        normal: {
                            show: true,
                            position: 'center',
                            color: '#333',
                            fontSize: 12,
                            fontWeight: 'bold',
                            formatter : function (data){
                                return  data. percent . toFixed ( 0 ) + "%";
                            },
                        }
                    },
                    data: [{
                        value: v0,
                        name: '受资金监管人',
                        itemStyle : {
                            normal: {
                                color: '#126AFC',
                                label: {
                                    normal: {show: true}
                                }
                            }
                        }
                    },
                        {
                            value: v1,
                            name: '未受资金监管人',
                            itemStyle : {
                                normal: {
                                    color: '#FF9452',
                                }
                            },
                            label: { normal: { show: false}}
                        }]
                }
            ]
        };
    }else{
        option = {
            title: {
                show: true,
                textStyle:{
                    fontSize:16
                },
                text: "无数据",
                left: 'center',
                top: 'center'
            },
            backgroundColor:'rgba(240,242,245)',
            xAxis: {
                show: false
            },
            yAxis: {
                show: false
            },
            series: []
        };
    }

    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }
}





function jgChart02() {
    var chartDom2 = document.getElementById('jg-chart02');
    var myChart2 = echarts.init(chartDom2);
    var option2;

    var superviseSchoolCount=$("#superviseSchoolCount").val();
    var noSuperviseSchoolCount=$("#noSuperviseSchoolCount").val();

    var v0 = 0;
    var v1 = 0;
    if (superviseSchoolCount != null){
        v0= superviseSchoolCount;
    }
    if (noSuperviseSchoolCount != null){
        v1= noSuperviseSchoolCount;
    }

    var dataCake=[
        { value: v0, name: '受监管驾校'},
        { value: v1, name: '未受监管驾校'}
    ];

    if ($.common.isNotEmpty(superviseSchoolCount)  && $.common.isNotEmpty(noSuperviseSchoolCount)) {
        option2 = {
            backgroundColor:'rgba(240,242,245)',
            tooltip: {
                trigger: 'item',
                formatter: "{b}: {c}",
                position:function(p){   //其中p为当前鼠标的位置
                    return [p[0] + 10, p[1] - 10];
                }
            },
            legend: {
                orient: 'vertical',
                y:'bottom',
                left: '50%',
                itemHeight: 12,
                itemWidth: 12,
                selectedMode: false,
                formatter:function(name){
                    let target;
                    for(let i=0;i<dataCake.length;i++){
                        if(dataCake[i].name===name){
                            target=dataCake[i].value
                        }
                    }
                    let arr=[name+':',""+target]
                    return arr.join("")
                }
            },
            series: [
                {
                    type: 'pie',
                    radius: ['55%', '80%'],
                    center: ['25%','55%'],
                    avoidLabelOverlap: false,
                    label: {
                        normal: {
                            show: true,
                            position: 'center',
                            color: '#333',
                            fontSize: 12,
                            fontWeight: 'bold',
                            formatter : function (data){
                                return  data.percent.toFixed (0) + "%";
                            },
                        }
                    },
                    data: [{
                        value: v0,
                        name: '受监管驾校',
                        itemStyle : {
                            normal: {
                                color: '#8878FF',
                                label: {
                                    normal: {show: true}
                                }
                            }
                        }
                    },
                        {
                            value: v1,
                            name: '未受监管驾校',
                            itemStyle : {
                                normal: {
                                    color: '#FF9452',
                                }
                            },
                            label: { normal: { show: false}}
                        }]
                }
            ]
        };
    }else{
        option2 = {
            title: {
                show: true,
                textStyle:{
                    fontSize:16
                },
                text: "无数据",
                left: 'center',
                top: 'center'
            },
            backgroundColor:'rgba(240,242,245)',
            xAxis: {
                show: false
            },
            yAxis: {
                show: false
            },
            series: []
        };
    }

    if (option2 && typeof option2 === "object") {
        myChart2.setOption(option2, true);
    }
}


function jgChart03() {
    var chartDom3 = document.getElementById('jg-chart03');
    var myChart3 = echarts.init(chartDom3);
    var option3;
    var v0 = 0;
    var v1 = 0;
    var simSuccessCount= $("#simSuccessCount").val();
    var simFailCount = $("#simFailCount").val();

    if ($.common.isNotEmpty(simSuccessCount)){
        v0=simSuccessCount;
    }
    if ($.common.isNotEmpty(simFailCount)){
        v1=simFailCount;
    }

    var dataCake=[
        { value: v0 , name: '成功' },
        { value: v1, name: '失败' }
    ];

    option3 = {
        legend: {
            orient: 'vertical',
            y:'bottom',
            left: '55%',
            itemHeight: 8,
            itemWidth: 8,
            formatter:function(name){
                let target;
                for(let i=0;i<dataCake.length;i++){
                    if(dataCake[i].name===name){
                        target=dataCake[i].value
                    }
                }
                let arr=[name+':',""+target]
                return arr.join("")
            }
        },
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                center: ['25%','45%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [

                    {
                        value: v0,
                        name: '成功',
                        itemStyle : {
                            normal: {
                                color: '#FF9452',
                                label: { normal: { show: true}}
                            }
                        },

                    },

                    {
                        value: v1,
                        name: '失败',
                        itemStyle : {
                            normal: {
                                color: '#DEDEDE',
                            }
                        },
                        label: {
                            normal: {show: false}
                        }
                    }

                    ]
            }
        ]
    };

    if (option3 && typeof option3 === "object") {
        myChart3.setOption(option3, true);
    }
}


function jgChart04() {
    var chartDom4 = document.getElementById('jg-chart04');
    var myChart4 = echarts.init(chartDom4);
    var option4;
    var v0 = 0;
    var v1 = 0;
    var simAllowanceSendedCount= $("#simAllowanceSendedCount").val();
    var simAllowanceUnsendCount = $("#simAllowanceUnsendCount").val();

    if ($.common.isNotEmpty(simAllowanceSendedCount)){
        v0=simAllowanceSendedCount;
    }
    if ($.common.isNotEmpty(simAllowanceUnsendCount)){
        v1=simAllowanceUnsendCount;
    }

    var dataCake=[
        { value: v0 , name: '成功' },
        { value: v1, name: '失败' }
    ];

    option4 = {
        legend: {
            orient: 'vertical',
            y:'bottom',
            left: '55%',
            itemHeight: 8,
            itemWidth: 8,
            formatter:function(name){
                let target;
                for(let i=0;i<dataCake.length;i++){
                    if(dataCake[i].name===name){
                        target=dataCake[i].value
                    }
                }
                let arr=[name+':',""+target]
                return arr.join("")
            }
        },
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                center: ['25%','45%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [

                    {
                        value: v0,
                        name: '成功',
                        itemStyle : {
                            normal: {
                                color: '#FF9452',
                                label: { normal: { show: true}}
                            }
                        },

                    },

                    {
                        value: v1,
                        name: '失败',
                        itemStyle : {
                            normal: {
                                color: '#DEDEDE',
                            }
                        },
                        label: {
                            normal: {show: false}
                        }
                    }

                ]
            }
        ]
    };

    if (option4 && typeof option4 === "object") {
        myChart4.setOption(option4, true);
    }
}



function jgChart05() {
    var chartDom5 = document.getElementById('jg-chart05');
    var myChart5 = echarts.init(chartDom5);
    var option5;
    var v0 = 0;
    var v1 = 0;
    var simNoneStopCount= $("#simNoneStopCount").val();
    var simNoneDoingCount = $("#simNoneDoingCount").val();

    if ($.common.isNotEmpty(simNoneStopCount)){
        v0=simNoneStopCount;
    }
    if ($.common.isNotEmpty(simNoneDoingCount)){
        v1=simNoneDoingCount;
    }

    var dataCake=[
        { value: v0 , name: '成功' },
        { value: v1, name: '失败' }
    ];

    option5 = {
        legend: {
            orient: 'vertical',
            y:'bottom',
            left: '55%',
            itemHeight: 8,
            itemWidth: 8,
            formatter:function(name){
                let target;
                for(let i=0;i<dataCake.length;i++){
                    if(dataCake[i].name===name){
                        target=dataCake[i].value
                    }
                }
                let arr=[name+':',""+target]
                return arr.join("")
            }
        },
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                center: ['25%','45%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [

                    {
                        value: v0,
                        name: '成功',
                        itemStyle : {
                            normal: {
                                color: '#FF9452',
                                label: { normal: { show: true}}
                            }
                        },

                    },

                    {
                        value: v1,
                        name: '失败',
                        itemStyle : {
                            normal: {
                                color: '#DEDEDE',
                            }
                        },
                        label: {
                            normal: {show: false}
                        }
                    }

                ]
            }
        ]
    };

    if (option5 && typeof option5 === "object") {
        myChart5.setOption(option5, true);
    }
}


function jgChart06() {
    var chartDom = document.getElementById('jg-chart06');
    var myChart = echarts.init(chartDom);
    var option;
    var v0 = 0;
    var v1 = 0;
    var simCalledCount= $("#simCalledCount").val();
    var simUncallCount = $("#simUncallCount").val();

    if ($.common.isNotEmpty(simCalledCount)){
        v0=simCalledCount;
    }
    if ($.common.isNotEmpty(simUncallCount)){
        v1=simUncallCount;
    }

    var dataCake=[
        { value: v0 , name: '成功' },
        { value: v1, name: '失败' }
    ];

    option = {
        legend: {
            orient: 'vertical',
            y:'bottom',
            left: '55%',
            itemHeight: 8,
            itemWidth: 8,
            formatter:function(name){
                let target;
                for(let i=0;i<dataCake.length;i++){
                    if(dataCake[i].name===name){
                        target=dataCake[i].value
                    }
                }
                let arr=[name+':',""+target]
                return arr.join("")
            }
        },
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                center: ['25%','45%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [

                    {
                        value: v0,
                        name: '成功',
                        itemStyle : {
                            normal: {
                                color: '#FF9452',
                                label: { normal: { show: true}}
                            }
                        },

                    },

                    {
                        value: v1,
                        name: '失败',
                        itemStyle : {
                            normal: {
                                color: '#DEDEDE',
                            }
                        },
                        label: {
                            normal: {show: false}
                        }
                    }

                ]
            }
        ]
    };

    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }
}




function jgChart07() {
    var chartDom7 = document.getElementById('jg-chart07');
    var myChart7 = echarts.init(chartDom7);
    var option7;
    var v0 = 0;
    var v1 = 0;
    var articleCheckedCount= $("#articleCheckedCount").text();
    var articelUncheckedCount = $("#articelUncheckedCount").text();

    if ($.common.isNotEmpty(articleCheckedCount)){
        v0=articleCheckedCount;
    }
    if ($.common.isNotEmpty(articelUncheckedCount)){
        v1=articelUncheckedCount;
    }

    option7 = {
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [

                    {
                        value: v0,
                        name: '已审核',
                        itemStyle : {
                            normal: {
                                color: '#2A82E4',
                                label: { normal: { show: true}}
                            }
                        },

                    },

                    {
                        value: v1,
                        name: '未审核',
                        itemStyle : {
                            normal: {
                                color: '#DEDEDE',
                            }
                        },
                        label: {
                            normal: {show: false}
                        }
                    }

                ]
            }
        ]
    };

    if (option7 && typeof option7 === "object") {
        myChart7.setOption(option7, true);
    }
}


function jgChart08() {
    var chartDom = document.getElementById('jg-chart08');
    var myChart = echarts.init(chartDom);
    var option;
    var v0 = 0;
    var v1 = 0;
    var messageHandledCount= $("#messageHandledCount").text();
    var messageUnhandledCount = $("#messageUnhandledCount").text();

    if ($.common.isNotEmpty(messageHandledCount)){
        v0=messageHandledCount;
    }
    if ($.common.isNotEmpty(messageUnhandledCount)){
        v1=messageUnhandledCount;
    }

    option = {
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [

                    {
                        value: v0,
                        name: '已处理',
                        itemStyle : {
                            normal: {
                                color: '#2A82E4',
                                label: { normal: { show: true}}
                            }
                        },

                    },

                    {
                        value: v1,
                        name: '未处理',
                        itemStyle : {
                            normal: {
                                color: '#DEDEDE',
                            }
                        },
                        label: {
                            normal: {show: false}
                        }
                    }

                ]
            }
        ]
    };

    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }
}


function jgChart09() {
    var chartDom = document.getElementById('jg-chart09');
    var myChart = echarts.init(chartDom);
    var option;
    var v0 = 0;
    var v1 = 0;
    var miniappStudentVisitCount= $("#miniappStudentVisitCount").text();
    var miniappArticelVisitCount = $("#miniappArticelVisitCount").text();

    if ($.common.isNotEmpty(miniappStudentVisitCount)){
        v0=miniappStudentVisitCount;
    }
    if ($.common.isNotEmpty(miniappArticelVisitCount)){
        v1=miniappArticelVisitCount;
    }

    option = {
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [

                    {
                        value: v0,
                        name: '学员访问',
                        itemStyle : {
                            normal: {
                                color: '#2A82E4',
                                label: { normal: { show: true}}
                            }
                        },

                    },

                    {
                        value: v1,
                        name: '资讯访问',
                        itemStyle : {
                            normal: {
                                color: '#DEDEDE',
                            }
                        },
                        label: {
                            normal: {show: false}
                        }
                    }

                ]
            }
        ]
    };

    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }
}


function zjqkTjChart() {
    var chartDom = document.getElementById('zjqk-tj-chart');
    var myChart = echarts.init(chartDom);
    var v0 = 0;
    var v1 = 0;

    // 指定图表的配置项和数据
    var option;
    var datas = [
        [
            { name: '银行卡限额',value: 80 },
            { name: '个人身份证过期', value: 20},
            { name: '银行卡冻结', value: 20 },
            { name:'其他',value:5}
        ]
    ];
    var a = 0;
    option = {
        legend: {
            orient: 'vertical',
            left: '65%',
            itemHeight: 8,
            itemWidth: 8,
            data: [
                {
                    name: '银行卡限额',
                    icon: 'circle',
                },
                {
                    name: '个人身份证过期',
                    icon: 'circle',
                },
                {
                    name: '银行卡冻结',
                    icon: 'circle',
                },
                {
                    name: '其他',
                    icon: 'circle',
                }
            ]
        },

        graphic: {
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
                text:
                    '总数' +
                    '\n\n' +
                    String(
                        datas.map((item) => {
                            item.forEach((e) => {
                                a += e.value;
                            });
                            a = a.toFixed(1);
                            return a;
                        })
                    ).replace(/(\d)(?=(?:\d{6})+$)/g, '$1.'),
                textAlign: 'center',
                fill: '#333',
                fontSize: 10
            }
        },
        series: datas.map(function (data, idx) {
            var top = idx * 33.3;
            return {
                type: 'pie',
                radius: [30, 40],
                top: '32%',
                height: '50.33%',
                left: 'bottom',
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 1
                },
                color: ["#FF9452","#FF5733","#2A82E4","#31D9AB"],
                label: {
                    alignTo: 'edge',
                    formatter: '{name|{b}}\n{time|{c}}',
                    minMargin: 5,
                    edgeDistance: 10,
                    lineHeight: 15,
                    rich: {
                        time: {
                            fontSize: 10,
                            color: '#999'
                        }
                    }
                },
                labelLayout: function (params) {
                    const isLeft = params.labelRect.x < myChart.getWidth() / 2;
                    const points = params.labelLinePoints;
                    points[2][0] = isLeft
                        ? params.labelRect.x
                        : params.labelRect.x + params.labelRect.width;
                    return {
                        labelLinePoints: points
                    };
                },
                data: data
            };
        })
    };
    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }
}


function blTjChart() {
    var chartDom = document.getElementById('bl-tj-chart');
    var myChart = echarts.init(chartDom);
    var v0 = 0;
    var v1 = 0;

    // 指定图表的配置项和数据
    var option;
    var datas = [
        [
            { value: 80, name: '学员问题' },
            { value: 20, name: '快递问题' },
            { value: 20, name: '系统问题' },
        ]
    ];
    var a = 0;
    option = {
        legend: {
            orient: 'vertical',
            left: '70%',
            itemHeight: 8,
            itemWidth: 8,
            data: [
                {
                    name: '学员问题',
                    icon: 'circle',
                },
                {
                    name: '快递问题',
                    icon: 'circle',
                },
                {
                    name: '系统问题',
                    icon: 'circle',
                }
            ]
        },

        graphic: {
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
                text:
                    '总数' +
                    '\n\n' +
                    String(
                        datas.map((item) => {
                            item.forEach((e) => {
                                a += e.value;
                            });
                            a = a.toFixed(1);
                            return a;
                        })
                    ).replace(/(\d)(?=(?:\d{6})+$)/g, '$1.'),
                textAlign: 'center',
                fill: '#333',
                fontSize: 10
            }
        },
        series: datas.map(function (data, idx) {
            var top = idx * 33.3;
            return {
                type: 'pie',
                radius: [30, 40],
                top: '32%',
                height: '50.33%',
                left: 'bottom',
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 1
                },
                color: ["#FF9452","#FF5733","#2A82E4"],
                label: {
                    alignTo: 'edge',
                    formatter: '{name|{b}}\n{time|{c}}',
                    minMargin: 5,
                    edgeDistance: 10,
                    lineHeight: 15,
                    rich: {
                        time: {
                            fontSize: 10,
                            color: '#999'
                        }
                    }
                },
                labelLayout: function (params) {
                    const isLeft = params.labelRect.x < myChart.getWidth() / 2;
                    const points = params.labelLinePoints;
                    points[2][0] = isLeft
                        ? params.labelRect.x
                        : params.labelRect.x + params.labelRect.width;
                    return {
                        labelLinePoints: points
                    };
                },
                data: data
            };
        })
    };
    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }
}


function djChart01() {
    var chartDom = document.getElementById('dj-chart01');
    var myChart = echarts.init(chartDom);
    var v0 = 0;
    var v1 = 0;

    var schoolTotal=$("#schoolTotal").text();
    var schoolCount=$("#schoolCount").val();

    if ($.common.isNotEmpty(schoolCount)){
        v0=schoolCount;
        if ($.common.isNotEmpty(schoolTotal)) {
            v1=schoolTotal -schoolCount;
        }
    }


    // 指定图表的配置项和数据
    var dataCake=[
        { value: v0 , name: '已对接' },
        { value: v1, name: '未对接' }
    ];

    var option;
    option = {
        tooltip: {
            trigger:'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        title: {
            text: '已对接占比',
            x:'center',
            y:'bottom',
            itemGap: 10,
            textStyle: {
                fontSize: 14,
                fontWeight: 'bolder',
                color: '#333'
            },
            top:'85',
            left:'20'
        },

        color:['#FF9452','#31D9AB'],
        legend: {
            orient: 'vertical',
            data: ['已对接', '未对接'],
            itemGap: 30,
            right: 100,
            y: 'center',
            itemWidth: 12,  // 设置图例图形的宽
            itemHeight: 12,
            selectedMode: false,
            formatter:function(name){
                let target;
                for(let i=0;i<dataCake.length;i++){
                    if(dataCake[i].name===name){
                        target=dataCake[i].value
                    }
                }
                let arr=[name+':',""+target]
                return arr.join("")
            },
        },

        series: [
            {
                name: '',
                type: 'pie',
                radius: ['50%', '70%'],
                center: ['22%', '40%'],
                avoidLabelOverlap: false,
                label: {
                    normal: {
                        show: false,
                        position: 'center'
                    },
                },
                labelLine: {
                    show: false
                },
                data: [
                    {value: v0, name: '已对接',label:{
                            normal:{
                                show:true,
                                formatter : function (data){
                                    return  data.percent.toFixed(0) + "%";
                                },
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bolder',
                                },
                            }}
                     },

                    {value: v1, name: '未对接'}
                ]
            }
        ]
    };

    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }

}