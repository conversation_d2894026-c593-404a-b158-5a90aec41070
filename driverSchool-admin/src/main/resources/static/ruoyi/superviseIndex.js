


function jgChart01() {
    var chartDom = document.getElementById('jg-chart01');
    var myChart = echarts.init(chartDom);
    var option;
    var v0 = 0;
    var v1 = 0;

    var superviseSchoolCount = $("#superviseSchoolCount").val();
    var noSuperviseSchoolCount= $("#noSuperviseSchoolCount").val();

    if ($.common.isNotEmpty(superviseSchoolCount)){
        v0=superviseSchoolCount;
    }

    if ($.common.isNotEmpty(noSuperviseSchoolCount)){
        v1=noSuperviseSchoolCount;
    }

    // 指定图表的配置项和数据
    var dataCake=[
        { value: v0 , name: '受监管驾校' },
        { value: v1, name: '未受监管驾校' }
    ];


    if ($.common.isNotEmpty(superviseStudentCount)  && $.common.isNotEmpty(noSuperviseStudentCount)) {

    }else{
        option = {
            title: {
                show: true,
                textStyle:{
                    fontSize:16
                },
                text: "无数据",
                left: 'center',
                top: 'center'
            },
            backgroundColor:'rgba(240,242,245)',
            xAxis: {
                show: false
            },
            yAxis: {
                show: false
            },
            series: []
        };
    }

    option = {
        tooltip: {
            trigger:'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        title: {
            text: '受监管驾校占比',
            x:'center',
            y:'bottom',
            itemGap: 10,
            textStyle: {
                fontSize: 12,
                color: '#333'
            },
            top:'85',
            left:'30'
        },

        color:['#FF9452','#31D9AB'],
        legend: {
            orient: 'vertical',
            data: ['受监管驾校', '未受监管驾校'],
            itemGap: 30,
            right: 5,
            y: 'center',
            itemWidth: 12,  // 设置图例图形的宽
            itemHeight: 12,
            selectedMode: false,
            formatter:function(name){
                let target;
                for(let i=0;i<dataCake.length;i++){
                    if(dataCake[i].name===name){
                        target=dataCake[i].value
                    }
                }
                let arr=[name+':',""+target]
                return arr.join("")
            },
        },

        series: [
            {
                name: '',
                type: 'pie',
                radius: ['50%', '70%'],
                center: ['30%', '40%'],
                avoidLabelOverlap: false,
                label: {
                    normal: {
                        show: false,
                        position: 'center'
                    },
                },
                labelLine: {
                    show: false
                },
                data: [
                    {value: v0, name: '受监管驾校',label:{
                            normal:{
                                show:true,
                                formatter: '{d}%',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bolder',
                                },
                            }}
                    },
                    {value: v1, name: '未受监管驾校'}
                ]
            }
        ]
    };



    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }
}
function jgChart02() {
    var chartDom2 = document.getElementById('jg-chart02');
    var myChart2 = echarts.init(chartDom2);
    var option2;
    var v0 = 0;
    var v1 = 0;
    var studentCount= $("#studentCount").text();
    var superviseStudentCount = $("#superviseStudentCount").text();


    if ($.common.isNotEmpty(studentCount)){
        v0=studentCount;
    }

    if ($.common.isNotEmpty(superviseStudentCount)){
        v1=superviseStudentCount;
    }

    option2 = {
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [{
                    value: v0-v1,
                    name: '',
                    itemStyle : {
                        normal: {
                            color: '#DEDEDE',
                        }
                    },
                    label: {
                        normal: {show: false}
                    }
                },
                    {
                        value: v1,
                        name: '受监管驾校',
                        itemStyle : {
                            normal: {
                                color: '#FF9452',
                                label: { normal: { show: true}}
                            }
                        },

                    }]
            }
        ]
    };

    if (option2 && typeof option2 === "object") {
        myChart2.setOption(option2, true);
    }
}
function jgChart03() {
    var chartDom3 = document.getElementById('jg-chart03');
    var myChart3 = echarts.init(chartDom3);
    var option3;
    var v0 = 0;
    var v1 = 0;
    var studentCount= $("#studentCount").text();
    var noSuperviseStudentCount = $("#noSuperviseStudentCount ").text();
    if ($.common.isNotEmpty(studentCount)){
        v0=studentCount;
    }

    if ($.common.isNotEmpty(noSuperviseStudentCount)){
        v1=noSuperviseStudentCount;
    }

    option3 = {
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [{
                    value: v0-v1,
                    name: '',
                    itemStyle : {
                        normal: {
                            color: '#DEDEDE',
                        }
                    },
                    label: {
                        normal: {show: false}
                    }
                },
                    {
                        value: v1,
                        name: '未受资金监管人数',
                        itemStyle : {
                            normal: {
                                color: '#7948EA',
                                label: { normal: { show: true}}
                            }
                        },

                    }]
            }
        ]
    };

    if (option3 && typeof option3 === "object") {
        myChart3.setOption(option3, true);
    }
}

function jgChart04() {
    var chartDom4 = document.getElementById('jg-chart04');
    var myChart4 = echarts.init(chartDom4);
    var option4;
    var v0 = 0;
    var v1 = 0;
    var studentCount= $("#studentCount").text();
    var finishStudentCount = $("#finishStudentCount").text();
    if ($.common.isNotEmpty(studentCount)){
        v0=studentCount;
    }

    if ($.common.isNotEmpty(finishStudentCount)){
        v1=finishStudentCount;
    }

    option4 = {
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [{
                    value: v0-v1,
                    name: '',
                    itemStyle : {
                        normal: {
                            color: '#DEDEDE',
                        }
                    },
                    label: {
                        normal: {show: false}
                    }
                },
                    {
                        value: v1,
                        name: '结业人数',
                        itemStyle : {
                            normal: {
                                color: '#2A82E4',
                                label: { normal: { show: true}}
                            }
                        },

                    }]
            }
        ]
    };

    if (option4 && typeof option4 === "object") {
        myChart4.setOption(option4, true);
    }
}

function jgChart05() {
    var chartDom5 = document.getElementById('jg-chart05');
    var myChart5 = echarts.init(chartDom5);
    var option5;
    var v0 = 0;
    var v1 = 0;
    var studentCount= $("#studentCount").text();
    var quitStudentCount = $("#quitStudentCount").text();
    if ($.common.isNotEmpty(studentCount)){
        v0=studentCount;
    }

    if ($.common.isNotEmpty(quitStudentCount)){
        v1=quitStudentCount;
    }

    option5 = {
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [{
                    value: v0-v1,
                    name: '',
                    itemStyle : {
                        normal: {
                            color: '#DEDEDE',
                        }
                    },
                    label: {
                        normal: {show: false}
                    }
                },
                    {
                        value: v1,
                        name: '退学人数',
                        itemStyle : {
                            normal: {
                                color: '#FF6568',
                                label: { normal: { show: true}}
                            }
                        },

                    }]
            }
        ]
    };

    if (option5 && typeof option5 === "object") {
        myChart5.setOption(option5, true);
    }
}

function jgChart06() {
    var chartDom6 = document.getElementById('jg-chart06');
    var myChart6 = echarts.init(chartDom6);
    var option6;
    var v0 = 0;
    var v1 = 0;
    var studentCount= $("#studentCount").text();
    var subject1StudentCount = $("#subject1StudentCount").text();
    if ($.common.isNotEmpty(studentCount)){
        v0=studentCount;
    }

    if ($.common.isNotEmpty(subject1StudentCount)){
        v1=subject1StudentCount;
    }

    option6 = {
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [{
                    value: v0-v1,
                    name: '',
                    itemStyle : {
                        normal: {
                            color: '#DEDEDE',
                        }
                    },
                    label: {
                        normal: {show: false}
                    }
                },
                    {
                        value: v1,
                        name: '退学人数',
                        itemStyle : {
                            normal: {
                                color: '#6CE1C2',
                                label: { normal: { show: true}}
                            }
                        },

                    }]
            }
        ]
    };

    if (option6 && typeof option6 === "object") {
        myChart6.setOption(option6, true);
    }

}


function jgChart07() {
    var chartDom7 = document.getElementById('jg-chart07');
    var myChart7 = echarts.init(chartDom7);
    var option7;
    var v0 = 0;
    var v1 = 0;
    var studentCount= $("#studentCount").text();
    var subject2StudentCount = $("#subject2StudentCount").text();
    if ($.common.isNotEmpty(studentCount)){
        v0=studentCount;
    }

    if ($.common.isNotEmpty(subject2StudentCount)){
        v1=subject2StudentCount;
    }

    option7 = {
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [{
                    value: v0-v1,
                    name: '',
                    itemStyle : {
                        normal: {
                            color: '#DEDEDE',
                        }
                    },
                    label: {
                        normal: {show: false}
                    }
                },
                    {
                        value: v1,
                        name: '退学人数',
                        itemStyle : {
                            normal: {
                                color: '#6CE1C2',
                                label: { normal: { show: true}}
                            }
                        },

                    }]
            }
        ]
    };

    if (option7 && typeof option7 === "object") {
        myChart7.setOption(option7, true);
    }

}


function jgChart08() {
    var chartDom8 = document.getElementById('jg-chart08');
    var myChart8 = echarts.init(chartDom8);
    var option8;
    var v0 = 0;
    var v1 = 0;
    var studentCount= $("#studentCount").text();
    var subject3StudentCount = $("#subject3StudentCount").text();
    if ($.common.isNotEmpty(studentCount)){
        v0=studentCount;
    }

    if ($.common.isNotEmpty(subject3StudentCount)){
        v1=subject3StudentCount;
    }
    option8 = {
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [{
                    value: v0-v1,
                    name: '',
                    itemStyle : {
                        normal: {
                            color: '#DEDEDE',
                        }
                    },
                    label: {
                        normal: {show: false}
                    }
                },
                    {
                        value: v1,
                        name: '退学人数',
                        itemStyle : {
                            normal: {
                                color: '#6CE1C2',
                                label: { normal: { show: true}}
                            }
                        },

                    }]
            }
        ]
    };

    if (option8 && typeof option8 === "object") {
        myChart8.setOption(option8, true);
    }
}


function jgChart09() {
    var chartDom9 = document.getElementById('jg-chart09');
    var myChart9 = echarts.init(chartDom9);
    var option9;
    var v0 = 0;
    var v1 = 0;
    var studentCount= $("#studentCount").text();
    var subject4StudentCount = $("#subject4StudentCount").text();
    if ($.common.isNotEmpty(studentCount)){
        v0=studentCount;
    }

    if ($.common.isNotEmpty(subject4StudentCount)){
        v1=subject4StudentCount;
    }
    option9 = {
        series: [
            {
                type: 'pie',
                hoverAnimation: false,
                radius: ['70%', '90%'],
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        color: '#333',
                        fontSize: 12,
                        fontWeight: 'bold',
                        formatter : function (data){
                            return  data. percent . toFixed ( 0 ) + "%";
                        },
                    }
                },
                data: [{
                    value: v0-v1,
                    name: '',
                    itemStyle : {
                        normal: {
                            color: '#DEDEDE',
                        }
                    },
                    label: {
                        normal: {show: false}
                    }
                },
                    {
                        value: v1,
                        name: '退学人数',
                        itemStyle : {
                            normal: {
                                color: '#6CE1C2',
                                label: { normal: { show: true}}
                            }
                        },

                    }]
            }
        ]
    };

    if (option9 && typeof option9 === "object") {
        myChart9.setOption(option9, true);
    }
}