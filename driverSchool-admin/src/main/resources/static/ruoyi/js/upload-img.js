// 用于记录选中的文件
var fileMap = new Map();

// 添加图片
function addImage() {
  if (maxImage && $(".image-box > .image-item").length >= maxImage) {
    $.modal.msgError("最多选择" + maxImage + "张照片");
    return false;
  }
  $("#add-input").val("");
  $("#add-input").click();
  return true;
}

// 选中图片
function selectImage(input) {
  var file = input.files[0];
  if (file.size / 1024 > 2096) {
    $.modal.msgWarning("图片不能大于2M");
    return false;
  }
  if (file) {
    fileMap.set(file.name, file);
    // 读取并显示
    var reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function (e) {
      var imageItem =
        '<div class="image-item" style="background-image: url(\'' +
        this.result +
        "');\">";
      imageItem +=
        '<img class="delete-img" src="/img/delete.png" onclick="deleteImage(this, \'' +
        file.name +
        "')\">";
      imageItem += "</div>";
      $(".image-box").append($(imageItem));
    };
  }
  return true;
}

// 删除图片
function deleteImage(obj, fileName) {
  if (fileName) fileMap.delete(fileName);
  $(obj).parent(".image-item").remove();
}

// 提交事件
function submit(suffix) {
  if ($.validate.form()) {
    var formData = new FormData($("form")[0]);
    for (var value of fileMap.values()) {
      formData.append("images", value);
    }
    var config = {
      url: prefix + suffix,
      type: "post",
      dataType: "json",
      // cache: false,
      contentType: false,
      processData: false,
      data: formData,
      beforeSend: function () {
        $.modal.loading("正在保存中，请稍后...");
      },
      success: function (result) {
        if (typeof callback == "function") {
          callback(result);
        }
        $.operate.successCallback(result);
      },
    };
    $.ajax(config);
  }
}

// 提交学生信息
function submitStudent(suffix, isCheck) {
  if ($.validate.form()) {
    var formData = new FormData($("form")[0]);
    formData.append("isCheck", isCheck);
    for (var value of fileMap.values()) {
      formData.append("images", value);
    }
    var config = {
      url: prefix + suffix,
      type: "post",
      dataType: "json",
      // cache: false,
      contentType: false,
      processData: false,
      data: formData,
      beforeSend: function () {
        $.modal.loading("正在保存中，请稍后...");
      },
      success: function (result) {
        if (typeof callback == "function") {
          callback(result);
        }
        $.operate.successTabCallback(result);
      },
    };
    $.ajax(config);
  }
}

function submitCustom(suffix, imageName) {
  if ($.validate.form()) {
    var formData = new FormData($("form")[0]);
    for (var value of fileMap.values()) {
      formData.append(imageName, value);
    }
    var config = {
      url: prefix + suffix,
      type: "post",
      dataType: "json",
      // cache: false,
      contentType: false,
      processData: false,
      data: formData,
      beforeSend: function () {
        $.modal.loading("正在保存中，请稍后...");
      },
      success: function (result) {
        if (typeof callback == "function") {
          callback(result);
        }
        $.operate.successCallback(result);
      },
    };
    $.ajax(config);
  }
}

function submitTab(suffix) {
  if ($.validate.form()) {
    var formData = new FormData($("form")[0]);
    for (var value of fileMap.values()) {
      formData.append("images", value);
    }
    var config = {
      url: prefix + suffix,
      type: "post",
      dataType: "json",
      // cache: false,
      contentType: false,
      processData: false,
      data: formData,
      beforeSend: function () {
        $.modal.loading("正在保存中，请稍后...");
      },
      success: function (result) {
        if (typeof callback == "function") {
          callback(result);
        }
        $.operate.successTabCallback(result);
      },
    };
    $.ajax(config);
  }
}

//图片尺寸验证
function verificationPicFile(file) {
  var filePath = file.value;
  if (filePath) {
    //读取图片数据
    var filePic = file.files[0];
    var reader = new FileReader();
    reader.onload = function (e) {
      var data = e.target.result;
      //加载图片获取图片真实宽度和高度
      var image = new Image();
      image.οnlοad = function () {
        var width = image.width;
        var height = image.height;
        if ((width == 500) | (height == 300)) {
          alert("图片尺寸符合！");
        } else {
          $.modal.msgError("图片尺寸应为：500*300！");
          file.value = "";
          return false;
        }
      };
      image.src = data;
    };
    reader.readAsDataURL(filePic);
  } else {
    return false;
  }
}

// 点击预览事件
// $(function () {
//     $(".image-box").on('click', '.image-item', function () {
//         let rotate = 0;
//         let scale = 1;
//         var url = $(this).css("background-image").split('(')[1].split(')')[0].replace(/'/g, '').replace(/"/g, '');
//         var img = '<div id="img" style="display: flex; justify-content: center; align-items: center; height: 100vh;"><img src="' + url + '" style="margin: 10px 10px;"/></div>';
//         window.parent.layer.open({
//             title: "图片预览",
//             type: 1,
//             fix: false,
//             //offset: 'auto',
//             maxmin: true, //不固定
//             shade: 0.3,
//             shadeClose: true, // 弹层外区域关闭
//             area: ['1000px', '600px'],
//             content: img,
//             btn: ['<i class="fa fa-rotate-right"></i> 旋转图片', '<i class="fa fa-search-plus"></i> 放大', '<i class="fa fa-search-minus"></i> 缩小', '<i class="fa fa-remove"></i> 关闭'],
//             //btnAlign: 'c',
//             btn1: function (index, layero, that) {
//                 const image = layero.find('img');
//                 rotate = 90 + rotate;
//                 image.css('transform', `rotate(${rotate}deg)`)
//             },
//             btn2: function (index, layero, that) {
//                 const image = layero.find('#img');
//                 scale = 0.3 + scale
//                 image.css('transform',`scale(${scale})`)
//                 return false
//             },
//             btn3: function (index, layero, that) {
//                 const image = layero.find('#img');
//                 scale = scale - 0.3
//                 image.css('transform',`scale(${scale})`)
//                 return false
//             }
//         });
//     });
// });

/** 点击预览事件 */
$(function () {
  $(".image-box").on("click", ".image-item", function () {
    var url = $(this)
      .css("background-image")
      .split("(")[1]
      .split(")")[0]
      .replace(/'/g, "")
      .replace(/"/g, "");

    var photoJson = {
      title: "图片预览", //相册标题
      id: new Date().getTime(), //相册id
      start: 0, //初始显示的图片序号，默认0
      data: [
        //相册包含的图片，数组格式
        {
          alt: "图片预览",
          pid: 1, //图片id
          src: url, //原图地址
          thumb: url, //缩略图地址
        },
      ],
    };
    layer.photos({
      photos: photoJson,
      anim: 5, //0-6的选择，指定弹出图片动画类型，默认随机
      closeBtn: 2,
      //添加旋转按钮
      tab: function (data, layero) {
        num = 0;

        /** 自定义旋转按钮*/
        $("#layui-layer-photos").parent()
          .append(`<div  class="layui-layer-imgbar" style="text-align:center;color:#fff">
                    <span class="pootosPlus" onclick="plusImg()" 
                    style="display: inline-block;
                    -moz-user-select: none;
                    -ms-user-select: none;
                    -webkit-user-select: none;
                    user-select: none;
                    padding-left:100px;
                    padding-right:100px;
                cursor:pointer;font-size:14px;">放大 <i class="fa fa-search-plus" style="font-size: 16px;"></i></span>
                <span class="pootosMinus" onclick="minusImg()" 
                style="display: inline-block;
                    -moz-user-select: none;
                    -ms-user-select: none;
                    -webkit-user-select: none;
                    user-select: none;
                    padding-right:100px;
                cursor:pointer;font-size:14px;">缩小 <i class="fa fa-search-minus" style="font-size: 16px;"></i></span>
                    <span class="pootosIcon" onclick="rotateImg()" 
                    style="display: inline-block;
                    -moz-user-select: none;
                    -ms-user-select: none;
                    -webkit-user-select: none;
                    user-select: none;
                padding-right:100px;cursor:pointer;font-size:14px;">旋转图片 <i class="fa fa-rotate-right" style="font-size: 16px;"></i></span>
                <span class="layui-layer-close" 
                        style="display: inline-block;
                        -moz-user-select: none;
                        -ms-user-select: none;
                        -webkit-user-select: none;
                        user-select: none;padding-right:0px;font-size:14px;cursor: pointer;">关闭 <i class="fa fa-remove" style="font-size: 16px;"></i></span>
                            </div>`);

        /** 去除原来的关闭按钮*/
        $(
          ".layui-layer.layui-layer-page.layui-layer-photos .layui-layer-setwin"
        ).css("display", "none");

        /** 显示抓手图案 */
        $(
          ".layui-layer.layui-layer-page.layui-layer-photos #layui-layer-photos .layui-layer-phimg img"
        ).css("cursor", "pointer");
      },
      anim: 0,
    });
  });
});

/** 旋转图片 */
function rotateImg() {
  /** 先将图片外层边框大小设置为最外面边框大小，图片实际大小与父边框大小不一致，会导致被裁剪 */
  let layerPhotos = $(
    ".layui-layer.layui-layer-page.layui-layer-photos #layui-layer-photos"
  );
  let phImg = $(
    ".layui-layer.layui-layer-page.layui-layer-photos #layui-layer-photos .layui-layer-phimg"
  );
  let image = $(
    ".layui-layer.layui-layer-page.layui-layer-photos #layui-layer-photos .layui-layer-phimg img"
  );

  image.css("width", "100%");
  image.css("height", "100%");
  num = (num + 90) % 360;
  /** 只旋转图片，不旋转div*/
  image.css("transform", "rotate(" + num + "deg)");
  // if (num == 90 || 270 == num) {
  //     /** 针对宽度大于高度的图片*/
  //     if (image.width() > image.height()) {
  //         /** 图片进行旋转时 宽度就等于外边框的高度 */
  //         image.css('height', phImg.height() / 1.5)
  //         image.css('width', phImg.height())
  //         /** 重新计算偏移量 */
  //         image.css('margin-top', (image.width() - image.height()) / 2)
  //     }
  // } else {
  //     image.css('width', phImg.width())
  //     image.css('height', "100%")
  //     image.css('margin-top', '0px');
  // }
  changeImgSize(layerPhotos, phImg);
}

/** 放大图片 */
function plusImg() {
  let layerPhotos = $(
    ".layui-layer.layui-layer-page.layui-layer-photos #layui-layer-photos"
  );
  layerPhotos.css("overflow", "visible");
  let phimg = $(
    ".layui-layer.layui-layer-page.layui-layer-photos #layui-layer-photos .layui-layer-phimg"
  );
  var h = phimg.height();
  var w = phimg.width();
  h = h * 1.05;
  w = w * 1.05;
  phimg.height(h);
  phimg.width(w);
  changeImgSize(layerPhotos, phimg);
}

/** 缩小图片 */
function minusImg() {
  let layerPhotos = $(
    ".layui-layer.layui-layer-page.layui-layer-photos #layui-layer-photos"
  );
  layerPhotos.css("overflow", "visible");
  let phimg = $(
    ".layui-layer.layui-layer-page.layui-layer-photos #layui-layer-photos .layui-layer-phimg"
  );
  var h = phimg.height();
  var w = phimg.width();
  h = h * 0.95;
  w = w * 0.95;
  phimg.height(h);
  phimg.width(w);
  changeImgSize(layerPhotos, phimg);
}

/** 鼠标滚动放大缩小图片方法 */
$(document).on("mousewheel DOMMouseScroll", ".layui-layer-phimg", function (e) {
  var delta =
    (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || // chrome & ie
    (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox

  let layerPhotos = $(
    ".layui-layer.layui-layer-page.layui-layer-photos #layui-layer-photos"
  );
  layerPhotos.css("overflow", "visible");

  let phimg = $(
    ".layui-layer.layui-layer-page.layui-layer-photos #layui-layer-photos .layui-layer-phimg"
  );
  var h = phimg.height();
  var w = phimg.width();
  if (delta > 0) {
    h = h * 1.05;
    w = w * 1.05;
  } else if (delta < 0) {
    if (h > 100) {
      h = h * 0.95;
      w = w * 0.95;
    }
  }
  phimg.height(h);
  phimg.width(w);
  let img = $(
    ".layui-layer.layui-layer-page.layui-layer-photos #layui-layer-photos .layui-layer-phimg img"
  );

  changeImgSize(layerPhotos, phimg);
  img.css("width", "100%");
  img.css("height", "100%");
});

/** 缩放后得重新计算一次偏移量*/
function changeImgSize(layerPhotos, val2) {
  if (layerPhotos.width() > val2.width()) {
    let leftSize = (layerPhotos.width() - val2.width()) / 2;
    val2.css("margin-left", leftSize);
    let heightSize = (layerPhotos.height() - val2.height()) / 2;
    val2.css("margin-top", heightSize);
  } else {
    val2.css("margin-left", 0);
    val2.css("margin-top", 0);
  }
  val2.css("position", "fixed");
}
