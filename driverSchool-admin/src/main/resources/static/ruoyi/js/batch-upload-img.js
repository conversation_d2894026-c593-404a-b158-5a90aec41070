// 用于记录选中的文件
// var fileMap = new Map();
var dataArray = new Array();

function initData(count) {
    for (var i = 0; i<count; i++) {
        var tempMap = new Map();
        dataArray[i] = tempMap;
    }
}

// 添加图片
function addImage(index) {
    var tempDiv = ".image-box-" + index + " > .image-item";
    if (maxImage && $(tempDiv).length >= maxImage) {
        $.modal.msgError("最多选择" + maxImage + "张照片");
        return false;
    }

    var input = "#add-input-" + index;
    $(input).click();
    return true;
}

// 选中图片
function selectImage(input) {
    var strArray = input.id.split("-");
    var index = strArray[2];

    var file = input.files[0];
    if (file.size / 1024 > 20480) {
        $.modal.msgWarning("图片不能大于20M");
        return false;
    }
    if (file) {
        var fileMap = dataArray[index];

        fileMap.set(file.name, file);
        // 读取并显示
        var reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = function (e) {
            var imageItem = '<div class="image-item" style="background-image: url(\'' + this.result + '\');">';
            imageItem += '<img class="delete-img" src="/img/delete.png" onclick="deleteImage(this, \'' + file.name + '\',' + index + ')">';
            imageItem += '</div>';

            var name = ".image-box-" + index;
            $(name).append($(imageItem));
        }
    }
    return true;
}

// 删除图片
function deleteImage(obj, fileName, index) {
    if (fileName) {
        var fileMap = dataArray[index];
        fileMap.delete(fileName);
    }
    $(obj).parent(".image-item").remove();
}

// 点击预览事件
$(function () {
    $(".image-box").on('click', '.image-item', function () {
        var url = $(this).css("background-image").split('(')[1].split(')')[0].replace(/'/g, '').replace(/"/g, '');
        window.parent.layer.open({
            title: "图片预览",
            type: 1,
            fix: false,
            //不固定
            maxmin: true,
            shade: 0.3,
            shadeClose: true,
            area: ['800px', document.documentElement.clientHeight],
            content: '<img src="' + url + '" style="margin: 20px 20px;height:' + (document.documentElement.clientHeight - 260) + 'px;" onclick="layer.close(layer.index);"/>'
        });
    });


});