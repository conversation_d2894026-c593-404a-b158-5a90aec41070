<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <title>场地监控</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title></title>
    <link rel="shortcut icon" href="favicon.ico">
    <link href="../static/css/sta.css" th:href="@{/css/sta.css}" rel="stylesheet"/>
</head>
<body style="background-color: #F0F2F5;">
<div class="content-box min-with">
    <div class="box-jk-cd">
        <div class="title-box">
            <div class="title-bg"></div>
            <div class="titlt-p">场地监控</div>
        </div>
        <div class="cd-box">
            <div class="search-box">
                <div class="box-select"><lable>驾校选择：</lable>
                    <select id="schoolId" name="schoolId">
                        <option value="">选择驾校</option>
                        <option th:each="dict : ${schoolList}" th:text="${dict.name}" th:value="${dict.id}"></option>
                    </select>
                </div>
                <div class="box-ipt"><lable>时间选择：</lable>
                    <input type="text" class="sta-time-input" placeholder="日期"/>
                </div>
                <div class="box-btn"><button>总量</button></div>
            </div>
        </div>
    </div>
    <div class="box-jkcd-sbxx">
        <div class="title-box">
            <div class="title-bg"></div>
            <div class="titlt-p">设备信息</div>
        </div>
        <div class="sbxx-box">
            <div class="icon-box" style="margin-left: 10%;"><img th:src="@{/img/sta/jk-shebei.png}" alt=""></div>
            <div class="msg-box">
                <h2 class="box-h2" style="line-height: 80px;">总设备数量：</h2>
                <a class="box-a fontsize36" href="" >5</a>
            </div>
        </div>
        <div class="box-shebei" style="margin-top: 30px;">
            <div class="msg-box" style="width: calc(40% - 20px);">
                <h2 class="box-h2">监控设备数量</h2><br>
                <a class="box-a fontsize24" href="" >5</a>
            </div>
            <div style="float: left;width: 60%; margin-top: 8px;">
                <div id="jk-chart01" style="width:100%;height: 94px;"></div>
            </div>
        </div>
        <div class="box-shebei">
            <div class="msg-box" style="width: calc(40% - 20px);">
                <h2 class="box-h2">道闸数量</h2><br>
                <a class="box-a fontsize24" href="" >0</a>
            </div>
            <div style="float: left; width: 60%; margin-top: 8px;">
                <div id="jk-chart02" style="width:100%;height: 94px;"></div>
            </div>
        </div>
    </div>
    <div class="box-jkcd-sbgj">
        <div class="title-box">
            <div class="title-bg"></div>
            <div class="titlt-p">设备告警统计</div>
        </div>
        <div class="sbgj">
            <div class="list4">
                <div class="icon-box" style="margin-left: 10%;"><img th:src="@{/img/sta/jk-car-unknow.png}" alt=""></div>
                <div class="msg-box">
                    <h2 class="box-h2">不明车辆数</h2><br>
                    <a class="box-a fontsize24" style="margin-top: 10px;" href="" >25</a>
                </div>
            </div>
            <div class="list4">
                <div class="icon-box" style="margin-left: 10%;"><img th:src="@{/img/sta/jk-car-warn.png}" alt=""></div>
                <div class="msg-box">
                    <h2 class="box-h2">不明车辆入场预警数</h2><br>
                    <a class="box-a fontsize24" style="margin-top: 10px;" href="" >100</a>
                </div>
            </div>
            <div class="list4">
                <div class="icon-box" style="margin-left: 10%;"><img th:src="@{/img/sta/jk-people-unknow.png}" alt=""></div>
                <div class="msg-box">
                    <h2 class="box-h2">不明人员数</h2><br>
                    <a class="box-a fontsize24" style="margin-top: 10px;" href="" >36</a>
                </div>
            </div>
            <div class="list4">
                <div class="icon-box" style="margin-left: 10%;"><img th:src="@{/img/sta/jk-people-warn.png}" alt=""></div>
                <div class="msg-box">
                    <h2 class="box-h2">不明人员入场预警数</h2><br>
                    <a class="box-a fontsize24" style="margin-top: 10px;" href="" >169</a>
                </div>
            </div>
        </div>
        <div class="sbgj-chart">
            <div class="left-chart">
                <div id="jk-chart03" style="width:100%;height:264px;"></div>
            </div>
            <div class="right-chart">
                <div id="jk-chart04" style="width:100%;height:264px;"></div>
            </div>
        </div>
    </div>
    <div class="box-jkcd-sjtj">
        <div class="title-box">
            <div class="title-bg"></div>
            <div class="titlt-p">事件统计</div>
        </div>
        <div class="box-sjtj">
            <div class="list2">
                <div class="icon-box" style="margin-left: 10%;"><img th:src="@{/img/sta/jk-jiashicang.png}" alt=""></div>
                <div class="msg-box">
                    <h2 class="box-h2">驾驶舱预警数量</h2><br>
                    <a class="box-a fontsize24" href="" >200</a>
                </div>
            </div>
            <div class="list2">
                <div class="icon-box" style="margin-left: 10%;"><img th:src="@{/img/sta/jk-jiaolian.png}" alt=""></div>
                <div class="msg-box">
                    <h2 class="box-h2">教练未穿正装</h2><br>
                    <a class="box-a fontsize24" href="" >70</a>
                </div>
            </div>
        </div>
        <div class="sjtj-4">
            <div class="sjtj-list">
                <p>副驾驶无人</p>
                <a href="">10</a>
            </div>
            <div class="sjtj-list">
                <p>前排抽烟</p>
                <a href="">20</a>
            </div>
            <div class="sjtj-list">
                <p>前排玩手机</p>
                <a href="">40</a>
            </div>
            <div class="sjtj-list">
                <p>未系安全带</p>
                <a href="">30</a>
            </div>
        </div>
        <div class="sjtj-chart">
            <div class="sjtj-chart-box">
                <div id="jk-chart05" style="width:100%;height:223px;"></div>
            </div>
        </div>
    </div>
    <div class="box-jkcd-yjgd">
        <div class="title-box">
            <div class="title-bg"></div>
            <div class="titlt-p">当下预警滚动栏</div>
        </div>
        <div class="yjgd-box tablebox">
            <table id="tableId" width="100%" border="0" cellpadding="0" cellspacing="0">
                <thead>
                <tr>
                    <th scope="col" width="10%">预警序号</th>
                    <th scope="col" width="20%">预警类型</th>
                    <th scope="col" width="25%">预警信息</th>
                    <th scope="col" width="20%">预警时间</th>
                    <th scope="col" width="25%">预警地点</th>
                </tr>
                </thead>

                <tbody>
                <tr th:each="item : ${warningRecordList}">
                    <td class="color-r" th:text="${item.seq}">3</td>
                    <td th:if="${item.type == 1}">人员预警</td>
                    <td th:if="${item.type == 7 || item.type == 6}">教练预警</td>
                    <td th:if="${item.type == 2 || item.type == 3 || item.type == 4 || item.type == 5}">驾驶预警</td>

                    <td th:if="${item.type == 1}">不明入场人员</td>
                    <td th:if="${item.type == 7}">未穿工装</td>
                    <td th:if="${item.type == 6}">违规聚集</td>

                    <td th:if="${item.type == 2}">副驾驶无人</td>
                    <td th:if="${item.type == 3}">未寄安全带</td>
                    <td th:if="${item.type == 4}">驾驶位抽烟</td>
                    <td th:if="${item.type == 5}">驾驶位玩手机</td>


                    <td th:text="${#dates.format(item.createdTime, 'yyyy-MM-dd HH:mm:ss')}">202-5-20</td>
                    <td th:if="${item.trainingGround != null}" th:text="${item.trainingGround.name}">广仁驾校桑园训练场</td>
                    <td th:if="${item.trainingGround == null}" >无</td>
                </tr>

                </tbody>
            </table>

        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<script th:src="@{/ajax/libs/echarts/echarts.min.js}"></script>
<script src="../static/ruoyi/siteMonitorIndex.js" th:src="@{/ruoyi/siteMonitorIndex.js}"></script>
<script type="text/javascript">

    $(function () {
        jkChart01();
        jkChart02();
        jkChart03();
        jkChart04();
        jkChart05();
    });

    // 参数1 tableID,参数2 div高度，参数3 速度，参数4 tbody中tr几条以上滚动
    tableScroll('tableId', 400, 30, 6)
    var MyMarhq;

    // laydate time-input 时间控件绑定
    if ($(".sta-time-input").length > 0) {
        layui.use('laydate', function () {
            var com = layui.laydate;
            $(".sta-time-input").each(function (index, item) {
                var time = $(item);
                // 控制控件外观
                var type = time.attr("data-type") || 'date';
                // 控制回显格式
                var format = time.attr("data-format") || 'yyyy-MM-dd';
                // 控制日期控件按钮
                var buttons = time.attr("data-btn") || 'clear|now|confirm', newBtnArr = [];
                // 日期控件选择完成后回调处理
                var callback = time.attr("data-callback") || {};
                if (buttons) {
                    if (buttons.indexOf("|") > 0) {
                        var btnArr = buttons.split("|"), btnLen = btnArr.length;
                        for (var j = 0; j < btnLen; j++) {
                            if ("clear" === btnArr[j] || "now" === btnArr[j] || "confirm" === btnArr[j]) {
                                newBtnArr.push(btnArr[j]);
                            }
                        }
                    } else {
                        if ("clear" === buttons || "now" === buttons || "confirm" === buttons) {
                            newBtnArr.push(buttons);
                        }
                    }
                } else {
                    newBtnArr = ['clear', 'now', 'confirm'];
                }
                com.render({
                    elem: item,
                    theme: 'molv',
                    trigger: 'click',
                    type: type,
                    format: format,
                    btns: newBtnArr,
                    done: function (value, data) {
                        if (typeof window[callback] != 'undefined'
                            && window[callback] instanceof Function) {
                            window[callback](value, data);
                        }
                        //
                        var schoolId= $("#schoolId").val();
                        if ($.common.isEmpty(schoolId)){
                            $.modal.msgError("驾校不能为空，请选择驾校");
                            return ;
                        }
                        console.log(schoolId);
                        var formData = new FormData();
                        formData.append("schoolId", schoolId);
                        formData.append("staDate", value);
                        $.ajax({
                            url: ctx + "business/sta/selectSchoolData",
                            data: formData,
                            type: "post",
                            processData: false,
                            contentType: false,
                            success: function(result) {
                                if (result.code == web_status.SUCCESS) {
                                    console.log();
                                    if (result.data !=null){
                                        $("#branchCount").text(result.data.branchCount);
                                        $("#trainingGroundCount").text(result.data.trainingGroundCount);
                                        $("#teacherCount").text(result.data.teacherCount);
                                        $("#carCount").text(result.data.carCount);
                                    }else{
                                        $.modal.alertWarning("未查询数据")
                                    }

                                } else if (result.code == web_status.WARNING) {
                                    $.modal.alertWarning(result.msg)
                                }  else {
                                    $.modal.alertError(result.msg);
                                }
                                $.modal.closeLoading();
                            }
                        })

                    }
                });
            });
        });
    }
</script>
</body>
</html>