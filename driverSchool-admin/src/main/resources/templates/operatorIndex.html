<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <title>运营商看板</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--360浏览器优先以webkit内核解析-->
    <title></title>
    <link rel="shortcut icon" href="favicon.ico">
    <link href="../static/css/sta.css" th:href="@{/css/sta.css}" rel="stylesheet"/>
</head>
<body style="background-color: #F0F2F5;">
<div class="content-box min-with">
    <div class="box-jxxx">
        <div class="title-box">
            <div class="title-bg"></div>
            <div class="titlt-p">驾校对接情况</div>
        </div>
        <div class="jxxx">
            <div class="jxxx-left">
                <div class="icon-box"><img th:src="@{/img/sta/jg-jigou.png}" alt=""></div>
                <div class="msg-box">
                    <h2 class="box-h2">全市驾校数量</h2><br>
                    <a class="box-a" href="" id="schoolTotal" th:text="${schoolTotal}">100</a>
                </div>
            </div>
            <div class="jg-chart01">
                <input type="hidden" id="schoolCount" th:value="${staDate?.schoolCount}"/>
                <div id="dj-chart01" style="width:280px;height: 103px;"></div>
            </div>
        </div>
    </div>
    <div class="box-jxqk">
        <div class="title-box">
            <div class="title-bg"></div>
            <div class="titlt-p">驾校情况</div>
            <div class="search-box">
                <div class="box-select">
                    <lable>驾校选择：</lable>
                    <select id="schoolId" name="schoolId">
                        <option value="">选择驾校</option>
                        <option th:each="dict : ${schoolList}" th:text="${dict.name}" th:value="${dict.id}"></option>
                    </select>
                </div>
            </div>
        </div>
        <div class="jxqk">
            <div class="list4">
                <div class="icon0"><img th:src="@{/img/sta/dj-fenxiao.png}" alt=""></div>
                <div class="titlt-p2">分校数量</div>
                <a class="jxqk-a" href="" id="branchCount" th:text="${staDate?.branchCount}">10</a>
            </div>
            <div class="list4">
                <div class="icon0"><img th:src="@{/img/sta/dj-mendian.png}" alt=""></div>
                <div class="titlt-p2">门店数量</div>
                <a class="jxqk-a" href=""id="registrationCount" th:text="${staDate?.registrationCount}">4</a>
            </div>
            <div class="list4">
                <div class="icon0"><img th:src="@{/img/sta/dj-s-fenxiao.png}" alt=""></div>
                <div class="titlt-p2">训练场数量</div>
                <a class="jxqk-a" href="" id="trainingGroundCount" th:text="${staDate?.trainingGroundCount}">0</a>
            </div>
            <div class="list4">
                <div class="icon0"><img th:src="@{/img/sta/dj-baoming.png}" alt=""></div>
                <div class="titlt-p2">报名总人数</div>
                <a class="jxqk-a" href="" id="studentCount" th:text="${staDate?.studentCount}">800</a>
            </div>
        </div>
    </div>
    <div class="box-zjqk-jg">
        <div class="box-zjqk">
            <div class="title-box">
                <div class="title-bg"></div>
                <div class="titlt-p">资金监管情况</div>
            </div>
            <div class="jxxx">
                <div class="jxxx-left">
                    <div class="icon-box"><img th:src="@{/img/sta/dj-qsxueyuan.png}" alt=""></div>
                    <div class="msg-box">
                        <h2 class="box-h2">全市学员数量</h2><br>
                        <a class="box-a" href="" id="superviseStudentCount" th:text="${staDate?.superviseStudentCount}">100</a>
                        <input type="hidden" id="noSuperviseStudentCount" th:value="${staDate?.noSuperviseStudentCount}"/>
                    </div>
                </div>
                <div class="jg-chart01">
                    <div id="jg-chart01" style=" width:264px;height: 103px;"></div>
                </div>
            </div>
        </div>
        <div class="box-zjqk">
            <div class="title-box">
                <div class="title-bg"></div>
                <div class="titlt-p">驾培机构</div>
            </div>
            <div class="jxxx">
                <div class="jxxx-left">
                    <div class="icon-box"><img th:src="@{/img/sta/dj-qsxueyuan.png}" alt=""></div>
                    <div class="msg-box">
                        <h2 class="box-h2">总驾培机构</h2><br>
                        <a class="box-a" href="" th:text="${staDate?.schoolCount}">84</a>
                    </div>
                </div>
                <div class="jg-chart01">
                    <input type="hidden" id="superviseSchoolCount" th:value="${staDate?.superviseSchoolCount}"/>
                    <input type="hidden" id="noSuperviseSchoolCount" th:value="${staDate?.noSuperviseSchoolCount}"/>
                    <div id="jg-chart02" style=" width:264px;height: 103px;"></div>
                </div>
            </div>
        </div>
        <div class="box-zjqk-tj">
            <div class="title-box">
                <div class="title-bg"></div>
                <div class="titlt-p">资金监管异常数量</div>
            </div>
            <div class="zjqk-tj">
                <div id="zjqk-tj-chart" style="width: 100%; height:200px;"></div>
            </div>
        </div>
    </div>
    <div class="box-zjqk-jg">
        <div class="box-kkqk">
            <div class="title-box">
                <div class="title-bg"></div>
                <div class="titlt-p">开卡情况</div>
            </div>
            <div class="kkqk">
                <div class="list4 list4-bg">
                    <div class="msg-box" style="margin-top: 30px;">
                        <h2 class="box-h2">已办理人数</h2><br>
                        <a class="box-a fontsize24" href="" th:text="${staDate?.simSuccessCount + staDate?.simFailCount}">30</a>
                        <input type="hidden" id="simSuccessCount" th:value="${staDate?.simSuccessCount}"/>
                        <input type="hidden" id="simFailCount" th:value="${staDate?.simFailCount}"/>
                    </div>
                    <div style="float: right;width: 155px; height: 64px; margin-top: 30px;">
                        <div id="jg-chart03" style="height: 74px;"></div>
                    </div>
                </div>
                <div class="list4 list4-bg">
                    <div class="msg-box" style="margin-top: 30px;">
                        <h2 class="box-h2">补贴已发放</h2><br>
                        <input type="hidden" id="simAllowanceSendedCount" th:value="${staDate?.simAllowanceSendedCount}"/>
                        <input type="hidden" id="simAllowanceUnsendCount" th:value="${staDate?.simAllowanceUnsendCount}"/>
                        <a class="box-a fontsize24" href="" th:text="${staDate?.simAllowanceSendedCount + staDate?.simAllowanceUnsendCount}">30</a>
                    </div>

                    <div style="float: right;width: 140px; height: 64px; margin-top: 30px;">
                        <div id="jg-chart04" style="height: 74px;"></div>
                    </div>
                </div>
                <div class="list4 list4-bg">
                    <div class="msg-box" style="margin-top: 30px;">
                        <h2 class="box-h2">未办理人数</h2><br>
                        <a class="box-a fontsize24" href="" th:text="${staDate?.simNoneStopCount + staDate?.simNoneDoingCount}">30</a>
                    </div>
                    <div style="float: right;width: 155px; height: 64px;margin-top: 30px;">
                        <input type="hidden" id="simNoneStopCount" th:value="${staDate?.simNoneStopCount}"/>
                        <input type="hidden" id="simNoneDoingCount" th:value="${staDate?.simNoneDoingCount}"/>
                        <div id="jg-chart05" style="height: 74px;"></div>
                    </div>
                </div>
                <div class="list4 list4-bg">
                    <div class="msg-box" style="margin-top: 30px;">
                        <h2 class="box-h2">外呼数</h2><br>
                        <a class="box-a fontsize24" href="" th:text="${staDate?.simCalledCount + staDate?.simUncallCount}">30</a>
                    </div>
                    <div style="float: right;width: 155px; height: 64px;;margin-top: 30px;">
                        <input type="hidden" id="simCalledCount" th:value="${staDate?.simCalledCount}"/>
                        <input type="hidden" id="simUncallCount" th:value="${staDate?.simUncallCount}"/>
                        <div id="jg-chart06" style="height: 74px;"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="box-zjqk-tj">
            <div class="title-box">
                <div class="title-bg"></div>
                <div class="titlt-p">办理失败原因</div>
            </div>
            <div class="zjqk-tj">
                <div id="bl-tj-chart" style="width: 100%; height:200px;"></div>
            </div>
        </div>
    </div>
    <div class="box-zjjgsj">
        <div class="title-box">
            <div class="title-bg"></div>
            <div class="titlt-p">系统运营情况</div>
            <div class="search-box">
                <div class="box-ipt">
                    <lable>月份筛选：</lable>
                    <input type="text" class="sta-month-input" placeholder="2023-06">
                </div>
            </div>
        </div>
        <div class="zjjgsj">
            <div class="list3 list3-bg">
                <div class="msg-box" style="margin-top: 30px;">
                    <h2 class="box-h2">资讯管理文章</h2><br>
                    <a class="box-a fontsize24" href="" id="articleNum" th:text="${staDate?.articleCheckedCount + staDate?.articelUncheckedCount}">30</a>
                </div>
                <div class="chart-p-box">
                    <div class="chart-p">已审核：<a href="" id="articleCheckedCount" th:text="${staDate?.articleCheckedCount}" class="color-blue">10</a></div>
                    <div class="chart-box">
                        <div id="jg-chart07" style="height:74px;"></div>
                    </div>
                    <div class="chart-p">未审核：<a href="" id="articelUncheckedCount" th:text="${staDate?.articelUncheckedCount}">10</a></div>
                </div>
            </div>
            <div class="list3 list3-bg">
                <div class="msg-box" style="margin-top: 30px;">
                    <h2 class="box-h2">留言中心问题</h2><br>
                    <a class="box-a fontsize24" href="" id="messageNum" th:text="${staDate?.messageHandledCount + staDate?.messageUnhandledCount}">10</a>
                </div>
                <div class="chart-p-box">
                    <div class="chart-p">已处理：<a href="" id="messageHandledCount" th:text="${staDate?.messageHandledCount}" class="color-blue">5</a></div>
                    <div class="chart-box">
                        <div id="jg-chart08" style="height:74px;"></div>
                    </div>
                    <div class="chart-p">未处理：<a href="" id="messageUnhandledCount" th:text="${staDate?.messageUnhandledCount}">5</a></div>
                </div>
            </div>
            <div class="list3 list3-bg">
                <div class="msg-box" style="margin-top: 30px;">
                    <h2 class="box-h2">小程序访问量</h2><br>
                    <a class="box-a fontsize24" href="" id="miniappNum" th:text="${staDate?.miniappStudentVisitCount + staDate?.miniappArticelVisitCount}">100</a>
                </div>
                <div class="chart-p-box">
                    <div class="chart-p">学员访问：<a href="" id="miniappStudentVisitCount" th:text="${staDate?.miniappStudentVisitCount}"  class="color-blue">10</a></div>
                    <div class="chart-box">
                        <div id="jg-chart09" style="height:74px;"></div>
                    </div>
                    <div class="chart-p">资讯访问：<a href="" id="miniappArticelVisitCount" th:text="${staDate?.miniappArticelVisitCount}" >10</a></div>
                </div>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:src="@{/ajax/libs/echarts/echarts.min.js}"></script>
<script src="../static/ruoyi/operatorIndex.js" th:src="@{/ruoyi/operatorIndex.js}"></script>
<script type="text/javascript">
    $(function () {
        jgChart01();
        jgChart02();
        jgChart03();
        jgChart04();
        jgChart05();
        jgChart06();
        jgChart07();
        jgChart08();
        jgChart09();
        zjqkTjChart();
        blTjChart();
        djChart01();
    });
</script>
</body>
</html>