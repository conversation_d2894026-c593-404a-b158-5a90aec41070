<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="renderer" content="webkit" />
    <title>东莞市驾驶员培训综合服务平台首页</title>
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <link th:href="@{favicon.ico}" rel="shortcut icon" />
    <link th:href="@{/css/bootstrap.min.css}" rel="stylesheet" />
    <link th:href="@{/css/jquery.contextMenu.min.css}" rel="stylesheet" />
    <link th:href="@{/css/font-awesome.min.css}" rel="stylesheet" />
    <link th:href="@{/css/animate.min.css}" rel="stylesheet" />
    <link th:href="@{/css/style.min.css}" rel="stylesheet" />
    <link th:href="@{/css/skins.css}" rel="stylesheet" />
    <link th:href="@{/ruoyi/css/ry-ui.css?v=4.7.6}" rel="stylesheet" />
  </head>
  <body
    class="fixed-sidebar full-height-layout gray-bg"
    th:classappend="${isMobile} ? 'canvas-menu'"
    style="overflow: hidden"
  >
    <div id="wrapper">
      <!--左侧导航开始-->
      <nav class="navbar-default navbar-static-side" role="navigation">
        <div class="nav-close">
          <i class="fa fa-times-circle"></i>
        </div>
        <a th:href="@{/index}">
          <li class="logo hidden-xs">
            <span class="logo-lg">[[${logoName}]]</span>
          </li>
        </a>
        <div class="sidebar-collapse">
          <ul class="nav" id="side-menu">
            <li
              th:each="menu ,state : ${menus}"
              th:class="${state.first && menu.url != '#'}  ?  |active selected|"
            >
              <a
                th:class="@{${!#strings.isEmpty(menu.url) && menu.url != '#'} ? ${menu.target}}"
                th:href="@{${#strings.isEmpty(menu.url)} ? |#| : ${menu.url}}"
                th:data-refresh="${menu.isRefresh == '0'}"
              >
                <i class="fa fa-bar-chart-o" th:class="${menu.icon}"></i>
                <span class="nav-label" th:text="${menu.menuName}"
                  >一级菜单</span
                >
                <span
                  th:class="${#strings.isEmpty(menu.url) || menu.url == '#'} ? |fa arrow|"
                ></span>
              </a>
              <ul class="nav nav-second-level collapse">
                <li th:each="cmenu : ${menu.children}">
                  <a
                    th:if="${#lists.isEmpty(cmenu.children)}"
                    th:class="${#strings.isEmpty(cmenu.target)} ? |menuItem| : ${cmenu.target}"
                    th:utext="${cmenu.menuName}"
                    th:href="@{${cmenu.url}}"
                    th:data-refresh="${cmenu.isRefresh == '0'}"
                    >二级菜单</a
                  >
                  <a th:if="${not #lists.isEmpty(cmenu.children)}" href="#"
                    >[[${cmenu.menuName}]]<span class="fa arrow"></span
                  ></a>
                  <ul
                    th:if="${not #lists.isEmpty(cmenu.children)}"
                    class="nav nav-third-level"
                  >
                    <li th:each="emenu : ${cmenu.children}">
                      <a
                        th:if="${#lists.isEmpty(emenu.children)}"
                        th:class="${#strings.isEmpty(emenu.target)} ? |menuItem| : ${emenu.target}"
                        th:text="${emenu.menuName}"
                        th:href="@{${emenu.url}}"
                        th:data-refresh="${emenu.isRefresh == '0'}"
                        >三级菜单</a
                      >
                      <a th:if="${not #lists.isEmpty(emenu.children)}" href="#"
                        >[[${emenu.menuName}]]<span class="fa arrow"></span
                      ></a>
                      <ul
                        th:if="${not #lists.isEmpty(emenu.children)}"
                        class="nav nav-four-level"
                      >
                        <li th:each="fmenu : ${emenu.children}">
                          <a
                            th:if="${#lists.isEmpty(fmenu.children)}"
                            th:class="${#strings.isEmpty(fmenu.target)} ? |menuItem| : ${fmenu.target}"
                            th:text="${fmenu.menuName}"
                            th:href="@{${fmenu.url}}"
                            th:data-refresh="${fmenu.isRefresh == '0'}"
                            >四级菜单</a
                          >
                        </li>
                      </ul>
                    </li>
                  </ul>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </nav>
      <!--左侧导航结束-->

      <!--右侧部分开始-->
      <div id="page-wrapper" class="gray-bg dashbard-1">
        <div class="row border-bottom">
          <nav
            class="navbar navbar-static-top"
            role="navigation"
            style="margin-bottom: 0"
          >
            <!--
				<div class="navbar-header">
                    <a class="navbar-minimalize minimalize-styl-2" style="color:#FFF;" href="#" title="收起菜单">
                        <i class="fa fa-bars"></i>
                    </a>
                </div>
                -->
            <ul class="nav navbar-top-links navbar-right welcome-message">
              <li>
                <a
                  data-toggle="tooltip"
                  data-trigger="hover"
                  data-placement="bottom"
                  title="退出"
                  th:href="@{logout}"
                  id="logout"
                  ><i class="fa fa-sign-out"></i> 退出</a
                >
              </li>
            </ul>
          </nav>
        </div>
        <div
          class="row content-tabs"
          th:classappend="${#bools.isFalse(tagsView)} ? |hide|"
        >
          <button class="roll-nav roll-left tabLeft">
            <i class="fa fa-backward"></i>
          </button>
          <nav class="page-tabs menuTabs">
            <div class="page-tabs-content">
              <a
                href="javascript:;"
                class="active menuTab"
                th:each="menu,proStat : ${menus}"
                th:if="${proStat.index == 0 && menu.url != '#'} "
                th:data-id="${menu.url}"
                th:text="${menu.menuName}"
              ></a>
            </div>
          </nav>
          <button class="roll-nav roll-right tabRight">
            <i class="fa fa-forward"></i>
          </button>
          <a href="javascript:void(0);" class="roll-nav roll-right tabReload"
            ><i class="fa fa-refresh"></i> 刷新</a
          >
        </div>

        <a id="ax_close_max" class="ax_close_max" href="#" title="关闭全屏">
          <i class="fa fa-times-circle-o"></i>
        </a>

        <div
          class="row mainContent"
          id="content-main"
          th:classappend="${mainClass}"
        >
          <iframe
            class="RuoYi_iframe"
            name="iframe0"
            width="100%"
            height="100%"
            th:each="menu,proStat : ${menus}"
            th:if="${proStat.index == 0 && menu.url != '#'} "
            th:data-id="${menu.url}"
            th:src="${menu.url}"
            frameborder="0"
            seamless
          ></iframe>

          <iframe
            class="RuoYi_iframe"
            name="iframe0"
            width="100%"
            height="100%"
            th:each="menu,proStat : ${menus}"
            th:if="${proStat.index == 0 && menu.url == '#'} "
            frameborder="0"
            seamless
          ></iframe>
        </div>

        <div th:if="${footer}" class="footer">
          <div class="pull-right">© [[${copyrightYear}]]</div>
        </div>
      </div>
      <!--右侧部分结束-->
    </div>
    <!-- 全局js -->
    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <script th:src="@{/js/plugins/metisMenu/jquery.metisMenu.js}"></script>
    <script
      th:src="@{/js/plugins/slimscroll/jquery.slimscroll.min.js}"
    ></script>
    <script th:src="@{/js/jquery.contextMenu.min.js}"></script>
    <script th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
    <script th:src="@{/ruoyi/js/ry-ui.js?v=4.7.7}"></script>
    <script th:src="@{/ruoyi/js/common.js?v=4.7.6}"></script>
    <script th:src="@{/ruoyi/index.js?v=20201208}"></script>
    <script th:src="@{/ajax/libs/fullscreen/jquery.fullscreen.js}"></script>
    <script th:inline="javascript">
      	window.history.forward(1);
      var ctx = [[@{/}]];
      	var lockscreen = [[${session.lockscreen}]];
      	if(lockscreen){window.top.location=ctx+"lockscreen";}
      // 皮肤缓存
      	var skin = storage.get("skin");
      // history（表示去掉地址的#）否则地址以"#"形式展示
      	var mode = "history";
      // 历史访问路径缓存
      	var historyPath = storage.get("historyPath");
      // 是否页签与菜单联动
      	var isLinkage = true;

      // 本地主题优先，未设置取系统配置
      	if($.common.isNotEmpty(skin)){
      		$("body").addClass(skin.split('|')[0]);
      		$("body").addClass(skin.split('|')[1]);
      	} else {
      		$("body").addClass([[${sideTheme}]]);
      		$("body").addClass([[${skinName}]]);
      	}

      	/* 用户管理-重置密码 */
      	function resetPwd() {
      		var url = ctx + 'system/user/profile/resetPwd';
      		$.modal.open("重置密码", url, '770', '380');
      	}

      	/* 切换主题 */
      	function switchSkin() {
      		layer.open({
      			type : 2,
      			shadeClose : true,
      			title : "切换主题",
      			area : ["530px", "386px"],
      			content : [ctx + "system/switchSkin", 'no']
      		})
      	}

      	/* 切换菜单 */
      	function toggleMenu() {
      		$.modal.confirm("确认要切换成横向菜单吗？", function() {
      			$.get(ctx + 'system/menuStyle/topnav', function(result) {
      				window.location.reload();
      			});
      		})
      	}

      	/** 刷新时访问路径页签 */
      	function applyPath(url) {
      		$('a[href$="' + decodeURI(url) + '"]').click();
      		if (!$('a[href$="' + url + '"]').hasClass("noactive")) {
      			$('a[href$="' + url + '"]').parent("li").addClass("selected").parents("li").addClass("active").end().parents("ul").addClass("in");
      		}
      	}

      	$(function() {
              //根据后台的运行环境加载水印
            var activeProfile = [[${activeProfile}]];

            // 自定义 repeat 函数
            function repeatString(str, count) {
                var result = '';
                for (var i = 0; i < count; i++) {
                    result += str;
                }
                return result;
            }

            // 添加水印函数
            function addWatermark(ContextText) {
                var watermark = document.createElement('div');
                watermark.className = 'watermark';
                watermark.style.position = 'fixed';
                watermark.style.top = '0';
                watermark.style.left = '0';
                watermark.style.width = '100%';
                watermark.style.height = '100%';
                watermark.style.pointerEvents = 'none';
                watermark.style.zIndex = '9999';
                watermark.style.overflow = 'hidden';

                var watermarkContent = document.createElement('div');
                watermarkContent.style.position = 'absolute';
                watermarkContent.style.top = '-50%';
                watermarkContent.style.left = '-50%';
                watermarkContent.style.width = '200%';
                watermarkContent.style.height = '200%';
                watermarkContent.style.fontSize = '30px';
                watermarkContent.style.color = 'rgba(0, 0, 0, 0.1)';
                watermarkContent.style.lineHeight = '3em';
                watermarkContent.style.letterSpacing = '5px';
                watermarkContent.style.transform = 'rotate(-45deg)';
                watermarkContent.style.pointerEvents = 'none';
                watermarkContent.style.userSelect = 'none';

                var text = ContextText;
                var repeatedText = repeatString(text, 1000);  // 使用自定义的 repeat 函数
                watermarkContent.textContent = repeatedText;

                watermark.appendChild(watermarkContent);
                document.body.appendChild(watermark);
            }

            // 只有在环境为 'dev' 时才添加水印
            if (activeProfile === 'dev') {
                // 页面加载完成后添加水印
                window.addEventListener('load', function() {
                    addWatermark('---开发环境---');
                });

                // 监听窗口大小变化，重新添加水印
                window.addEventListener('resize', function() {
                    var existingWatermark = document.querySelector('.watermark');
                    if (existingWatermark) {
                        existingWatermark.remove();
                    }
                    addWatermark('---开发环境---');
                });
            } else if(activeProfile === 'test') {
                window.addEventListener('load', function() {
                    addWatermark('---测试环境---');
                });

                window.addEventListener('resize', function() {
                    var existingWatermark = document.querySelector('.watermark');
                    if (existingWatermark) {
                        existingWatermark.remove();
                    }
                    addWatermark('---测试环境---');
                });
            }
            var qrcodeExpiredMsg=[[${qrcodeExpiredMsg}]];
            if(qrcodeExpiredMsg.length>0){
              $.modal.alert(qrcodeExpiredMsg)
            }
      		var lockPath = storage.get('lockPath');
      		if($.common.equals("history", mode) && window.performance.navigation.type == 1) {
      			var url = storage.get('publicPath');
      			if ($.common.isNotEmpty(url)) {
      				applyPath(url);
      			}
      		} else if($.common.isNotEmpty(lockPath)) {
      			applyPath(lockPath);
      			storage.remove('lockPath');
      		} else {
      			var hash = location.hash;
      			if ($.common.isNotEmpty(hash)) {
      				var url = hash.substring(1, hash.length);
      				applyPath(url);
      			} else {
      				if($.common.equals("history", mode)) {
      					storage.set('publicPath', "");
      				}
      			}
      		}

      		/* 初始密码提示 */
      		if([[${isDefaultModifyPwd}]]) {
      			layer.confirm("您的密码还是初始密码，请修改密码！", {
      				icon: 0,
      				title: "安全提示",
      				btn: ['确认'	, '取消'],
      				offset: ['30%']
      			}, function (index) {
      				resetPwd();
      				layer.close(index);
      			});
      		}

      		/* 过期密码提示 */
      		if([[${isPasswordExpired}]]) {
      			layer.confirm("您的密码已过期，请尽快修改密码！", {
      				icon: 0,
      				title: "安全提示",
      				btn: ['确认'	, '取消'],
      				offset: ['30%']
      			}, function (index) {
      				resetPwd();
      				layer.close(index);
      			});
      		}
      		$("[data-toggle='tooltip']").tooltip();
      	})
      ;
    </script>
  </body>
</html>
