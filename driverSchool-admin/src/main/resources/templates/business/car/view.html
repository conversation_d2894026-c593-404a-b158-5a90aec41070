<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改车辆管理')" />
    <th:block th:include="include :: upload-img-css" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-car-edit" th:object="${schoolCar}">
            <input name="id" th:field="*{id}" type="hidden">


            <div class="form-group">
                <label class="col-sm-3 control-label">所属学校：</label>
                <div class="form-control-static" th:if="*{school != null}" th:text="*{school.name}">
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">所属分校：</label>
                <div class="form-control-static" th:if="*{branch != null}"  th:text="*{branch.name}">
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">所属报名点：</label>
                <div class="form-control-static" th:if="*{registration != null}"  th:text="*{registration.name}">
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">车牌号：</label>
                <div class="form-control-static" th:text="*{carNo}"></div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label">品牌：</label>
                <div class="form-control-static" th:text="*{brand}"></div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label">培训车型：</label>
                <div class="col-sm-8">
                    <select name="isAuto" class="form-control m-b" th:with="type=${@dict.getType('is_car_auto')}" disabled="disabled">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{isAuto}"></option>
                    </select>
                </div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label">投入时间：</label>
                <div class="form-control-static" th:text="${#dates.format(schoolCar.joinDate, 'yyyy-MM-dd')}"></div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label">车龄：</label>
                <div class="form-control-static" th:text="*{ageLimit}"></div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否模拟机：</label>
                <div class="col-sm-8">
                    <select name="isSimulator" class="form-control m-b" th:with="type=${@dict.getType('is_simulator')}" disabled="disabled">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{isSimulator}"></option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">备案号：</label>
                <div class="form-control-static" th:text="*{recNo}">
                </div>
            </div>

            <div class="form-group date">
                <label class="col-sm-3 control-label">备案时间：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="*{#calendars.format(recDate,'yyyy-MM-dd')}">
                    </div>
                </div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label">使用状态：</label>
                <div class="col-sm-8">
                    <select name="status" class="form-control m-b" th:with="type=${@dict.getType('is_car_status')}" disabled="disabled">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{status}"></option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">图片：</label>
                <div class="col-sm-8">
                    <div class="image-box">
                        <div class="image-item" th:each="file : *{imageFileList}" th:style="|background-image: url('${file.webPath}');|">
                            <input type="hidden" name="imageIds" th:value="${file.id}">
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="*{remark}"></div>
                </div>
            </div>

        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: upload-img-js" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <th:block th:include="include :: datetimepicker-js" />
</body>
</html>