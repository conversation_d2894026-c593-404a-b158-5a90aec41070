<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('分账账户列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>相关ID，type=1,为总校ID，type=2,为分校ID，type=3为门店ID：</label>
                                <input type="text" name="refId"/>
                            </li>
                            <li>
                                <label>账户号：</label>
                                <input type="text" name="accountNo"/>
                            </li>
                            <li>
                                <label>账户名：</label>
                                <input type="text" name="accountName"/>
                            </li>
                            <li>
                                <label>客户号：</label>
                                <input type="text" name="customerNo"/>
                            </li>
                            <li>
                                <label>密钥：</label>
                                <input type="text" name="secretKey"/>
                            </li>
                            <li>
                                <label>分账金额：</label>
                                <input type="text" name="divisionFee"/>
                            </li>
                            <li>
                                <label>是否审核：</label>
                                <input type="text" name="isCheck"/>
                            </li>
                            <li>
                                <label>创建时间：</label>
                                <input type="text" class="time-input" placeholder="请选择创建时间" name="createdTime"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="business:divisionAccount:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="business:divisionAccount:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="business:divisionAccount:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="business:divisionAccount:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:divisionAccount:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:divisionAccount:remove')}]];
        var prefix = ctx + "business/divisionAccount";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "分账账户",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'refId',
                    title: '相关ID，type=1,为总校ID，type=2,为分校ID，type=3为门店ID'
                },
                {
                    field: 'type',
                    title: '1-总校，2-分校，3-门店'
                },
                {
                    field: 'accountNo',
                    title: '账户号'
                },
                {
                    field: 'accountName',
                    title: '账户名'
                },
                {
                    field: 'customerNo',
                    title: '客户号'
                },
                {
                    field: 'secretKey',
                    title: '密钥'
                },
                {
                    field: 'divisionFee',
                    title: '分账金额'
                },
                {
                    field: 'isCheck',
                    title: '是否审核'
                },
                {
                    field: 'createdTime',
                    title: '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>