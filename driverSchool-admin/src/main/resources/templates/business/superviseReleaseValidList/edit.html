<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改释放明单')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-superviseReleaseValidList-edit" th:object="${superviseReleaseValidList}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">：</label>
                <div class="col-sm-8">
                    <input name="studentId" th:field="*{studentId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">学号：</label>
                <div class="col-sm-8">
                    <input name="studentStunum" th:field="*{studentStunum}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="studentName" th:field="*{studentName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="studentIdentity" th:field="*{studentIdentity}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="studentMobile" th:field="*{studentMobile}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">：</label>
                <div class="col-sm-8">
                    <input name="schoolId" th:field="*{schoolId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="schoolName" th:field="*{schoolName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="branchId" th:field="*{branchId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="branchName" th:field="*{branchName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="registrationId" th:field="*{registrationId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="registrationName" th:field="*{registrationName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">归属科目：0=其他，1=科目一，2=科目二，3=科目三，4=科目四，5=退学：</label>
                <div class="col-sm-8">
                    <input name="subjectName" th:field="*{subjectName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">释放金额：</label>
                <div class="col-sm-8">
                    <input name="releaseMoney" th:field="*{releaseMoney}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="releaseOrderNo" th:field="*{releaseOrderNo}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="releaseBankNo" th:field="*{releaseBankNo}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="releaseTime" th:value="${#dates.format(superviseReleaseValidList.releaseTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">释放操作者：</label>
                <div class="col-sm-8">
                    <input name="releaseOperator" th:field="*{releaseOperator}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">失败原因：</label>
                <div class="col-sm-8">
                    <input name="failReason" th:field="*{failReason}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="pushtime" th:value="${#dates.format(superviseReleaseValidList.pushtime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">条目创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="createdTime" th:value="${#dates.format(superviseReleaseValidList.createdTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/superviseReleaseValidList";
        $("#form-superviseReleaseValidList-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-superviseReleaseValidList-edit').serialize());
            }
        }

        $("input[name='releaseTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='pushtime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='createdTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>