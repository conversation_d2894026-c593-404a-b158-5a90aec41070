<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('释放明单列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>
                <li>
                  <label>分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="branchId"
                    name="branchId"
                    class="branchId form-control m-b"
                    data-first-title="选择分校"
                  ></select>
                </li>
                <li>
                  <label>报名点：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="registrationId"
                    name="registrationId"
                    class="registrationId form-control m-b"
                    data-first-title="选择报名点"
                  ></select>
                </li>
                <li>
                  <label>学员姓名：</label>
                  <input
                    type="text"
                    name="studentName"
                    placeholder="请输入学员姓名"
                  />
                </li>
                <li>
                  <label>身份证号：</label>
                  <input
                    type="text"
                    name="studentIdentity"
                    placeholder="请输入身份证号 多个用空格隔开"
                  />
                </li>
                <li>
                  <label>释放状态：</label>
                  <select
                    name="releaseStatus"
                    th:with="type=${@dict.getType('release_status')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label>订单号：</label>
                  <input
                    type="text"
                    name="releaseOrderNo"
                    placeholder="请输入订单号"
                  />
                </li>
                <li>
                  <label>目前科目：</label>
                  <select
                    name="subjectName"
                    th:with="type=${@dict.getType('study_stage')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                      th:if="${dict.dictLabel != '分账'}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label>释放金额：</label>
                  <input
                    type="text"
                    name="releaseMoney"
                    placeholder="请输入释放金额"
                  />
                </li>
                <li>
                  <label>释放人员：</label>
                  <input
                    type="text"
                    name="releaseOperator"
                    placeholder="请输入释放人员"
                  />
                </li>
                <li class="select-time">
                  <label>释放时间： </label>
                  <input
                    type="text"
                    class="time-input"
                    id="beginReleaseTime"
                    placeholder="开始日期"
                    name="beginReleaseTime"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    class="time-input"
                    id="endReleaseTime"
                    placeholder="结束日期"
                    name="endReleaseTime"
                  />
                </li>
                <li class="select-time">
                  <label>共享时间： </label>
                  <input
                    type="text"
                    class="time-input"
                    id="beginPushTime"
                    placeholder="开始日期"
                    name="beginPushTime"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    class="time-input"
                    id="endPushTime"
                    placeholder="结束日期"
                    name="endPushTime"
                  />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-success multiple disabled"
            onclick="$.operate.removeAll()"
            shiro:hasPermission="business:superviseReleaseValidList:remove"
          >
            删除
          </a>
          <a
            class="btn btn-success multiple disabled"
            onclick="release()"
            shiro:hasPermission="business:superviseReleaseValidList:release"
          >
            释放
          </a>
          <a
            class="btn btn-success"
            onclick="allRelease()"
            shiro:hasPermission="business:superviseReleaseValidList:allRelease"
          >
            全部释放
          </a>
          <a
            class="btn btn-success"
            onclick="generate()"
            shiro:hasPermission="business:superviseReleaseValidList:generate"
          >
            产生新名单
          </a>
          <a
            class="btn btn-success multiple disabled"
            onclick="checkbankTrans()"
            shiro:hasPermission="business:superviseReleaseValidList:checkbankTrans"
          >
            核对银行账
          </a>
          <a
            class="btn btn-success multiple disabled"
            onclick="bankReturnMoneyOperate()"
            shiro:hasPermission="business:superviseReleaseValidList:bankReturnMoneyOperate"
          >
            银行退款回滚
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:superviseReleaseValidList:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:superviseReleaseValidList:remove')}]];
      var genderDatas = [[${@dict.getType('sys_user_sex')}]];
      var releaseStatus = [[${@dict.getType('release_status')}]];
      var subjectName = [[${@dict.getType('study_stage')}]];
      var prefix = ctx + "business/superviseReleaseValidList";

      $(function() {
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              modalName: "释放明单",
              columns: [{
                  checkbox: true
              },
              {
                  field: 'id',
                  title: '',
                  visible: false
              },
              {
                  field: 'schoolName',
                  title: '驾校'
              },
              {
                  field: 'branchName',
                  title: '分校'
              },
              {
                  field: 'registrationName',
                  title: '报名点'
              },
              {
                  field: 'studentStunum',
                  title: '学号'
              },
              {
                  field: 'studentName',
                  title: '学员姓名'
              },
              {
                  field: 'studentIdentity',
                  title: '身份证号',
                  formatter: function (value, row, index) {
                      if ($.common.isNotEmpty(value)) {
                          return basecclusion(value,6,4);
                      }
                  }
              },
              {
                  field: 'studentSex',
                  title: '性别',
                  formatter: function(value, row, index) {
                    return $.table.selectDictLabel(genderDatas, value);
                  }
              },
              {
                  field: 'studentMobile',
                  title: '联系号码'
              },
              {
                  field: 'subjectName',
                  title: '目前科目',
                  formatter: function(value, row, index) {
                      return $.table.selectDictLabel(subjectName, value);
                  }
              },
              {
                  field: 'pushtime',
                  title: '共享时间'
              },
              {
                  field: 'releaseMoney',
                  title: '需要释放金额'
              },
              {
                  field: 'releaseStatus',
                  title: '释放状态',
                  formatter: function(value, row, index) {
                      return $.table.selectDictLabel(releaseStatus, value);
                  }
              },
              {
                  field: 'releaseTime',
                  title: '释放时间'
              },
              {
                  field: 'releaseOperator',
                  title: '释放人员'
              },
              {
                  field: 'releaseOrderNo',
                  title: '订单号'
              },
              {
                  field: 'releaseBankNo',
                  title: '银行单号'
              },
              {
                  field: 'failReason',
                  title: '释放错误信息'
              },
              {
                  field: 'createdTime',
                  title: '创建时间'
              },]
          };
          $.table.init(options);

          //加载驾校、分校
          $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
          $('#cxSelectSchool').cxSelect({
              selects: ['schoolId', 'branchId','registrationId'],
              jsonValue: 'v',
          });

          /** 初始化时间组件 */
          layui.use('laydate', function(){
            var laydate = layui.laydate;
                laydate.render({
                elem: '#beginReleaseTime',
                type: 'datetime',
                trigger: 'click'
              });
                laydate.render({
                elem: '#endReleaseTime',
                type: 'datetime',
                trigger: 'click'
              });
              laydate.render({
                elem: '#beginPushTime',
                type: 'datetime',
                trigger: 'click'
              });
              laydate.render({
                elem: '#endPushTime',
                type: 'datetime',
                trigger: 'click'
              });
          });
      });

      function checkbankTrans(){
        var ids = $.table.selectColumns("id");
        var params;
        if($.common.isNotEmpty(ids)){
          tipMsg = "确定核对勾选" + ids.length + "条数据吗？";
          params += "&ids="+ids;
        }
        $.modal.confirm(tipMsg, function() {
          var config = {
            url: prefix + "/checkbankTrans",
            type: "post",
            dataType: "json",
            data: params,
            beforeSend: function () {
              $.modal.loading("正在处理中，请稍候...");
            },
            success: function (result) {
              if (result.code == web_status.SUCCESS) {
                $.modal.alertSuccess(result.msg);
              } else {
                $.modal.alertError(result.msg);
              }
              $.table.refresh();
              $.modal.closeLoading();
            },
          };
          $.ajax(config);
        });
      }

      function bankReturnMoneyOperate(){
        var ids = $.table.selectColumns("id");
        var params;
        if($.common.isNotEmpty(ids)){
          tipMsg = "该种情况适合银行退款，需要手动回滚的情况，确定操作勾选" + ids.length + "条数据吗？";
          params += "&ids="+ids;
        }
        $.modal.confirm(tipMsg, function() {
          var config = {
            url: prefix + "/bankReturnMoneyOperate",
            type: "post",
            dataType: "json",
            data: params,
            beforeSend: function () {
              $.modal.loading("正在处理中，请稍候...");
            },
            success: function (result) {
              if (result.code == web_status.SUCCESS) {
                $.modal.alertSuccess(result.msg);
              } else {
                $.modal.alertError(result.msg);
              }
              $.table.refresh();
              $.modal.closeLoading();
            },
          };
          $.ajax(config);
        });
      }

      /* 释放 */
      function release() {
        var ids = $.table.selectColumns("id");
        var params;
        if($.common.isNotEmpty(ids)){
            tipMsg = "确定释放勾选" + ids.length + "条数据吗？";
            params += "&ids="+ids;
        }
        $.modal.confirm(tipMsg, function() {
                var config = {
                  url: prefix + "/release",
                  type: "post",
                  dataType: "json",
                  data: params,
                  beforeSend: function () {
                    $.modal.loading("正在处理中，请稍候...");
                  },
                  success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess(result.msg);
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.table.refresh();
                    $.modal.closeLoading();
                  },
                };
                $.ajax(config);
            });
      }

      /* 全部释放 */
      function allRelease() {
        var params = $("#formId").serialize();
        var tipMsg = "确定释放所有数据吗？";
        $.modal.confirm(tipMsg, function() {
            var config = {
              url: prefix + "/allRelease",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                  $.modal.alertSuccess(result.msg);
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
        });
      }

      /* 产生新名单 */
      function generate() {
        tipMsg = "确定要产生新名单吗？";
        $.modal.confirm(tipMsg, function() {
            var config = {
                url: prefix + "/generate",
                type: "post",
                dataType: "json",
                data: '',
                beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
                },
                success: function (result) {
                  if (result.code == web_status.SUCCESS) {
                      $.modal.alertSuccess(result.msg);
                  } else {
                      $.modal.alertError(result.msg);
                  }
                  $.table.refresh();
                  $.modal.closeLoading();
                },
            };
            $.ajax(config);
        });
      }
    </script>
  </body>
</html>
