<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增释放明单')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-superviseReleaseValidList-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">：</label>
                <div class="col-sm-8">
                    <input name="studentId" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">学号：</label>
                <div class="col-sm-8">
                    <input name="studentStunum" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="studentName" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="studentIdentity" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="studentMobile" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">：</label>
                <div class="col-sm-8">
                    <input name="schoolId" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="schoolName" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="branchId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="branchName" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="registrationId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="registrationName" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">归属科目：0=其他，1=科目一，2=科目二，3=科目三，4=科目四，5=退学：</label>
                <div class="col-sm-8">
                    <input name="subjectName" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">释放金额：</label>
                <div class="col-sm-8">
                    <input name="releaseMoney" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="releaseOrderNo" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="releaseBankNo" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="releaseTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">释放操作者：</label>
                <div class="col-sm-8">
                    <input name="releaseOperator" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">失败原因：</label>
                <div class="col-sm-8">
                    <input name="failReason" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="pushtime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">条目创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="createdTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/superviseReleaseValidList"
        $("#form-superviseReleaseValidList-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-superviseReleaseValidList-add').serialize());
            }
        }

        $("input[name='releaseTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='pushtime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='createdTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>