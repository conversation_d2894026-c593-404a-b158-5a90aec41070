<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('联通号码列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list">
              <ul>
                <li>
                  <label>批次：</label>
                  <input type="text" name="batch" placeholder="请输入批次" />
                </li>
                <li>
                  <label>号码：</label>
                  <input type="text" name="number" placeholder="请输入号码" />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-danger multiple disabled"
            onclick="$.operate.removeAll()"
            shiro:hasPermission="business:unicomNumber:remove"
          >
            <i class="fa fa-remove"></i> 删除
          </a>
          <a
            class="btn btn-info"
            onclick="importUnicomNumberData()"
            shiro:hasPermission="business:unicomNumber:import"
          >
            <i class="fa fa-upload"></i> 导入
          </a>
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:unicomNumber:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:unicomNumber:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:unicomNumber:remove')}]];
      var prefix = ctx + "business/unicomNumber";

      $(function() {
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              sortName: "batch",
              sortOrder: "desc",
              modalName: "联通号码",
              columns: [{
                  checkbox: true
              },
              {
                  field: 'id',
                  title: '主键',
                  visible: false
              },
              {
                  field: 'batch',
                  title: '批次'
              },
              {
                  field: 'number',
                  title: '号码'
              },
              {
                  field: 'createdTime',
                  title: '导入时间'
              },]
          };
          $.table.init(options);
      });

      /* 导入联通号码数据 */
      function importUnicomNumberData() {
        table.set();
        top.layer.open({
            type: 1,
            area: ['400px', '230px'],
            fix: false,
            maxmin: true,
            shade: 0.3,
            title: '导入号码卡',
            content: $('#importNumberData').html(),
            btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
            shadeClose: true,
            btn1: function(index, layero){
                var file = layero.find('#file').val();
                if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                    $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                    return false;
                }
                var index = top.layer.load(2, {shade: false});
                $.modal.disable();
                var formData = new FormData(layero.find('form')[0]);
                $.ajax({
                    url: prefix + '/importNumberData',
                    data: formData,
                    cache: false,
                    contentType: false,
                    processData: false,
                    timeout : 180000,
                    type: 'POST',
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                          $.modal.close(index);
                            $.modal.closeAll();
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else if (result.code == web_status.WARNING) {
                          $.modal.close(index);
                            $.modal.enable();
                            $.modal.alertWarning(result.msg)
                        } else {
                            $.modal.close(index);
                            $.modal.enable();
                            $.modal.alertError(result.msg);
                        }
                    },
                    complete: function () {
                      layero.find('#file').val('');
                    }
                });
            }
              });
          }

          /* 导出联通号码 */
          function exportSelected() {
            var params = $("#formId").serialize();
            var ids = $.table.selectColumns("id");
            var tipMsg = "确定导出所有数据吗？";
            if($.common.isNotEmpty(ids)){
                tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
                params += "&ids="+ids;
            }
            $.modal.confirm(tipMsg, function() {
                var config = {
                  url: prefix + "/export",
                  type: "post",
                  dataType: "json",
                  data: params,
                  beforeSend: function () {
                    $.modal.loading("正在处理中，请稍候...");
                  },
                  success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                  },
                };
                $.ajax(config);
            });
          }
    </script>
  </body>

  // 号码卡导入区域
  <script id="importNumberData" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                <a onclick="$.table.importNoOpenSimTemplate('/business/unicomNumber/importNumberTemplate')" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
  </script>
</html>
