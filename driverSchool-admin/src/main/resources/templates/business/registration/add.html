<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org"
xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('新增报名点')" />
    <th:block th:include="include :: upload-img-css" />
    <th:block th:include="include :: datetimepicker-css" />
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJsCode:'7a9e030c8ccd0c69f4dc506c177e53f9',
        }
    </script>
    <script src="https://webapi.amap.com/maps?v=2.0&key=e7be917da311ee7c80cf7865191e1ca4&plugin=AMap.PolygonEditor"></script>
    <script src="https://a.amap.com/jsapi_demos/static/demo-center/js/demoutils.js"></script>
    <link th:href="@{/css/wind_info_css.css}" rel="stylesheet"/>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-registration-add">
            <div class="row">
                <div class="col-sm-12" style="height: 680px;">
                    <div class="col-sm-7">
                        <div id="container"></div>
                    </div>
                    <div class="col-sm-5">
                        <h4 class="form-header h4">报名点管理信息</h4>

                        <div id="cxSelectSchool" class="form-group">
                            <label class="col-sm-3 control-label is-required">所属学校：</label>
                            <div class="col-sm-4">
                                <select id="schoolId" name="schoolId" class="schoolId form-control m-b" data-first-title="所属驾校" required>
                                </select>

                            </div>
                            <div class="col-sm-4">
                                <select id="branchId" name="branchId" class="branchId form-control m-b" data-first-title="所属分校">
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">报名点名称：</label>
                            <div class="col-sm-8">
                                <input id="name" name="name" class="form-control onlyChineseEnglishNumbers" type="text" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">联系人：</label>
                            <div class="col-sm-8">
                                <input id="schoolContactName" name="schoolContactList[0].name" class="form-control" type="text" required maxlength="20">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">联系电话：</label>
                            <div class="col-sm-8">
                                <input name="schoolContactList[0].tel" class="form-control" type="text" placeholder="固话或者手机号码" required >
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">投诉电话：</label>
                            <div class="col-sm-8">
                                <input name="complaintTel" class="form-control" type="text" placeholder="固话或者手机号码">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">经度：</label>
                            <div class="col-sm-8">
                                <input id="longitude"  name="longitude" class="form-control" required>
                            </div>
                            <label class="col-sm-3 control-label is-required">纬度：</label>
                            <div class="col-sm-8">
                                <input id="latitude"  name="latitude" class="form-control" required>
                            </div>
                            <label class="col-sm-3 control-label ">坐标地址：</label>
                            <div class="col-sm-8">
                                <input id="gpsAddress" name="gpsAddress"  class="form-control" type="text" >
                            </div>
                            <button id="location" class="btn btn-danger btn-circle" type="button"><i class="fa fa-map-marker"></i></button>
                        </div>

                        <div id="cxSelect" class="form-group">
                            <label class="col-sm-3 control-label is-required">地址：</label>
                            <div class="col-sm-3">
                                <select id="province" name="province" class="province form-control" data-first-title="所属省份" required>
                                    <option value="广东省" selected>广东省</option>
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <select id="city" name="city" class="city form-control" data-first-title="所属城市" required>
                                    <option value="东莞市" selected>东莞市</option>
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <select id="town" name="town" class="town form-control" data-first-title="所属县/镇/区">
                                    <option value="">所属县/镇/区</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">具体地址：</label>
                            <div class="col-sm-8">
                                <input id="address" name="address" class="form-control" type="text" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">驾照类型：</label>
                            <div class="col-sm-8" th:with="type=${@dict.getType('license_types')}">
                                <label th:each="dict : ${type}" class="check-box">
                                    <input name="licenseTypes" type="checkbox" th:value="${dict.dictValue}" th:text="${dict.dictLabel}" required>
                                </label>
                            </div>

                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">收费模式：</label>
                            <div class="col-sm-8" th:with="type=${@dict.getType('t_charge_modes')}">
                                <label th:each="dict : ${type}" class="check-box">
                                    <input name="chargeModes" type="checkbox" th:value="${dict.dictValue}" th:text="${dict.dictLabel}" required>
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">图片：</label>
                            <div class="col-sm-8">
                                <div class="image-box">
                                </div>
                                <a shiro:hasAnyPermissions="business:registration:imgeditor" class="btn btn-success" href="javascript:addImage();">
                                    <label>选择文件</label>
                                    <input id="add-input" type="file" accept="image/*" style="display: none" onchange="selectImage(this);">
                                </a>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">营业执照号码：</label>
                            <div class="col-sm-8">
                                <input  name="schoolBusinessLicenseList[0].no" class="form-control" type="text" placeholder="营业执照号码">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">营业执照有效期：</label>
                            <div class="col-sm-8">
                                <label class="check-box">
                                    <input name="schoolBusinessLicenseList[0].isExpiration" type="checkbox" th:value="1" th:text="永久">
                                </label>
                                <input  name="schoolBusinessLicenseList[0].expiration" class="form-control" type="text" placeholder="营业执照有效期" >
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-sm-3 control-label">营业时间：</label>
                            <div class="col-sm-8">
                                <input  name="businessTime" class="form-control" type="text" placeholder="营业时间">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">状态：</label>
                            <div class="col-sm-8">
                                <select name="status" class="form-control m-b" th:with="type=${@dict.getType('t_school_status')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                        
                         <div class="form-group">
                            <label class="col-sm-3 control-label">等级：</label>
                            <div class="col-sm-8">
                                <select name="level" class="form-control m-b" th:with="type=${@dict.getType('t_school_level')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: upload-img-js" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:src="@{/ruoyi/registrationMap.js}"></script>
    <script th:inline="javascript">
        var prefix = ctx + "business/registration"

        var maxImage = 5;
        var currentla ;
        var currentlg ;
        var address ;
        var circle ;
        var circleName ;
        var circleId ;
        var la ;
        var lg ;
        var center;
        $.validator.addMethod("onlyChineseEnglishNumbers", function(value, element) {
            return this.optional(element) || /^[a-zA-Z0-9\u4e00-\u9fa5]+$/.test(value);
        }, "只能输入中英文和数字");

        $("#form-registration-add").validate({
            onkeyup: false,
            focusCleanup: true,
            rules:{
                name:{
                    maxlength:20,
                    remote: {
                        url: prefix + "/checkUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "schoolId":function () {
                                return $.common.trim($.form.selectSelects("schoolId"));
                            },
                            "branchId":function () {
                                return $.common.trim($.form.selectSelects("branchId"));
                            },
                            "name": function() {
                                return $.common.trim($("#name").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        },
                         // 添加自定义验证规则
                        onlyChineseEnglishNumbers: true
                    }
                },
                'schoolContactList[0].tel':{
                    isPhoneOrTel: true
                },
                complaintTel:{
                    isPhoneOrTel: true
                }
            },
            messages: {
                "name": {
                    remote: "该报名点名称已经存在",
                    onlyChineseEnglishNumbers: "只能输入中英文和数字"
                }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                submitCustom("/add","schoolImages");
            }
        }


        $(function() {
            //加载驾校、分校
            $.cxSelect.defaults.url = ctx + "business/school/schoolData";
            $('#cxSelectSchool').cxSelect({
                selects: ['schoolId', 'branchId'],
                jsonValue: 'v',
            });


            // 加载省市区
            $.cxSelect.defaults.url = ctx + "business/area/areaData";
            $('#cxSelect').cxSelect({
                selects: ['province', 'city', 'town'],
                nodata: 'none'
            });

        });

        $("input[name='schoolBusinessLicenseList[0].expiration']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $('input').on('ifChanged', function(obj){
            var type = $(this).val();
            var checked = obj.currentTarget.checked;
            if (type == 1) {
                if (checked) {
                    $("input[name='schoolBusinessLicenseList[0].expiration']").attr("disabled","disabled");
                } else {
                    $("input[name='schoolBusinessLicenseList[0].expiration']").removeAttr('disabled');
                }
            }
        })

    </script>
</body>
</html>