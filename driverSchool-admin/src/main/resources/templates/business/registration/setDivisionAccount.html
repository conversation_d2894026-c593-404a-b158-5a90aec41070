<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <th:block th:include="include :: header('设置收学费账户')" />
  </head>
  <body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <form
        class="form-horizontal m"
        id="form-account-edit-union"
        th:object="${divisionAccountVo}"
      >
        <input
          name="refId"
          th:field="*{refId}"
          class="form-control"
          type="hidden"
        />
        <div class="panel-body">
          <div class="form-group">
            <div class="form-group">
              <label class="col-sm-8 control-label" style="color: blue"
                >科目合格释放账户设置（1500.00元）</label
              >
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label">账户号：</label>
            <div class="col-sm-8">
              <input
                name="accountNo1"
                id="accountNo1"
                class="form-control"
                type="text"
                th:field="*{accountNo1}"
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label">账户名：</label>
            <div class="col-sm-8">
              <input
                name="accountName1"
                id="accountName1"
                class="form-control"
                type="text"
                th:field="*{accountName1}"
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label">客户号：</label>
            <div class="col-sm-8">
              <input
                name="customerNo1"
                id="customerNo1"
                class="form-control"
                type="text"
                th:field="*{customerNo1}"
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label">客户密钥：</label>
            <div class="col-sm-8">
              <input
                name="secretKey1"
                id="secretKey1"
                class="form-control"
                type="text"
                th:field="*{secretKey1}"
              />
            </div>
          </div>

          <div class="form-group">
            <div class="form-group">
              <label class="col-sm-8 control-label" style="color: blue"
                >加盟费释放账户设置</label
              >
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label">账户号：</label>
            <div class="col-sm-8">
              <input
                name="accountNo2"
                id="accountNo2"
                class="form-control"
                type="text"
                th:field="*{accountNo2}"
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label">账户名：</label>
            <div class="col-sm-8">
              <input
                name="accountName2"
                id="accountName2"
                class="form-control"
                type="text"
                th:field="*{accountName2}"
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label">客户号：</label>
            <div class="col-sm-8">
              <input
                name="customerNo2"
                id="customerNo2"
                class="form-control"
                type="text"
                th:field="*{customerNo2}"
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label">客户密钥：</label>
            <div class="col-sm-8">
              <input
                name="secretKey2"
                id="secretKey2"
                class="form-control"
                type="text"
                th:field="*{secretKey2}"
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label">分账金额：</label>
            <div class="col-sm-8">
              <input
                name="divisionFee"
                id="divisionFee"
                class="form-control"
                type="text"
                th:field="*{divisionFee}"
              />
            </div>
          </div>

          <div class="form-group">
            <div class="form-group">
              <label class="col-sm-8 control-label" style="color: blue"
                >剩余学费收款账户设置</label
              >
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label">账户号：</label>
            <div class="col-sm-8">
              <input
                name="accountNo3"
                id="accountNo3"
                class="form-control"
                type="text"
                th:field="*{accountNo3}"
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label">账户名：</label>
            <div class="col-sm-8">
              <input
                name="accountName3"
                id="accountName3"
                class="form-control"
                type="text"
                th:field="*{accountName3}"
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label">客户号：</label>
            <div class="col-sm-8">
              <input
                name="customerNo3"
                id="customerNo3"
                class="form-control"
                type="text"
                th:field="*{customerNo3}"
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label">客户密钥：</label>
            <div class="col-sm-8">
              <input
                name="secretKey3"
                id="secretKey3"
                class="form-control"
                type="text"
                th:field="*{secretKey3}"
              />
            </div>
          </div>
        </div>
      </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
      var prefix = ctx + "business/registration";

      function submitHandler() {
        if ($.validate.form()) {
          var tipMsg =
            "设置后，需要审核之后才能生效，请通知相关权限人员进行审核。";
          var params = $("#form-account-edit-union").serializeArray();
          params.push({ name: "type", value: 3 });
          $.modal.confirm(tipMsg, function () {
            $.operate.save(prefix + "/setDivisionAccountSave", params);
          });
        }
      }
    </script>
  </body>
</html>
