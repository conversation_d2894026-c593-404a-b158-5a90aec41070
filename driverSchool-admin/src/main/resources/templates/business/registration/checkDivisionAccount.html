<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <th:block th:include="include :: header('审核收学费账户')" />
  </head>
  <body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <form
        class="form-horizontal m"
        id="form-account-edit-union"
        th:object="${divisionAccountVo}"
      >
        <input
          name="refId"
          th:field="*{refId}"
          class="form-control"
          type="hidden"
        />
        <div class="panel-body">
          <div class="form-group">
            <div class="form-group">
              <label class="col-sm-8 control-label" style="color: blue"
                >科目合格释放账户设置（1500.00元）</label
              >
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label is-required">账户号：</label>
            <div class="col-sm-8">
              <input
                name="accountNo1"
                id="accountNo1"
                class="form-control"
                type="text"
                th:field="*{accountNo1}"
                readonly
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label is-required">账户名：</label>
            <div class="col-sm-8">
              <input
                name="accountName1"
                id="accountName1"
                class="form-control"
                type="text"
                th:field="*{accountName1}"
                readonly
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label is-required">客户号：</label>
            <div class="col-sm-8">
              <input
                name="customerNo1"
                id="customerNo1"
                class="form-control"
                type="text"
                th:field="*{customerNo1}"
                readonly
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label is-required">客户密钥：</label>
            <div class="col-sm-8">
              <input
                name="secretKey1"
                id="secretKey1"
                class="form-control"
                type="text"
                th:field="*{secretKey1}"
                readonly
              />
            </div>
          </div>

          <div class="form-group">
            <div class="form-group">
              <label class="col-sm-8 control-label" style="color: blue"
                >加盟费释放账户设置</label
              >
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label is-required">账户号：</label>
            <div class="col-sm-8">
              <input
                name="accountNo2"
                id="accountNo2"
                class="form-control"
                type="text"
                th:field="*{accountNo2}"
                readonly
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label is-required">账户名：</label>
            <div class="col-sm-8">
              <input
                name="accountName2"
                id="accountName2"
                class="form-control"
                type="text"
                th:field="*{accountName2}"
                readonly
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label is-required">客户号：</label>
            <div class="col-sm-8">
              <input
                name="customerNo2"
                id="customerNo2"
                class="form-control"
                type="text"
                th:field="*{customerNo2}"
                readonly
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label is-required">客户密钥：</label>
            <div class="col-sm-8">
              <input
                name="secretKey2"
                id="secretKey2"
                class="form-control"
                type="text"
                th:field="*{secretKey2}"
                readonly
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label is-required">分账金额：</label>
            <div class="col-sm-8">
              <input
                name="divisionFee"
                id="divisionFee"
                class="form-control"
                type="text"
                th:field="*{divisionFee}"
                readonly
              />
            </div>
          </div>

          <div class="form-group">
            <div class="form-group">
              <label class="col-sm-8 control-label" style="color: blue"
                >剩余学费收款账户设置</label
              >
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label is-required">账户号：</label>
            <div class="col-sm-8">
              <input
                name="accountNo3"
                id="accountNo3"
                class="form-control"
                type="text"
                th:field="*{accountNo3}"
                readonly
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label is-required">账户名：</label>
            <div class="col-sm-8">
              <input
                name="accountName3"
                id="accountName3"
                class="form-control"
                type="text"
                th:field="*{accountName3}"
                readonly
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label is-required">客户号：</label>
            <div class="col-sm-8">
              <input
                name="customerNo3"
                id="customerNo3"
                class="form-control"
                type="text"
                th:field="*{customerNo3}"
                readonly
              />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label is-required">客户密钥：</label>
            <div class="col-sm-8">
              <input
                name="secretKey3"
                id="secretKey3"
                class="form-control"
                type="text"
                th:field="*{secretKey3}"
                readonly
              />
            </div>
          </div>
        </div>
      </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
      var prefix = ctx + "business/registration";

      function submitHandler() {
        if ($.validate.form()) {
          var params = $("#form-account-edit-union").serializeArray();
          $.operate.save(prefix + "/checkDivisionAccountSave", params);
        }
      }
    </script>
  </body>
</html>
