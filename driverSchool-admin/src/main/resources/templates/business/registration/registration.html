<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('报名点列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>
                <li>
                  <label>分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="branchId"
                    name="branchId"
                    class="branchId"
                    data-first-title="选择分校"
                  ></select>
                </li>
                <li>
                  <label style="width: 100px">报名点名称：</label>
                  <input type="text" name="name" />
                </li>
                <li>
                  <label>状态：</label>
                  <select
                    name="status"
                    th:with="type=${@dict.getType('t_school_status')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label  style="width: 110px">二维码审核状态：</label>
                  <select
                          name="qrcodeAuditStatus"
                          th:with="type=${@dict.getType('qrcode_audit_status')}"
                  >
                    <option value="">所有</option>
                    <option
                            th:each="dict : ${type}"
                            th:text="${dict.dictLabel}"
                            th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li class="select-time">
                  <label style="width: 120px; display: inline-block"
                  >二维码审核时间：
                  </label>
                  <input
                          style="width: 135px"
                          type="text"
                          class="layui-input dateTime"
                          id="beginLastAuditTime"
                          placeholder="开始时间"
                          name="params[beginLastAuditTime]"
                  />
                  <span>-</span>
                  <input
                          style="width: 135px"
                          type="text"
                          class="layui-input dateTime"
                          id="endLastAuditTime"
                          placeholder="结束时间"
                          name="params[endLastAuditTime]"
                  />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-success"
            onclick="add()"
            shiro:hasPermission="business:registration:add"
          >
            <i class="fa fa-plus"></i> 添加
          </a>
          <a
            class="btn btn-primary single disabled"
            onclick="edit()"
            shiro:hasPermission="business:registration:edit"
          >
            <i class="fa fa-edit"></i> 修改
          </a>
          <a
            class="btn btn-danger multiple disabled"
            onclick="$.operate.removeAll()"
            shiro:hasPermission="business:registration:remove"
          >
            <i class="fa fa-remove"></i> 删除
          </a>
          <a
            class="btn btn-warning single disabled"
            onclick="view('',1300,'')"
            shiro:hasPermission="business:registration:view"
          >
            查看
          </a>
          <a class="btn btn-info"
             onclick="$.table.importExcel()"
             shiro:hasPermission="business:registration:import"
          >
            <i class="fa fa-upload"></i> 导入
          </a>
          <a
            class="btn btn-success"
            onclick="exportSelected()"
            shiro:hasPermission="business:registration:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
          <a
            class="btn btn-primary single disabled"
            onclick="setDivisionAccount()"
            shiro:hasPermission="business:registration:setDivisionAccount"
          >
            设置收款账户
          </a>
          <a
            class="btn btn-primary single disabled"
            onclick="checkDivisionAccount()"
            shiro:hasPermission="business:registration:checkDivisionAccount"
          >
            审核收款账户
          </a>
          <a
            class="btn btn-primary single disabled"
            onclick="setStudyCenterCode()"
            shiro:hasPermission="business:registration:setStudyCenterCode"
          >
            设置签约代码
          </a>
          <a
            class="btn btn-primary single disabled"
            onclick="reviewStudentType()"
            shiro:hasPermission="business:registration:reviewStudentType"
          >
            审核学员设置
          </a>
          <a class="btn btn-info multiple disabled"
             onclick="submitAudit()"
             shiro:hasPermission="business:registration:submitaudit"
          >
            <i class="fa fa-address-book"></i> 提交二维码审核申请
          </a>
          <a class="btn btn-warning multiple disabled"
             onclick="audit()"
             shiro:hasPermission="business:registration:audit"
          >
            <i class="fa fa-address-book"></i> 审核二维码
          </a>
          <a class="btn btn-warning multiple disabled"
             onclick="downloadQrcode()"
             shiro:hasPermission="business:registration:downloadqrcode"
          >
            <i class="fa fa-address-book"></i> 下载二维码
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:registration:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:registration:remove')}]];
      var licenseTypesDatas = [[${@dict.getType('license_types')}]];
      var chargeModesDatas = [[${@dict.getType('t_charge_modes')}]];
      var statusDatas = [[${@dict.getType('t_school_status')}]];
      var viewPerm = [[${@permission.hasPermi('business:school:view')}]];
      var qrcodeAuditStatus = [[${@dict.getType('qrcode_audit_status')}]];
      var prefix = ctx + "business/registration";
      $(function () {
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              detailUrl: prefix + "/view/{id}",
              exportUrl: prefix + "/export",
              importTemplateUrl: prefix + "/importTemplate",
              importUrl: prefix + "/importData",
              submitAuditUrl: prefix + "/submitAudit",
              auditStatusUrl: prefix+"/audit",
              downloadQrcode: prefix+"/downloadQrcode",
              getNotPassQrcodeInfoUrl: prefix+"/getnotpassqrcodeinfo",
              modalName: "报名点",
              showPageGo: true,
              columns: [{
                  checkbox: true
              },
                  {
                      field: 'id',
                      title: '',
                      visible: false
                  },
                  {
                      field: 'name',
                      title: '报名点名称'
                  },
                  {
                      field: 'buslic',
                      title: '营业执照',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(row.schoolBusinessLicenseList != null)) {
                              var no = "";
                              $.each(row.schoolBusinessLicenseList, function (i, item) {
                                  if (i == 0) {
                                      no = item.no;
                                      return;
                                  }
                              })
                              return no;
                          } else {
                              return "-";
                          }
                      }
                  },

                  {
                      field: 'perDate',
                      title: '有效期',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(row.schoolBusinessLicenseList != null)) {
                              var expiration = "";
                              $.each(row.schoolBusinessLicenseList, function (i, item) {
                                  if (i == 0) {
                                      expiration = item.expiration;
                                      return;
                                  }
                              })
                              if (expiration != 1) {
                                  return expiration;
                              } else {
                                  return "永久";
                              }
                          } else {
                              return "-";
                          }
                      }
                  },

                  {
                      field: 'businessTime',
                      title: '营业时间'
                  },

                  {
                      field: 'buslicStatus',
                      title: '营业执照状态',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(row.schoolBusinessLicenseList != null)) {
                              var isExpiration = "";
                              var expiration = "";
                              $.each(row.schoolBusinessLicenseList, function (i, item) {
                                  if (i == 0) {
                                      isExpiration = item.isExpiration;
                                      expiration = item.expiration;
                                      return;
                                  }
                              })
                              if (expiration != 1) {
                                  return isExpiration == 1 ? '<span class="badge badge-success">有效</span>' : '<span class="badge badge-danger">无效</span>';
                              } else {
                                  return "<span class=\"badge badge-success\">有效</span>";
                              }
                          } else {
                              return "-";
                          }
                      }
                  },

                  {
                      field: 'licenseTypes',
                      title: '驾照类型',
                      formatter: function (value, row, index) {
                          var json = JSON.parse(value);
                          var actions = [];
                          $.each(json, function (i, item) {
                              var licenseType = item;
                              if ($.common.isNotEmpty(licenseType)) {
                                  actions.push(licenseType);
                              }
                          });
                          return actions.join('，');
                      }
                  },
                  {
                      field: 'chargeModes',
                      title: '收费模式',
                      formatter: function (value, row, index) {
                          var json = JSON.parse(value);
                          var actions = [];
                          $.each(json, function (i, item) {
                              var licenseType = item;
                              if ($.common.isNotEmpty(licenseType)) {
                                  actions.push(licenseType);
                              }
                          });
                          return actions.join('，');
                      }

                  },
                  {
                      field: 'id',
                      title: '联系人',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(row.schoolContactList != null)) {
                              var name = "";
                              $.each(row.schoolContactList, function (i, item) {
                                  if (i == 0) {
                                      name = item.name;
                                      return;
                                  }
                              })
                              return name;
                          } else {
                              return "-";
                          }
                      }
                  },

                  {
                      field: 'id',
                      title: '联系电话',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(row.schoolContactList != null)) {
                              var tel = "";
                              $.each(row.schoolContactList, function (i, item) {
                                  if (i == 0) {
                                      tel = item.tel;
                                      return;
                                  }
                              })
                              return tel;
                          } else {
                              return "-";
                          }
                      }
                  },
                  {
                      field: 'longitude',
                      title: '经度'
                  },
                  {
                      field: 'latitude',
                      title: '纬度'
                  },
                  {
                      field: 'address',
                      title: '具体地址'
                  },
                  {
                      field: 'schoolId',
                      title: '所属驾校',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              if (row.school != null){
                                  return row.school.name;
                              }
                          }
                      }
                  },

                  {
                      field: 'branchId',
                      title: '所属分校',
                      formatter: function(value, row, index) {
                          if ($.common.isNotEmpty(value)){
                              if (row.branch != null){
                                  return row.branch.name;
                              }
                          }
                      }
                  },

                  {
                      field: 'status',
                      title: '状态',
                      formatter: function (value, row, index) {
                          return $.table.selectDictLabel(statusDatas, value);
                      }
                  },
                {
                  field: 'qrcodeAuditStatus',
                  title: '二维码申请状态',
                  formatter: function (value,row,index){
                    return $.table.selectDictLabel(qrcodeAuditStatus,value);
                  }
                },
                {
                  field: 'lastAuditTime',
                  title: '最近审核时间'
                },

              ]
          };
          $.table.init(options);

          //隐藏列
          if(viewPerm == 'hidden') {
              $.table.hideColumn("buslic");
              $.table.hideColumn("perDate");
              $.table.hideColumn("buslicStatus");
          }

          //加载驾校、分校
          $.cxSelect.defaults.url = ctx + "business/school/schoolData";
          $('#cxSelectSchool').cxSelect({
              selects: ['schoolId', 'branchId'],
              jsonValue: 'v',
          });


      });

      function add(id) {
          table.set();
          $.modal.open("添加" + table.options.modalName, $.operate.addUrl(id),1300);
      }

      function edit(id) {
          table.set();
          if ($.common.isEmpty(id) && table.options.type == table_type.bootstrapTreeTable) {
              var row = $("#" + table.options.id).bootstrapTreeTable('getSelections')[0];
              if ($.common.isEmpty(row)) {
                  $.modal.alertWarning("请至少选择一条记录");
                  return;
              }
              var url = table.options.updateUrl.replace("{id}", row[table.options.uniqueId]);
              $.modal.open("修改" + table.options.modalName, url);
          } else {
              $.modal.open("修改" + table.options.modalName, $.operate.editUrl(id),1300);
          }
      }

      function view(id,width, height) {
          table.set();
          var _url = $.operate.detailUrl(id);
          var options = {
              title: table.options.modalName + "详细",
              width: width,
              height: height,
              url: _url,
              skin: 'layui-layer-gray',
              btn: ['关闭'],
              yes: function (index, layero) {
                  $.modal.close(index);
              }
          };
          $.modal.openOptions(options);
      }

      //驾校提交审核申请
      function submitAudit(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId)
                ? $.table.selectFirstColumns()
                : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
          $.modal.alertWarning("请至少选择一条记录");
          return;
        }
        var dataList = $('#bootstrap-table').bootstrapTable('getSelections');
        var flag = false;
        //已提交审核的数据不能重复审核
        try {
          dataList.forEach(function(item) {
            if(item.qrcodeAuditStatus ===1) {
              flag = true;
              throw new Error('auditStatus error')
            }
          });
        }catch (e){

        }
        if(flag) {
          $.modal.alertWarning("选中的行记录中，存在一条申请状态为【待审核】的数据，请勿重复提交申请");
          return;
        }
        $.modal.confirm(
                "确认要提交" + rows.length + "条数据来申请审核吗?",
                function () {
                  var url = table.options.submitAuditUrl;
                  var data = { ids: rows.join() };
                  $.operate.submit(url, "post", "json", data);
                }
        );
      }

      //管理员进行审核
      function audit(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId)
                ? $.table.selectFirstColumns()
                : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
          $.modal.alertWarning("请至少选择一条记录");
          return;
        }
        //获取选中行
        var dataList = $('#bootstrap-table').bootstrapTable('getSelections');
        var flag = false;
        //已提交审核的数据不能重复审核
        try {
          dataList.forEach(function(item) {
            if(item.qrcodeAuditStatus !== 1) {
              flag = true;
              throw new Error('auditStatus error')
            }
          });
        }catch (e){

        }
        if(flag) {
          $.modal.alertWarning("选中的行记录中，存在申请状态不是【待审核】的数据，请确认。");
          return;
        }
        layer.open({
          id:'001',
          title: '批量审核二维码申请',
          area: ['500px', '300px'],
          content:  "即将对" + rows.length + "条数据进行审核", //这里content是一个普通的String
          btn: ['批量合格','批量不合格','关闭'],
          yes:function (index){
            $.modal.confirm(
                    "确定要将所选的数据审核为【通过】吗？",
                    function () {
                      var url = table.options.auditStatusUrl;
                      var data = { ids: rows.join(),auditStatus: 2 };
                      $.operate.submit(url, "post", "json", data);
                    }
            );
            layer.closeAll();
          },
          btn2:function (index) {
            $.modal.confirm(
                    "确定要将所选的数据审核为【不合格】吗？",
                    function () {
                      var url = table.options.auditStatusUrl;
                      var data = { ids: rows.join(),auditStatus: 3 };
                      $.operate.submit(url, "post", "json", data);
                    }
            );
            layer.closeAll();
          },
          btn3: function (index, layero) {
            //关闭弹窗
            $.modal.close(index);
          }
        });
      }

      //导出二维码
      function downloadQrcode(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId)
                ? $.table.selectFirstColumns()
                : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
          $.modal.alertWarning("请至少选择一条记录");
          return;
        }
        //获取选中行
        var dataList = $('#bootstrap-table').bootstrapTable('getSelections');
        var flag = false;
        //已提交审核的数据不能重复审核
        try {
          dataList.forEach(function(item) {
            if(item.qrcodeAuditStatus !== 2) {
              flag = true;
              throw new Error('auditStatus error')
            }
          });
        }catch (e){

        }
        if(flag) {
          $.modal.alertWarning("只能导出二维码审核状态为【通过】的图片");
          return;
        }
        $.modal.confirm(
                "确认要下载" + rows.length + "条数据来申请审核吗?",
                function () {
                  //loading层
                  var loadingIndex = layer.load(2, { //icon支持传入0-2
                    shade: [0.5, '#fff'], //0.5透明度的灰色背景
                    content: '正在为您导出二维码图片，图片较大，请耐心等候...',
                    success: function (layero) {
                      layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '160px'
                      });
                    }
                  });
                  $.ajax({
                    type: "post",
                    url:  table.options.downloadQrcode,
                    data: {
                      "ids":rows.join()
                    },
                    xhrFields: {
                      responseType: 'blob'  // 告诉 jQuery 把响应作为二进制数据（Blob）
                    },
                    success: function (blob, status, xhr) {
                      // 关闭 layui 遮罩层
                      layer.close(loadingIndex);

                      // 从 Content-Disposition 获取文件名
                      var disposition = xhr.getResponseHeader('Content-Disposition');
                      var fileName = disposition ? disposition.split('filename=')[1] : 'downloaded-file';

                      // 如果文件名包含引号，移除引号
                      if (fileName && fileName.indexOf('"') === 0) {
                        fileName = fileName.substring(1, fileName.length - 1);
                      }

                      // 创建 Blob URL
                      var url = window.URL.createObjectURL(blob);

                      // 创建临时下载链接
                      var a = document.createElement('a');
                      a.href = url;
                      a.download = fileName;
                      document.body.appendChild(a);
                      a.click();  // 触发下载
                      document.body.removeChild(a);  // 下载完成后移除临时链接
                      window.URL.revokeObjectURL(url);  // 释放内存
                    },
                    error: function (xhr, status, error) {
                      // 关闭 layui 遮罩层
                      layer.close(loadingIndex);
                      console.error("文件下载失败：", error);
                    }
                  });
                }
        );
      }

      // 导出数据
      function exportSelected() {
          var ids = $.table.selectColumns("id");
          var schoolId = $('#schoolId').val(); //驾校id
          var branchId = $('#branchId').val(); //分校id
          var name = $("input[name='name']").val(); //分校名称
          var status = $('select[name="status"]').val(); //是否审核

          let fromData = new FormData();
          fromData.append('schoolId', schoolId != null ? schoolId : '');
          fromData.append('branchId', branchId != null ? branchId : '' );
          fromData.append('name', name != null ? name : '');
          fromData.append('status', status != null ? status : '');

          var tipMsg = "确定导出所有数据吗？";
          if($.common.isNotEmpty(ids)){
              tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
              fromData.append('ids', ids);
          }
          $.modal.confirm(tipMsg, function() {
                  $.ajax({
                      url: prefix + "/export",
                      data: fromData,
                      type: "post",
                      processData: false,
                      contentType: false,
                      async: true,
                      success: function(result) {
                          if (result.code == web_status.SUCCESS) {
                              window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                          } else {
                              $.modal.alertError(result.msg);
                          }
                      }
                  })
              });
      }

      /* 查看报名点详情 */
      $('#bootstrap-table').on('dbl-click-row.bs.table', function (e, row, $element) {
          table.set();
            var _url = $.operate.detailUrl(row.id);
            var options = {
                title: table.options.modalName + "详细",
                width: "1300",
                height: "",
                url: _url,
                skin: 'layui-layer-gray',
                btn: ['关闭'],
                yes: function (index, layero) {
                    $.modal.close(index);
                }
            };
            $.modal.openOptions(options);
        });

        /* 设置收学费账户 */
      function setDivisionAccount() {
        var rows = $.table.selectRows();
        if (rows.length == 0) {
          $.modal.alertWarning("请至少选择一条记录");
          return;
        }
        var url = prefix + "/setDivisionAccount/"+rows[0].id;
        $.modal.openPro('设置收学费账户', url, '提交', '', '900');
      }

      /* 审核收学费账户 */
      function checkDivisionAccount() {
        var rows = $.table.selectRows();
        if (rows.length == 0) {
          $.modal.alertWarning("请至少选择一条记录");
          return;
        }
        //检查收学费账户是否已经审核过
        var isCheck = 0;
        var params = 'id='+rows[0].id;
        var config = {
            url: prefix + "/isCheck",
            type: "post",
            dataType: "json",
            data: params,
            beforeSend: function () {
            $.modal.loading("正在处理中，请稍候...");
            },
            success: function (result) {
            if (result.code == web_status.SUCCESS) {
                isCheck = result.data;
                startCheck(isCheck, rows);
            } else {
                $.modal.alertError(result.msg);
            }
            $.modal.closeLoading();
            },
        };
        $.ajax(config);
      }

      function startCheck(isCheck, rows) {
        var url = prefix + "/checkDivisionAccount/"+rows[0].id;
        if (isCheck == 0) {
            $.modal.openPro('审核收学费账户', url, '审核通过', '', '900');
        }else {
            var options = {
              title: "审核收学费账户",
              width: "",
              height: "900",
              url: url,
              skin: 'layui-layer-gray',
              btn: ['关闭'],
              yes: function (index, layero) {
                  $.modal.close(index);
              }
          };
          $.modal.openOptions(options);
        }
      }

      /* 设置签约代码 */
      function setStudyCenterCode() {
        var rows = $.table.selectRows();
        if (rows.length == 0) {
          $.modal.alertWarning("请至少选择一条记录");
          return;
        }
        var _url = prefix + "/setStudyCenterCode?schoolId="+rows[0].schoolId+"&registrationId="+rows[0].id;
        var options = {
              title: "设置签约代码",
              width: "",
              height: "",
              url: _url,
              skin: 'layui-layer-gray',
              btn: ['关闭'],
              yes: function (index, layero) {
                  $.modal.close(index);
              }
          };
          $.modal.openOptions(options);
      }

      /* 审核学员设置 */
      function reviewStudentType() {
        var rows = $.table.selectRows();
        if (rows.length == 0) {
          $.modal.alertWarning("请至少选择一条记录");
          return;
        }
        var url = prefix + "/reviewStudentType/"+rows[0].id;
        $.modal.openPro('审核学员设置', url, '提交', '', '300');
      }
    </script>
  </body>
  <!-- 导入区域 -->
  <script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
      <div class="col-xs-offset-1">
        <input type="file" id="file" name="file"/>
        <div class="mt10 pt5">
          <a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
        </div>
        <font color="red" class="pull-left mt10">
          提示：仅允许导入“xls”或“xlsx”格式文件！
        </font>
      </div>
    </form>
  </script>
</html>
