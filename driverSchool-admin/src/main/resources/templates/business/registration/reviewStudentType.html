<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <th:block th:include="include :: header('审核学员设置')" />
  </head>
  <body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <form
        class="form-horizontal m"
        id="form-reviewStudentType-edit"
        th:object="${registration}"
      >
        <input name="id" th:field="*{id}" type="hidden" />
        <div class="form-group">
          <label class="col-sm-3 control-label is-required">门店名称：</label>
          <div class="col-sm-8">
            <input
              name="name"
              th:field="*{name}"
              class="form-control"
              type="text"
              readonly
            />
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-3 control-label">审核学员设置：</label>
          <div class="col-sm-8">
            <select
              name="reviewStudentType"
              class="form-control m-b"
              th:field="*{reviewStudentType}"
              th:with="type=${@dict.getType('review_student_type')}"
            >
              <option value="">请选择</option>
              <option
                th:each="dict : ${type}"
                th:text="${dict.dictLabel}"
                th:value="${dict.dictValue}"
              ></option>
            </select>
          </div>
        </div>
      </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
      var prefix = ctx + "business/registration";
      $("#form-reviewStudentType-edit").validate({
        focusCleanup: true,
      });

      function submitHandler() {
        if ($.validate.form()) {
          $.operate.save(
            prefix + "/reviewStudentTypeSave",
            $("#form-reviewStudentType-edit").serialize()
          );
        }
      }
    </script>
  </body>
</html>
