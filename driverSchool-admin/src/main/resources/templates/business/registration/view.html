<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('查看报名点管理')" />
    <th:block th:include="include :: upload-img-css" />
    <th:block th:include="include :: datetimepicker-css" />
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJsCode:'7a9e030c8ccd0c69f4dc506c177e53f9',
        }
    </script>
    <script src="https://webapi.amap.com/maps?v=2.0&key=e7be917da311ee7c80cf7865191e1ca4&plugin=AMap.PolygonEditor"></script>
    <script src="https://a.amap.com/jsapi_demos/static/demo-center/js/demoutils.js"></script>
    <link th:href="@{/css/wind_info_css.css}" rel="stylesheet"/>
    <link href="../static/css/awesome-bootstrap-checkbox.css" th:href="@{/css/awesome-bootstrap-checkbox.css}" rel="stylesheet"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m-t" th:object="${schoolRegistration}">
        <div class="row">
            <div class="col-sm-12" style="height: 680px;">
                <div class="col-sm-7">
                    <div id="container"></div>
                </div>
                <div class="col-sm-5">
                    <h4 class="form-header h4">驾校管理信息</h4>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">所属学校：</label>
                        <div class="form-control-static" th:if="*{school != null}" th:text="*{school.name}">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">所属分校：</label>
                        <div class="form-control-static" th:text="*{branch != null? branch.name : '无'}">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">报名点名称：</label>
                        <div class="form-control-static" th:text="*{name}">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">联系人：</label>
                        <div class="form-control-static" th:if="${not #lists.isEmpty(schoolRegistration.schoolContactList)}" th:text="*{schoolContactList[0].name}">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">联系电话：</label>
                        <div class="form-control-static" th:if="${not #lists.isEmpty(schoolRegistration.schoolContactList)}" th:text="*{schoolContactList[0].tel}">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label ">经度：</label>
                        <div class="col-sm-3">
                            <input  name="longitude" th:field="*{longitude}" class="form-control" >
                        </div>
                        <label class="col-sm-3 control-label ">纬度：</label>
                        <div class="col-sm-3">
                            <input  name="latitude" th:field="*{latitude}" class="form-control"  >
                        </div>
                        <label class="col-sm-3 control-label">坐标地址：</label>
                        <div class="col-sm-6">
                            <input name="gpsAddress" th:field="*{gpsAddress}" class="form-control" type="text" >
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">具体地址：</label>
                        <div class="form-control-static" th:text="*{address}">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">驾照类型：</label>
                        <div class="col-sm-8">
                            <div class="checkbox checkbox-danger"  th:each="dict,state: ${licenseTypeList}" >
                                <input th:id="|licenseTypes${state.index}|" name="licenseTypes" type="checkbox"  th:value="${dict.dictValue}" th:checked="${dict.flag}"  disabled="disabled">
                                <label th:for="|licenseTypes${state.index}|" th:text="${dict.dictLabel}">
                                </label>
                            </div>
                        </div>

                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">收费模式：</label>
                        <div class="col-sm-8">
                            <div class="checkbox checkbox-danger"  th:each="dict,state: ${chargeModesList}" >
                                <input th:id="|chargeModes{state.index}|" name="chargeModes" type="checkbox"  th:value="${dict.dictValue}" th:checked="${dict.flag}"  disabled="disabled">
                                <label th:for="|chargeModes${state.index}|" th:text="${dict.dictLabel}">
                                </label>
                            </div>

                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">图片：</label>
                        <div class="col-sm-8">
                            <div class="image-box">
                                <div class="image-item" th:each="file : *{imageFileList}" th:style="|background-image: url('${file.webPath}');|">
                                    <input type="hidden" name="imageIds" th:value="${file.id}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">营业执照号码：</label>
                        <div class="form-control-static" th:if="${not #lists.isEmpty(schoolRegistration.schoolBusinessLicenseList)}" th:text="*{schoolBusinessLicenseList[0].no}"></div>
                    </div>


                    <div class="form-group">
                        <label class="col-sm-3 control-label">营业执照有效期：</label>
                        <div class="col-sm-8">
                            <div class="checkbox checkbox-danger" >
                                <input id="schoolBusinessLicenseList[0].isExpiration" name="schoolBusinessLicenseList[0].isExpiration" type="checkbox" th:value="1" th:if="${not #lists.isEmpty(schoolRegistration.schoolBusinessLicenseList)}" th:checked="${schoolRegistration.schoolBusinessLicenseList[0].expiration == '1' ? true : false}" disabled="disabled">
                                <label for="schoolBusinessLicenseList[0].isExpiration" th:text="永久">
                                </label>
                            </div>

                            <input th:if="${not #lists.isEmpty(schoolRegistration.schoolBusinessLicenseList) && schoolRegistration.schoolBusinessLicenseList[0].expiration != '1'}" th:disabled="${schoolRegistration.schoolBusinessLicenseList[0].expiration == '1'}" name="schoolBusinessLicenseList[0].expiration" th:field="*{schoolBusinessLicenseList[0].expiration}" class="form-control" type="text" placeholder="营业执照有效期" autocomplete="off" disabled="disabled">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">营业时间：</label>
                        <div class="form-control-static" th:text="*{businessTime}"></div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">状态：</label>
                        <div class="form-control-static" th:class="*{status == 0 ? 'label label-danger' : 'label label-primary'}" th:text="*{status == 0 ? '撤消' : '正常'}">
                        </div>
                    </div>
            </div>
        </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: upload-img-js" />
<script th:inline="javascript">

    var lg =[[${schoolRegistration.longitude}]];
    var la =[[${schoolRegistration.latitude}]];
    var center;
    // 初始化已标记地位置
    //地图加载
    var map = new AMap.Map("container", {
        resizeEnable: false,
        zoom:11,
    });
    //第一次加载
    if (la){
        loadMapAddress(la,lg) ;
    }
    function  loadMapAddress(la,lg) {
        console.log("la======"+la);
        console.log("lg==="+lg);
        $("#longitude").val(lg);
        $("#latitude").val(la);
        currentla =la;
        currentlg =lg ;
        map.clearMap();
        var marker = new AMap.Marker({
            icon: "https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png",
            position: [currentlg, currentla],
            offset: new AMap.Pixel(-13, -30)
        });
        map.add(marker);
        map.setCenter([currentlg,currentla]);
        //已知点坐标
        var lnglatXY = new AMap.LngLat(currentlg, currentla);
        var MGeocoder;
        //加载地理编码插件
        map.plugin(["AMap.Geocoder"], function() {
            MGeocoder = new AMap.Geocoder({
                radius: 1000,
                extensions: "all"
            });
            //返回地理编码结果
            MGeocoder.on("complete", geocoder_CallBack);
            //逆地理编码
            MGeocoder.getAddress(lnglatXY);
        });


    }

    //回调函数
    function geocoder_CallBack(data) {
        //返回地址描述
        console.log("返回地址描述"+data) ;
        if (data.info == "OK"){
            address = data.regeocode.formattedAddress;
            var infoWindow = new AMap.InfoWindow({
                isCustom: true,  //使用自定义窗体
                content: createInfoWindow(),
                offset: new AMap.Pixel(16, -45)
            });
            infoWindow.open(map, [currentlg,currentla]);
            circleName=null;
            circleId=null ;
            var region = address.match(/.+?(省|市|特别行政区|自治区|自治州|街道|镇|县|区)/g);
            $('#gpsAddress').val(address.split(region[2])[1]);
        }else{
            map = new AMap.Map("container", {
                resizeEnable: false,
                zoom:11,
            });
            map.setFitView();
        }

    }
    //构建自定义信息窗体
    function createInfoWindow() {
        //实例化信息窗体
        var title = '<span style="font-size:11px;color:#F00;">地址</span>',
            content = [];
        content.push(address);
        content.join("<br/>");
        var info = document.createElement("div");
        info.className = "custom-info input-card content-window-card";
        //可以通过下面的方式修改自定义窗体的宽高
        //info.style.width = "400px";
        // 定义顶部标题
        var top = document.createElement("div");
        var titleD = document.createElement("div");
        var closeX = document.createElement("img");
        top.className = "info-top";
        titleD.innerHTML = title;
        closeX.src = "https://webapi.amap.com/images/close2.gif";
        closeX.onclick = closeInfoWindow;

        top.appendChild(titleD);
        //top.appendChild(closeX);
        info.appendChild(top);

        // 定义中部内容
        var middle = document.createElement("div");
        middle.className = "info-middle";
        middle.style.backgroundColor = 'white';
        middle.innerHTML = content;
        info.appendChild(middle);

        // 定义底部内容
        var bottom = document.createElement("div");
        bottom.className = "info-bottom";
        bottom.style.position = 'relative';
        bottom.style.top = '0px';
        bottom.style.margin = '0 auto';
        var sharp = document.createElement("img");
        sharp.src = "https://webapi.amap.com/images/sharp.png";
        bottom.appendChild(sharp);
        info.appendChild(bottom);
        return info;
    }
    //关闭信息窗体
    function closeInfoWindow() {
        map.clearInfoWindow();
        map.remove(circle);
    }

</script>
</body>
</html>