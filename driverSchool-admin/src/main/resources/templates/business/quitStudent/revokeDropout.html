<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <th:block th:include="include :: header('撤销学员退学')" />
  </head>
  <body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <form
        class="form-horizontal m"
        id="form-revokeDropout"
        th:object="${schoolStudentDropOut}"
      >
        <input name="id" th:field="*{id}" type="hidden" />
        <div class="row">
          <div class="form-group">
            <label class="col-sm-3 control-label is-required">撤销原因：</label>
            <div class="col-sm-8">
              <input
                name="revokeReason"
                class="form-control"
                type="text"
                required
              />
            </div>
          </div>
        </div>
      </form>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
      var prefix = ctx + "business/quitStudent";

      function submitHandler() {
        if ($.validate.form()) {
          $.operate.save(
            prefix + "/revokeDropoutSave",
            $("#form-revokeDropout").serialize()
          );
          $.table.refresh();
        }
      }
    </script>
  </body>
</html>
