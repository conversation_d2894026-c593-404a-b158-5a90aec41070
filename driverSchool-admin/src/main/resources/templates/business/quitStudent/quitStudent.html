<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('7天退学学员')" />
    <th:block th:include="include :: select2-css"/>
    <link rel="shortcut icon" href="favicon.ico" />
    <link
      href="../static/css/bootstrap.min.css"
      th:href="@{/css/bootstrap.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/font-awesome.min.css"
      th:href="@{/css/font-awesome.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/animate.min.css"
      th:href="@{/css/animate.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/style.min.css"
      th:href="@{/css/style.min.css}"
      rel="stylesheet"
    />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>
                <li>
                  <label>分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="branchId"
                    name="branchId"
                    class="branchId form-control m-b"
                    data-first-title="选择分校"
                  ></select>
                </li>
                <li>
                  <label>报名点：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="registrationId"
                    name="registrationId"
                    class="registrationId form-control m-b"
                    data-first-title="所属报名点"
                  ></select>
                </li>
                <li>
                  <label>姓名：</label>
                  <input
                    type="text"
                    name="studentName"
                    placeholder="请输入学员姓名"
                  />
                </li>
                <li>
                  <label>身份证号：</label>
                  <input
                          type="text"
                          name="identity"
                          placeholder="多个身份证号使用空格进行分隔"
                  />
                </li>
                <li>
                  <label>手机号：</label>
                  <input type="text" name="mobile" placeholder="请输入手机号" />
                </li>
                <li>
                <li>
                  <label>审核状态：</label>
                  <select
                          name="isFirstTrial"
                          th:with="type=${@dict.getType('is_first_trial')}"
                  >
                    <option value="">所有</option>
                    <option
                            th:each="dict : ${type}"
                            th:text="${dict.dictLabel}"
                            th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li class="select-time">
                  <label style="width: 120px">初审时间： </label>
                  <input
                          style="width: 150px"
                          type="text"
                          class="time-input"
                          id="beginFirstTrialTime"
                          placeholder="开始日期"
                          name="params[beginFirstTrialTime]"
                  />
                  <span>-</span>
                  <input
                          style="width: 150px"
                          type="text"
                          class="time-input"
                          id="endFirstTrialTime"
                          placeholder="结束日期"
                          name="params[endFirstTrialTime]"
                  />
                </li>
                <li class="select-time">
                  <label style="width: 120px">终审时间： </label>
                  <input
                          style="width: 150px"
                          type="text"
                          class="time-input"
                          id="beginDropoutDoneTime"
                          placeholder="开始日期"
                          name="params[beginDropoutDoneTime]"
                  />
                  <span>-</span>
                  <input
                          style="width: 150px"
                          type="text"
                          class="time-input"
                          id="endDropoutDoneTime"
                          placeholder="结束日期"
                          name="params[endDropoutDoneTime]"
                  />
                <li>
                  <label style="width: 100px">监管资金状态：</label>
                  <select
                          name="superviseFeeIsOk"
                          th:with="type=${@dict.getType('supervise_fee_is_ok')}"
                  >
                    <option value="">所有</option>
                    <option
                            th:each="dict : ${type}"
                            th:text="${dict.dictLabel}"
                            th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li id="isMember_li" >
                  <label style="width: 100px">是否归属驾协：</label>
                  <select
                    id="isMember"
                    name="isMember"
                    th:with="type=${@dict.getType('is_member')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label>流程过程：</label>
                  <select
                          name="status"
                          th:with="type=${@dict.getType('drop_out_state')}"
                  >
                    <option value="">所有</option>
                    <option
                            th:each="dict : ${type}"
                            th:text="${dict.dictLabel}"
                            th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label>流程状态：</label>
                  <select
                          name="isDone"
                          th:with="type=${@dict.getType('is_done')}"
                  >
                    <option value="">所有</option>
                    <option
                            th:each="dict : ${type}"
                            th:text="${dict.dictLabel}"
                            th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="resetQueryForm()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-primary single disabled"
            onclick="view('',1300,'')"
            shiro:hasPermission="business:quitStudent:view"
          >
            查看
          </a>
          <a
            class="btn btn-primary"
            onclick="exportSelected()"
            shiro:hasPermission="business:quitStudent:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
          <a
                  class="btn btn-success single disabled"
                  onclick="firstTrial()"
                  shiro:hasPermission="business:quitStudent:firstTrial"
          >
            初审
          </a>
          <a
            class="btn btn-warning single disabled"
            onclick="check()"
            shiro:hasPermission="business:quitStudent:check"
          >
            终审
          </a>
          <a
             class="btn btn-success single disabled"
             onclick="viewQuitStudentContract()"
             shiro:hasPermission="business:student:viewContract"
          >
            查看签署合同
          </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js"/>
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">

          var prefix = ctx + "business/quitStudent";
          var sysOrganType=[[${sysOrganType}]];
          var identityTypeDatas = [[${@dict.getType('identity_type')}]];
          var genderDatas = [[${@dict.getType('sys_user_sex')}]];
          var nationDatas = [[${@dict.getType('nation_type')}]];
          var educationDatas = [[${@dict.getType('education')}]];
          var licenseTypeDatas = [[${@dict.getType('license_types')}]];
          var businessTypeDatas = [[${@dict.getType('business_type')}]];
          var oldLicenseTypeDatas = [[${@dict.getType('license_types')}]];
          var isSuperviseDatas = [[${@dict.getType('supervise_fee_is_ok')}]];
          var studentStatusDatas =[[${@dict.getType('student_status')}]];
          var isDone =[[${@dict.getType('is_done')}]];
          var  isFirstTrial = [[${@dict.getType('is_first_trial')}]];
          var studentContractUrl = ctx +"business/student";

          $(function() {
            //初始化搜索框
            $("#schoolId").select2({

            });
            //根据权限默认展示数据
            if (sysOrganType == 7){
              //驾协默认展示驾协成员信息
              $("#isMember").val("1")
            }else if(sysOrganType ==5){
              //政府监管默认展示成员
              $("#isMember").val("0")
            }

              var options = {
                  url: prefix + "/list",
                  exportUrl: prefix + "/export",
                  detailUrl: prefix + "/view/{id}",
                  modalName: "退学学员",
                  showPageGo: true,
                  columns: [{
                      checkbox: true
                  },
                  {
                      field: 'id',
                      title: '',
                      visible: false
                  },
                  {
                      field: 'schoolName',
                      title: '驾校',
                  },
                  {
                      field: 'branchName',
                      title: '分校',
                  },
                  {
                      field: 'registrationName',
                      title: '报名点',
                  },
                  {
                    field: 'isMember',
                    title: '是否归属驾协',
                    formatter: function(value, row, index) {
                       switch (value) {
                        case -1: return "-"; break;
                        case 0: return "非驾协成员"; break;
                        case 1: return "驾协成员"; break;
                       }
                    }
                },
                  {
                      field: 'studentName',
                      title: '学员',
                  },
                  {
                      field: 'gender',
                      title: '性别',
                      formatter: function(value, row, index) {
                          return $.table.selectDictLabel(genderDatas, value);
                      }
                  },
                  {
                      field: 'identity',
                      title: '身份证号',
                      formatter: function (value, row, index) {
                        return basecclusion(value,6,4);
                      }
                  },
                  {
                      field: 'mobile',
                      title: '手机号',
                      formatter: function (value, row, index) {
                        return basecclusion(value,3,4);
                      }
                  },
                  {
                      field: 'licenseType',
                      title: '学车类型',
                      formatter: function(value, row, index) {
                         return $.table.selectDictLabel(licenseTypeDatas, value);
                      }
                  },
                  {
                      field: '',
                      title: '报名时间',
                      visible: false
                  },
                    {
                      field: 'superviseFeeIsOk',
                      title: '资金监管状态',
                      formatter: function(value, row, index) {
                        return $.table.selectDictLabel(isSuperviseDatas, value);
                      }
                    },
                  {
                      field: 'studentStatus',
                      title: '学习状态',
                      formatter: function(value, row, index) {
                          return $.table.selectDictLabel(studentStatusDatas, value);
                      }
                  },
                  {
                      field: 'createdTime',
                      title: '退学申请时间',
                   },
                    {
                      field: 'isFirstTrial',
                      title: '审核状态',
                      formatter: function (value, row, index) {
                        return $.table.selectDictLabel(isFirstTrial, value);
                      }
                    },
                    {
                      field: 'firstTrialTime',
                      title: '初审时间',
                    },
                    {
                      field: 'dropoutDoneTime',
                      title: '终审时间',
                    },
                    {
                      field: 'status',
                      title: '流程过程',
                      formatter: function (value, row, index) {
                        switch (value) {
                          case 0: return "等待学员确认"; break;
                          case 1: return "学员已确认"; break;
                          case 2: return "等待驾协确认"; break;
                          case 3: return statusPack(row); break;
                          case 4: return "驾协已确认"; break;
                          case 5: return "学员已确认"; break;
                                // case 6: return row.revokeReason ? "驾校撤销：" + row.revokeReason : "驾校撤销"; break;
                          case 6: return "驾校撤销"; break;
                          case 7: return "审核确认"; break;
                                // case 8: return row.reviewerRejectReason ? "审核退回：" + row.reviewerRejectReason : "审核退回"; break;
                          case 8: return "审核退回"; break;
                        }
                      }
                    },
                    {
                      field: 'isDone',
                      title: '流程状态',
                      formatter: function(value, row, index) {
                        return $.table.selectDictLabel(isDone, value);
                      },
                    },
                  ]
              };
              $.table.init(options);


              //加载驾校、分校
              $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
              $('#cxSelectSchool').cxSelect({
                  selects: ['schoolId', 'branchId','registrationId'],
                  jsonValue: 'v',
              });

              /** 初始化时间组件 */
              layui.use('laydate', function(){
                  var laydate = layui.laydate;
                      laydate.render({
                          elem: '#beginPrepareRegisteDate',
                          type: 'datetime',
                          trigger: 'click'
         		        });
                         laydate.render({
                          elem: '#endPrepareRegisteDate',
                          type: 'datetime',
                          trigger: 'click'
         		        });
                  });
          });

          /* 查看退学学员 */
          function view(id,width, height) {
              table.set();
              var rows = $.table.selectRows();
              var _url = $.operate.detailUrl(rows[0].studentId);
              var options = {
                  title: table.options.modalName + "详细",
                  width: width,
                  height: height,
                  url: _url,
                  skin: 'layui-layer-gray',
                  btn: ['关闭'],
                  yes: function (index, layero) {
                      $.modal.close(index);
                  }
              };
              $.modal.openOptions(options);
          }

          /* 查看签署合同 */
          function viewQuitStudentContract() {
            var rows = $.table.selectRows();
            if (rows.length == 0) {
              $.modal.alertWarning("请至少选择一条记录");
              return;
            }

            // 检查是否签署了合同
            var params = 'id='+rows[0].studentId;
            var config = {
              url: studentContractUrl + "/checkContract",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                  var _url = studentContractUrl + "/viewContract/" + rows[0].studentId;
                  var options = {
                    title: "查看签署合同",
                    width: "1200",
                    height: "900",
                    url: _url,
                    skin: 'layui-layer-gray',
                    btn: ['关闭'],
                    yes: function (index, layero) {
                      $.modal.close(index);
                    }
                  };
                  $.modal.openOptions(options);
                } else {
                  $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
          }

          //重置搜索条件
          function resetQueryForm(){
            // 1. 先移除验证
            $('#formId').validate().resetForm();
            
            // 2. 重置表单
            $('#formId')[0].reset();
            
            // 3. 重置 select2 字段
            $("#schoolId").val('').trigger('change');
            $("#branchId").empty().trigger('change');
            $("#registrationId").empty().trigger('change');
            
            // 4. 清除错误样式
            $("#schoolId, #branchId, #registrationId").removeClass('error');
            $("label.error[for='schoolId'], label.error[for='branchId'], label.error[for='registrationId']").remove();
            
            // 5. 重新触发表格搜索
            $.table.search();
          }

          /* 审核退学学员 */
          function check() {
            var row = $.table.selectRows();
            if(row[0].isDone == 1){
              $.modal.alertWarning("流程已结束，无法审核");
              return;
            }
            if(row[0].isFirstTrial !=1){
              $.modal.alertWarning("学员未通过初审，无法进行终审");
              return;
            }
              layer.open({
              type: 2,
              content: prefix + "/check",
              title: '审核',
              area: ['800px', '300px'], //宽，高
              btn: ['提交','取消'],
              offset: 'auto', // 居中显示
              yes: function (index, layero) {
                      //获取单选框的值
                      var iframeWindow = window[layero.find('iframe')[0]['name']];
                      var selectedValue = iframeWindow.$("input[name='gender']:checked").val();
                      var rows = $.table.selectRows();
                      if (rows.length < 0) {
                          $.modal.alertWarning("请至少选择一条记录");
                          return;
                      }
                      var rejectReason = "";
                      if (selectedValue == 1) {
                        rejectReason = iframeWindow.$("input[name='rejectReason']").val();
                        if (rejectReason == "") {
                          $.modal.alertWarning("选择退回驾校时，退回原因不能为空");
                          return;
                        }
                      }
                      var data = {
                        "id": rows[0].id,
                        "studentId": rows[0].studentId,
                        "rejectReason": rejectReason,
                        "action": selectedValue,
                      };
                      $.ajax({
                      url: prefix + "/checkSave",
                      dataType: "json",
                      data: data,
                      type: "post",
                      success: function(result) {
                          if (result.code == web_status.SUCCESS) {
                              $.modal.alertSuccess(result.msg);
                              $.table.refresh();
                              layer.close(index);
                          } else {
                              $.modal.alertError(result.msg);
                          }
                      }
                  });
              },
          });
        }

          /* 封装流程过程 */
          function statusPack(row) {
            if (row.isMember == 1) {
              // return row.rejectReason == null ? "驾协退回：" + row.revokeReason : "驾协退回："+row.rejectReason;
              return "-";
            }else {
              // return row.rejectReason == null ? "平台退回：" + row.revokeReason : "平台退回："+row.rejectReason;
              return "-" ;
            }
          }

          /*初审 */
          function firstTrial(){
            var row = $.table.selectRows();
            if(row[0].isDone == 1){
              $.modal.alertWarning("流程已结束，无法初审");
              return;
            }
            if(row[0].isFirstTrial!=0){
              $.modal.alertWarning("该学员已初审，无法再次初审");
              return;
            }
            layer.open({
              type: 2,
              content: prefix + "/firstTrialView",
              title: '初审',
              area: ['800px', '300px'], //宽，高
              btn: ['提交','取消'],
              offset: 'auto', // 居中显示
              yes: function (index, layero) {
                var rows = $.table.selectRows();
                //获取单选框的值
                var iframeWindow = window[layero.find('iframe')[0]['name']];
                var selectedValue = iframeWindow.$("input[name='gender']:checked").val();

                if (rows.length < 0) {
                  $.modal.alertWarning("请至少选择一条记录");
                  return;
                }
                var rejectReason = "";
                if (selectedValue == 1) {
                  rejectReason = iframeWindow.$("input[name='rejectReason']").val();
                  if (rejectReason == "") {
                    $.modal.alertWarning("选择退回驾校时，退回原因不能为空");
                    return;
                  }
                }
                var params = {
                  "id": rows[0].id,
                  "studentId": rows[0].studentId,
                  "rejectReason": rejectReason,
                  "action": selectedValue,
                };
                $.ajax({
                  url: prefix + "/firstTrialBySeven",
                  dataType: "json",
                  data: params,
                  type: "post",
                  success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                      $.modal.alertSuccess(result.msg);
                      $.table.refresh();
                      layer.close(index);
                    } else {
                      $.modal.alertError(result.msg);
                    }
                  }
                });
              },
            });
          }

      /* 双击查看退学学员详情 */
      $('#bootstrap-table').on('dbl-click-row.bs.table', function (e, row, $element) {
            table.set();
            var _url = $.operate.detailUrl(row.studentId);
            var options = {
                title: table.options.modalName + "详细",
                width: "1300",
                height: "",
                url: _url,
                skin: 'layui-layer-gray',
                btn: ['关闭'],
                yes: function (index, layero) {
                    $.modal.close(index);
                }
            };
            $.modal.openOptions(options);
        });

        /* 导出7天退学学员 */
        function exportSelected() {
            var params = $("#formId").serialize();
            var ids = $.table.selectColumns("id");
            var tipMsg = "确定导出所有数据吗？";
            if($.common.isNotEmpty(ids)){
                tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
                params += "&ids="+ids;
            }
            $.modal.confirm(tipMsg, function() {
                var config = {
                  url: prefix + "/export",
                  type: "post",
                  dataType: "json",
                  data: params,
                  beforeSend: function () {
                    $.modal.loading("正在处理中，请稍候...");
                  },
                  success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                  },
                };
                $.ajax(config);
            });
          }
    </script>
  </body>
</html>
