<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('每日留存金额列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>
                <li class="select-time">
                  <label>日期： </label>
                  <input
                    type="text"
                    class="time-input"
                    id="beginCountTime"
                    placeholder="开始日期"
                    name="beginCountTime"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    class="time-input"
                    id="endCountTime"
                    placeholder="结束日期"
                    name="endCountTime"
                  />
                </li>
                <li>
                  <label>是否结算：</label>
                  <select
                    name="isPay"
                    th:with="type=${@dict.getType('is_pay')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:retainedAmount:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
          <a
            class="btn btn-success"
            onclick="interestCount()"
            shiro:hasPermission="business:retainedAmount:interestCount"
          >
            结息统计
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:retainedAmount:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:retainedAmount:remove')}]];
      var isPay = [[${@dict.getType('is_pay')}]];
      var prefix = ctx + "business/retainedAmount";

      $(function() {
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              modalName: "每日留存金额",
              columns: [{
                  checkbox: true
              },
              {
                  field: 'id',
                  title: '主键',
                  visible: false
              },
              {
                  field: '',
                  title: '驾校',
                  formatter: function(value, row, index) {
                    if (row.school != null) {
                        return row.school.name;
                    }
                }
              },
              {
                  field: 'countTime',
                  title: '日期'
              },
              {
                  field: 'yesterdaySuperviseAmount',
                  title: '昨日监管金额'
              },
              {
                  field: 'todaySuperviseAmount',
                  title: '当日导入金额'
              },
              {
                  field: 'todayReleaseAmount',
                  title: '当日释放金额'
              },
              {
                  field: 'retainedAmount',
                  title: '有效留存金额'
              },
              {
                  field: 'todayRetainedRatio',
                  title: '当日留存金额比例',
                  formatter: function(value, row, index) {
                    return value + "%";
                }
              },
              {
                  field: 'isPay',
                  title: '是否结算',
                  formatter: function(value, row, index) {
                    return $.table.selectDictLabel(isPay, value);
                  }
              },]
          };
          $.table.init(options);

          //加载驾校、分校
          $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
          $('#cxSelectSchool').cxSelect({
              selects: ['schoolId', 'branchId','registrationId'],
              jsonValue: 'v',
          });
      });

      /* 导出每日有效留存人数列表 */
      function exportSelected() {
        var params = $("#formId").serialize();
        var ids = $.table.selectColumns("id");
        var tipMsg = "确定导出所有数据吗？";
        if($.common.isNotEmpty(ids)){
            tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
            params += "&ids="+ids;
        }
        $.modal.confirm(tipMsg, function() {
            var config = {
              url: prefix + "/export",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
        });
      }

      /* 结息统计 */
      function interestCount() {
        var _url = prefix + "/interestCount"
        $.modal.open('结息统计', _url, '', '500')
      }
    </script>
  </body>
</html>
