<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('反馈意见列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>
                <li>
                  <label>分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="branchId"
                    name="branchId"
                    class="branchId form-control m-b"
                    data-first-title="选择分校"
                  ></select>
                </li>
                <li>
                  <label>反馈对象：</label>
                  <input
                    type="text"
                    name="feedBackObjName"
                    placeholder="请输入反馈对象"
                  />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:feedBackOpinion:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:feedBackOpinion:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:feedBackOpinion:remove')}]];
      var prefix = ctx + "business/feedBackOpinion";

      $(function() {
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              modalName: "反馈意见",
              columns: [{
                  checkbox: true
              },
              {
                  field: 'id',
                  title: '主键',
                  visible: false
              },
              {
                  field: 'schoolName',
                  title: '驾校'
              },
              {
                  field: 'branchName',
                  title: '分校'
              },
              {
                  field: 'feedBackObjType',
                  title: '反馈对象类型'
              },
              {
                  field: 'feedBackObjName',
                  title: '反馈对象'
              },
              {
                  field: 'feedBackObjTel',
                  title: '反馈对象联系电话'
              },
              {
                  field: 'feedBackPersonTel',
                  title: '反馈人联系电话'
              },
              {
                  field: 'feedBackOpinion',
                  title: '反馈意见'
              },]
          };

          $.table.init(options);

          //加载驾校、分校
          $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
          $('#cxSelectSchool').cxSelect({
              selects: ['schoolId', 'branchId','registrationId'],
              jsonValue: 'v',
          });
      });


      /* 导出数据 */
      function exportSelected() {
        var params = $("#formId").serialize();
        var ids = $.table.selectColumns("id");
        var tipMsg = "确定导出所有数据吗？";
        if($.common.isNotEmpty(ids)){
            tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
            params += "&ids="+ids;
        }
        $.modal.confirm(tipMsg, function() {
            var config = {
              url: prefix + "/export",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
        });
      }
    </script>
  </body>
</html>
