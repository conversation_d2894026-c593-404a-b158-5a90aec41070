<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('学车指引列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>标题：</label>
                                <input type="text" name="title"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="business:article:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="business:article:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="business:article:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:article:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:article:remove')}]];
        var isShowDatas = [[${@dict.getType('is_show_article')}]];
        var prefix = ctx + "business/article/learnDriving";

        $(function() {
            var options = {
                url: ctx + "business/article/list?category=1",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: ctx + "business/article/remove",
                modalName: "资讯内容",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '学车指引key为“study_guidence”',
                    visible: false
                },
                {
                    field: 'title',
                    title: '标题'
                },
                {
                    field: 'orderNo',
                    title: '排序'
                },
                {
                    field: 'content',
                    title: '内容',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(getNoMarkupStr(value));
                    }
                },
                {
                    field: 'updatedTime',
                    title: '更新时间'
                },
                ]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>