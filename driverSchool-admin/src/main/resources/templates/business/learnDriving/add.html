<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增学车指引')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: summernote-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-article-add">
            <input id="category" name="category" class="form-control" type="hidden" th:value="1">

            <div class="form-group">
                <label class="col-sm-3 control-label is-required">标题：</label>
                <div class="col-sm-8">
                    <input id="title" name="title" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">排序：</label>
                <div class="col-sm-8">
                    <input name="orderNo" class="form-control" type="number" required>
                    <span class="help-block m-b-none">
                      <i class="fa fa-info-circle" style="color: red;"></i> 数字越小越排前面
                    </span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">指引内容：</label>
                <div class="col-sm-8">
                    <input type="hidden" class="form-control" name="content">
                    <div class="summernote" id="content"></div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: summernote-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/article"
        $("#form-article-add").validate({
            onkeyup: false,
            focusCleanup: true,
            rules:{
                title:{
                    maxlength:100,
                    remote: {
                        url: prefix + "/checkUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "category":function () {
                                return $.common.trim($("#category").val());
                            },
                            "title":function () {
                                return $.common.trim($("#title").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                }
            },
            messages: {
                "title": {
                    remote: "该资讯内容标题已经存在"
                }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                if ($("#content").summernote('isEmpty')){
                    $.modal.msgError("内容不能为空");
                    return;
                }
                $.operate.save(prefix + "/add", $('#form-article-add').serialize());
            }
        }


        $(function() {
            $('.summernote').summernote({
                lang: 'zh-CN',
                height : 192,
                followingToolbar: false,
                dialogsInBody: true,
                toolbar: [
                    ['font', ['bold', 'italic', 'underline']],
                    ['fontsize',['fontsize']],
                    ['fontname', ['fontname']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']],
                    ['insert', ['table', 'link', 'picture']],
                    ['view', ['fullscreen', 'help', 'preview']],
                ],
                callbacks: {
                    onChange: function(contents, $edittable) {
                        $("input[name='" + this.id + "']").val(contents);
                    },
                    onImageUpload: function(files) {
                        var obj = this;
                    	var data = new FormData();
                    	data.append("file", files[0]);
                    	$.ajax({
                            type: "post",
                            url: ctx + "common/upload",
                    		data: data,
                    		cache: false,
                    		contentType: false,
                    		processData: false,
                    		dataType: 'json',
                    		success: function(result) {
                    		    if (result.code == web_status.SUCCESS) {
                    		        $('#' + obj.id).summernote('insertImage', result.url);
                    		    } else {
                    		        $.modal.alertError(result.msg);
                    		    }
                    		},
                    		error: function(error) {
                    		    $.modal.alertWarning("图片上传失败。");
                    		}
                    	});
                    }
                }
            });
        });
    </script>
</body>
</html>