<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <!-- 分校管理页面 -->
    <th:block th:include="include :: header('分校列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select id="schoolId" name="schoolId">
                    <option value="">选择驾校</option>
                    <option
                      th:each="dict : ${schoolList}"
                      th:text="${dict.name}"
                      th:value="${dict.id}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label>分校名称：</label>
                  <input type="text" name="name" />
                </li>
                <li>
                  <label>状态：</label>
                  <select
                    name="status"
                    th:with="type=${@dict.getType('t_school_status')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-success"
            onclick="add()"
            shiro:hasPermission="business:branch:add"
          >
            <i class="fa fa-plus"></i> 添加
          </a>
          <a
            class="btn btn-primary single disabled"
            onclick="edit()"
            shiro:hasPermission="business:branch:edit"
          >
            <i class="fa fa-edit"></i> 修改
          </a>

          <!-- <a class="btn btn-info single disabled" onclick="setAccount()" shiro:hasPermission="business:branch:edit">
                    设置监管账户
                </a> -->

          <a
            class="btn btn-danger multiple disabled"
            onclick="$.operate.removeAll()"
            shiro:hasPermission="business:branch:remove"
          >
            <i class="fa fa-remove"></i> 删除
          </a>
          <a
            class="btn btn-warning single disabled"
            onclick="view('',1300,'')"
            shiro:hasPermission="business:school:view"
          >
            查看
          </a>
          <!-- <a class="btn btn-info" onclick="$.table.importExcel()">
                    <i class="fa fa-upload"></i> 导入
                </a>-->
          <a
            class="btn btn-success"
            onclick="exportSelected()"
            shiro:hasPermission="business:branch:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:branch:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:branch:remove')}]];
        var licenseTypesDatas = [[${@dict.getType('license_types')}]];
        var chargeModesDatas = [[${@dict.getType('t_charge_modes')}]];
        var statusDatas = [[${@dict.getType('t_school_status')}]];
        var viewPerm = [[${@permission.hasPermi('business:school:view')}]];
        var prefix = ctx + "business/branch";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                detailUrl: prefix + "/view/{id}",
                exportUrl: prefix + "/export",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "分校",
                showPageGo: true,
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'name',
                    title: '分校名称'
                },

                    {
                        field: 'buslic',
                        title: '营业执照',
                        formatter: function(value, row, index) {
                            if ($.common.isNotEmpty(row.schoolBusinessLicenseList != null)) {
                                var no="";
                                $.each(row.schoolBusinessLicenseList, function(i, item) {
                                    if (i == 0){
                                        no=item.no;
                                        return ;
                                    }
                                })
                                return no;
                            }else{
                                return "-";
                            }
                        }

                    },

                    {
                        field: 'perDate',
                        title: '有效期',
                        formatter: function(value, row, index) {
                            if ($.common.isNotEmpty(row.schoolBusinessLicenseList != null)) {
                                var expiration="";
                                $.each(row.schoolBusinessLicenseList, function(i, item) {
                                    if (i == 0){
                                        expiration= item.expiration;
                                        return ;
                                    }
                                })
                                if (expiration != 1){
                                    return expiration;
                                }else{
                                    return "永久";
                                }
                            }else{
                                return "-";
                            }
                        }
                    },

                    {
                        field: 'buslicStatus',
                        title: '营业执照状态',
                        formatter: function(value, row, index) {
                            if ($.common.isNotEmpty(row.schoolBusinessLicenseList != null)) {
                                var isExpiration="";
                                var expiration="";
                                $.each(row.schoolBusinessLicenseList, function(i, item) {
                                    if (i == 0){
                                        isExpiration= item.isExpiration;
                                        expiration= item.expiration;
                                        return ;
                                    }
                                })
                                if (expiration != 1){
                                    return isExpiration==1? '<span class="badge badge-success">有效</span>': '<span class="badge badge-danger">无效</span>';
                                }else{
                                    return "<span class=\"badge badge-success\">有效</span>";
                                }
                            }else{
                                return "-";
                            }
                        }
                    },

                    {
                        field: 'licenseTypes',
                        title: '驾照类型',
                        formatter: function(value, row, index) {
                            var json= JSON.parse(value);
                            var actions = [];
                            $.each(json, function(i, item) {
                                var licenseType=item;
                                if ($.common.isNotEmpty(licenseType)) {
                                    actions.push(licenseType);
                                }
                            });
                            return actions.join('，');
                        }
                    },
                    {
                        field: 'chargeModes',
                        title: '收费模式',
                        formatter: function(value, row, index) {
                            var json= JSON.parse(value);
                            var actions = [];
                            $.each(json, function(i, item) {
                                var licenseType=item;
                                if ($.common.isNotEmpty(licenseType)) {
                                    actions.push(licenseType);
                                }
                            });
                            return actions.join('，');
                        }

                    },
                    {
                        field: 'id',
                        title: '联系人',
                        formatter: function(value, row, index) {
                            if ($.common.isNotEmpty(row.schoolContactList != null)) {
                                var name="";
                                $.each(row.schoolContactList, function(i, item) {
                                    if (i == 0){
                                        name=item.name;
                                        return ;
                                    }
                                })
                                return name;
                            }else{
                                return "-";
                            }
                        }
                    },

                    {
                        field: 'id',
                        title: '联系电话',
                        formatter: function(value, row, index) {
                            if ($.common.isNotEmpty(row.schoolContactList != null)) {
                                var tel="";
                                $.each(row.schoolContactList, function(i, item) {
                                    if (i == 0){
                                        tel= item.tel;
                                        return ;
                                    }
                                })
                                return tel;
                            }else{
                                return "-";
                            }
                        }
                    },

                    {
                        field: 'address',
                        title: '具体地址'
                    },
                    {
                        field: 'schoolId',
                        title: '所属驾校',
                        formatter: function(value, row, index) {
                            if ($.common.isNotEmpty(value)){
                                return row.school.name;
                            }
                        }
                    },

                    {
                        field: 'status',
                        title: '状态',
                        formatter: function(value, row, index) {
                            return $.table.selectDictLabel(statusDatas, value);
                        }
                    }


                ]
            };

            $.table.init(options);

            //隐藏列
            if(viewPerm == 'hidden') {
                $.table.hideColumn("buslic");
                $.table.hideColumn("perDate");
                $.table.hideColumn("buslicStatus");
            }
        });

        function add(id) {
            table.set();
            $.modal.open("添加" + table.options.modalName, $.operate.addUrl(id),1300);
        }

        function edit(id) {
            table.set();
            if ($.common.isEmpty(id) && table.options.type == table_type.bootstrapTreeTable) {
                var row = $("#" + table.options.id).bootstrapTreeTable('getSelections')[0];
                if ($.common.isEmpty(row)) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                var url = table.options.updateUrl.replace("{id}", row[table.options.uniqueId]);
                $.modal.open("修改" + table.options.modalName, url);
            } else {
                $.modal.open("修改" + table.options.modalName, $.operate.editUrl(id),1300);
            }
        }


        function setAccount() {
            table.set();
            if ($.common.isNotEmpty(id)) {
                url = table.options.updateUrl.replace("{id}", id);
            } else {
                var id = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
                if (id.length == 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                url = prefix +"/setAccount/{id}".replace("{id}", id);
            }
            $.modal.open("设置监管账户" , url);
        }


        function view(id,width, height) {
            table.set();
            var _url = $.operate.detailUrl(id);
            var options = {
                title: table.options.modalName + "详细",
                width: width,
                height: height,
                url: _url,
                skin: 'layui-layer-gray',
                btn: ['关闭'],
                yes: function (index, layero) {
                    $.modal.close(index);
                }
            };
            $.modal.openOptions(options);
        }

        // 导出数据
        function exportSelected() {
            var ids = $.table.selectColumns("id");
            var schoolId = $('#schoolId').val(); //驾校id
            var branchId = $('#branchId').val(); //分校id
            var name = $("input[name='name']").val(); //报名点名称
            var status = $('select[name="status"]').val(); //是否审核

            let fromData = new FormData();
            fromData.append('schoolId', schoolId != null ? schoolId : '');
            fromData.append('name', name != null ? name : '');
            fromData.append('status', status != null ? status : '');

            var tipMsg = "确定导出所有数据吗？";
            if($.common.isNotEmpty(ids)){
                tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
                fromData.append('ids', ids);
            }

            $.modal.confirm(tipMsg, function() {
                $.ajax({
                    url: prefix + "/export",
                    data: fromData,
                    type: "post",
                    processData: false,
                    contentType: false,
                    async: true,
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    }
                })
            });
        }

        /* 查看分校详情 */
      $('#bootstrap-table').on('dbl-click-row.bs.table', function (e, row, $element) {
        table.set();
          var _url = $.operate.detailUrl(row.id);
          var options = {
              title: table.options.modalName + "详细",
              width: "1300",
              height: "",
              url: _url,
              skin: 'layui-layer-gray',
              btn: ['关闭'],
              yes: function (index, layero) {
                  $.modal.close(index);
              }
          };
          $.modal.openOptions(options);
      });
    </script>
  </body>
  <!-- 导入区域 -->
  <script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                <a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
  </script>
</html>
