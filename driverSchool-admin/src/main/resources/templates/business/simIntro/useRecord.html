<!DOCTYPE html>
<html
        lang="zh"
        xmlns:th="http://www.thymeleaf.org"
        xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
<head>
    <th:block th:include="include :: header('卡品更换记录')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">

            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">

    var prefix = ctx + "business/simIntro/useRecord";
    var simType = [[${simType}]];
    $(function () {
        var options = {
            id: 'bootstrap-table',
            url: prefix + "/list/"+simType,
            modalName: "卡品更换记录",
            showPageGo: true,
            columns: [
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'simName',
                    title: '卡品'
                },
                {
                    field: 'useTime',
                    title: '使用时间'
                }]
        };
        $.table.init(options);
    });
</script>
</body>
</html>
