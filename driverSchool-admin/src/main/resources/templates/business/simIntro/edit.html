<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改卡品内容')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: summernote-css" />
    <th:block th:include="include :: upload-img-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-simIntro-edit" th:object="${simIntro}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">卡品名称：</label>
                <div class="col-sm-8">
                    <input id="simName" name="simName" class="form-control" type="text" th:field="*{simName}" required >
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">卡品说明：</label>
                <div class="col-sm-8">
                    <input type="hidden" class="form-control" name="simIntro" th:field="*{simIntro}">
                    <div class="summernote" id="simIntro"></div>
                </div>
            </div>

        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: summernote-js" />
    <th:block th:include="include :: upload-img-js" />
    <script th:inline="javascript">
        var maxImage = 1;
        var prefix = ctx + "business/simIntro";

        function submitHandler() {
            if ($.validate.form()) {
                if ($("#simIntro").summernote('isEmpty')){
                    $.modal.msgError("内容不能为空");
                    return;
                }
                $.operate.save(prefix + "/edit", $('#form-simIntro-edit').serialize());
            }
        }


        $(function() {
            $('.summernote').each(function(i) {
                $('#' + this.id).summernote({
                    lang: 'zh-CN',
                    height : 192,
                    followingToolbar: false,
                    dialogsInBody: true,
                    toolbar: [
                        ['font', ['bold', 'italic', 'underline']],
                        ['fontsize',['fontsize']],
                        ['fontname', ['fontname']],
                        ['color', ['color']],
                        ['para', ['ul', 'ol', 'paragraph']],
                        ['height', ['height']],
                        ['insert', ['table', 'link', 'picture']],
                        ['view', ['fullscreen', 'help', 'preview']],
                    ],
                    callbacks: {
                        onChange: function(contents, $edittable) {
                            $("input[name='" + this.id + "']").val(contents);
                        },
                        onImageUpload: function(files) {
                            var obj = this;
                            var data = new FormData();
                            data.append("file", files[0]);
                            $.ajax({
                                type: "post",
                                url: ctx + "common/upload",
                                data: data,
                                cache: false,
                                contentType: false,
                                processData: false,
                                dataType: 'json',
                                success: function(result) {
                                    if (result.code == web_status.SUCCESS) {
                                        $('#' + obj.id).summernote('insertImage', result.url);
                                    } else {
                                        $.modal.alertError(result.msg);
                                    }
                                },
                                error: function(error) {
                                    $.modal.alertWarning("图片上传失败。");
                                }
                            });
                        }
                    }
                });
                var content = $("input[name='" + this.id + "']").val();
                $('#' + this.id).summernote('code', content);
            })
        });
    </script>
</body>
</html>