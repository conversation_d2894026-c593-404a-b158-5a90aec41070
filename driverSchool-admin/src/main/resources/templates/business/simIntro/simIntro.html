<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('卡品列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <input type="hidden" name="simType" th:value="${simType}">
            <div class="select-list">
              <ul>
                <li>
                  <label>卡品名称：</label>
                  <input type="text" name="simName" />
                </li>
                <li>
                  <label>使用状态：</label>
                  <select name="status"  th:with="type=${@dict.getType('sim_intro_status')}">
                    <option value="">所有</option>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                  </select>
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-success"
            onclick="$.operate.add()"
            shiro:hasPermission="business:simIntro:add"
          >
            <i class="fa fa-plus"></i> 添加
          </a>
          <a
            class="btn btn-primary single disabled"
            onclick="$.operate.edit()"
            shiro:hasPermission="business:simIntro:edit"
          >
            <i class="fa fa-edit"></i> 修改
          </a>
          <a
            class="btn btn-danger multiple disabled"
            onclick="$.operate.removeAll()"
            shiro:hasPermission="business:simIntro:remove"
          >
            <i class="fa fa-remove"></i> 删除
          </a>
          <a
            class="btn btn-warning single disabled"
            onclick="selectUse()"
            shiro:hasPermission="business:simIntro:use"
          >
            使用
          </a>
          <a
            class="btn btn-info"
            onclick="viewUseRecord()"
            shiro:hasPermission="business:simIntro:useRecord"
          >
            更换记录
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:simIntro:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:simIntro:remove')}]];
      var simIntrostatus = [[${@dict.getType('sim_intro_status')}]];
      var simType = [[${simType}]];
      var prefix = ctx + "business/simIntro";

      $(function () {
          var options = {
              id: 'bootstrap-table',
              url: prefix + "/list",
              createUrl: prefix + "/add/"+simType,
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              modalName: "卡品",
              showPageGo: true,
              columns: [{
                  checkbox: true
              },
              {
                  field: 'id',
                  title: '',
                  visible: false
              },
              {
                  field: 'simName',
                  title: '卡品'
              },
              {
                  field: 'simIntro',
                  title: '内容',
                formatter: function(value, row, index) {
                  return $.table.tooltip(getNoMarkupStr(value));
                }
              },
              {
                  field: 'status',
                  title: '状态',
                  formatter: function (value, row, index) {
                     return $.table.selectDictLabel(simIntrostatus, value);
                  }
              },
              {
                  field: 'useTime',
                  title: '使用时间'
              }]
          };
          $.table.init(options);
      });

      function selectUse(){
          let url = prefix+"/use";
          let row = $.table.selectRows()[0];
          if ($.common.isEmpty(row)) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
          }
          let id = row.id;
          let data = {"id":id,"simType":simType};
          $.operate.submit(url, "post", "json", data);
      }

      function viewUseRecord() {
          var _url = prefix+"/useRecord/"+simType;
          var options = {
              title: "卡品更换记录",
              width: 600,
              height: 400,
              url: _url,
              skin: 'layui-layer-gray',
              btn: ['关闭'],
              yes: function (index, layero) {
                  $.modal.close(index);
              }
          };
          $.modal.openOptions(options);
      }
    </script>
  </body>
</html>
