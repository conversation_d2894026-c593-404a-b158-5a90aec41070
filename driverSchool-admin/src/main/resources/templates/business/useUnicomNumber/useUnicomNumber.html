<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('联通号码开卡列表')" />
    <!-- <th:block th:include="include :: select2-css" /> -->
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list">
              <ul>
                <li>
                  <label>门店：</label>
                  <select
                    class="form-control"
                    id="registrationId"
                    name="registrationId"
                    style="width: 200px"
                  >
                    <option value="">请选择</option>
                    <option
                      th:each="dict : ${registrationList}"
                      th:text="${dict.name}"
                      th:value="${dict.id}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label>学员姓名：</label>
                  <input
                    type="text"
                    name="studentName"
                    placeholder="请输入学员姓名"
                  />
                </li>
                <li>
                  <label>号码：</label>
                  <input type="text" name="number" placeholder="请输入号码" />
                </li>
                <li class="select-time">
                  <label style="width: 78px; display: inline-block"
                    >预登记时间：
                  </label>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input dateTime"
                    id="prepareRegisteDateStartTime"
                    placeholder="开始时间"
                    name="prepareRegisteDateStartTime"
                  />
                  <span>-</span>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input dateTime"
                    id="prepareRegisteDateEndTime"
                    placeholder="结束时间"
                    name="prepareRegisteDateEndTime"
                  />
                </li>
                <li class="select-time">
                  <label style="width: 100px">报名审核时间： </label>
                  <input
                    type="text"
                    style="width: 135px"
                    class="time-input"
                    id="registeDateBeginTime"
                    placeholder="开始时间"
                    name="registeDateBeginTime"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    style="width: 135px"
                    class="time-input"
                    id="registeDateEndTime"
                    placeholder="结束时间"
                    name="registeDateEndTime"
                  />
                </li>
                <li>
                  <label>联系电话：</label>
                  <input
                    type="text"
                    name="mobile"
                    placeholder="请输入联系电话"
                  />
                </li>
                <li>
                  <label>身份证号：</label>
                  <input
                    type="text"
                    name="identity"
                    placeholder="请输入身份证号"
                  />
                </li>
                <li class="select-time">
                  <label style="width: 78px; display: inline-block"
                    >开卡时间：
                  </label>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input dateTime"
                    id="beginUseTime"
                    placeholder="开始时间"
                    name="beginUseTime"
                  />
                  <span>-</span>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input dateTime"
                    id="endUseTime"
                    placeholder="结束时间"
                    name="endUseTime"
                  />
                </li>
                <li>
                  <label>开卡状态：</label>
                  <select
                    name="useStatus"
                    th:with="type=${@dict.getType('use_status')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label>收货号码：</label>
                  <input
                    type="text"
                    name="recipientMobile"
                    placeholder="请输入收货电话"
                  />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset('','','','', true)"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-danger multiple disabled"
            onclick="$.operate.removeAll()"
            shiro:hasPermission="business:useUnicomNumber:remove"
          >
            <i class="fa fa-remove"></i> 删除
          </a>
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:useUnicomNumber:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
          <a
            class="btn btn-primary multiple disabled"
            onclick="setNumberStatus()"
            shiro:hasPermission="business:useUnicomNumber:setNumberStatus"
          >
            设置开卡状态
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <!-- <th:block th:include="include :: select2-js" /> -->
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:useUnicomNumber:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:useUnicomNumber:remove')}]];
      var useStatus = [[${@dict.getType('use_status')}]];
      var prefix = ctx + "business/useUnicomNumber";

      $(function() {
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              modalName: "联通号码开卡",
              columns: [{
                  checkbox: true
              },
              {
                  field: 'id',
                  title: '主键',
                  visible: false
              },
              {
                  field: '',
                  title: '门店',
                  formatter: function(value, row, index) {
                    if (row.schoolStudent != null) {
                        return row.schoolStudent.registration.name;
                    }
                }
              },
              {
                  field: '',
                  title: '号码',
                  formatter: function(value, row, index) {
                    if (row.unicomNumber != null) {
                        return row.unicomNumber.number;
                    }
                }
              },
              {
                  field: '',
                  title: '学员姓名',
                  formatter: function(value, row, index) {
                    if (row.schoolStudent != null) {
                        return row.schoolStudent.name;
                    }
                }
              },
              {
                  field: '',
                  title: '联系电话',
                  formatter: function(value, row, index) {
                    if (row.schoolStudent != null) {
                        return row.schoolStudent.mobile;
                    }
                }
              },
              {
                  field: '',
                  title: '身份证号',
                  formatter: function(value, row, index) {
                    if (row.schoolStudent != null) {
                        return row.schoolStudent.identity;
                    }
                }
              },
              {
                  field: '',
                  title: '预登记时间',
                  formatter: function(value, row, index) {
                    if (row.schoolStudent != null) {
                        return row.schoolStudent.prepareRegisteDate;
                    }
                }
              },
              {
                  field: '',
                  title: '报名审核时间',
                  formatter: function(value, row, index) {
                    if (row.schoolStudent != null) {
                        return row.schoolStudent.registeDate;
                    }
                }
              },
              {
                  field: 'recipientMobile',
                  title: '收货号码',
              },
              {
                  field: 'deliverAddress',
                  title: '配送地址',
                  formatter: function(value, row, index) {
                    return row.deliverProvince + row.deliverCity + row.deliverTown + row.deliverAddress;
                }
              },
              {
                  field: 'useStatus',
                  title: '开卡状态',
                  formatter: function(value, row, index) {
                    return $.table.selectDictLabel(useStatus, value);
                }
              },
              {
                  field: 'useTime',
                  title: '开卡时间'
              },]
          };
          $.table.init(options);

          /** 初始化时间组件 */
          layui.use('laydate', function(){
              var laydate = layui.laydate;
                  laydate.render({
                      elem: '#prepareRegisteDateStartTime',
                      type: 'datetime',
                      trigger: 'click'
      	        });
                     laydate.render({
                      elem: '#prepareRegisteDateEndTime',
                      type: 'datetime',
                      trigger: 'click'
      	        });
                laydate.render({
                      elem: '#beginUseTime',
                      type: 'datetime',
                      trigger: 'click'
      	        });
                laydate.render({
                      elem: '#endUseTime',
                      type: 'datetime',
                      trigger: 'click'
      	        });
              });
      });

      /* 设置开卡状态 */
      function setNumberStatus() {
        var ids = $.table.selectColumns("id");
        var params = "ids="+ids;
        var config = {
              url: prefix + "/setNumberCache",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                  url = prefix + "/setNumberStatus/"+result.data.cacheId;
                  $.modal.open("设置开卡状态", url);
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
      }

      /* 导出开卡详情 */
      function exportSelected() {
        var params = $("#formId").serialize();
        var ids = $.table.selectColumns("id");
        var tipMsg = "确定导出所有数据吗？";
        if($.common.isNotEmpty(ids)){
            tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
            params += "&ids="+ids;
        }
        $.modal.confirm(tipMsg, function() {
            var config = {
              url: prefix + "/export",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
        });
      }
    </script>
  </body>
</html>
