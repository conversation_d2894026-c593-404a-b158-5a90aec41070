<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <th:block th:include="include :: header('设置开卡状态')" />
  </head>
  <body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <form
        class="form-horizontal m"
        id="form-useUnicomNumber-setNumberStatus"
        th:object="${useUnicomNumber}"
      >
        <input name="ids" th:field="*{ids}" type="hidden" />
        <div class="form-group">
          <label class="col-sm-3 control-label">号码：</label>
          <div class="form-control-static" th:text="*{numbers}"></div>
        </div>
        <div class="form-group">
          <label class="col-sm-3 control-label is-required">开卡状态：</label>
          <div class="col-sm-8">
            <div
              class="radio-box"
              th:each="dict : ${@dict.getType('use_status')}"
            >
              <input
                type="radio"
                th:id="${dict.dictCode}"
                name="useStatus"
                th:value="${dict.dictValue}"
                th:checked="${dict.default}"
                required
              />
              <label
                th:for="${dict.dictCode}"
                th:text="${dict.dictLabel}"
              ></label>
            </div>
          </div>
        </div>
      </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
      var prefix = ctx + "business/useUnicomNumber";

      $("#form-useUnicomNumber-setNumberStatus").validate({
        focusCleanup: true,
      });

      function submitHandler() {
        if ($.validate.form()) {
          $.operate.save(
            prefix + "/setNumberStatus",
            $("#form-useUnicomNumber-setNumberStatus").serialize()
          );
        }
      }
    </script>
  </body>
</html>
