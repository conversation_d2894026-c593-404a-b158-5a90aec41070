<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改白名单学员')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-whitelistStudents-edit" th:object="${whitelistStudents}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">学员id：</label>
                <div class="col-sm-8">
                    <input name="studentId" th:field="*{studentId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">学员姓名：</label>
                <div class="col-sm-8">
                    <input name="studentName" th:field="*{studentName}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">身份证号：</label>
                <div class="col-sm-8">
                    <input name="identity" th:field="*{identity}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">原驾校：</label>
                <div class="col-sm-8">
                    <input name="primarySchool" th:field="*{primarySchool}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">原分校：</label>
                <div class="col-sm-8">
                    <input name="primaryBranch" th:field="*{primaryBranch}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">原门店：</label>
                <div class="col-sm-8">
                    <input name="primaryRegistration" th:field="*{primaryRegistration}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">转学驾校id：</label>
                <div class="col-sm-8">
                    <input name="transferSchoolId" th:field="*{transferSchoolId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">转学驾校：</label>
                <div class="col-sm-8">
                    <input name="transferSchoolName" th:field="*{transferSchoolName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">转学分校id：</label>
                <div class="col-sm-8">
                    <input name="transferBranchId" th:field="*{transferBranchId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">转学分校：</label>
                <div class="col-sm-8">
                    <input name="transferBranchName" th:field="*{transferBranchName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">转学门店id：</label>
                <div class="col-sm-8">
                    <input name="transferRegistrationId" th:field="*{transferRegistrationId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">转学门店：</label>
                <div class="col-sm-8">
                    <input name="transferRegistrationName" th:field="*{transferRegistrationName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">原资金监管状态：0=未到账，1=已到账：</label>
                <div class="col-sm-8">
                    <input name="primarySuperviseFeeIsOk" th:field="*{primarySuperviseFeeIsOk}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">原上报计时平台状态：0=未上报，1=已上报：</label>
                <div class="col-sm-8">
                    <input name="primaryStudyCenterIsSyn" th:field="*{primaryStudyCenterIsSyn}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否曾在驾培平台报名：0=未报名，1=已报名：</label>
                <div class="col-sm-8">
                    <input name="hasRegistered" th:field="*{hasRegistered}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否转学：0=未转学，1=已转学：</label>
                <div class="col-sm-8">
                    <input name="hasTransfer" th:field="*{hasTransfer}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否退学：0=未退学，1=已退学：</label>
                <div class="col-sm-8">
                    <input name="hasQuit" th:field="*{hasQuit}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <input name="remark" th:field="*{remark}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">审核时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="reviewerTime" th:value="${#dates.format(whitelistStudents.reviewerTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">审核人：</label>
                <div class="col-sm-8">
                    <input name="reviewer" th:field="*{reviewer}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="createdTime" th:value="${#dates.format(whitelistStudents.createdTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/whitelistStudents";
        $("#form-whitelistStudents-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-whitelistStudents-edit').serialize());
            }
        }

        $("input[name='reviewerTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='createdTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>