<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <th:block th:include="include :: header('白名单学员退学')" />
    <th:block th:include="include :: upload-img-css" />
  </head>
  <body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <form
        class="form-horizontal m"
        id="form-student-out"
        th:object="${whitelistStudents}"
      >
        <input name="id" th:field="*{id}" type="hidden" />

        <div class="row">
          <div class="form-group">
          <div class="form-group">
            <label class="col-sm-3 control-label">学员姓名：</label>
            <div class="form-control-static" th:text="*{studentName}"></div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label">身份证号：</label>
            <div class="form-control-static" th:text="*{identity}"></div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label is-required"
              >上次退学凭证或合同照片<br />（最多不超过5张照片）：</label
            >
            <div class="col-sm-8">
              <div class="image-box"></div>
              <a class="btn btn-success" href="javascript:addImage();">
                <label>选择图片</label>
                <input
                  id="add-input"
                  type="file"
                  accept="image/*"
                  style="display: none"
                  onchange="selectImage(this);"
                />
              </a>
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label"></label>
            <div class="col-sm-9">
              <span
                class="help-block m-b-none"
                style="color: red; font-size: 18px"
              >
                <i style="color: red"></i> 确定该学员退学吗？
              </span>
            </div>
          </div>
        </div>
      </form>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: upload-img-js" />
    <script th:inline="javascript">
      var prefix = ctx + "business/whitelistStudents";

      var maxImage = 5;

      $("#form-student-out").validate({
        focusCleanup: true,
      });

      function submitHandler() {
        var imageCount = $(".image-box > .image-item").length;
        if (!imageCount) {
          $.modal.msgWarning("必须上传退学凭证");
          return;
        }
        if ($.validate.form()) {
          submit("/dropOutSave");
        }
      }
    </script>
  </body>
</html>
