<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <th:block th:include="include :: header('查看白名单学员')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: upload-img-css" />
    <style type="text/css">
      .user-info-head {
        position: relative;
        display: inline-block;
      }
      .user-info-head:hover:after {
        content: "\f030";
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        color: #eee;
        background: rgba(0, 0, 0, 0.5);
        font-family: FontAwesome;
        font-size: 24px;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        cursor: pointer;
        line-height: 110px;
        border-radius: 50%;
      }
    </style>
  </head>
  <body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <form
        class="form-horizontal m"
        id="form-student-edit"
        th:object="${schoolStudent}"
      >
        <input name="id" th:field="*{id}" type="hidden" />

        <div class="row">
          <h4 class="form-header h4">基本信息</h4>
          <div class="col-xs-6 col-md-3">
            <div class="text-center" style="margin-top: 40px">
              <p class="user-info-head">
                <img
                  id="img_id"
                  class="img-circle img-lg"
                  th:src="(${#strings.isEmpty(schoolStudent.headImage)}) ? @{/img/default_avatar.png} : @{${schoolStudent.headImage}}"
                />
              </p>
            </div>
          </div>
          <div class="col-xs-12 col-md-9">
            <div class="row">
              <div class="col-xs-6 col-md-4">
                <div class="form-group">
                  <label class="col-sm-4 control-label is-required"
                    >姓名：</label
                  >
                  <div class="form-control-static" th:text="*{name}"></div>
                </div>
              </div>
              <div class="col-xs-6 col-md-4">
                <div class="form-group">
                  <label class="col-sm-4 control-label is-required"
                    >身份证类型：</label
                  >
                  <div
                    class="form-control-static"
                    th:text="*{identityType}"
                  ></div>
                </div>
              </div>
              <div class="col-xs-6 col-md-4">
                <div class="form-group">
                  <label class="col-sm-4 control-label is-required"
                    >身份证号：</label
                  >
                  <div class="form-control-static" th:text="*{identity}"></div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-xs-6 col-md-4">
                <div class="form-group">
                  <label class="col-sm-4 control-label is-required"
                    >手机号：</label
                  >
                  <div class="form-control-static" th:text="*{mobile}"></div>
                </div>
              </div>

              <div class="col-xs-6 col-md-4">
                <div class="form-group">
                  <label class="col-sm-4 control-label is-required"
                    >性别：</label
                  >
                  <div class="col-sm-8">
                    <select
                      name="gender"
                      class="form-control m-b"
                      th:with="type=${@dict.getType('sys_user_sex')}"
                      disabled="disabled"
                    >
                      <option
                        th:each="dict : ${type}"
                        th:text="${dict.dictLabel}"
                        th:value="${dict.dictValue}"
                        th:field="*{gender}"
                      ></option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="col-xs-6 col-md-4">
                <div class="form-group">
                  <label class="col-sm-4 control-label is-required"
                    >出生日期：</label
                  >
                  <div class="form-control-static" th:text="*{birthday}"></div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-xs-6 col-md-4">
                <div class="form-group">
                  <label class="col-sm-4 control-label">民族：</label>
                  <div class="form-control-static" th:text="*{nation}"></div>
                </div>
              </div>
              <div class="col-xs-6 col-md-4">
                <div class="form-group">
                  <label class="col-sm-4 control-label">学历：</label>
                  <div class="form-control-static" th:text="*{education}"></div>
                </div>
              </div>
              <div class="col-xs-6 col-md-4">
                <div class="form-group">
                  <label class="col-sm-4 control-label">学号：</label>
                  <div class="form-control-static" th:text="*{stunum}"></div>
                </div>
              </div>
            </div>

            <div id="cxSelect1" class="row">
              <div class="col-xs-12 col-sm-6 col-md-8">
                <div class="form-group">
                  <label class="col-sm-2 control-label">现住地址：</label>
                  <div
                    class="col-sm-10 form-control-static"
                    th:if="*{province != null and province != ''}"
                    th:text="|*{province}*{city}*{town}*{address}|"
                  ></div>
                </div>
              </div>
            </div>

            <div id="cxSelect2" class="row">
              <div class="col-xs-12 col-sm-6 col-md-8">
                <div class="form-group">
                  <label class="col-sm-2 control-label">户籍地址：</label>
                  <div
                    class="col-sm-10 form-control-static"
                    th:if="*{hujiProvince != null and hujiProvince != ''}"
                    th:text="|*{hujiProvince}*{hujiCity}*{hujiTown}*{hujiAddress}|"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <h4 class="form-header h4">驾照信息</h4>
          <div class="row">
            <div class="col-xs-12 col-sm-6 col-md-8">
              <div id="cxSelectSchool" class="form-group">
                <label class="col-sm-2 control-label">所属学校：</label>
                <div
                  class="col-sm-2 form-control-static"
                  th:if="*{school != null }"
                  th:text="*{school.name}"
                ></div>
                <label class="col-sm-2 control-label">分校：</label>
                <div
                  class="col-sm-2 form-control-static"
                  th:if="*{branch != null }"
                  th:text="*{branch.name}"
                ></div>
                <label class="col-sm-2 control-label">报名点：</label>
                <div
                  class="col-sm-2 form-control-static"
                  th:if="*{registration != null}"
                  th:text="*{registration.name}"
                ></div>
              </div>
            </div>

            <div class="col-xs-6 col-md-4">
              <div class="form-group">
                <label class="col-sm-4 control-label is-required"
                  >学时平台：</label
                >
                <div class="col-sm-8">
                  <select
                    id="studyCenterId"
                    name="studyCenterId"
                    class="form-control m-b"
                    th:field="*{studyCenterId}"
                    disabled="disabled"
                  >
                    <option
                      th:each="dict : ${studyCenterList}"
                      th:text="${dict.organText}"
                      th:value="${dict.organId}"
                    ></option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-xs-6 col-md-4">
              <div class="form-group">
                <label class="col-sm-4 control-label is-required"
                  >考取驾照类型：</label
                >
                <div
                  class="col-sm-8 form-control-static"
                  th:text="*{licenseType}"
                ></div>
              </div>
            </div>
            <div class="col-xs-6 col-md-4">
              <div class="form-group">
                <label class="col-sm-4 control-label is-required"
                  >业务类型：</label
                >
                <div
                  class="col-sm-8 form-control-static"
                  th:text="*{businessType}"
                ></div>
              </div>
            </div>
            <div class="col-xs-6 col-md-4">
              <div class="form-group">
                <label class="col-md-4 control-label">原驾照号码：</label>
                <div
                  class="col-sm-8 form-control-static"
                  th:text="*{oldLicenseNo}"
                ></div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-xs-6 col-md-4">
              <div class="form-group">
                <label class="col-md-4 control-label">原驾照类型：</label>
                <div
                  class="col-sm-8 form-control-static"
                  th:text="*{oldLicenseType}"
                ></div>
              </div>
            </div>
            <div class="col-xs-6 col-md-4">
              <div class="form-group">
                <label class="col-sm-4 control-label">原领驾照日期：</label>
                <div
                  class="col-sm-8 form-control-static"
                  th:text="${#dates.format(schoolStudent.oldLicenseDate, 'yyyy-MM-dd')}"
                ></div>
              </div>
            </div>
            <!--<div class="col-xs-6 col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label is-required">是否资金监管：</label>
                            <div class="col-sm-8">
                                <select name="isSupervise" class="form-control m-b" th:with="type=${@dict.getType('student_is_supervise')}" disabled>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{isSupervise}"></option>
                                </select>
                            </div>
                        </div>
                    </div>-->
          </div>

          <div class="row">
            <div class="col-xs-6 col-md-4">
              <div class="form-group">
                <label class="col-sm-4 control-label">原驾驶证图片：</label>
                <div class="image-box">
                  <!-- <div class="image-item" th:style="'background-image: url(data:image/png;base64,' + ${schoolStudent.oldLicenseImage} + ');'"> -->
                  <div
                    class="image-item"
                    th:if="${schoolStudent.oldLicenseImage} != null"
                    th:style="'background-image: url(\'' + ${schoolStudent.oldLicenseImage} + '\');'"
                  >
                    <input
                      type="hidden"
                      name="imageId"
                      th:value="${schoolStudent.id}"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xs-12 col-sm-8">
              <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div
                  class="col-sm-10 form-control-static"
                  th:text="*{remark}"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- <div th:if="*{businessType=='增驾'}" class="row">
                <h4 class="form-header h4">原驾驶证图片</h4>
                <div class="row">
                        <div class="col-sm-8">
                            <div class="image-box">
                                    <div class="image-item" th:if="${schoolStudent.oldLicenseImage} != null" th:style="'background-image: url(\'' + ${schoolStudent.oldLicenseImage} + '\');'">
                                    <input type="hidden" name="imageId" th:value="${schoolStudent.id}">
                                </div>
                            </div>
                        </div>
                </div>
            </div> -->
      </form>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <th:block th:include="include :: upload-img-js" />
    <script th:inline="javascript">
      var prefix = ctx + "business/student";
    </script>
  </body>
</html>
