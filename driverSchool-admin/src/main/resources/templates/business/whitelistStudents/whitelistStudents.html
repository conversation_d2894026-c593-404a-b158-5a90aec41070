<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('白名单学员列表')" />
    <th:block th:include="include :: select2-css"/>
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>原驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="primarySchoolId"
                    name="primarySchoolId"
                    class="primarySchoolId form-control m-b"
                    data-first-title="选择原驾校"
                    required
                  ></select>
                </li>
                <li>
                  <label>原分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="primaryBranchId"
                    name="primaryBranchId"
                    class="primaryBranchId form-control m-b"
                    data-first-title="选择原分校"
                  ></select>
                </li>
                <li>
                  <label>原门店：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="primaryRegistrationId"
                    name="primaryRegistrationId"
                    class="primaryRegistrationId form-control m-b"
                    data-first-title="选择原门店"
                  ></select>
                </li>
                <li>
                  <label>转学驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="transferSchoolId"
                    name="transferSchoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择转学驾校"
                    required
                  ></select>
                </li>
                <li>
                  <label>转学分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="transferBranchId"
                    name="transferBranchId"
                    class="branchId form-control m-b"
                    data-first-title="选择转学分校"
                  ></select>
                </li>
                <li>
                  <label>转学门店：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="transferRegistrationId"
                    name="transferRegistrationId"
                    class="registrationId form-control m-b"
                    data-first-title="选择转学门店"
                  ></select>
                </li>
                <li>
                  <label>学员姓名：</label>
                  <input
                    type="text"
                    name="studentName"
                    placeholder="请输入学员姓名"
                  />
                </li>
                <li>
                  <label style="width: 120px">学员证件编码：</label>
                  <input   style="width: 135px"
                          type="text"
                          name="identity"
                           placeholder="多个身份证号使用空格进行分隔"
                  />
                </li>
                <li class="select-time">
                  <label style="width: 100px">转学时间： </label>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input"
                    id="beginTransferTime"
                    placeholder="开始时间"
                    name="beginTransferTime"
                  />
                  <span>-</span>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input"
                    id="endTransferTime"
                    placeholder="结束时间"
                    name="endTransferTime"
                  />
                </li>
                <li>
                  <label style="width: 100px">资金监管状态：</label>
                  <select name="primarySuperviseFeeIsOk">
                    <option value="">所有</option>
                    <option value="1">已到账</option>
                    <option value="0">未到账</option>
                  </select>
                </li>

                <li>
                  <label style="width: 100px">转学审核：</label>
                  <select name="hasTransfer">
                    <option value="">所有</option>
                    <option value="1">已审核</option>
                    <option value="0">未审核</option>
                  </select>
                </li>
                <li>
                  <label style="width: 135px">是否在驾培平台报名：</label>
                  <select
                    name="hasRegistered"
                    th:with="type=${@dict.getType('has_registered')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label>备注：</label>
                  <input type="text" name="remark" placeholder="请输入备注" />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="resetQueryForm()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-primary single disabled"
            onclick="view('',1300,'')"
            shiro:hasPermission="business:whitelistStudents:view"
          >
            查看
          </a>
          <a
            class="btn btn-success"
            onclick="$.table.importExcel()"
            shiro:hasPermission="business:whitelistStudents:import"
          >
            <i class="fa fa-upload"></i> 导入
          </a>
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:whitelistStudents:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
          <a
            class="btn btn-warning single disabled"
            onclick="transfer()"
            shiro:hasPermission="business:whitelistStudents:transfer"
          >
            转学
          </a>
          <a
            class="btn btn-warning multiple disabled"
            onclick="dropOut()"
            shiro:hasPermission="business:whitelistStudents:dropOut"
          >
            退学
          </a>
          <a
            class="btn btn-warning single disabled"
            onclick="reviewer()"
            shiro:hasPermission="business:whitelistStudents:reviewer"
          >
            审核
          </a>
          <a
            class="btn btn-danger multiple disabled"
            onclick="$.operate.removeAll()"
            shiro:hasPermission="business:whitelistStudents:remove"
          >
            <i class="fa fa-remove"></i> 删除
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js"/>
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:whitelistStudents:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:whitelistStudents:remove')}]];
      var hasQuit = [[${@dict.getType('student_is_quit')}]];
      var superviseFeeIsOk = [[${@dict.getType('supervise_fee_is_ok')}]];
      var studyCenterIsSyn = [[${@dict.getType('study_center_is_syn')}]];
      var hasRegistered = [[${@dict.getType('has_registered')}]];
      var hasTransfer = [[${@dict.getType('has_transfer')}]];
      var prefix = ctx + "business/whitelistStudents";

      $(function() {
        //初始化搜索框
        $("#primarySchoolId").select2({

        });
          var options = {
              url: prefix + "/list",
              detailUrl: prefix + "/view/{id}",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              importTemplateUrl: prefix + "/importTemplate",
              importUrl: prefix + "/import",
              modalName: "白名单学员",
              columns: [{
                  checkbox: true
              },
              {
                  field: 'id',
                  title: '主键',
                  visible: false
              },
              {
                  field: 'studentName',
                  title: '学员姓名'
              },
              {
                  field: 'identity',
                  title: '身份证号',
                  formatter: function (value, row, index) {
                      if ($.common.isNotEmpty(value)) {
                          return basecclusion(value,6,4);
                      }
                  }
              },
              {
                  field: 'primarySchool',
                  title: '原驾校'
              },
              {
                  field: 'primaryBranch',
                  title: '原分校'
              },
              {
                  field: 'primaryRegistration',
                  title: '原门店'
              },
              {
                  field: 'transferSchoolName',
                  title: '转学驾校'
              },
              {
                  field: 'transferBranchName',
                  title: '转学分校'
              },
              {
                  field: 'transferRegistrationName',
                  title: '转学门店'
              },
              {
                  field: 'primarySuperviseFeeIsOk',
                  title: '原资金监管状态',
                  formatter: function(value, row, index) {
                      return $.table.selectDictLabel(superviseFeeIsOk, value);
                  }
              },
              {
                  field: 'primaryStudyCenterIsSyn',
                  title: '原上报计时平台状态',
                  formatter: function(value, row, index) {
                      return $.table.selectDictLabel(studyCenterIsSyn, value);
                  }
              },
              {
                  field: 'hasRegistered',
                  title: '是否曾在驾培平台报名',
                  formatter: function(value, row, index) {
                      return $.table.selectDictLabel(hasRegistered, value);
                  }
              },
              {
                  field: 'hasTransfer',
                  title: '是否转学',
                  formatter: function(value, row, index) {
                      return $.table.selectDictLabel(hasTransfer, value);
                  }
              },
              {
                  field: 'hasQuit',
                  title: '是否退学',
                  formatter: function(value, row, index) {
                      return $.table.selectDictLabel(hasQuit, value);
                  }
              },
              {
                  field: 'remark',
                  title: '备注'
              },
              {
                  field: 'reviewerTime',
                  title: '审核时间'
              },
              {
                  field: 'reviewer',
                  title: '审核人'
              },
              {
                  field: 'transferTime',
                  title: '转学时间'
              },
              {
                  title: '操作',
                  align: 'center',
                  formatter: function(value, row, index) {
                      var actions = [];
                      actions.push(
                      '<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="viewTransferImg(\'' +
                        row.id +
                        '\')"><i class="fa fa-search"></i>转学凭证</a> '
                    );
                    actions.push(
                      '<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="viewDropOutImg(\'' +
                        row.id +
                        '\')"><i class="fa fa-search"></i>退学凭证</a> '
                    );
                      return actions.join('');
                  }
              }]
          };
          $.table.init(options);

          /** 初始化时间组件 */
          layui.use('laydate', function(){
            var laydate = layui.laydate;
                laydate.render({
                    elem: '#beginTransferTime',
                    type: 'datetime',
                    trigger: 'click'
              });
                    laydate.render({
                    elem: '#endTransferTime',
                    type: 'datetime',
                    trigger: 'click'
              });
          });

          //加载驾校、分校
          $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
          $('#cxSelectSchool').cxSelect({
              selects: ['schoolId', 'branchId','registrationId'],
              jsonValue: 'v',
          });

          $('#cxSelectSchool').cxSelect({
              selects: ['primarySchoolId', 'primaryBranchId','primaryRegistrationId'],
              jsonValue: 'v',
          });
      });

                //重置搜索条件
          function resetQueryForm(){
            // 1. 先移除验证
            $('#formId').validate().resetForm();
            
            // 2. 重置表单
            $('#formId')[0].reset();
            
            // 3. 重置 select2 字段
            $("#transferSchoolId").val('').trigger('change');
            $("#transferRegistrationId").empty().trigger('change');
            $("#transferBranchId").empty().trigger('change');

            $("#primarySchoolId").val('').trigger('change');
            $("#primaryBranchId").empty().trigger('change');
            $("#primaryRegistrationId").empty().trigger('change');
            
            // 4. 清除错误样式
            $("#transferSchoolId, #transferRegistrationId, #transferBranchId").removeClass('error');
            $("#primarySchoolId, #primaryBranchId, #primaryRegistrationId").removeClass('error');
            $("label.error[for='transferSchoolId'], label.error[for='transferRegistrationId'], label.error[for='transferBranchId']").remove();
            $("label.error[for='primarySchoolId'], label.error[for='primaryBranchId'], label.error[for='primaryRegistrationId']").remove();
            
            // 5. 重新触发表格搜索
            $.table.search();
          }

      /* 查看详情 */
      function view(id, width, height) {
          table.set();
          var rows = $.table.selectRows();
          if (rows[0].studentId == null) {
            $.modal.alertWarning('无此学生的报名信息');
            return;
          }
          var _url = $.operate.detailUrl(rows[0].studentId);
          var options = {
              title: table.options.modalName + "详细",
              width: width,
              height: height,
              url: _url,
              skin: 'layui-layer-gray',
              btn: ['关闭'],
              yes: function (index, layero) {
                  $.modal.close(index);
              }
          };
          $.modal.openOptions(options);
      }

      $('#bootstrap-table').on('dbl-click-row.bs.table', function (e, row, $element) {
        table.set();
        if (row.studentId == null) {
            $.modal.alertWarning('无此学员的报名信息');
            return;
          }
          var _url = $.operate.detailUrl(row.studentId);
          var options = {
              title: table.options.modalName + "详细",
              width: "1300",
              height: "",
              url: _url,
              skin: 'layui-layer-gray',
              btn: ['关闭'],
              yes: function (index, layero) {
                  $.modal.close(index);
              }
          };
          $.modal.openOptions(options);
      });

      /* 转学 */
      function transfer() {
        table.set();
        var rows = $.table.selectRows();
        if (rows[0].hasTransfer == 1) {
            $.modal.alertWarning('该学员已转学');
            return;
        }
        var url = prefix + "/transfer/" + rows[0].id;
        $.modal.open('学员转学', url, '', '500');
      }

      /* 查看转学凭证 */
      function viewTransferImg(id) {
            var _url = prefix + "/viewTransferImg/" + id;
            var options = {
              title: "查看转学凭证",
              width: "",
              height: "",
              url: _url,
              btn: ["关闭"],
              yes: function (index, layero) {
                $.modal.close(index);
              },
            };
            $.modal.openOptions(options);
        }

        /* 查看退学凭证 */
      function viewDropOutImg(id) {
            var _url = prefix + "/viewDropOutImg/" + id;
            var options = {
              title: "查看退学凭证",
              width: "",
              height: "",
              url: _url,
              btn: ["关闭"],
              yes: function (index, layero) {
                $.modal.close(index);
              },
            };
            $.modal.openOptions(options);
        }

       /* 审核 */
      function reviewer() {
        table.set();
        var rows = $.table.selectRows();
        if (rows[0].hasTransfer == 1) {
            $.modal.alertWarning('该学员已转学');
            return;
        }
        if (rows[0].studentId == null && rows[0].transferSchoolId == null) {
            $.modal.alertWarning('该记录未发起转学');
            return;
        }
        var url = prefix + "/reviewer/" + rows[0].id;
        $.modal.openPro('审核转学学员', url, '转学通过', '1200', '500');
      }

      /* 退学 */
      function dropOut() {
        var rows = $.table.selectRows();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        if (rows[0].hasQuit == 1) {
          $.modal.alertWarning("学员" + rows[0].studentName + "已经是退学状态。");
          return;
        }

          $.modal.confirm("确认要学员" + rows[0].studentName + "退学吗?", function () {
            var url = prefix + "/dropOut/"+rows[0].id;
            $.modal.open("学员退学", url, 800, 600);
          });
      }

      /* 导出白名单学员 */
      function exportSelected() {
            var params = $("#formId").serialize();
            var ids = $.table.selectColumns("id");
            var tipMsg = "确定导出所有数据吗？";
            if($.common.isNotEmpty(ids)){
                tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
                params += "&ids="+ids;
            }
            $.modal.confirm(tipMsg, function() {
                var config = {
                  url: prefix + "/export",
                  type: "post",
                  dataType: "json",
                  data: params,
                  beforeSend: function () {
                    $.modal.loading("正在处理中，请稍候...");
                  },
                  success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                  },
                };
                $.ajax(config);
            });
          }
    </script>
  </body>

  <script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                <a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
  </script>
</html>
