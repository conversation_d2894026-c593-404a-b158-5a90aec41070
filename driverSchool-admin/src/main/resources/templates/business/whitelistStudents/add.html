<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增白名单学员')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-whitelistStudents-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label">学员id：</label>
                <div class="col-sm-8">
                    <input name="studentId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">学员姓名：</label>
                <div class="col-sm-8">
                    <input name="studentName" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">身份证号：</label>
                <div class="col-sm-8">
                    <input name="identity" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">原驾校：</label>
                <div class="col-sm-8">
                    <input name="primarySchool" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">原分校：</label>
                <div class="col-sm-8">
                    <input name="primaryBranch" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">原门店：</label>
                <div class="col-sm-8">
                    <input name="primaryRegistration" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">转学驾校id：</label>
                <div class="col-sm-8">
                    <input name="transferSchoolId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">转学驾校：</label>
                <div class="col-sm-8">
                    <input name="transferSchoolName" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">转学分校id：</label>
                <div class="col-sm-8">
                    <input name="transferBranchId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">转学分校：</label>
                <div class="col-sm-8">
                    <input name="transferBranchName" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">转学门店id：</label>
                <div class="col-sm-8">
                    <input name="transferRegistrationId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">转学门店：</label>
                <div class="col-sm-8">
                    <input name="transferRegistrationName" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">原资金监管状态：0=未到账，1=已到账：</label>
                <div class="col-sm-8">
                    <input name="primarySuperviseFeeIsOk" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">原上报计时平台状态：0=未上报，1=已上报：</label>
                <div class="col-sm-8">
                    <input name="primaryStudyCenterIsSyn" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否曾在驾培平台报名：0=未报名，1=已报名：</label>
                <div class="col-sm-8">
                    <input name="hasRegistered" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否转学：0=未转学，1=已转学：</label>
                <div class="col-sm-8">
                    <input name="hasTransfer" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否退学：0=未退学，1=已退学：</label>
                <div class="col-sm-8">
                    <input name="hasQuit" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <input name="remark" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">审核时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="reviewerTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">审核人：</label>
                <div class="col-sm-8">
                    <input name="reviewer" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="createdTime" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/whitelistStudents"
        $("#form-whitelistStudents-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-whitelistStudents-add').serialize());
            }
        }

        $("input[name='reviewerTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='createdTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>