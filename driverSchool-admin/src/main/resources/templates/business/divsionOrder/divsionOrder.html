<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('分账总订单列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>
                <li>
                  <label>分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="branchId"
                    name="branchId"
                    class="branchId form-control m-b"
                    data-first-title="选择分校"
                  ></select>
                </li>
                <li>
                  <label>报名点：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="registrationId"
                    name="registrationId"
                    class="registrationId form-control m-b"
                    data-first-title="所属报名点"
                  ></select>
                </li>
                <li>
                  <label>姓名：</label>
                  <input
                    type="text"
                    name="studentName"
                    placeholder="请输入学员姓名"
                  />
                </li>
                <li>
                  <label>身份证号：</label>
                  <input
                    type="text"
                    name="studentIdcard"
                    placeholder="请输入身份证号 多个用身份证隔开"
                  />
                </li>
                <li>
                  <label>手机号：</label>
                  <input
                    type="text"
                    name="studentMobile"
                    placeholder="请输入系统单号"
                  />
                </li>
                <li>
                  <label>系统单号：</label>
                  <input
                    type="text"
                    name="orderNo"
                    placeholder="请输入系统单号"
                  />
                </li>
                <li>
                  <label>交易状态：</label>
                  <select
                    name="isPay"
                    th:with="type=${@dict.getType('student_order_is_pay')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li class="select-time">
                  <label style="width: 78px; display: inline-block"
                    >交易时间：
                  </label>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input dateTime"
                    id="beginOrderTime"
                    placeholder="开始时间"
                    name="beginOrderTime"
                  />
                  <span>-</span>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input dateTime"
                    id="endOrderTime"
                    placeholder="结束时间"
                    name="endOrderTime"
                  />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:divsionOrder:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
          <a
                  class="btn btn-primary multiple disabled"
                  onclick="checkBankOrder()"
                  shiro:hasPermission="business:divsionOrder:checkBankOrder"
          >
            <i class="fa fa-download"></i> 和银行对账
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:divsionOrder:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:divsionOrder:remove')}]];
      var isPay = [[${@dict.getType('student_order_is_pay')}]];
      var prefix = ctx + "business/divsionOrder";

      $(function() {
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              modalName: "交费订单",
              columns: [{
                  checkbox: true
              },
              {
                  field: 'id',
                  title: '',
                  visible: false
              },
              {
                  field: 'schoolName',
                  title: '驾校'
              },
              {
                  field: 'branchName',
                  title: '分校'
              },
              {
                  field: 'registrationName',
                  title: '报名点'
              },
              {
                  field: 'studentName',
                  title: '学员姓名'
              },
              {
                  field: 'studentIdcard',
                  title: '身份证号'
              },
              {
                  field: 'studentMobile',
                  title: '手机号'
              },
              {
                  field: 'bankOrderNo',
                  title: '银行单号'
              },
              {
                  field: 'orderNo',
                  title: '系统单号'
              },
              {
                  field: 'totalFee',
                  title: '交易金额'
              },
              {
                  field: 'superviseFee',
                  title: '科二科三监管费'
              },
              {
                  field: 'schoolFee',
                  title: '总校学费'
              },
              // {
              //   field: 'commissionFee',
              //   title: '手续费'
              // },
              // {
              //   field: 'associationFee',
              //   title: '平台费'
              // },
              {
                  field: 'schoolFactFee',
                  title: '总校实际到账'
              },
              {
                  field: 'registrationFee',
                  title: '门店学费'
              },
              {
                  field: 'isPay',
                  title: '交易状态',
                  formatter: function(value, row, index) {
                    return $.table.selectDictLabel(isPay, value);
                }
              },
              {
                  field: 'orderTime',
                  title: '交易时间'
              },
              {
                field: 'isBankCheck',
                title: '是否已对账',
                formatter:function (value,row,index){
                  if(value===0){
                    return "未核对";
                  }
                  if(value===1){
                    return "已核对";
                  }
                }
              }]
          };
          $.table.init(options);

          //加载驾校、分校
          $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
          $('#cxSelectSchool').cxSelect({
              selects: ['schoolId', 'branchId','registrationId'],
              jsonValue: 'v',
          });

          /** 初始化时间组件 */
          layui.use('laydate', function(){
              var laydate = layui.laydate;
                  laydate.render({
                      elem: '#beginOrderTime',
                      type: 'datetime',
                      trigger: 'click'
      	        });
                     laydate.render({
                      elem: '#endOrderTime',
                      type: 'datetime',
                      trigger: 'click'
      	        });
            });
      });

      function checkBankOrder(){
        var params = $("#formId").serialize();
        var ids = $.table.selectColumns("id");
        if($.common.isNotEmpty(ids)){
          tipMsg = "确定核对勾选" + ids.length + "条数据吗？";
          params += "&ids="+ids;
        }else{
          $.modal.alertWarning("请选择要核对的数据");
          return;
        }
        $.modal.confirm(tipMsg, function() {
          var config = {
            url: prefix + "/checkBankOrder",
            type: "post",
            dataType: "json",
            data: params,
            beforeSend: function () {
              $.modal.loading("正在处理中，请稍候...");
            },
            success: function (result) {
              if (result.code == web_status.SUCCESS) {
                $.table.refresh();
                $.modal.alertSuccess(result.msg);
              } else {
                $.modal.alertError(result.msg);
              }
              $.modal.closeLoading();
            },
          };
          $.ajax(config);
        });
      }

      /* 导出交费订单 */
      function exportSelected() {
        var params = $("#formId").serialize();
        var ids = $.table.selectColumns("id");
        var tipMsg = "确定导出所有数据吗？";
        if($.common.isNotEmpty(ids)){
            tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
            params += "&ids="+ids;
        }
        $.modal.confirm(tipMsg, function() {
            var config = {
              url: prefix + "/export",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
        });
      }
    </script>
  </body>
</html>
