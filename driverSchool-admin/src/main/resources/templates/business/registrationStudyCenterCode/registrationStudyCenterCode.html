<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('门店与学时平台签约代码列表')" />
    <!-- <th:block th:include="include :: bootstrap-editable-css" /> -->
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="btn-group-sm" id="toolbar" role="group">
          <a class="btn btn-primary single disabled" onclick="edit()"> 修改 </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <!-- <th:block th:include="include :: bootstrap-table-editable-js" /> -->
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:registrationStudyCenterCode:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:registrationStudyCenterCode:remove')}]];
        var schoolId = [[${schoolId}]];
        var registrationId = [[${registrationId}]];
        var prefix = ctx + "business/registrationStudyCenterCode";

        $(function() {
            var options = {
              url: prefix + "/list?schoolId="+schoolId+"&registrationId="+registrationId,
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              modalName: "门店与学时平台签约代码",
              showSearch: false,
              showRefresh: true,
              showToggle: false,
              showColumns: false,
              // onEditableSave: onEditableSave,
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'organName',
                    title: '学时平台'
                },
                {
                    field: 'registrationCode',
                    title: '签约学校/签约代码',
                    editable: true
                },]
            };
            $.table.init(options);
        });

        /* 行内编辑 */
        // function onEditableSave (field, row, rowIndex, oldValue, $el) {
        //     var params = "id="+row.id+"&registrationCode="+row.registrationCode;
        //     var config = {
        //       url: prefix + "/edit",
        //       type: "post",
        //       dataType: "json",
        //       data: params,
        //       beforeSend: function () {
        //         $.modal.loading("正在处理中，请稍候...");
        //       },
        //       success: function (result) {
        //         if (result.code == web_status.SUCCESS) {
        //           $.modal.alertSuccess(result.msg);
        //         } else {
        //             $.modal.alertError(result.msg);
        //         }
        //         $.modal.closeLoading();
        //       },
        //     };
        //     $.ajax(config);
        //   }

        /* 修改 */
        function edit() {
          var rows = $.table.selectRows();
          if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
          }
          var _url = prefix + "/edit/" + rows[0].id;
          var options = {
              title: '修改签约代码',
              url: _url,
              callBack: doSubmit
          };
          $.modal.openOptions(options);
        }

        function doSubmit(index, layero) {
          var body = $.modal.getChildFrame(index);
          var params = body.find('form').serialize();
          var config = {
              url: prefix + "/edit",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                  $.table.refresh();
                  $.modal.close(index);
                  $.modal.alertSuccess(result.msg);
                }else {
                  $.modal.alertError(result.msg);
                }
                  $.modal.closeLoading();
              },
          };
          $.ajax(config);
      }
    </script>
  </body>
</html>
