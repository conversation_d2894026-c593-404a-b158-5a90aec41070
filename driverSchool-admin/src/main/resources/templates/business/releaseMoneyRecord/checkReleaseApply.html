<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <th:block th:include="include :: header('审核释放申请')" />
    <th:block th:include="include :: upload-img-css" />
  </head>
  <body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <form
        class="form-horizontal m"
        id="form-checkReleaseApply"
        th:object="${releaseMoneyRecord}"
      >
        <input name="id" th:field="*{id}" type="hidden" />
        <div class="form-group">
          <label class="col-sm-3 control-label">学员姓名：</label>
          <div
            class="form-control-static"
            th:text="*{schoolStudent.name}"
          ></div>
        </div>
        <div class="form-group">
          <label class="col-sm-3 control-label">身份证号：</label>
          <div
            class="form-control-static"
            th:text="*{schoolStudent.identity}"
          ></div>
        </div>
        <div class="form-group">
          <label class="col-sm-3 control-label">归属科目：</label>
          <div class="col-sm-8">
            <select
              id="subjectNameBox"
              name="subjectName"
              th:field="*{subjectName}"
              class="form-control m-b"
              th:with="type=${@dict.getType('release_money_apply_type')}"
              disabled="disabled"
            >
              <option
                th:each="dict : ${type}"
                th:text="${dict.dictLabel}"
                th:value="${dict.dictValue}"
              ></option>
            </select>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-3 control-label">申请释放原因：</label>
          <div class="col-sm-8">
            <input
              name="releaseReason"
              th:field="*{releaseReason}"
              class="form-control"
              type="text"
              readonly="readonly"
            />
          </div>
        </div>
        <div id="releaseAmountInput" class="form-group">
          <label class="col-sm-3 control-label">申请释放金额：</label>
          <div class="col-sm-8">
            <input
              name="releaseMoney"
              th:field="*{releaseMoney}"
              class="form-control"
              type="number"
              readonly="readonly"
            />
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-3 control-label is-required">审核情况：</label>
          <div class="col-sm-8">
            <div
              class="radio-box"
              th:each="dict : ${@dict.getType('release_money_apply_check')}"
              th:unless="${dict.dictValue == '3' or dict.dictValue == '4' or dict.dictValue == '5'}"
            >
              <input
                type="radio"
                th:id="${dict.dictCode}"
                name="checkType"
                th:field="*{checkType}"
                th:value="${dict.dictValue}"
                th:checked="${dict.default}"
                required
              />
              <label
                th:for="${dict.dictCode}"
                th:text="${dict.dictLabel}"
              ></label>
            </div>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-3 control-label">审核意见：</label>
          <div class="col-sm-8">
            <input name="checkOpinion" class="form-control" type="text" />
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-3 control-label">释放凭证：</label>
          <div class="col-sm-8">
            <div class="image-box">
              <div
                class="image-item"
                th:each="file : *{releaseVouchers}"
                th:style="|background-image: url('${file.webPath}');|"
              >
                <input type="hidden" name="imageIds" th:value="${file.id}" />
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: upload-img-js" />
    <script th:inline="javascript">
      var prefix = ctx + "business/releaseMoneyRecord";

      $("#form-checkReleaseApply").validate({
        focusCleanup: true,
      });

      function submitHandler() {
        if ($.validate.form()) {
          $.operate.save(
            prefix + "/checkReleaseApplySave",
            $("#form-checkReleaseApply").serialize()
          );
        }
      }
    </script>
  </body>
</html>
