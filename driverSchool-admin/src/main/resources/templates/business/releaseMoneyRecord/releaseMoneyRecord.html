<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('手动释放资金记录列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>
                <li>
                  <label>分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="branchId"
                    name="branchId"
                    class="branchId form-control m-b"
                    data-first-title="选择分校"
                  ></select>
                </li>
                <li>
                  <label>报名点：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="registrationId"
                    name="registrationId"
                    class="registrationId form-control m-b"
                    data-first-title="所属报名点"
                  ></select>
                </li>
                <li>
                  <label>审核情况：</label>
                  <select
                    name="checkType"
                    th:with="type=${@dict.getType('release_money_apply_check')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <!-- <li>
                  <label>审核意见：</label>
                  <input
                    type="text"
                    name="checkOpinion"
                    placeholder="请输入审核意见"
                  />
                </li> -->
                <li>
                  <label>申请原因：</label>
                  <input
                    type="text"
                    name="releaseReason"
                    placeholder="请输入申请原因"
                  />
                </li>
                <li class="select-time">
                  <label>申请时间： </label>
                  <input
                    type="text"
                    class="time-input"
                    style="width: 135px"
                    id="beginApplyTime"
                    placeholder="开始时间"
                    name="beginApplyTime"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    class="time-input"
                    style="width: 135px"
                    id="endApplyTime"
                    placeholder="结束时间"
                    name="endApplyTime"
                  />
                </li>
                <li class="select-time">
                  <label>初审时间： </label>
                  <input
                    type="text"
                    class="time-input"
                    style="width: 135px"
                    id="beginCheckTime"
                    placeholder="开始时间"
                    name="beginCheckTime"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    class="time-input"
                    style="width: 135px"
                    id="endCheckTime"
                    placeholder="结束时间"
                    name="endCheckTime"
                  />
                </li>
                <li class="select-time">
                  <label>终审时间： </label>
                  <input
                    type="text"
                    class="time-input"
                    style="width: 135px"
                    id="beginFinalReviewTime"
                    placeholder="开始时间"
                    name="beginFinalReviewTime"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    class="time-input"
                    style="width: 135px"
                    id="endFinalReviewTime"
                    placeholder="结束时间"
                    name="endFinalReviewTime"
                  />
                </li>
                <li>
                  <label>姓名：</label>
                  <input type="text" name="studentName" placeholder="请输入姓名" />
                </li>
                <li>
                  <label>身份证号：</label>
                  <input
                          type="text"
                          name="identity"
                          placeholder="多个身份证号使用空格进行分隔"
                  />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-warning single disabled"
            onclick="checkReleaseApply()"
            shiro:hasPermission="business:releaseMoneyRecord:check"
          >
            初审
          </a>
          <a
            class="btn btn-warning single disabled"
            onclick="finalReview()"
            shiro:hasPermission="business:releaseMoneyRecord:finalReview"
          >
            终审
          </a>
          <!-- <a
            class="btn btn-warning single disabled"
            onclick="release()"
            shiro:hasPermission="business:releaseMoneyRecord:release"
          >
            释放
          </a> -->
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
          var subjectNames = [[${@dict.getType('release_money_apply_type')}]];
          var checkTypes = [[${@dict.getType('release_money_apply_check')}]];
          var prefix = ctx + "business/releaseMoneyRecord";

          $(function () {
            var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              modalName: "手动释放资金记录",
              columns: [
                {
                  checkbox: true,
                },
                {
                  field: "id",
                  title: "主键",
                  visible: false,
                },
                {
                  field: "schoolName",
                  title: "驾校",
                },
                {
                  field: "branchName",
                  title: "分校",
                },
                {
                  field: "registrationName",
                  title: "报名点",
                },
                {
                  field: "studentName",
                  title: "学员",
                },
                {
                  field: 'identity',
                  title: '身份证号'
              },
                {
                  field: "applyTime",
                  title: "申请时间",
                },
                {
                  field: "releaseReason",
                  title: "申请原因",
                },
                {
                  field: "subjectName",
                  title: "归属科目",
                  formatter: function (value, row, index) {
                    return $.table.selectDictLabel(subjectNames, value);
                  },
                },
                {
                  field: "releaseMoney",
                  title: "申请释放金额",
                },
                {
                  field: "checkType",
                  title: "审核情况",
                  formatter: function (value, row, index) {
                    switch (value) {
                      case 0:
                        return "待审核"; break;
                      case 1:
                        return (row.checkOpinion != null && row.checkOpinion != '') ? "成功：" + row.checkOpinion : "成功"; break;
                      case 2:
                        return (row.checkOpinion != null && row.checkOpinion != '') ? "退回：" + row.checkOpinion : "退回"; break;
                      case 3:
                        return "待终审"; break;
                      case 4:
                        return (row.finalReviewOpinion != null && row.finalReviewOpinion != '') ? "终审通过：" + row.finalReviewOpinion : "终审通过"; break;
                      case 5:
                        return (row.finalReviewOpinion != null && row.finalReviewOpinion != '') ? "终审不通过：" + row.finalReviewOpinion : "终审不通过"; break;
                    }
                  },
                },
                {
                  field: "checkTime",
                  title: "初审时间",
                },
                {
                  field: "checkOperator",
                  title: "初审人",
                },
                {
                  field: "finalReviewTime",
                  title: "终审时间",
                },
                {
                  field: "finalReviewOperator",
                  title: "终审人",
                },
          //       {
          //         field: "releaseStatus",
          //         title: "释放状态",
          //         formatter:function(value,row,index){
      		//   if(value==0){
      		// 	  return "未释放";
      		//   }
      		//   if(value==1){
      		// 	  return `<span class=\"label label-primary\">成功</span>`;
      		//   }
      		//   if(value==2){
      		// 	  return `<span class=\"label label-danger\">失败</span>`;
      		//   }
      	  // }
          //       },
                // {
                //   field: "releaseTime",
                //   title: "释放时间",
                // },
                // {
                //   field: "operator",
                //   title: "释放人",
                // },
                {
                  title: "查看凭证",
                  align: "center",
                  formatter: function (value, row, index) {
                    var actions = [];
                    actions.push(
                      '<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="viewVoucher(\'' +
                        row.id +
                        '\')"><i class="fa fa-search"></i>查看凭证</a> '
                    );
                    return actions.join("");
                  },
                },
              ],
            };
            $.table.init(options);

            //加载驾校、分校
            $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
            $("#cxSelectSchool").cxSelect({
              selects: ["schoolId", "branchId", "registrationId"],
              jsonValue: "v",
            });

            /** 初始化时间组件 */
            layui.use('laydate', function(){
                var laydate = layui.laydate;
                    laydate.render({
                        elem: '#beginApplyTime',
                        type: 'datetime',
                        trigger: 'click'
                  });
                        laydate.render({
                        elem: '#endApplyTime',
                        type: 'datetime',
                        trigger: 'click'
                  });
                  laydate.render({
                        elem: '#beginCheckTime',
                        type: 'datetime',
                        trigger: 'click'
                  });
                  laydate.render({
                        elem: '#endCheckTime',
                        type: 'datetime',
                        trigger: 'click'
                  });
                  laydate.render({
                        elem: '#beginFinalReviewTime',
                        type: 'datetime',
                        trigger: 'click'
                  });
                  laydate.render({
                        elem: '#endFinalReviewTime',
                        type: 'datetime',
                        trigger: 'click'
                  });
              });
          });

          /* 查看释放凭证 */
          function viewVoucher(id) {
            var _url = prefix + "/viewVoucher/" + id;
            var options = {
              title: "查看凭证",
              width: "",
              height: "",
              url: _url,
              btn: ["关闭"],
              yes: function (index, layero) {
                $.modal.close(index);
              },
            };
            $.modal.openOptions(options);
          }

          /* 审核释放资金申请 */
          function checkReleaseApply() {
            var rows = $.table.selectRows();
            if (rows.length == 0) {
              $.modal.alertWarning("请至少选择一条记录进行审核");
              return;
            }
            if (rows[0].checkType == 2 || rows[0].checkType == 4 || rows[0].checkType == 5) {
              $.modal.alertWarning("流程已结束，无法再审核");
              return;
            }
            if (rows[0].checkType != 0) {
              $.modal.alertWarning("待初审状态才可以进行初审");
              return;
            }
            var config = {
                  url: prefix + "/checkState",
                  type: "get",
                  dataType: "json",
                  data: 'id='+rows[0].id,
                  beforeSend: function () {
                    $.modal.loading("正在处理中，请稍候...");
                  },
                  success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                      var _url = prefix + "/checkReleaseApply/"+rows[0].id;
                      $.modal.open('审核释放申请', _url);
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                  },
            };
            $.ajax(config);
          }

          /* 释放资金 */
          function release() {
            var rows = $.table.selectRows();
            if (rows.length == 0) {
              $.modal.alertWarning("请至少选择一条记录进行审核");
              return;
            }
            var config = {
                  url: prefix + "/release",
                  type: "post",
                  dataType: "json",
                  data: 'id='+rows[0].id,
                  beforeSend: function () {
                    $.modal.loading("正在处理中，请稍候...");
                  },
                  success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess(result.msg);
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();

                  },
            };
            $.modal.confirm("确定释放所选记录吗?",function(){
      	$.ajax(config);
      });
          }

          /* 释放资金申请终审 */
          function finalReview() {
            var rows = $.table.selectRows();
            if (rows.length == 0) {
              $.modal.alertWarning("请至少选择一条记录进行审核");
              return;
            }
            if (rows[0].checkType == 2 || rows[0].checkType == 4 || rows[0].checkType == 5) {
              $.modal.alertWarning("流程已结束，无法再审核");
              return;
            }
            if(rows[0].checkType != 3) {
              $.modal.alertWarning("初审通过后才可以进行终审");
              return;
            }else if (rows[0].checkType == 4 || rows[0].checkType == 5) {
              $.modal.alertWarning("该记录流程已结束");
              return;
            }
            var _url = prefix + "/finalReview/"+rows[0].id;
            $.modal.open('审核释放申请', _url);
          }
    </script>
  </body>
</html>
