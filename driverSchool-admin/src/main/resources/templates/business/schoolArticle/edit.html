<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改驾校图片')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: summernote-css" />
    <th:block th:include="include :: upload-img-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-article-edit" th:object="${basicArticle}">
            <input name="id" th:field="*{id}" type="hidden">
            <input id="category" name="category" class="form-control" type="hidden" th:field="*{category}">
            <input id="imageUrl" th:value="${basicArticle.image}" type="hidden">
            <input id="isCheck" name="isCheck" type="hidden" value="0">


            <div class="form-group">
                <label class="col-sm-3 control-label">所属学校：</label>
                <div class="col-sm-8">
                    <select id="schoolId" name="schoolId" class="form-control m-b">
                        <option th:each="dict : ${schoolList}" th:text="${dict.name}" th:value="${dict.id}" th:field="*{schoolId}"></option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label is-required">主标题：</label>
                <div class="col-sm-8">
                    <input name="title" th:field="*{title}" class="form-control" type="text">
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label is-required">副标题：</label>
                <div class="col-sm-8">
                    <input name="subhead" th:field="*{subhead}" class="form-control" type="text">
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label is-required">图片：</label>
                <div class="col-sm-8">
                    <div class="image-box">
                        <div class="image-item"  th:style="|background-image: url('${basicArticle.image}');|">
                            <img class="delete-img" src="/img/delete.png" onclick="deleteLedImage(this);">
                        </div>
                    </div>
                    <a class="btn btn-success" href="javascript:addImage();">
                        <label>选择图片</label>
                        <input id="add-input" type="file" accept="image/*" style="display: none" onchange="selectLedSignImage(this);" required>
                    </a>

                    <span class="help-block m-b-none">
                      <i class="fa fa-info-circle" style="color: red;"></i>建议图片尺寸148*148或者296*296, 不能超过2M
                    </span>
                </div>
            </div>



            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">是否显示：</label>
                <div class="col-sm-8">
                    <select name="isShow" class="form-control m-b" th:with="type=${@dict.getType('is_show_article')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{isShow}"></option>
                    </select>
                </div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">排序：</label>
                <div class="col-sm-8">
                    <input name="orderNo" th:field="*{orderNo}" class="form-control" type="text" required>
                    <span class="help-block m-b-none">
                      <i class="fa fa-info-circle" style="color: red;"></i> 数字越小越排前面
                    </span>
                </div>
            </div>


            <div class="form-group">
                <label class="col-sm-3 control-label">详细内容：</label>
                <div class="col-sm-8">
                    <input type="hidden" class="form-control" th:field="*{content}">
                    <div class="summernote" id="content"></div>
                </div>
            </div>


        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: summernote-js" />
    <th:block th:include="include :: upload-img-js" />
    <script th:inline="javascript">
        var maxImage = 1;
        var prefix = ctx + "business/article";
        $("#form-article-edit").validate({
            onkeyup: false,
            focusCleanup: true,
            rules:{
                title:{
                    maxlength:100,
                    remote: {
                        url: prefix + "/checkUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "id": function() {
                                return $.common.trim($("#id").val());
                            },
                            "category":function () {
                                return $.common.trim($("#category").val());
                            },
                            "title":function () {
                                return $.common.trim($("#title").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                }
            },
            messages: {
                "title": {
                    remote: "该资讯内容标题已经存在"
                }
            },
        });

        function submitHandler() {
            if ($.validate.form()) {

                if ($.common.isEmpty($("#imageUrl").val())){
                    $.modal.msgError("图片不能为空");
                    return;
                }

                submitCustom("/edit","ledSignImages");
            }
        }

        // 选中图片
        function selectLedSignImage(input) {
            var file = input.files[0];
            if (file.size / 1024 > 2048) {
                $.modal.msgWarning("图片不能大于2M");
                return false;
            }
            if (file) {
                fileMap.set(file.name, file);
                // 读取并显示
                var reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = function (e) {
                	 var datas = e.target.result;
                     //加载图片获取图片真实宽度和高度
                     var image = new Image();
                     image.onload = function() {
                         var width = image.width;
                         var height = image.height;
                         if ((width != 148 || height != 148) && (width != 296 || height != 296)) {
                             $.modal.msgError("图片尺寸不合要求：148*148或者296*296!");
                             if (file.name) fileMap.delete(file.name);
                             $(".image-item").remove();
                             $("#imageUrl").val('');
                             return false;
                         }
                     };
                    image.src = datas
                     
                    var imageItem = '<div class="image-item" style="background-image: url(\'' + this.result + '\');">';
                    imageItem += '<img class="delete-img" src="/img/delete.png" onclick="deleteImage(this, \'' + file.name + '\')">';
                    imageItem += '</div>';
                    $(".image-box").append($(imageItem));

                    $("#imageUrl").val(this.result);
                }

            }
            return true;
        }

        // 删除图片
        function deleteLedImage(obj, fileName) {
            if (fileName) fileMap.delete(fileName);
            $(obj).parent(".image-item").remove();
            $("#imageUrl").val('');
        }


        $(function() {
            $('.summernote').each(function(i) {
                $('#' + this.id).summernote({
                    lang: 'zh-CN',
                    height : 192,
                    followingToolbar: false,
                    dialogsInBody: true,
                    toolbar: [
                        ['font', ['bold', 'italic', 'underline']],
                        ['fontsize',['fontsize']],
                        ['fontname', ['fontname']],
                        ['color', ['color']],
                        ['para', ['ul', 'ol', 'paragraph']],
                        ['height', ['height']],
                        ['insert', ['table', 'link', 'picture']],
                        ['view', ['fullscreen', 'help', 'preview']],
                    ],
                    callbacks: {
                        onChange: function(contents, $edittable) {
                            $("input[name='" + this.id + "']").val(contents);
                        },
                        onImageUpload: function(files) {
                            var obj = this;
                            var data = new FormData();
                            data.append("file", files[0]);
                            $.ajax({
                                type: "post",
                                url: ctx + "common/upload",
                                data: data,
                                cache: false,
                                contentType: false,
                                processData: false,
                                dataType: 'json',
                                success: function(result) {
                                    if (result.code == web_status.SUCCESS) {
                                        $('#' + obj.id).summernote('insertImage', result.url);
                                    } else {
                                        $.modal.alertError(result.msg);
                                    }
                                },
                                error: function(error) {
                                    $.modal.alertWarning("图片上传失败。");
                                }
                            });
                        }
                    }
                });
                var content = $("input[name='" + this.id + "']").val();
                $('#' + this.id).summernote('code', content);
            })
        });
    </script>
</body>
</html>