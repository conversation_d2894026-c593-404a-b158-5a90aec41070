<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('驾校图片列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>驾校：</label>
                                <select id="schoolId" name="schoolId">
                                    <option value="">选择驾校</option>
                                    <option th:each="dict : ${schoolList}" th:text="${dict.name}" th:value="${dict.id}"></option>
                                </select>
                            </li>

                            <li>
                                <label>标题：</label>
                                <input type="text" name="title"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="business:schoolArticle:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="business:schoolArticle:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="business:article:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning multiple disabled" onclick="check()" shiro:hasPermission="business:article:check">
                    审核
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript" charset="utf-8" th:src="@{/js/show.img.js}"></script>
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:schoolArticle:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:article:remove')}]];
        var isShowDatas = [[${@dict.getType('is_show_article')}]];
        var prefix = ctx + "business/article/schoolArticle";

        $(function() {
            var options = {
                url: ctx + "business/article/list?category=6",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: ctx + "business/article/remove",
                exportUrl: prefix + "/export",
                modalName: "驾校图片",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '学车指引key为“study_guidence”',
                    visible: false
                },
                    {
                        field: 'schoolId',
                        title: '驾校名称',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.school != null) {
                                    return row.school.name;
                                }
                            }
                        }
                    },
                {
                    field: 'title',
                    title: '标题'
                },

                    {
                        field: 'title',
                        title: '副标题'
                    },
                    {
                        field: 'image',
                        title: '图片',
                        formatter: function(value, row, index) {
                            if(index % 2 == 0){
                                var tag = $.common.sprintf("<img id='img' class=\'img-circle img-xs\' data-height=\'%s\' data-width=\'%s\' data-target=\'%s\' src=\'%s\'/>", 300, 800, null, value);
                                var actions = [];
                                actions.push('<a href="javascript:void(0)" onclick="$.img.showImg(1000,600, \'' + value + '\')">'+tag+'</a>');
                                return actions.join('');
                            }else {
                                var tag = $.common.sprintf("<img id='img' class=\'img-circle img-xs\' data-height=\'%s\' data-width=\'%s\' data-target=\'%s\' src=\'%s\'/>", 300, 800, null, value);
                                var actions = [];
                                actions.push('<a href="javascript:void(0)" onclick="$.img.showImg(1000,600, \'' + value + '\')">'+tag+'</a>');
                                return actions.join('');
                            }
                        }
                    },


                {
                    field: 'isShow',
                    title: '是否显示',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(isShowDatas, value);
                    }
                },
                    {
                        field: 'isCheck',
                        title: '是否审核',
                        formatter: function(value, row, index) {
                            if(value == 0){
                                return "未审核";
                            }else if (value == 1) {
                                return "已审核";
                            }else{
                                return "-";
                            }
                        }
                    },
                    // {
                    //     title: '审核状态',
                    //     align: 'center',
                    //     formatter: function (value, row, index) {
                    //         return statusTools(row);
                    //     }
                    // },
                {
                    field: 'orderNo',
                    title: '排序'
                },
                {
                    field: 'updatedTime',
                    title: '更新时间'
                },
                ]
            };
            $.table.init(options);
        });

        function check() {
            var rows = $.table.selectRows();
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            } else if (rows.length == 1) {
                if (rows[0].isCheck == 1) {
                    $.modal.alertWarning("驾校图片" + rows[0].title + "已经是已审核状态。");
                } else {
                    $.modal.confirm("确认要审核驾校图片" + rows[0].title + "吗?", function () {
                        $.operate.submitPro(ctx + "business/article/checkBasicArticle", "post", "json", {ids: $.table.selectColumns("id").join(",")}, "正在审核中");
                    });
                }
            } else {
                $.modal.confirm("确认要审核选中的驾校图片吗?", function () {
                    $.operate.submitPro(ctx + "business/article/checkBasicArticle", "post", "json", {ids: $.table.selectColumns("id").join(",")}, "正在审核中");
                });
            }
        }

        /*状态显示 */
        function statusTools(row) {
            if (row.isCheck == 0) {
                return '<i class=\"fa fa-toggle-off text-info fa-2x\" onclick="enable(\'' + row.id + '\')"></i> ';
            } else {
                return '<i class=\"fa fa-toggle-on text-info fa-2x\" onclick="disable(\'' + row.id + '\')"></i> ';
            }
        }

        /* 资讯内容-停用 */
        function disable(id) {
            $.modal.confirm("确认要停用资讯内容吗？", function() {
                $.operate.post(ctx + "business/article/changeStatus", { "id": id, "isCheck": 0 });
            })
        }
        /* 资讯内容启用 */
        function enable(id) {
            $.modal.confirm("确认要启用资讯内容吗？", function() {
                $.operate.post(ctx + "business/article/changeStatus", { "id": id, "isCheck": 1 });
            })
        }

        //图片预览
        // function showImg(width,height,imgHtml) {
        //     let rotate = 0;
        //     let scale = 1;
        //     var img = '<div id="img" style="display: flex; justify-content: center; align-items: center;"><img src="data:image/png;base64,' + imgHtml + '"/></div>';
        //     table.set();
        //     var _width = $.common.isEmpty(width) ? "430" : width;
        //     var _height = $.common.isEmpty(height) ? "230" : height;
        //     top.layer.open({
        //         type: 1,
        //         area: [_width + 'px', _height + 'px'],
        //         fix: false,
        //         //不固定
        //         maxmin: true,
        //         shade: 0.3,
        //         title: '图片预览',
        //         content: img,
        //         btn: ['旋转图片','放大','缩小','<i class="fa fa-remove"></i> 关闭'],
        //         // 弹层外区域关闭
        //         shadeClose: true,
        //         btn1: function (index, layero, that) {
        //         const image = layero.find('img');
        //         rotate = 90 + rotate;
        //         image.css('transform', `rotate(${rotate}deg)`)
        //         },

        //         btn2: function (index, layero, that) {
        //             const image = layero.find('#img');
        //             //if (scale > 2) return;
        //             scale = 0.3 + scale
        //             image.css('transform', `scale(${scale})`)
        //             return false
        //         },
        //         btn3: function (index, layero, that) {
        //             const image = layero.find('#img');
        //             //if (scale < 0.5) return;
        //             scale = scale - 0.3
        //             image.css('transform', `scale(${scale})`)
        //             return false
        //         }
        //     });
        // }
    </script>
</body>
</html>