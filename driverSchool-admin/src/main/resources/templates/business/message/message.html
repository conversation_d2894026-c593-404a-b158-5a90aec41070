<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('留言中心列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>联系方式：</label>
                                <input type="text" name="contact"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="business:message:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="business:message:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:message:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:message:remove')}]];
        var prefix = ctx + "business/message";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "留言中心",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'content',
                    title: '内容',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value, 50);
                    }
                },
                {
                    field: 'type',
                    title: '留言类型',
                    formatter: function(value, row, index) {
                        if(value == 1){
                            return "产品建议";
                        }else if (value == 2) {
                            return "功能异常";
                        }else{
                            return "-";
                        }
                    }

                },
                {
                    field: 'image',
                    title: '相关图片',
                    formatter:function (value, row,index) {
                        if (value != null){
                            var json= JSON.parse(value);
                            var actions = [];
                            $.each(json, function(i, item) {
                                var monitorImageUrl=item.url;
                                if ($.common.isNotEmpty(monitorImageUrl)) {
                                    if(index % 2 == 0){
                                        actions.push($.table.imageView(monitorImageUrl,$(window).height() - 50,800));
                                    }else {
                                        actions.push($.table.imageView(monitorImageUrl,$(window).height() - 50,800));
                                    }
                                }
                            });
                            return actions.join('');
                        }else{
                            return "无"
                        }
                    }

                },
                {
                    field: 'contact',
                    title: '联系方式'
                },
                {
                    field: 'is48HourContact',
                    title: '48小时允许联系',
                    formatter: function(value, row, index) {
                        if(value == 1){
                            return "是";
                        }else if (value == 2) {
                            return "否";
                        }else{
                            return "-";
                        }
                    }
                },
                {
                    field: 'createTime',
                    title: '提交时间'
                }
                ]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>