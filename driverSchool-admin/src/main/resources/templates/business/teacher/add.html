<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增教练')" />
    <th:block th:include="include :: upload-img-css" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-teacher-add">

            <div id="cxSelectSchool" class="form-group">
                <label class="col-sm-3 control-label is-required">所属学校：</label>
                <div class="col-sm-3">
                    <select id="schoolId" name="schoolId" class="schoolId form-control m-b" data-first-title="所属驾校" required>
                    </select>
                </div>
                <div class="col-sm-3">
                    <select id="branchId" name="branchId" class="branchId form-control m-b" data-first-title="所属分校">
                    </select>
                </div>
                <div class="col-sm-3">
                    <select id="registrationId" name="registrationId" class="registrationId form-control m-b" data-first-title="所属报名点">
                    </select>
                </div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">姓名：</label>
                <div class="col-sm-8">
                    <input id="name" name="name" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">联系方式：</label>
                <div class="col-sm-8">
                    <input name="mobile" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">身份证件号码：</label>
                <div class="col-sm-8">
                    <input id="identity" name="identity" class="form-control" type="text"   onblur="identityValidate()">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">性别：</label>
                <div class="col-sm-8">
                    <select id="gender" name="gender" class="form-control m-b" th:with="type=${@dict.getType('sys_user_sex')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">年龄：</label>
                <div class="col-sm-8">
                    <input id="age" name="age" class="form-control" type="number">
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">入职时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="joinDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">教学类型：</label>
                <div class="col-sm-8" th:with="type=${@dict.getType('school_teacher_type')}">
                    <label th:each="dict : ${type}" class="check-box">
                        <input name="type" type="checkbox" th:value="${dict.dictValue}" th:text="${dict.dictLabel}" required>
                    </label>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">驾照类型：</label>
                <div class="col-sm-8" th:with="type=${@dict.getType('license_types')}">
                    <label th:each="dict : ${type}" class="check-box">
                        <input name="licenseTypes" type="checkbox" th:value="${dict.dictValue}" th:text="${dict.dictLabel}" required>
                    </label>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label is-required">证件有效期：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="licenseExpiration" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label">头像：</label>
                <div class="col-sm-8">
                    <div class="image-box">
                    </div>
                    <a class="btn btn-success" href="javascript:addImage();">
                        <label>选择头像</label>
                        <input id="add-input" type="file" accept="image/*" style="display: none" onchange="selectImage(this);">
                    </a>
                </div>
            </div>


            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: upload-img-js" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/teacher"
        var maxImage = 1;
        $("#form-teacher-add").validate({
            onkeyup: false,
            focusCleanup: true,
            rules:{
                name:{
                    maxlength:20,
                    remote: {
                        url: prefix + "/checkUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "schoolId":function () {
                                return $.common.trim($.form.selectSelects("schoolId"));
                            },
                            "branchId":function () {
                                return $.common.trim($.form.selectSelects("branchId"));
                            },
                            "registrationId":function () {
                                return $.common.trim($.form.selectSelects("registrationId"));
                            },
                            "name": function() {
                                return $.common.trim($("#name").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },
                mobile:{
                    isPhoneOrTel: true
                },
                identity:{
                    isIdentity18:true
                }
            },
            messages: {
                "name": {
                    remote: "该教练名称已经存在"
                }
            }

        });

        function submitHandler() {
            if ($.validate.form()) {
                submitCustom("/add","schoolImages");
            }
        }

        // 输入身份证后，自动补全年龄数据
        function identityValidate() {
            var identity= $("#identity").val();
            if ($.common.isNotEmpty(identity)) {
                var id= /^(\d{15}$|^\d{18}$|^\d{17}(\d|X))$/;
                var isidentityValidate= id.test($("#identity").val());
                if (isidentityValidate) {
                    var formData = new FormData();
                    formData.append("identity", identity);
                    $.ajax({
                        url: prefix + "/getTeacherIdentityInfo",
                        data: formData,
                        type: "post",
                        processData: false,
                        contentType: false,
                        success: function(result) {
                            if (result.code == web_status.SUCCESS) {
                                $("#age").val(result.data.age);
                                $("#gender").val(result.data.gender);
                            } else if (result.code == web_status.WARNING) {
                                $.modal.alertWarning(result.msg)
                            }  else {
                                $.modal.alertError(result.msg);
                            }
                            $.modal.closeLoading();
                        }
                    })
                }
            }
        };

        $("input[name='birthday']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='joinDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='licenseExpiration']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });


        $(function() {
            //加载驾校、分校
            $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
            $('#cxSelectSchool').cxSelect({
                selects: ['schoolId', 'branchId','registrationId'],
                jsonValue: 'v',
            });
        });


    </script>
</body>
</html>