<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('查看教练')" />
    <th:block th:include="include :: upload-img-css" />
    <th:block th:include="include :: datetimepicker-css" />
    <link href="../static/css/awesome-bootstrap-checkbox.css" th:href="@{/css/awesome-bootstrap-checkbox.css}" rel="stylesheet"/>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-teacher-edit" th:object="${schoolTeacher}">
            <input name="id" th:field="*{id}" type="hidden">

            <div class="form-group">
                <label class="col-sm-3 control-label">所属学校：</label>
                <div class="form-control-static" th:if="*{school != null}" th:text="*{school.name}">
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">所属分校：</label>
                <div class="form-control-static" th:if="*{branch != null}"  th:text="*{branch.name}">
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">所属报名点：</label>
                <div class="form-control-static" th:if="*{registration != null}"  th:text="*{registration.name}">
                </div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label">姓名：</label>
                <div class="form-control-static" th:text="*{name}"></div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">联系方式：</label>
                <div class="form-control-static" th:text="*{mobile}"></div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label">身份证件号码：</label>
                <div class="form-control-static" th:text="*{identity}"></div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">性别：</label>
                <div class="col-sm-8">
                    <select name="gender" class="form-control m-b" th:with="type=${@dict.getType('sys_user_sex')}" disabled="disabled">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{gender}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">年龄：</label>
                <div class="form-control-static" th:text="*{age}"></div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">入职时间：</label>
                <div class="form-control-static" th:text="${#dates.format(schoolTeacher.joinDate, 'yyyy-MM-dd')}"></div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label">教学类型：</label>
                <div class="col-sm-8" th:with="type=${@dict.getType('school_teacher_type')}">
                    <div class="checkbox checkbox-danger"  th:each="dict,state: ${type}" >
                        <input th:id="|type${state.index}|" name="licenseTypes" type="checkbox"  th:value="${dict.dictValue}" th:attr="checked=${schoolTeacher.type.contains(dict.dictValue)?true:false}" disabled="disabled">
                        <label th:for="|type{state.index}|" th:text="${dict.dictLabel}">
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">驾照类型：</label>
                <div class="col-sm-8" th:with="type=${@dict.getType('license_types')}">
                    <div class="checkbox checkbox-danger"  th:each="dict,state: ${type}" >
                        <input th:id="|licenseTypes${state.index}|" name="licenseTypes" type="checkbox"  th:value="${dict.dictValue}"  th:attr="checked=${schoolTeacher.licenseTypes.contains(dict.dictValue)?true:false}" disabled="disabled">
                        <label th:for="|licenseTypes${state.index}|" th:text="${dict.dictLabel}">
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">证件有效期：</label>
                <div class="form-control-static" th:text="${#dates.format(schoolTeacher.licenseExpiration, 'yyyy-MM-dd')}"></div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label">头像：</label>
                <div class="col-sm-8">
                    <div class="image-box">
                        <div class="image-item" th:each="file : *{imageFileList}" th:style="|background-image: url('${file.webPath}');|">
                            <input type="hidden" name="imageIds" th:value="${file.id}">
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="form-control-static" th:text="*{remark}"></div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: upload-img-js" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/teacher";
        var maxImage = 1;
        $("#form-teacher-edit").validate({
            focusCleanup: true
        });
    </script>
</body>
</html>