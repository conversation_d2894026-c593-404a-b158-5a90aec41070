<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('教练列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>
                <li>
                  <label>分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="branchId"
                    name="branchId"
                    class="branchId form-control m-b"
                    data-first-title="选择分校"
                  ></select>
                </li>
                <li>
                  <label>报名点：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="registrationId"
                    name="registrationId"
                    class="registrationId form-control m-b"
                    data-first-title="所属报名点"
                  ></select>
                </li>

                <li>
                  <label>姓名：</label>
                  <input type="text" name="name" />
                </li>
                <li>
                  <label style="width: 100px">身份证件号码：</label>
                  <input type="text" name="identity" />
                </li>
                <li>
                  <label>联系方式：</label>
                  <input type="text" name="mobile" />
                </li>
                <li>
                  <label>驾照类型：</label>
                  <select
                    name="licenseTypes"
                    th:with="type=${@dict.getType('license_types')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>

                <li>
                  <label>证件状态：</label>
                  <select
                    name="isLicenseValid"
                    th:with="type=${@dict.getType('is_license_valid')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-success"
            onclick="$.operate.add()"
            shiro:hasPermission="business:teacher:add"
          >
            <i class="fa fa-plus"></i> 添加
          </a>
          <a
            class="btn btn-primary single disabled"
            onclick="$.operate.edit()"
            shiro:hasPermission="business:teacher:edit"
          >
            <i class="fa fa-edit"></i> 修改
          </a>
          <a
            class="btn btn-danger multiple disabled"
            onclick="$.operate.removeAll()"
            shiro:hasPermission="business:teacher:remove"
          >
            <i class="fa fa-remove"></i> 删除
          </a>
          <a
            class="btn btn-warning single disabled"
            onclick="view('','','')"
            shiro:hasPermission="business:teacher:view"
          >
            查看
          </a>
          <a
            class="btn btn-info"
            onclick="$.table.importExcel()"
            shiro:hasPermission="business:teacher:import"
          >
            <i class="fa fa-upload"></i> 导入
          </a>
          <a
            class="btn btn-success"
            onclick="exportSelected()"
            shiro:hasPermission="business:teacher:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:teacher:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:teacher:remove')}]];
      var genderDatas = [[${@dict.getType('sys_user_sex')}]];
      var typeDatas = [[${@dict.getType('school_teacher_type')}]];
      var licenseValidDatas = [[${@dict.getType('is_license_valid')}]];
      var prefix = ctx + "business/teacher";

      $(function () {
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              detailUrl: prefix + "/view/{id}",
              exportUrl: prefix + "/export",
              importTemplateUrl: prefix + "/importTemplate",
              importUrl: prefix + "/importData",
              modalName: "教练",
              showPageGo: true,
              columns: [{
                  checkbox: true
              },
                  {
                      field: 'id',
                      title: '',
                      visible: false
                  },

                  {
                      field: 'schoolId',
                      title: '驾校',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              if (row.school != null) {
                                  return row.school.name;
                              }
                          }
                      }
                  },

                  {
                      field: 'branchId',
                      title: '分校',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              if (row.branch != null) {
                                  return row.branch.name;
                              }
                          }
                      }
                  },

                  {
                      field: 'registrationId',
                      title: '报名点',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              if (row.registration != null) {
                                  return row.registration.name;
                              }
                          }
                      }
                  },

                  {
                      field: 'name',
                      title: '姓名'
                  },
                  {
                      field: 'identity',
                      title: '身份证件号码',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              return basecclusion(value,6,4);
                          }
                      }
                  },
                  {
                      field: 'gender',
                      title: '性别',
                      formatter: function (value, row, index) {
                          return $.table.selectDictLabel(genderDatas, value);
                      }
                  },
                  {
                      field: 'age',
                      title: '年龄'
                  },
                  {
                      field: 'mobile',
                      title: '联系方式',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              return basecclusion(value,3,4);
                          }
                      }
                  },
                  {
                      field: 'type',
                      title: '教学类型',
                      formatter: function (value, row, index) {
                          var json= JSON.parse(value);
                          var actions = [];
                          $.each(json, function(i, item) {
                              var type=item;
                              if ($.common.isNotEmpty(type)) {
                                  actions.push(type);
                              }
                          });
                          return actions.join('，');
                      }
                  },
                  {
                      field: 'licenseTypes',
                      title: '驾照类型',
                      formatter: function (value, row, index) {
                          var json= JSON.parse(value);
                          var actions = [];
                          $.each(json, function(i, item) {
                              var licenseType=item;
                              if ($.common.isNotEmpty(licenseType)) {
                                  actions.push(licenseType);
                              }
                          });
                          return actions.join('，');
                      }
                  },
                  {
                      field: 'joinDate',
                      title: '入职时间'
                  },
                  {
                      field: 'licenseExpiration',
                      title: '证件有效期'
                  },
                  {
                      field: 'isLicenseValid',
                      title: '证件状态',
                      formatter: function (value, row, index) {
                          return $.table.selectDictLabel(licenseValidDatas, value);
                      }
                  }
              ]
          };
          $.table.init(options);

          //加载驾校、分校
          $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
          $('#cxSelectSchool').cxSelect({
              selects: ['schoolId', 'branchId','registrationId'],
              jsonValue: 'v',
          });

      });

      function view(id,width, height) {
          table.set();
          var _url = $.operate.detailUrl(id);
          var options = {
              title: table.options.modalName + "详细",
              width: width,
              height: height,
              url: _url,
              skin: 'layui-layer-gray',
              btn: ['关闭'],
              yes: function (index, layero) {
                  $.modal.close(index);
              }
          };
          $.modal.openOptions(options);
      }


      // 导出数据
      function exportSelected() {
          var ids = $.table.selectColumns("id");
          var schoolId = $('#schoolId').val(); //驾校id
          var branchId = $('#branchId').val(); //分校id
          var registrationId = $('#registrationId').val(); //报名点id
          var name = $("input[name='name']").val(); //教练名称
          var identity = $("input[name='identity']").val(); //身份证号
          var mobile = $("input[name='mobile']").val(); //联系电话
          var licenseTypes = $('select[name="licenseTypes"]').val(); //驾照类型
          var isLicenseValid = $('select[name="isLicenseValid"]').val(); //证件状态

          let fromData = new FormData();
          fromData.append('schoolId', schoolId != null ? schoolId : '');
          fromData.append('branchId', branchId != null ? branchId : '' );
          var registrationId = $('#registrationId').val(); //报名点id
          fromData.append('name', name != null ? name : '');
          fromData.append('identity', identity != null ? identity : '');
          fromData.append('mobile', mobile != null ? mobile : '');
          fromData.append('licenseTypes', licenseTypes != null ? licenseTypes : '');
          fromData.append('isLicenseValid', isLicenseValid != null ? isLicenseValid : '');

          var tipMsg = "确定导出所有数据吗？";
          if($.common.isNotEmpty(ids)){
              tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
              fromData.append('ids', ids);
          }
          $.modal.confirm(tipMsg, function() {
                  $.ajax({
                      url: prefix + "/export",
                      data: fromData,
                      type: "post",
                      processData: false,
                      contentType: false,
                      async: true,
                      success: function(result) {
                          if (result.code == web_status.SUCCESS) {
                              window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                          } else {
                              $.modal.alertError(result.msg);
                          }
                      }
                  })
              });
      }

      /* 查看教练详情 */
      $('#bootstrap-table').on('dbl-click-row.bs.table', function (e, row, $element) {
          table.set();
            var _url = $.operate.detailUrl(row.id);
            var options = {
                title: table.options.modalName + "详细",
                width: "",
                height: "",
                url: _url,
                skin: 'layui-layer-gray',
                btn: ['关闭'],
                yes: function (index, layero) {
                    $.modal.close(index);
                }
            };
            $.modal.openOptions(options);
        });
    </script>
  </body>
  <!-- 导入区域 -->
  <script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                <a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
  </script>
</html>
