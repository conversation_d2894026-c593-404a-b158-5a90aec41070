<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('待处理数据列表')" />
    <th:block th:include="include :: bootstrap-editable-css" />
    <style>
        .bootstrap-table .fixed-table-container .fixed-table-body{
            overflow-x:unset;
            overflow-y:unset;
        }
    </style>
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>驾校：</label>
                                <select id="schoolId" name="params[schoolId]">
                                    <option value="">选择驾校</option>
                                    <option th:each="dict : ${schoolList}" th:text="${dict.name}" th:value="${dict.id}"></option>
                                </select>
                            </li>

                            <li>
                                <label>姓名：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>身份证号：</label>
                                <input type="text" name="identity" placeholder="身份证6位"/>
                            </li>

                            <li>
                                <label>学员卡号：</label>
                                <input type="text" name="simMobile" placeholder="卡号6位"/>
                            </li>

                            <li>
                                <label>快递单号：</label>
                                <input type="text" name="expressNo" placeholder="快递单号6位"/>
                            </li>

                            <li class="select-time">
                                <label style="width: 100px">报名时间： </label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                                       name="params[beginTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                                       name="params[endTime]"/>
                            </li>

                            <li class="select-time">
                                <label style="width: 100px">预登记时间： </label>
                                <input style="width: 135px" type="text" class="layui-input dateTime" id="prepareRegisteDateStartTime" placeholder="开始时间"
                                       name="params[prepareRegisteDateStartTime]"/>
                                <span>-</span>
                                <input style="width: 135px" type="text" class="layui-input dateTime" id="prepareRegisteDateEndTime" placeholder="结束时间"
                                       name="params[prepareRegisteDateEndTime]"/>
                            </li>

                            <li class="select-time">
                                <label style="width: 100px">配送完成时间： </label>
                                <input style="width: 135px" type="text" class="layui-input dateTime" id="activeDateStartTime" placeholder="开始时间"
                                       name="params[activeDateStartTime]"/>
                                <span>-</span>
                                <input style="width: 135px" type="text" class="layui-input dateTime" id="activeDateEndTime" placeholder="结束时间"
                                       name="params[activeDateEndTime]"/>
                            </li>

                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <!-- <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="business:pendingStudentSim:import">
                    <i class="fa fa-upload"></i> 导入
                </a> -->

                <a class="btn btn-warning" onclick="exportSelected()" shiro:hasPermission="business:pendingStudentSim:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped table table-hover" style="overflow: auto">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-table-editable-js" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:studentSim:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:studentSim:remove')}]];
        var studentStatusDatas =[[${@dict.getType('student_status')}]]

        var prefix = ctx + "business/pendingStudentSim";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                importUrl: prefix + "/importData",
                importTemplateUrl: prefix + "/importTemplate",
                exportUrl: prefix + "/export",
                detailUrl: prefix + "/expressInfo/{id}",
                modalName: "待处理数据",
                onEditableSave: onEditableSave,
                showPageGo: true,
                columns: [{
                    checkbox: true
                },

                    {
                        field: 'id',
                        title: '',
                        visible: false
                    },

                    {
                        field: 'studentId',
                        title: '驾校',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.schoolStudent != null) {
                                    return '<div style="width:120px;">' + row.schoolName + '</div>';
                                }
                            }
                        },
                    },

                    {
                        field: 'name',
                        title: '学员',
                        formatter: function (value, row, index) {
                            return '<div style="width:120px;">' + value + '</div>';
                        }
                    },

                    {
                        field: 'identity',
                        title: '身份证号',
                        formatter: function (value, row, index) {
                            return '<div style="width:150px;">' + basecclusion(value,6,4) + '</div>';
                        }
                    },

                    {
                        field: 'mobile',
                        title: '联系号码',
                        formatter: function (value, row, index) {
                            return '<div style="width:120px;">' + value + '</div>';
                        }
                    },

                    {
                        field: 'studentId',
                        // title: '报名时间',
                        title: '报名审核时间',
                        formatter: function (value, row, index) {
                            if (row.schoolStudent != null) {
                                    if ($.common.isNotEmpty(value) && $.common.isNotEmpty(row.schoolStudent.registeDate)) {
                                        return '<div style="width:120px;">' + row.schoolStudent.registeDate + '</div>';
                                }else {
                                    return '<div style="width:120px;">-</div>';
                                }
                            }
                        }
                    },
                    {
                        field: 'studentId',
                        title: '预登记时间',
                        formatter: function (value, row, index) {
                            if (row.schoolStudent != null) {
                                    if ($.common.isNotEmpty(value) && $.common.isNotEmpty(row.schoolStudent.prepareRegisteDate)) {
                                        return '<div style="width:200px;">' + row.schoolStudent.prepareRegisteDate + '</div>';
                                }else {
                                    return '<div style="width:200px;">-</div>';
                                }
                            }
                        }
                    },
                    {
                        field: 'studentId',
                        title: '学习进度',
                        formatter: function(value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.schoolStudent != null) {
                                    return '<div style="width:120px;">' + $.table.selectDictLabel(studentStatusDatas, row.schoolStudent.status) + '</div>';
                                }
                            }
                        }
                    },

                {
                    field: 'simMobile',
                    title: '学员卡号码',
                    formatter: function (value, row, index) {
                            return '<div style="width:120px;">' + value + '</div>';
                        }
                },
                {
                    field: 'deliverAddress',
                    title: '配送地址',
                    formatter: function(value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.deliverProvince!= null && row.deliverCity!= null && row.deliverTown!= null){
                                return '<div style="width:180px;">' + row.deliverProvince+row.deliverCity+row.deliverTown+value + '</div>';
                            }else{
                                return '<div style="width:180px;">' + value + '</div>';
                            }
                        }
                    }
                },
                {
                    field: 'loginCount',
                    title: '登录小程序次数',
                    visible: false
                },
                {
                    field: 'deliverStatus',
                    title: '派送状态',
                    formatter: function(value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            return '<div style="width:120px;">' + value + '</div>';
                        }else {
                            return '<div style="width:120px;">-</div>';
                        }
                    }
                },
                {
                    field: 'activeStatus',
                    title: '订单状态',
                    formatter: function(value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            return '<div style="width:120px;">' + value + '</div>';
                        }else {
                            return '<div style="width:120px;">-</div>';
                        }
                    }
                },
                {
                    field: 'orderNo',
                    title: '订单号',
                    formatter: function(value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            return '<div style="width:120px;">' + value + '</div>';
                        }else {
                            return '<div style="width:120px;">-</div>';
                        }
                    }
                },
                {
                    field: 'expressNo',
                    title: '快递单号',
                    formatter: function(value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            return '<div style="width:120px;">' + value + '</div>';
                        }else {
                            return '<div style="width:120px;">-</div>';
                        }
                    }
                },
                {
                    field: 'activeDate',
                    title: '完成时间',
                    formatter: function(value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            return '<div style="width:200px;">' + value + '</div>';
                        }else {
                            return '<div style="width:200px;">-</div>';
                        }
                    }
                },
                {
                    field: 'isSyn',
                    title: '是否已开卡',
                    visible: false,
                    formatter: function(value, row, index) {
                        if(value == 0){
                            return '<div style="width:120px;">未开卡</div>';
                        }else if (value == 1) {
                            return '<div style="width:120px;">已开卡</div>';
                        }else{
                            return '<div style="width:120px;">未开卡</div>';
                        }
                    }
                },
                {
                    field: 'synFailMsg',
                    title: '开卡反馈',
                    visible: false,
                    formatter: function(value, row, index) {
                        return '<div style="width:120px;">' + value + '</div>';
                    }
                },
                {
                    field: 'isAddWechat',
                    title: '是否加企微',
                    formatter: function(value, row, index) {
                            if(value == 0){
                                return '<div style="width:120px;">否</div>';
                            }else if (value == 1) {
                                return '<div style="width:120px;">是</div>';
                            }else{
                                return '<div style="width:120px;">-</div>';
                            }
                        }
                },
                {
                    field: '',
                    title: '开卡是否需要外呼',
                    formatter: function(value, row, index) {
                        if (row.schoolStudent != null && $.common.isNotEmpty(row.schoolStudent.registeDate)) {
                                let registeDateStr = row.schoolStudent.registeDate;
                                let registeDate = new Date(Date.parse(registeDateStr.replace(/-/g, "/")));
                                let today = new Date();
                                let daysDiff = daysBetween(today, registeDate);
                                if(row.activeStatus != '激活成功' && daysDiff >= 11) {
                                    return '<div style="width:120px;">需要</div>';
                                }else {
                                    return '<div style="width:120px;">-</div>';
                                }
                            }
                    }
                },
                {
                    field: 'isCallOut',
                    title: '有无开卡外呼',
                    editable : {
						type : 'select',
						title : '编辑',
                        emptytext: '请选择',
						source : [{
							value : 0,
							text : '无'
						}, {
							value : 1,
							text : '有'
						}]
					}
                },
                {
                    field: 'callOutInfo',
                    title: '外呼情况说明',
                    editable : {
						type : 'select',
						title : '编辑',
                        model: 'inline',
                        emptytext: '请选择',
						source : [
                        {
							value : 1,
							text : "学员拒接"
						}, 
                        {
							value : 2,
							text : "学员拒绝开卡"
						},
                        {
							value : 3,
							text : "学员不知情"
						},
                        {
							value : 4,
							text : "学员同意"
						},
                        {
							value : 5,
							text : "满五户"
						},
                        {
							value : 6,
							text : "黑户"
						}]
					}
                },
                {
                    field: 'isResetSim',
                    title: '是否重新派卡',
                    formatter: function(value, row, index) {
                            switch(value) {
                                case 0: return '<div style="width:120px;">-</div>'
                                break;
                                case 1: return '<div style="width:120px;">是</div>'
                                break;
                                default: return '<div style="width:120px;">-</div>'
                            }
                        }
                },
                {
                    field: 'failReason',
                    title: '失败原因归类',
                    editable : {
						type : 'select',
						title : '编辑',
                        model: 'inline',
                        emptytext: '请选择',
						source : [
                        {
							value : 1,
							text : "学员原因"
						}, 
                        {
							value : 2,
							text : "系统原因"
						},
                        {
							value : 3,
							text : "快递原因"
						},
                        {
							value : 4,
							text : "门店原因"
						}]
					}
                },
                {
                    field: 'isReturnCash',
                    title: '是否返现',
                    visible: false,
                    editable : {
						type : 'select',
						title : '编辑',
                        model: 'inline',
                        emptytext: '请选择',
						source : [
                        {
							value : 0,
							text : '否'
						},
                        {
							value : 1,
							text : '是'
						}]
					}
                },
                {
                    field: 'returnCashFailReason',
                    title: '返现失败原因',
                    visible: false,
                    editable : {
						type : 'select', //输入框类型
						title : '编辑', //标题
                        model: 'inline',    //编辑模式
                        emptytext: '请选择',  //无对应的值时显示的文字
                        /** 动态显示编辑状态 */
                        noEditFormatter: function(value, row, index){
                            if(row.isReturnCash == 0){
                                return false;
                            } else {
                                return null;
                            }
                        },
						source : [
                        {
							value : 1,
							text : "学员未充值"
						},
                        {
							value : 2,
							text : "学员分开充值"
						},
                        {
							value : 3,
							text : "学员不加企微"
						}]
					}
                },
                {
                    field: 'isReturnCashCallOut',
                    title: '返现是否需要外呼',
                    visible: false,
                    formatter: function(value, row, index) {
                        if ($.common.isNotEmpty(row.activeDate)) {
                                let activeDateStr = row.activeDate;
                                let activeDate = new Date(Date.parse(activeDateStr.replace(/-/g, "/")));
                                let today = new Date();
                                let daysDiff = daysBetween(today, activeDate);
                                if(row.isReturnCash == 0 && daysDiff >= 11) {
                                    return "需要";
                                }else {
                                    return "-";
                                }
                            }
                        }
                },
                {
                    field: 'returnCashIsCallOut',
                    title: '有无返现外呼',
                    visible: false,
                    editable : {
						type : 'select',
						title : '编辑',
                        model: 'inline',
                        emptytext: '请选择',
						source : [
                        {
							value : 0,
							text : "无"
						},
                        {
							value : 1,
							text : "有"
						}]
					}
                },
                {
                    field: 'isDynamic',
                    title: '是否活跃',
                    visible: false,
                    editable : {
						type : 'select',
						title : '编辑',
                        model: 'inline',
                        emptytext: '请选择',
						source : [
                        {
							value : 0,
							text : "否"
						},
                        {
							value : 1,
							text : "是"
						}]
					}
                },
                {
                    field: 'simCardStatus',
                    title: '学员卡状态',
                    visible: false,
                    editable : {
						type : 'select',
						title : '编辑',
                        model: 'inline',
                        emptytext: '请选择',
						source : [
                        {
							value : 1,
							text : "未返现未活跃"
						},
                        {
							value : 2,
							text : "未返现已活跃"
						},
                        {
							value : 3,
							text : "已返现未活跃"
						},
                        {
							value : 4,
							text : "已返现已活跃"
						},
                        {
							value : 5,
							text : "已活跃营销失败"
						},
                        {
							value : 6,
							text : "已活跃营销成功"
						}]
					}
                },
                {
                    field: 'isAddCallOutSale',
                    title: '是否进行新增外呼营销',
                    visible: false,
                    editable : {
						type : 'select',
						title : '编辑',
                        model: 'inline',
                        emptytext: '请选择',
						source : [
                        {
							value : 0,
							text : "否"
						},
                        {
							value : 1,
							text : "是"
						}]
					}
                },
                {
                    field: 'registrationName',
                    title: '门店信息',
                    formatter: function(value, row, index) {
                        if (row.schoolRegistration != null && $.common.isNotEmpty(row.schoolRegistration.name)) {
                                return '<div style="width:180px;">' + row.schoolRegistration.name + '</div>';
                            }else {
                                return '-';
                            }
                        }
                }
                ]
            };
            $.table.init(options);

            /** 初始化时间组件 */
            layui.use('laydate', function(){
                var laydate = layui.laydate;
                lay('.dateTime').each(function(){
                    laydate.render({
                                elem: this,
                                type: 'datetime',
                                trigger: 'click'
                    })
                });
            });
        });


        // 导出数据
        function exportSelected() {
            // var ids = $.table.selectColumns("id");
            // var dataParam = $("#formId").serializeArray();
            // var tipMsg = "确定导出所有数据吗？注意：每次仅能导出3千条数据，超出部分请缩短查询结果的范围。";
            // if($.common.isNotEmpty(ids)){
            //     tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
            //     dataParam.push({ "name": "ids", "value": ids });
            // }
            // $.modal.confirm(tipMsg, function() {
            //     $.post(prefix + "/export", dataParam, function(result) {
            //         if (result.code == web_status.SUCCESS) {
            //             window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
            //         } else {
            //             $.modal.alertError(result.msg);
            //         }
            //     });
            // });
            var ids = $.table.selectColumns("id"); //勾选的行数据id
            var schoolId = $('#schoolId').val(); //驾校id
            var name = $("input[name='name']").val(); //学生名称
            var identity = $("input[name='identity']").val(); //身份证号
            var simMobile = $("input[name='simMobile']").val(); //学员卡号
            var expressNo = $("input[name='expressNo']").val(); //快递单号
            var startTime = $("input[id='startTime']").val(); //报名开始时间
            var endTime = $("input[id='endTime']").val(); //报名结束时间
            var prepareRegisteDateStartTime = $("input[id='prepareRegisteDateStartTime']").val(); //预登记开始时间
            var prepareRegisteDateEndTime = $("input[id='prepareRegisteDateEndTime']").val(); //预登记开始时间
            var activeDateStartTime = $("input[id='activeDateStartTime']").val(); //配送完成开始时间
            var activeDateEndTime = $("input[id='activeDateEndTime']").val(); //配送完成结束时间

            var tipMsg = "确定导出所有数据吗？注意：每次仅能导出5万条数据，超出部分请缩短查询结果的范围。";

            let fromData = new FormData();
            fromData.append('schoolId', schoolId != null ? schoolId : '');
            fromData.append('name', name != null ? name : '' );
            fromData.append('identity', identity != null ? identity : '');
            fromData.append('simMobile', simMobile != null ? simMobile : '');
            fromData.append('expressNo', expressNo != null ? expressNo : '');
            fromData.append('startTime', startTime != null ? startTime : '');
            fromData.append('endTime', endTime != null ? endTime : '');
            fromData.append('prepareRegisteDateStartTime', prepareRegisteDateStartTime != null ? prepareRegisteDateStartTime : '');
            fromData.append('prepareRegisteDateEndTime', prepareRegisteDateEndTime != null ? prepareRegisteDateEndTime : '');
            fromData.append('activeDateStartTime', activeDateStartTime != null ? activeDateStartTime : '');
            fromData.append('activeDateEndTime', activeDateEndTime != null ? activeDateEndTime : '');

            if($.common.isNotEmpty(ids)){
                tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
                fromData.append('ids', ids);
            }
            $.modal.confirm(tipMsg, function() {
                $.ajax({
                url: prefix + "/export",
                data: fromData,
                type: "post",
                processData: false,
                contentType: false,
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                            window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                            $.modal.alertError(result.msg);
                    }
                }
            })
        });
    }

        /** 比较时间 */
        function daysBetween(date1, date2) {
            const ONE_DAY_MS = 1000 * 60 * 60 * 24;
            const timeDiff = Math.abs(date2.getTime() - date1.getTime());
            return Math.floor(timeDiff / ONE_DAY_MS);
        }

        /** 表格行内编辑事件 */
        function onEditableSave (field, row, rowIndex, oldValue, $el) {
            $.modal.loading('修改中...');
            let fromData = new FormData();
            fromData.append('id', row.id);
            fromData.append('isAddWechat', row.isAddWechat != null ? row.isAddWechat : '' );
            fromData.append('isCallOut', row.isCallOut != null ? row.isCallOut : '');
            fromData.append('callOutInfo', row.callOutInfo != null ? row.callOutInfo : '');
            fromData.append('failReason', row.failReason != null ? row.failReason : '');
            fromData.append('isReturnCash', row.isReturnCash != null ? row.isReturnCash : '');
            fromData.append('returnCashFailReason', row.returnCashFailReason != null ? row.returnCashFailReason : '');
            fromData.append('returnCashIsCallOut', row.returnCashIsCallOut != null ? row.returnCashIsCallOut : '');
            fromData.append('isDynamic', row.isDynamic != null ? row.isDynamic : '');
            fromData.append('simCardStatus', row.simCardStatus != null ? row.simCardStatus : '');
            fromData.append('isDynamic', row.isDynamic != null ? row.isDynamic : '');
            fromData.append('simCardStatus', row.simCardStatus != null ? row.simCardStatus : '');
            $.ajax({
                url: prefix + "/update",
                data: fromData,
                type: "post",
                processData: false,
                contentType: false,
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess(result.msg);
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.table.refresh();
                    $.modal.closeLoading();
                }
            })
        }


    </script>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                <a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
</script>
</html>