<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('学时平台数据列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
      var stunum = [[${schoolStudent.stunum}]];
      var prefix = ctx + "business/student";

      $(function () {
        var options = {
          url: prefix + "/studyTimeList?stunum="+stunum,
          showSearch: false,
          showRefresh: false,
          showToggle: false,
          showColumns: false,
          sortName: "trainphase",
          sortOrder: "desc",
          modalName: "学时平台数据",
          columns: [
            {
              field: "id",
              title: "主键",
              visible: false,
            },
            {
              field: "stunum",
              title: "学号",
            },
            {
              field: "name",
              title: "学员姓名",
            },
            {
              field: "idcard",
              title: "证件号",
            },
            {
              field: "inscode",
              title: "驾校编号",
            },
            {
              field: "traintype",
              title: "培训车型",
            },
            {
              field: "trainphase",
              title: "培训阶段",
            },
            {
              field: "pushtime",
              title: "共享公安时间",
            },
            {
              field: "updateTime",
              title: "更新时间",
            },
          ],
        };
        $.table.init(options);
      });
    </script>
  </body>
</html>
