<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
  <th:block th:include="include :: header('审核学员')" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
  <form
          class="form-horizontal m"
          id="form-schoolStudent-edit"
          th:object="${schoolStudent}"
  >
    <input name="id" th:field="*{id}" type="hidden" />
    <!-- 必填字段校验-->
    <div class="form-group col-sm-6">
    <div class="form-group">
      <label class="col-sm-5 control-label is-required">综合服务费：</label>
      <div class="col-sm-6">
        <input
                name="serviceFee"
                th:field="*{serviceFee}"
                class="form-control"
                type="text"
                required
        />
        <span class="help-block m-b-none">
            </span>
      </div>
    </div>

    <div class="form-group">
      <label class="col-sm-5 control-label is-required">理论培训费：</label>
      <div class="col-sm-6">
        <input
                name="textTrainFee"
                th:field="*{textTrainFee}"
                class="form-control"
                type="text"
                required
        />
        <span class="help-block m-b-none">
            </span>
      </div>
    </div>

    <div class="form-group">
      <label class="col-sm-5 control-label is-required">科二实际操作培训费：</label>
      <div class="col-sm-6">
        <input
                name="operateFee"
                th:field="*{operateFee}"
                class="form-control"
                type="text"
                required
        />
        <span class="help-block m-b-none">
            </span>
      </div>
    </div>

    <div class="form-group">
      <label class="col-sm-5 control-label is-required">科三实际操作培训费：</label>
      <div class="col-sm-6">
        <input
                name="operateFee2"
                th:field="*{operateFee2}"
                class="form-control"
                type="text"
                required
        />
        <span class="help-block m-b-none">
            </span>
      </div>
      <div class="col-sm-12"></div>
      <div class="form-group">
        <!--合同金额只能查看，不能修改-->
        <label class="col-sm-5 control-label is-required">培训费合计：</label>
        <div class="col-sm-6">
          <input
                  name="contractFee"
                  readonly="readonly"
                  th:field="*{contractFee}"
                  class="form-control"
                  type="text"
                  required
          />
          <span class="help-block m-b-none">
              <p style="color: red">培训费合计由综合服务费、理论培训费、科二、科三实操培训费汇总得出。</p>
            </span>
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-5 control-label is-required">平台支付金额：</label>
        <div class="col-sm-6">
          <select
                  name="payFee"
                  class="form-control"
                  th:field="*{payFee}"
                  required>
            <option value="1500.00">1500.00</option>
            <option value="4300.00">4300.00</option>
          </select>
        </div>
      </div>
    </div>

    </div>
    <div class="form-group col-sm-6">
    <div class="form-group">
      <label class="col-sm-5 control-label"
      >科二学时单价：</label
      >
      <div class="col-sm-6">
        <input
                name="studyTimeFee"
                id="studyTimeFee"
                class="form-control"
                type="text"
                th:field="*{studyTimeFee}"
        />
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-5 control-label"
      >科三学时单价：</label
      >
      <div class="col-sm-6">
        <input
                name="studyTimeFee2"
                id="studyTimeFee2"
                class="form-control"
                type="text"
                th:field="*{studyTimeFee2}"
        />
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-5 control-label"
      >科目二补训费/次：</label
      >
      <div class="col-sm-6">
        <input
                name="phase2Fee1"
                id="phase2Fee1"
                class="form-control"
                type="text"
                th:field="*{phase2Fee1}"
        />
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-5 control-label"
      >科目三补训费/次：</label
      >
      <div class="col-sm-6">
        <input
                name="phase3Fee1"
                id="phase3Fee1"
                class="form-control"
                type="text"
                th:field="*{phase3Fee1}"
        />
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-5 control-label"
      >接送费/次：</label
      >
      <div class="col-sm-6">
        <input
                name="jiesongFee"
                id="jiesongFee"
                class="form-control"
                type="text"
                th:field="*{jiesongFee}"
        />
      </div>
    </div>
    </div>

    <div class="form-group">
      <label class="col-sm-12 control-label" style="text-align: left">补充条款：</label>
      <div class="col-sm-12">
            <textarea
                    name="attachedItem"
                    id="attachedItem"
                    class="form-control"
                    rows="4"
                    th:field="*{attachedItem}" maxlength="300"></textarea>
        <span class="help-block m-b-none">
              <p style="color: red">补充条款不多于300个字</p>
            </span>
      </div>
    </div>
  </form>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
  var prefix = ctx + "business/student";
  $("#form-schoolStudent-edit").validate({
    focusCleanup: true,
  });

  function submitHandler() {
    if ($.validate.form()) {
      var textTrainFee = parseFloat($('[name="textTrainFee"]').val()) || 0;
      var serviceFee = parseFloat($('[name="serviceFee"]').val()) || 0;
      var operateFee = parseFloat($('[name="operateFee"]').val()) || 0;
      var operateFee2 = parseFloat($('[name="operateFee2"]').val()) || 0;

      // 检查是否所有值都有效
      if (isNaN(operateFee) || isNaN(serviceFee) || isNaN(textTrainFee)|| isNaN(operateFee2)) {
        var errMsg="综合服务费，理论培训费，科二、科三实际操作培训费必须输入数字"
        $.modal.alert("审核失败："+errMsg);
        return false;
      }
      if(operateFee+serviceFee+textTrainFee+operateFee2<1500){
        var errMsg="综合服务费，理论培训费，科二、科三实际操作培训费总计不能少于1500"
        $.modal.alert("审核失败："+errMsg);
        return false;
      }


      $.operate.save(
              prefix + "/checkStudent",
              $("#form-schoolStudent-edit").serialize()
      );
    }
  }
  $(document).ready(function() {
    // Function to update contractFee
    function updateContractFee() {
      // Fetch values from input fields
      var textTrainFee = parseFloat($('[name="textTrainFee"]').val()) || 0;
      var serviceFee = parseFloat($('[name="serviceFee"]').val()) || 0;
      var operateFee = parseFloat($('[name="operateFee"]').val()) || 0;
      var operateFee2 = parseFloat($('[name="operateFee2"]').val()) || 0;

      // Calculate total contract fee
      var total = textTrainFee + serviceFee + operateFee + operateFee2;

      // Update contractFee input field
      $('[name="contractFee"]').val(total.toFixed(2)); // Adjust decimal places as needed
    }

    // Initial update when the page loads
    updateContractFee();

    // Attach input event listeners to the input fields
    $('[name="textTrainFee"], [name="serviceFee"], [name="operateFee"], [name="operateFee2"]').on('input', function() {
      updateContractFee();
    });
  });
</script>
</body>
</html>
