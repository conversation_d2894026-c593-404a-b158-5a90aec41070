<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('学员交费码列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>

                            <li>
                                <label>驾校：</label>
                                <select id="schoolId" name="schoolId">
                                    <option value="">选择驾校</option>
                                    <option th:each="dict : ${schoolList}" th:text="${dict.name}" th:value="${dict.id}"></option>
                                </select>
                            </li>

                            <li>
                                <label>分校：</label>
                                <select id="branchId" name="branchId">
                                    <option value="">选择分校</option>
                                    <option th:each="dict : ${schoolBranchList}" th:text="${dict.name}" th:value="${dict.id}"></option>
                                </select>
                            </li>

                            <li>
                                <label>报名点：</label>
                                <select id="registrationId" name="registrationId">
                                    <option value="">选择报名点</option>
                                    <option th:each="dict : ${registrationList}" th:text="${dict.name}" th:value="${dict.id}"></option>
                                </select>
                            </li>

                            <li>
                                <label>姓名：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>身份证号：</label>
                                <input type="text" name="params[identity]" placeholder="身份证6位"/>
                            </li>
                            <li>
                                <label>手机号：</label>
                                <input type="text" name="mobile"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-warning" onclick="exportSelected()" shiro:hasPermission="business:student:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "business/student";

        $(function() {
            var options = {
                url: prefix + "/list",
                exportUrl: prefix + "/payCodeExport",
                modalName: "学员交费码",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                    {
                        field: 'schoolId',
                        title: '驾校',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.school != null) {
                                    return row.school.name;
                                }
                            }
                        }
                    },
                    {
                        field: 'branchId',
                        title: '分校',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.branch != null) {
                                    return row.branch.name;
                                }
                            }
                        }
                    },
                    {
                        field: 'registrationId',
                        title: '报名点',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.registration != null) {
                                    return row.registration.name;
                                }
                            }
                        }
                    },
                    {
                        field: 'name',
                        title: '学员'
                    },
                    {
                        field: 'identity',
                        title: '身份证号',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                return basecclusion(value,6,4);
                            }
                        }
                    },
                    {
                        field: 'mobile',
                        title: '手机号',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                return basecclusion(value,3,4);
                            }
                        }
                    },
                {
                    field: 'registeDate',
                    title: '报名时间'
                },
                {
                    field: 'payCode',
                    title: '交费码'
                }]
            };
            $.table.init(options);
        });

        // 导出数据
        function exportSelected() {
            var ids = $.table.selectColumns("id");
            var dataParam = $("#formId").serializeArray();
            var tipMsg = "确定导出所有数据吗？";
            if($.common.isNotEmpty(ids)){
                tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
                dataParam.push({ "name": "ids", "value": ids });
            }
            $.modal.confirm(tipMsg, function() {
                $.post(prefix + "/payCodeExport", dataParam, function(result) {
                    if (result.code == web_status.SUCCESS) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }

    </script>
</body>
</html>