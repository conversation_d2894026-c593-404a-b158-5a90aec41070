<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <th:block th:include="include :: header('学员退学')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: upload-img-css" />
  </head>
  <link
    href="../static/css/dropOut.css"
    th:href="@{/css/dropOut.css}"
    rel="stylesheet"
  />
  <body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <form
        class="form-horizontal m"
        id="form-student-out"
        th:object="${schoolStudent}"
      >
        <input name="id" th:field="*{id}" type="hidden" />

        <div class="row">
          <div class="form-group">
            <label
              class="col-sm-3 control-label"
              style="color: red; font-size: 18px; font-weight: bold"
              >备注：</label
            >
            <div
              class="col-sm-9"
              style="color: red; font-size: 18px; font-weight: bold"
            >
              退学之后，请通知学员在手机端进行确认，<br />确认之后计时平台才会同步退学，监管资金同步释放。
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label">学员姓名：</label>
            <div class="form-control-static" th:text="*{name}"></div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label">身份证号：</label>
            <div class="form-control-static" th:text="*{identity}"></div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label">学号：</label>
            <div class="form-control-static" th:text="*{stunum}"></div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label">考取驾照类型：</label>
            <div class="form-control-static" th:text="*{licenseType}"></div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label">学习状态：</label>
            <div class="form-control-static" th:text="${statusLable}"></div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label is-required">退学原因：</label>
            <div class="col-sm-8">
              <input
                id="dropOutReason"
                name="dropOutReason"
                class="form-control"
                type="text"
                placeholder="请输入退学原因"
                required
              />
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label"
              >上传退学凭证或合同照片*<br />（最多不超过5张照片）：</label
            >
            <div class="col-sm-8">
              <div class="image-box"></div>
              <a class="btn btn-success" href="javascript:addImage();">
                <label>选择图片</label>
                <input
                  id="add-input"
                  type="file"
                  accept="image/*"
                  style="display: none"
                  onchange="selectImage(this);"
                />
              </a>
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-3 control-label"></label>
            <div class="col-sm-12">
              <span
                class="help-block m-b-none"
                style="color: red; font-size: 18px"
              >
                <i style="color: red"></i>凭证实例图：退费协议证明*、公众网截图
                (学员已注销/学员已转学)*、或其他合同证明其他问题请咨询系统客服。
              </span>
            </div>
          </div>
          <div class="imgBox">
            <div id="img1" class="img1"></div>
            <div id="img2" class="img2"></div>
            <div id="img3" class="img3"></div>
          </div>
        </div>
      </form>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <th:block th:include="include :: upload-img-js" />
    <script th:inline="javascript">
      var prefix = ctx + "business/student";

      var maxImage = 5;

      $("#form-student-out").validate({
        focusCleanup: true,
      });

      $("#img1").click(function () {
        var json = {
          title: "",
          id: 1,
          start: 0,
          data: [
            {
              alt: "",
              pid: 1,
              src: "/img/info1.png",
              thumb: "/img/info1.png",
            },
          ],
        };
        layer.photos({
          photos: json,
          closeBtn: 0,
          anim: 5,
        });
      });

      $("#img2").click(function () {
        var json = {
          title: "",
          id: 2,
          start: 0,
          data: [
            {
              alt: "",
              pid: 2,
              src: "/img/info2.png",
              thumb: "/img/info2.png",
            },
          ],
        };
        layer.photos({
          photos: json,
          closeBtn: 0,
          anim: 5,
        });
      });

      $("#img3").click(function () {
        var json = {
          title: "",
          id: 3,
          start: 0,
          data: [
            {
              alt: "",
              pid: 3,
              src: "/img/info3.png",
              thumb: "/img/info3.png",
            },
          ],
        };
        layer.photos({
          photos: json,
          closeBtn: 0,
          anim: 5,
        });
      });

      function submitHandler() {
        var imageCount = $(".image-box > .image-item").length;
        if (!imageCount) {
          $.modal.msgWarning("必须上传退学凭证");
          return;
        }
        if ($.validate.form()) {
          submit("/dropOutSave");
        }
      }
    </script>
  </body>
</html>
