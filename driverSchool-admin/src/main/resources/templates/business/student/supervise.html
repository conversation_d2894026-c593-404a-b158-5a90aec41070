<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改已监管学员')" />
    <th:block th:include="include :: datetimepicker-css" />
    <style type="text/css">.user-info-head{position:relative;display:inline-block;}.user-info-head:hover:after{content:'\f030';position:absolute;left:0;right:0;top:0;bottom:0;color:#eee;background:rgba(0,0,0,0.5);font-family:FontAwesome;font-size:24px;font-style:normal;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;cursor:pointer;line-height:110px;border-radius:50%;}</style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-student-edit" th:object="${schoolStudent}">
            <input name="id" th:field="*{id}" type="hidden">
            <input name="headImage" th:field="*{headImage}" type="hidden">

            <div class="row">
                <h4 class="form-header h4">基本信息</h4>
                <div class="col-xs-6 col-md-3">
                    <div class="text-center" style="margin-top: 40px;">
                        <p class="user-info-head" onclick="avatar()"><img id="img_id" class="img-circle img-lg" th:src="(${#strings.isEmpty(schoolStudent.headImage)}) ? @{/img/default_avatar.png} : @{${schoolStudent.headImage}}"></p>
                        <p><a href="javascript:avatar()">点击上传头像</a></p>
                    </div>
                </div>
                <div class="col-xs-12 col-md-9">
                    <div class="row">
                        <div class="col-xs-6 col-md-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">姓名：</label>
                                <div class="col-sm-8">
                                    <input name="name" th:field="*{name}" class="form-control" type="text" required disabled="disabled">
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6 col-md-4">
                        </div>
                        <div class="col-xs-6 col-md-4">
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-xs-6 col-md-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">手机号：</label>
                                <div class="col-sm-8">
                                    <input  name="mobile" th:field="*{mobile}" class="form-control" type="text" required>
                                </div>
                            </div>
                        </div>

                        <div class="col-xs-6 col-md-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">性别：</label>
                                <div class="col-sm-8">
                                    <select name="gender" class="form-control m-b"  th:with="type=${@dict.getType('sys_user_sex')}" required>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{gender}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6 col-md-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">出生日期：</label>
                                <div class="col-sm-8">
                                    <div class="input-group date">
                                        <input name="birthday" class="form-control" th:field="*{birthday}" placeholder="yyyy-MM-dd" type="text" required>
                                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xs-6 col-md-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">民族：</label>
                                <div class="col-sm-8">
                                    <select name="nation" class="form-control m-b" th:with="type=${@dict.getType('nation_type')}">
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"  th:field="*{nation}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6 col-md-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">学历：</label>
                                <div class="col-sm-8">
                                    <select name="education" class="form-control m-b" th:with="type=${@dict.getType('education')}">
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"  th:field="*{education}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6 col-md-4">
                        </div>
                    </div>

                    <div id="cxSelect1" class="row">
                        <div class="col-xs-12 col-sm-6 col-md-8">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">现住具体地址：</label>
                                <div class="col-sm-3">
                                    <select id="province" name="province" class="province form-control" data-first-title="现住省份" required>
                                        <option value="">现住省份</option>
                                        <option th:if="*{province != null and province != ''}" th:value="*{province}" th:text="*{province}" selected></option>
                                    </select>
                                </div>
                                <div class="col-sm-3">
                                    <select id="city" name="city" class="city form-control" data-first-title="现住城市" required>
                                        <option value="">现住城市</option>
                                        <option th:if="*{city != null and city != ''}" th:value="*{city}" th:text="*{city}" selected></option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select id="town" name="town" class="town form-control" data-first-title="现住镇区/县">
                                        <option value="">现住镇区/县</option>
                                        <option th:if="*{town != null and town != ''}" th:value="*{town}" th:text="*{town}" selected></option>
                                    </select>
                                </div>
                            </div>
                        </div>


                        <div class="col-xs-12 col-sm-6 col-md-4">
                            <div class="col-sm-12">
                                <input name="address" class="form-control" type="text" th:field="*{address}">
                            </div>
                        </div>


                    </div>

                    <div id="cxSelect2" class="row">
                        <div class="col-xs-12 col-sm-6 col-md-8">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">户籍具体地址：</label>
                                <div class="col-sm-3">
                                    <select id="hujiProvince" name="hujiProvince" class="hujiProvince form-control" data-first-title="户籍省" required>
                                        <option value="">户籍省</option>
                                        <option th:if="*{hujiProvince != null and hujiProvince != ''}" th:value="*{hujiProvince}" th:text="*{hujiProvince}" selected></option>
                                    </select>
                                </div>
                                <div class="col-sm-3">
                                    <select id="hujiCity" name="hujiCity" class="hujiCity form-control" data-first-title="户籍城市" required>
                                        <option value="">户籍城市</option>
                                        <option th:if="*{hujiCity != null and hujiCity != ''}" th:value="*{hujiCity}" th:text="*{hujiCity}" selected></option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select id="hujiTown" name="hujiTown" class="hujiTown form-control" data-first-title="户籍镇区/县">
                                        <option value="">户籍镇区/县</option>
                                        <option th:if="*{hujiTown != null and hujiTown != ''}" th:value="*{hujiTown}" th:text="*{hujiTown}" selected></option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-xs-12 col-sm-6 col-md-4">
                            <div class="col-sm-12">
                                <input name="hujiAddress" class="form-control" type="text" th:field="*{hujiAddress}" >
                            </div>
                        </div>

                    </div>

                </div>
            </div>


            <div class="row">
                <h4 class="form-header h4">驾照信息</h4>
                <div  class="row">
                    <div class="col-xs-12 col-sm-6 col-md-12">
                        <div id="cxSelectSchool" class="form-group">
                            <label class="col-sm-2 control-label is-required">所属学校：</label>
                            <div class="col-sm-3">
                                <input  th:if="*{school != null }" class="form-control" type="text" disabled="disabled"  th:field="*{school.name}">
                            </div>
                            <div class="col-sm-4">
                                <select id="branchId" name="branchId" class="branchId form-control m-b" data-first-title="所属分校" >
                                    <option th:if="*{branch != null }" th:value="*{branch.id}" th:text="*{branch.name}" selected></option>
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <select id="registrationId" name="registrationId" class="registrationId form-control m-b" data-first-title="所属报名点">
                                    <option th:if="*{registration != null }" th:value="*{registration.id}" th:text="*{registration.name}" selected></option>
                                </select>
                            </div>
                        </div>
                    </div>


                </div>


<!--                <div class="row">
                    <div class="col-xs-6 col-md-4">
                        <div class="form-group" th:if="*{isSupervise == 0}">
                            <label class="col-sm-4 control-label is-required">是否资金监管：</label>
                            <div class="col-sm-8">
                                <select name="isSupervise" class="form-control m-b" th:with="type=${@dict.getType('student_is_supervise')}" required>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{isSupervise}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-6 col-md-4">
                    </div>
                    <div class="col-xs-6 col-md-4">

                    </div>
                </div>-->


                <div class="row">
                    <div class="col-xs-12 col-sm-6 col-md-12">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">备注：</label>
                            <div class="col-sm-10">
                                <textarea name="remark" class="form-control" rows="5" maxlength="500">[[*{remark}]]</textarea>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </form>
    </div>

    <div class="row">
        <div class="col-sm-offset-5 col-sm-10">
            <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
            <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭 </button>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/student";

        $("#form-student-edit").validate({
            onkeyup: false,
            rules:{
                name:{
                    maxlength:20,
                    remote: {
                        url: prefix + "/checkUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "id": function() {
                                return $.common.trim($("#id").val());
                            },
                            "name": function() {
                                return $.common.trim($("#name").val());
                            },
                            "schoolId":function () {
                                return $.common.trim($.form.selectSelects("schoolId"));
                            },
                            "branchId":function () {
                                return $.common.trim($.form.selectSelects("branchId"));
                            },
                            "registrationId":function () {
                                return $.common.trim($.form.selectSelects("registrationId"));
                            },
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },
                identity:{
                    isIdentity18:true,
                    remote: {
                        url: prefix + "/checkUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "id": function() {
                                return $.common.trim($("#id").val());
                            },
                            "identity": function() {
                                return $.common.trim($("#identity").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },
                mobile:{
                    isPhone: true,
                    remote: {
                        url: prefix + "/checkUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "id": function() {
                                return $.common.trim($("#id").val());
                            },
                            "name": function() {
                                return $.common.trim($("#name").val());
                            },
                            "mobile": function() {
                                return $.common.trim($("#mobile").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                }
            },

            messages: {
                "name": {
                    remote: "该学员姓名已经存在"
                },
                "identity":{
                    remote: "该学员身份证号已经存在"
                },
                "mobile":{
                    remote: "该学员手机号已经存在"
                }
            },
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-student-edit').serialize());
            }
        }


        $("input[name='birthday']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='oldLicenseDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });


        /*用户管理-头像*/
        function avatar() {
            var url = ctx + 'system/user/profile/avatar?id='+$("#id").val();
            top.layer.open({
                type: 2,
                area: [$(window).width() + 'px', $(window).height() + 'px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "添加头像",
                content: url,
                btn: ['确定', '关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                yes: function(index, layero) {
                    var iframeWin = layero.find('iframe')[0];
                    iframeWin.contentWindow.submitHandler(index, layero);
                },
                cancel: function(index) {
                    return true;
                }
            });
        }


        var studyCenterId= [[${schoolStudent.studyCenterId}]]

        function selectSchoolChange(select){
            var schoolId= $(select).find("option:selected").val();
            if ($.common.isNotEmpty(schoolId)){
                var formdata = new FormData();
                formdata.append("schoolId", schoolId);
                $.ajax({
                    url: ctx + "business/school/selectStudyCenterData",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            $("#studyCenterId option").remove();
                            var option="<option value=''>请选择</option>";
                            $.each(result.data, function(index, value) {
                                if (value.organId == studyCenterId){
                                    option +="<option value='"+value.organId+"' selected='selected'>"+value.organText+"</option>"
                                }else{
                                    option +="<option value='"+value.organId+"'>"+value.organText+"</option>"
                                }
                            });
                            $("#studyCenterId").append(option);

                        } else if (result.code == web_status.WARNING) {
                            $.modal.alertWarning(result.msg)
                        }  else {
                            $.modal.alertError(result.msg);
                        }
                        $.modal.closeLoading();
                    }
                })
            }else{
                $("#studyCenterId option").remove();
                $("#studyCenterId").append("<option value=''>请选择</option>");
            }
        }


        $(function() {
            // 加载省市区
            $.cxSelect.defaults.url = ctx + "business/area/areaData";
            $('#cxSelect1').cxSelect({
                selects: ['province', 'city', 'town'],
                nodata: 'none'
            });

            $.cxSelect.defaults.url = ctx + "business/area/areaData";
            $('#cxSelect2').cxSelect({
                selects: ['hujiProvince', 'hujiCity', 'hujiTown'],
                nodata: 'none'
            });


            //加载驾校、分校
            $.cxSelect.defaults.url = ctx + "business/school/schoolTowData";
            $('#cxSelectSchool').cxSelect({
                selects: ['branchId','registrationId'],
                jsonValue: 'v',
            });


            });

    </script>
</body>
</html>