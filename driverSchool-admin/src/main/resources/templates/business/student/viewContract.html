<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <th:block th:include="include :: header('查看签署合同')" />
  </head>
  <body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <form
        class="form-horizontal m"
        id="form-contract-edit"
        th:object="${schoolStudentContract}"
      >
        <input
          name="studentId"
          th:field="*{studentId}"
          class="form-control"
          type="hidden"
        />
        <div class="form-group">
          <embed
            th:inline="text"
            th:src="${schoolStudentContract.contractUrl}"
            type="application/pdf"
            width="100%"
            height="1000px"
          />
        </div>
        <div class="form-group" th:if="${hasSpecialSchool}">
          <div class="col-sm-8" style="margin-left: 500px">
            <a class="btn btn-success" onclick="createPayCode()">
              产生支付二维码
            </a>
          </div>
        </div>
      </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
      var prefix = ctx + "business/student";

        /* 产生支付二维码 */
        function createPayCode() {
          var _url = prefix + "/createPayCode/" + [[${schoolStudentContract.studentId}]];
          var options = {
            title: "合同支付二维码",
            width: "500",
            height: "500",
            url: _url,
            skin: "layui-layer-gray",
            btn: ["关闭"],
            yes: function (index, layero) {
              $.modal.close(index);
            },
          };
          $.modal.openOptions(options);
        }
    </script>
  </body>
</html>
