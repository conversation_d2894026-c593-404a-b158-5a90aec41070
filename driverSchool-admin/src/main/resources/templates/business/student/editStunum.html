<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <th:block th:include="include :: header('修改学员编号')" />
  </head>
  <body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <form
        class="form-horizontal m"
        id="form-student-editStunum"
        th:object="${schoolStudent}"
      >
        <input name="id" th:field="*{id}" type="hidden" />
        <div class="row">
          <div class="form-group">
            <label class="col-sm-3 control-label">当前学号：</label>
            <div class="col-sm-6" style="width: 320px">
              <input
                name="stunum"
                class="form-control"
                type="text"
                style="width: 300px"
              />
              <span class="help-block m-b-none">
                <p style="color: red">学号一旦确定，不能修改，请谨慎填写</p>
              </span>
            </div>
            <div class="col-sm-3">
              <a class="btn btn-success" onclick="getPlatformStudentNumber()">
                从计时平台获取
              </a>
            </div>
          </div>
        </div>
      </form>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
      var identity = [[${schoolStudent.identity}]];
      var prefix = ctx + "business/student";

      function submitHandler() {
        if ($.validate.form()) {
          $.operate.save(
            prefix + "/editStunumSave",
            $("#form-student-editStunum").serialize()
          );
          $.table.refresh();
        }
      }

      /* 从计时平台获取学员的学号 */
      function getPlatformStudentNumber() {
        var data = {
            "identity": identity,
        }
        $.ajax({
            url: prefix + "/getPlatformStudentNumber",
            data: JSON.stringify(data),
            type: "post",
            contentType: 'application/json',
            beforeSend: function () {
              $.modal.loading("正在处理中，请稍候...");
            },
            success: function(result) {
                if (result.code == web_status.SUCCESS) {
                    $('input[name="stunum"]').val(result.data.stunum);
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            }
        })
      }
    </script>
  </body>
</html>
