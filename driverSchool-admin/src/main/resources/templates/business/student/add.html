<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增学员')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: upload-img-css" />
    <style type="text/css">.user-info-head{position:relative;display:inline-block;}.user-info-head:hover:after{content:'\f030';position:absolute;left:0;right:0;top:0;bottom:0;color:#eee;background:rgba(0,0,0,0.5);font-family:FontAwesome;font-size:24px;font-style:normal;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;cursor:pointer;line-height:110px;border-radius:50%;}</style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-student-add">
            <input type="hidden" id="headImage" name="headImage"/>
            <div class="row">
                <h4 class="form-header h4">基本信息</h4>
                <div class="col-xs-6 col-md-3">
                    <div class="text-center" style="margin-top: 40px;">
                        <p class="user-info-head" onclick="avatar()"><img id="img_id" class="img-circle img-lg" th:src="@{/img/default_avatar.png}"></p>
                        <p><a href="javascript:avatar()">点击上传头像</a></p>
                    </div>
                </div>
                <div class="col-xs-12 col-md-9">
                    <div class="row">
                        <div class="col-xs-6 col-md-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">姓名：</label>
                                <div class="col-sm-8">
                                    <input id="name" name="name" class="form-control" type="text" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6 col-md-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">身份证类型：</label>
                                <div class="col-sm-8">
                                    <select id="identityType" name="identityType" class="form-control m-b" th:with="type=${@dict.getType('identity_type')}" required onchange="identityTypeChange(this)">
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6 col-md-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">身份证号：</label>
                                <div class="col-sm-8">
                                    <input id="identity" name="identity" class="form-control" type="text" required onblur="identityValidate()">
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-xs-6 col-md-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">手机号：</label>
                                <div class="col-sm-8">
                                    <input id="mobile" name="mobile" class="form-control" type="text" required>
                                </div>
                            </div>
                        </div>


                        <div class="col-xs-6 col-md-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">出生日期：</label>
                                <div class="col-sm-8">
                                    <div class="input-group date">
                                        <input id="birthday" name="birthday" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="col-xs-6 col-md-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">性别：</label>
                                <div class="col-sm-8">
                                    <select id="gender" name="gender" class="form-control m-b" th:with="type=${@dict.getType('sys_user_sex')}" required>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>

                    </div>



                  <div class="row">
                      <div class="col-xs-6 col-md-4">
                          <div class="form-group">
                              <label class="col-sm-4 control-label">民族：</label>
                              <div class="col-sm-8">
                                  <select name="nation" class="form-control m-b" th:with="type=${@dict.getType('nation_type')}">
                                      <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                  </select>
                              </div>
                          </div>
                      </div>
                      <div class="col-xs-6 col-md-4">
                          <div class="form-group">
                              <label class="col-sm-4 control-label">学历：</label>
                              <div class="col-sm-8">
                                  <select name="education" class="form-control m-b" th:with="type=${@dict.getType('education')}">
                                      <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                  </select>
                              </div>
                          </div>
                      </div>
                  </div>

                  <div id="cxSelect1" class="row">
                      <div class="col-xs-12 col-sm-6 col-md-8">
                          <div class="form-group">
                              <label class="col-sm-2 control-label">现住具体地址：</label>
                              <div class="col-sm-3">
                                  <select id="province" name="province" class="province form-control" data-first-title="现住省份">
                                      <option value="">现住省份</option>
                                  </select>
                              </div>
                              <div class="col-sm-3">
                                  <select id="city" name="city" class="city form-control" data-first-title="现住城市">
                                      <option value="">现住城市</option>
                                  </select>
                              </div>
                              <div class="col-sm-4">
                                  <select id="town" name="town" class="town form-control" data-first-title="现住镇区/县">
                                      <option value="">现住镇区/县</option>
                                  </select>
                              </div>
                          </div>
                      </div>


                      <div class="col-xs-12 col-sm-6 col-md-4">
                          <div class="col-sm-12">
                              <input name="address" class="form-control" type="text">
                          </div>
                      </div>


                  </div>

                  <div id="cxSelect2" class="row">
                      <div class="col-xs-12 col-sm-6 col-md-8">
                          <div class="form-group">
                              <label class="col-sm-2 control-label">户籍具体地址：</label>
                              <div class="col-sm-3">
                                  <select id="hujiProvince" name="hujiProvince" class="hujiProvince form-control" data-first-title="户籍省">
                                      <option value="">户籍省</option>
                                  </select>
                              </div>
                              <div class="col-sm-3">
                                  <select id="hujiCity" name="hujiCity" class="hujiCity form-control" data-first-title="户籍城市">
                                      <option value="">户籍城市</option>
                                  </select>
                              </div>
                              <div class="col-sm-4">
                                  <select id="hujiTown" name="hujiTown" class="hujiTown form-control" data-first-title="户籍镇区/县">
                                      <option value="">户籍镇区/县</option>
                                  </select>
                              </div>
                          </div>
                      </div>

                      <div class="col-xs-12 col-sm-6 col-md-4">
                          <div class="col-sm-12">
                              <input name="hujiAddress" class="form-control" type="text">
                          </div>
                      </div>

                  </div>

                </div>
            </div>


            <div class="row">
                <h4 class="form-header h4">驾照信息</h4>
                <div  class="row">
                    <div class="col-xs-12 col-sm-6 col-md-8">
                        <div id="cxSelectSchool" class="form-group">
                            <label class="col-sm-2 control-label is-required">所属学校：</label>
                            <div th:if="${sysOrganType == 1}">
                                <div class="col-sm-3">
                                    <select id="schoolId" name="schoolId" class="schoolId form-control m-b" data-first-title="所属驾校" required onchange="selectSchoolChange(this)">
                                        <option th:if="${schoolList != null }" th:value="${schoolList[0].id}" th:text="${schoolList[0].name}" selected></option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select id="branchId" name="branchId" class="branchId form-control m-b" data-first-title="所属分校" >
                                    </select>
                                </div>
                                <div class="col-sm-3">
                                    <select id="registrationId" name="registrationId" class="registrationId form-control m-b" data-first-title="所属报名点">
                                    </select>
                                </div>
                            </div>



                            <div th:if="${sysOrganType == 2}">
                                <div class="col-sm-3">
                                    <select id="schoolId" name="schoolId" class="schoolId form-control m-b" data-first-title="所属驾校" required onchange="selectSchoolChange(this)">
                                        <option th:if="${schoolList != null }" th:value="${schoolList[0].id}" th:text="${schoolList[0].name}" selected></option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select id="branchId" name="branchId" class="branchId form-control m-b" data-first-title="所属分校" >
                                        <option th:if="${schoolBranchList != null }" th:value="${schoolBranchList[0].id}" th:text="${schoolBranchList[0].name}" selected></option>
                                    </select>
                                </div>
                                <div class="col-sm-3">
                                    <select id="registrationId" name="registrationId" class="registrationId form-control m-b" data-first-title="所属报名点">
                                    </select>
                                </div>
                            </div>

                            <div th:if="${sysOrganType == 3}">
                                <div class="col-sm-3">
                                    <select id="schoolId" name="schoolId" class="schoolId form-control m-b" data-first-title="所属驾校" required onchange="selectSchoolChange(this)">
                                        <option th:if="${schoolList != null }" th:value="${schoolList[0].id}" th:text="${schoolList[0].name}" selected></option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select id="branchId" name="branchId" class="branchId form-control m-b" data-first-title="所属分校" >
                                        <option th:if="${schoolBranchList != null }" th:value="${schoolBranchList[0].id}" th:text="${schoolBranchList[0].name}" selected></option>
                                    </select>
                                </div>
                                <div class="col-sm-3">
                                    <select id="registrationId" name="registrationId" class="registrationId form-control m-b" data-first-title="所属报名点">
                                        <option th:if="${registrationList != null }" th:value="${registrationList[0].id}" th:text="${registrationList[0].name}" selected></option>
                                    </select>
                                </div>
                            </div>



                            <div th:if="${sysOrganType == 7}">
                                <div class="col-sm-3">
                                    <select id="schoolId" name="schoolId" class="schoolId form-control m-b" data-first-title="所属驾校" required onchange="selectSchoolChange(this)">
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select id="branchId" name="branchId" class="branchId form-control m-b" data-first-title="所属分校" >
                                    </select>
                                </div>
                                <div class="col-sm-3">
                                    <select id="registrationId" name="registrationId" class="registrationId form-control m-b" data-first-title="所属报名点">
                                    </select>
                                </div>
                            </div>


                        </div>
                    </div>

                    <div class="col-xs-6 col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label is-required">学时平台：</label>
                            <div class="col-sm-8">
                                <select id="studyCenterId" name="studyCenterId" class="form-control m-b" required>
                                    <option value="">请选择</option>
                                </select>
                                <span class="help-block m-b-none">
                                    <i class="fa fa-info-circle" style="color: red;"></i> 请选择驾校
                                 </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-xs-6 col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label is-required">考取驾照类型：</label>
                            <div class="col-sm-8">
                                <select name="licenseType" class="form-control m-b" th:with="type=${@dict.getType('license_types')}" required>
                                    <option value="">请选择</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-6 col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label is-required">业务类型：</label>
                            <div class="col-sm-8">
                                <select id="businessType" name="businessType" class="form-control m-b" th:with="type=${@dict.getType('business_type')}" required>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-6 col-md-4">
                        <div class="form-group">
                            <label class="col-md-4 control-label">原驾照号码：</label>
                            <div class="col-sm-8">
                                <input name="oldLicenseNo" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-xs-6 col-md-4">
                        <div class="form-group">
                            <label class="col-md-4 control-label">原驾照类型：</label>
                            <div class="col-sm-8">
                                <select name="oldLicenseType" class="form-control m-b" th:with="type=${@dict.getType('license_types')}">
                                    <option value="">请选择</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-6 col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">原领驾照日期：</label>
                            <div class="col-sm-8">
                                <div class="input-group date">
                                    <input name="oldLicenseDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--<div class="col-xs-6 col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label is-required">是否资金监管：</label>
                            <div class="col-sm-8">
                                <select name="isSupervise" class="form-control m-b" th:with="type=${@dict.getType('student_is_supervise')}" required>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>-->
                </div>

                <div class="row">
                    <div class="col-xs-6 col-md-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">原驾驶证图片：</label>
                            <div class="image-box"></div>
                            <a class="btn btn-success" href="javascript:addImage();">
                                <label>选择图片</label>
                                <input id="add-input" type="file" accept="image/*" style="display: none" onchange="selectImage(this);">
                            </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-8">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">备注：</label>
                            <div class="col-sm-10">
                                <textarea name="remark" class="form-control" rows="5" maxlength="500"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="col-xs-12 col-sm-4">
                        <div class="form-group">
                            <label class="col-sm-4 control-label">是否审核：</label>
                            <div class="col-sm-8">
                                <label class="toggle-switch switch-solid">
                                    <input type="checkbox" id="isCheck">
                                    <span></span>
                                </label>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- <div id="oldLicenseImage" class="row">
                    <h4 class="form-header h4">原驾驶证图片</h4>
                    <div class="row">
                            <div class="col-sm-8">
                                <div class="image-box"></div>
                                <a class="btn btn-success" href="javascript:addImage();">
                                    <label>选择图片</label>
                                    <input id="add-input" type="file" accept="image/*" style="display: none" onchange="selectImage(this);">
                                </a>
                            </div>
                    </div>
                </div> -->

            </div>

        </form>
    </div>

    <div class="row">
        <div class="col-sm-offset-5 col-sm-10">
            <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
            <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭 </button>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <th:block th:include="include :: upload-img-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/student"

        var maxImage = 1;

        $("#form-student-add").validate({
            onkeyup: false,
            rules:{
                name:{
                    maxlength:20,
                    remote: {
                        url: prefix + "/checkUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "name": function() {
                                return $.common.trim($("#name").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },
                identity:{
                    maxlength:18,
                    remote: {
                        url: prefix + "/checkUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "identity": function() {
                                return $.common.trim($("#identity").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },
                mobile:{
                    isPhone: true,
                    remote: {
                        url: prefix + "/checkUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "name": function() {
                                return $.common.trim($("#name").val());
                            },
                            "mobile": function() {
                                return $.common.trim($("#mobile").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                }
            },

            messages: {
                "name": {
                    remote: "该学员姓名已经存在"
                },
                "identity":{
                    remote: "该学员身份证号已经存在"
                },
                "mobile":{
                    remote: "该学员手机号已经存在"
                }
            },
            focusCleanup: true
        });


        function identityTypeChange(select) {
            var identityType= $(select).find("option:selected").val();
            if (identityType != '身份证'){
                $("#birthday").val('');
                $("#gender").val(1);
                $("#identity").val('')
            }
        }
        // 添加自定义校验
        function identityValidate() {
            var identityType= $("#identityType").val();
            if (identityType == '身份证'){
                var id= /^(\d{15}$|^\d{18}$|^\d{17}(\d|X))$/;
                var isidentityValidate= id.test($("#identity").val());
                if (!isidentityValidate) {
                    $.modal.msgError("请输入正确的15或18位身份证号,末尾若为X请大写");
                    return ;
                }
                $.ajax({
                    type: "post",
                    url: prefix + "/getIdentityInfo",
                    data: {
                        "identityType": $("#identityType").val(),
                        "identity": $("#identity").val(),
                    },
                    success: function(data) {
                        var code=data.code;
                        if (code == 0){
                            $("#birthday").val(data.data.birthday);
                            $("#gender").val(data.data.gender);
                        }
                    }
                });


            }
        };
        
        function submitHandler() {
            if ($.validate.form()) {
                var identityType= $("#identityType").val();
                if (identityType == '身份证'){
                    var id= /^(\d{15}$|^\d{18}$|^\d{17}(\d|X))$/;
                    var isidentityValidate= id.test($("#identity").val());
                    if (!isidentityValidate) {
                        $.modal.msgError("请输入正确的15或18位身份证号,末尾若为X请大写");
                        return ;
                    }
                }

                if ($.common.isEmpty($("#headImage").val())){
                    $.modal.msgError("学员头像不能为空");
                    return;
                }

                //var data = $("#form-student-add").serializeArray();
                var isCheck = $("input[id='isCheck']").is(':checked') == true ? 1 : 0;
                //data.push({"name": "isCheck", "value": isCheck});
                //$.operate.saveTab(prefix + "/add", data);
                submitStudent("/add", isCheck);
            }
        }

        $("input[name='birthday']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='oldLicenseDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });


        /*用户管理-头像*/
        function avatar() {
            var url = ctx + 'system/user/profile/avatar?id=0';
            top.layer.open({
                type: 2,
                area: [$(window).width() + 'px', $(window).height() + 'px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "添加头像",
                content: url,
                btn: ['确定', '关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                yes: function(index, layero) {
                    var iframeWin = layero.find('iframe')[0];
                    iframeWin.contentWindow.submitHandler(index, layero);
                },
                cancel: function(index) {
                    return true;
                }
            });
        }




        function selectSchoolChange(select){
            var schoolId= $(select).find("option:selected").val();
            if ($.common.isNotEmpty(schoolId)){
                var formdata = new FormData();
                formdata.append("schoolId", schoolId);
                $.ajax({
                    url: ctx + "business/school/selectStudyCenterData",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            $("#studyCenterId option").remove();
                            var option="<option value=''>请选择</option>";
                            $.each(result.data, function(index, value) {
                                if (index == 0){
                                    option +="<option value='"+value.organId+"' selected='selected'>"+value.organText+"</option>"
                                } else{
                                    option +="<option value='"+value.organId+"'>"+value.organText+"</option>"
                                }
                            });
                            $("#studyCenterId").append(option);

                        } else if (result.code == web_status.WARNING) {
                            $.modal.alertWarning(result.msg)
                        }  else {
                            $.modal.alertError(result.msg);
                        }
                        $.modal.closeLoading();
                    }
                })
            }else{
                $("#studyCenterId option").remove();
                $("#studyCenterId").append("<option value=''>请选择</option>");
            }
        }


        $(function() {
            // 加载省市区
            $.cxSelect.defaults.url = ctx + "business/area/areaData";
            $('#cxSelect1').cxSelect({
                selects: ['province', 'city', 'town'],
                nodata: 'none'
            });

            $.cxSelect.defaults.url = ctx + "business/area/areaData";
            $('#cxSelect2').cxSelect({
                selects: ['hujiProvince', 'hujiCity', 'hujiTown'],
                nodata: 'none'
            });


            //加载驾校、分校
            $.cxSelect.defaults.url = ctx + "business/school/schoolThreeNormalData";
            $('#cxSelectSchool').cxSelect({
                selects: ['schoolId', 'branchId','registrationId'],
                jsonValue: 'v',
            });

        });

        /** 页面初次加载动态显示标签 */
        $(document).ready(function() {
            $('#businessType').change(function() {
                var selectValue = $(this).val();
                if (selectValue == '初领') {
                    $('#oldLicenseImage').hide();
                } else if (selectValue == '重考') {
                    $('#oldLicenseImage').hide();
                } else if (selectValue == '增驾') {
                    $('#oldLicenseImage').show();
                }
            });
            // 初始状态下执行一次，以展示当前选中的内容
            $('#businessType').trigger('change');
        });

    </script>
</body>
</html>