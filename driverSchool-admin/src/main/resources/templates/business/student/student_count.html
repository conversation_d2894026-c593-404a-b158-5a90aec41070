<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('学员统计数据')" />
    <style type="text/css">
        table.tftable {
            font-size: 12px;
            color: #333333;
            width: 100%;
            border-width: 1px;
            border-color: #729ea5;
            border-collapse: collapse;
        }

        table.tftable th {
            font-size: 12px;
            background-color: #9fe3f8;
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #729ea5;
            text-align: center;
        }

        table.tftable tr {
            background-color: #ffffff;
        }

        table.tftable td {
            font-size: 12px;
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #729ea5;
        }
        
        td {
            text-align: center;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <table id="tfhover" class="tftable" border="1">
                <tr>
                    <th>总报名</th>
                    <th>未审核</th>
                    <th>未同步学时</th>
                    <th>未监管</th>
                    <th>退学</th>
                    <th>结业</th>
                <tr>
                    <td th:text="${studentCount.registeCount}">0</td>
                    <td th:text="${studentCount.uncheckCount}">0</td>
                    <td th:text="${studentCount.unStudyCenterIsSynCount}">0</td>
                    <td th:text="${studentCount.unSuperviseCount}">0</td>
                    <td th:text="${studentCount.quitCount}">0</td>
                    <td th:text="${studentCount.graduationCount}">0</td>
                </tr>
            </table>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">

        $(function() {
            
        });

    </script>
</body>
</html>

<script type="text/javascript">
    window.onload = function () {
        var tfrow = document.getElementById('tfhover').rows.length;
        var tbRow = [];
        for (var i = 1; i < tfrow; i++) {
            tbRow[i] = document.getElementById('tfhover').rows[i];
            tbRow[i].onmouseover = function () {
                this.style.backgroundColor = '#e6e6e6';
            };
            tbRow[i].onmouseout = function () {
                this.style.backgroundColor = '#ffffff';
            };
        }
    };
</script>