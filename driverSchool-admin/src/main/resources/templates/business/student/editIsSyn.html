<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <th:block th:include="include :: header('修改同步学时状态')" />
  </head>
  <body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <form
        class="form-horizontal m"
        id="form-editIsSyn-check"
        th:object="${schoolStudent}"
      >
        <input name="id" th:field="*{id}" type="hidden" />
        <div class="form-group">
          <label class="col-sm-3 control-label is-required">修改状态：</label>
          <div class="col-sm-8">
            <select
              id="studyCenterIsSyn"
              class="form-control"
              name="studyCenterIsSyn"
              th:field="*{studyCenterIsSyn}"
              th:with="type=${@dict.getType('is_yes_no_2')}"
              data-first-title="请选择"
              required
            >
              <option
                th:each="dict : ${type}"
                th:text="${dict.dictLabel}"
                th:value="${dict.dictValue}"
              ></option>
            </select>
          </div>
        </div>
      </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
      var prefix = ctx + "business/student";

      $("#form-driverInfo-check").validate({
        focusCleanup: true,
      });

      function submitHandler() {
        if ($.validate.form()) {
          $.operate.save(
            prefix + "/editIsSynSave",
            $("#form-editIsSyn-check").serialize()
          );
          $.table.refresh();
        }
      }
    </script>
  </body>
</html>
