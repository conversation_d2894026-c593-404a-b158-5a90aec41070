<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('学员列表')" />
    <th:block th:include="include :: select2-css"/>
    <link rel="shortcut icon" href="favicon.ico" />
    <link
      href="../static/css/bootstrap.min.css"
      th:href="@{/css/bootstrap.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/font-awesome.min.css"
      th:href="@{/css/font-awesome.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/animate.min.css"
      th:href="@{/css/animate.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/style.min.css"
      th:href="@{/css/style.min.css}"
      rel="stylesheet"
    />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b "
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>
                <li>
                  <label>分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="branchId"
                    name="branchId"
                    class="branchId form-control m-b"
                    data-first-title="选择分校"
                  ></select>
                </li>
                <li>
                  <label>报名点：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="registrationId"
                    name="registrationId"
                    class="registrationId form-control m-b"
                    data-first-title="所属报名点"
                  ></select>
                </li>
                <li>
                  <label>姓名：</label>
                  <input type="text" name="name" placeholder="请输入姓名" />
                </li>
                <li>
                  <label>身份证号：</label>
                  <input
                    type="text"
                    name="identity"
                    placeholder="多个身份证号使用空格进行分隔"
                  />
                </li>
                <li>
                  <label>手机号：</label>
                  <input type="text" name="mobile" placeholder="请输入手机号" />
                </li>
                <li>
                  <label>学号：</label>
                  <input type="text" name="stunum" placeholder="请输入学号" />
                </li>
                <li>
                  <label>监管资金：</label>
                  <select name="superviseFeeIsOk" id="superviseFeeIsOk">
                    <option value="">所有</option>
                    <option value="1">已到账</option>
                    <option value="0">未到账</option>
                  </select>
                </li>
                <li>
                  <label>是否审核：</label>
                  <select
                    name="isCheck"
                    th:with="type=${@dict.getType('student_is_check')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label>学习状态：</label>
                  <select
                    name="status"
                    th:with="type=${@dict.getType('student_status')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label>阶段：</label>
                  <input
                    type="text"
                    name="trainphase"
                    placeholder="请输入阶段"
                  />
                </li>
                <li class="select-time">
                  <label style="width: 100px">报名审核时间： </label>
                  <input
                    type="text"
                    class="time-input"
                    id="startTime"
                    placeholder="开始时间"
                    name="params[beginTime]"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    class="time-input"
                    id="endTime"
                    placeholder="结束时间"
                    name="params[endTime]"
                  />
                </li>
                <li class="select-time">
                  <label style="width: 100px">预登记时间： </label>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input"
                    id="beginPrepareRegisteDate"
                    placeholder="开始时间"
                    name="params[beginPrepareRegisteDate]"
                  />
                  <span>-</span>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input"
                    id="endPrepareRegisteDate"
                    placeholder="结束时间"
                    name="params[endPrepareRegisteDate]"
                  />
                </li>
                <li>
                  <label>是否上报计时平台：</label>
                  <select
                    name="studyCenterIsSyn"
                    th:with="type=${@dict.getType('study_center_is_syn')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    id="reset"
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="resetSearchForm()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-success"
            onclick="$.operate.addTab()"
            shiro:hasPermission="business:student:add"
          >
            <i class="fa fa-plus"></i> 添加
          </a>
          <a
            class="btn btn-primary single disabled"
            onclick="editTab()"
            shiro:hasPermission="business:student:edit"
          >
            <i class="fa fa-edit"></i> 修改
          </a>
          <a id="viewBtn"
            class="btn btn-primary single disabled"
            onclick="view('',1300,'')"
            shiro:hasPermission="business:student:view"
          >
            查看
          </a>

          <a
            class="btn btn-warning single disabled"
            onclick="check()"
            shiro:hasPermission="business:student:check"
          >
            审核
          </a>

          <a
            class="btn btn-success multiple disabled"
            onclick="studyCenterSyn()"
            shiro:hasPermission="business:student:syn"
          >
            上报计时平台
          </a>

          <a
            class="btn btn-danger multiple disabled"
            onclick="removeAll()"
            shiro:hasPermission="business:student:remove"
          >
            <i class="fa fa-remove"></i> 删除
          </a>
          <a
            class="btn btn-info"
            onclick="exportSelected()"
            shiro:hasPermission="business:student:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
          <a
            class="btn btn-info single disabled"
            onclick="dropOut()"
            shiro:hasPermission="business:student:edit"
          >
            <i class="fa fa-gear"></i> 退学
          </a>
          <a
            class="btn btn-info fa fa-area-chart"
            onclick="studentCount()"
            shiro:hasPermission="business:student:studentCount"
          >
            统计数据
          </a>
          <a
            class="btn btn-warning single disabled"
            onclick="editIsSyn()"
            shiro:hasPermission="business:student:editIsSyn"
          >
            修改同步学时状态
          </a>
          <a
            class="btn btn-success single disabled"
            onclick="editStunum()"
            shiro:hasPermission="business:student:editStunum"
          >
            修改学员编号
          </a>
          <a
            class="btn btn-success single disabled"
            onclick="studyTimeList()"
            shiro:hasPermission="business:student:studyTimeList"
          >
            查询省平台数据
          </a>
          <a
            class="btn btn-success single disabled"
            onclick="viewContract()"
            shiro:hasPermission="business:student:viewContract"
          >
            查看签署合同
          </a>
          <a
            class="btn btn-success"
            onclick="$.table.importExcelPro('','','','.zip')"
            shiro:hasPermission="business:student:grStudentImport"
          >
            特殊学员导入
          </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js"/>
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:student:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:student:remove')}]];
      var identityTypeDatas = [[${@dict.getType('identity_type')}]];
      var genderDatas = [[${@dict.getType('sys_user_sex')}]];
      var nationDatas = [[${@dict.getType('nation_type')}]];
      var educationDatas = [[${@dict.getType('education')}]];
      var licenseTypeDatas = [[${@dict.getType('license_types')}]];
      var businessTypeDatas = [[${@dict.getType('business_type')}]];
      var oldLicenseTypeDatas = [[${@dict.getType('license_types')}]];
      var isSuperviseDatas = [[${@dict.getType('student_is_supervise')}]];
      var isQuitDatas = [[${@dict.getType('student_is_quit')}]];
      var studentStatusDatas =[[${@dict.getType('student_status')}]];
      var originDatas=[[${@dict.getType('origin_data')}]];
      var prefix = ctx + "business/student";
      var importResignUrl= ctx +"business/resign-contract/importData";
      var isJiaxie=[[${isJiaxie}]];
      $(function() {
          $("#schoolId").select2({

          });
          var options = {
              id: "bootstrap-table",
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              detailUrl: prefix + "/view/{id}",
              importUrl: prefix + "/grStudentImport",
              useDefaultimportUrl: false,
              importUrl2: importResignUrl,
              modalName: "学员",
              showPageGo: true,
              columns: [{
                  checkbox: true
              },
              {
                  field: 'id',
                  title: '',
                  visible: false
              },
                  {
                      field: 'schoolId',
                      title: '驾校',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              if (row.school != null) {
                                  return row.school.name;
                              }
                          }
                      }
                  },
                  {
                      field: 'branchId',
                      title: '分校',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              if (row.branch != null) {
                                  return row.branch.name;
                              }
                          }
                      }
                  },
                  {
                      field: 'registrationId',
                      title: '报名点',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              if (row.registration != null) {
                                  return row.registration.name;
                              }
                          }
                      }
                  },
              {
                  field: 'name',
                  title: '学员'
              },
                  {
                      field: 'gender',
                      title: '性别',
                      formatter: function(value, row, index) {
                          return $.table.selectDictLabel(genderDatas, value);
                      }
                  },
              {
                  field: 'identity',
                  title: '身份证号',
                  formatter: function (value, row, index) {
                      if ($.common.isNotEmpty(value)) {
                          return basecclusion(value,6,4);
                      }
                  }
              },
              {
                  field: 'mobile',
                  title: '手机号',
                  formatter: function (value, row, index) {
                      if ($.common.isNotEmpty(value)) {
                          return basecclusion(value,3,4);
                      }
                  }
              },


              {
                  field: 'licenseType',
                  title: '学车类型',
                  formatter: function(value, row, index) {
                     return $.table.selectDictLabel(licenseTypeDatas, value);
                  }
              },

                  {
                      field: 'originData',
                      title: '来源',
                      formatter: function(value, row, index) {
                          return $.table.selectDictLabel(originDatas, value);
                      }
                  },

              {
                  field: 'prepareRegisteDate',
                  title: '预登记时间'
               },

              {
                  field: 'registeDate',
                  title: '报名审核时间'
              },
              {
                  field: 'status',
                  title: '学习状态',
                  formatter: function(value, row, index) {
                      return $.table.selectDictLabel(studentStatusDatas, value);
                  }
              },
              {
                  field: 'trainphase',
                  title: '阶段'
              },
                  {
                      field: 'studyCenterIsSyn',
                      title: '上报计时平台',
                      formatter: function(value, row, index) {
                          if(value == 0){
                              return "否";
                          }else if (value == 1) {
                              return "是";
                          }else{
                              return "-";
                          }
                      }
                  },
                  {
                      field: 'isCheck',
                      title: '是否审核',
                      formatter: function(value, row, index) {
                          if(value == 0){
                              return "未审核";
                          }else if (value == 1) {
                              return "已审核";
                          }else{
                              return "-";
                          }
                      }
                  },

                  {
                      field: 'superviseFeeIsOk',
                      title: '监管资金状态',
                      formatter: function(value, row, index) {
                          if(value == 0){
                              return "未到帐";
                          }else if (value == 1) {
                              return "已到帐";
                          }else{
                              return "-";
                          }
                      }
                  },

              /* {
                  field: 'isSupervise',
                  title: '监管状态',
                  formatter: function(value, row, index) {
                     return $.table.selectDictLabel(isSuperviseDatas, value);
                  }
              }, */
              {
                  field: 'isQuit',
                  title: '是否退学',
                  formatter: function(value, row, index) {
                     return $.table.selectDictLabel(isQuitDatas, value);
                  }
              },
              {
                  field: 'stunum',
                  title: '学号',
              },
              {
                  field: 'openId',
                  title: '微信id',
                  visible: false
              }
              ]
          };
          $.table.init(options);

          if(isJiaxie===1){
            //隐藏查看按钮
            $("#viewBtn").addClass('hidden');
          }

          //加载驾校、分校
          $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
          $('#cxSelectSchool').cxSelect({
              selects: ['schoolId', 'branchId','registrationId'],
              jsonValue: 'v',
          });

          /** 初始化时间组件 */
          layui.use('laydate', function(){
            var laydate = layui.laydate;
                laydate.render({
                    elem: '#beginPrepareRegisteDate',
                    type: 'datetime',
                    trigger: 'click'
              });
                    laydate.render({
                    elem: '#endPrepareRegisteDate',
                    type: 'datetime',
                    trigger: 'click'
              });
          });
      });

      function importResignData(){
        table.options.useDefaultimportUrl = true;
        $.table.importExcel('','','')
      }
      //重置搜索条件
      function resetSearchForm(){
            // 1. 先移除验证
            $('#formId').validate().resetForm();
            
            // 2. 重置表单
            $('#formId')[0].reset();
            
            // 3. 重置 select2 字段
            $("#schoolId").val('').trigger('change');
            $("#branchId").empty().trigger('change');
            $("#registrationId").empty().trigger('change');
            
            // 4. 清除错误样式
            $("#schoolId, #branchId, #registrationId").removeClass('error');
            $("label.error[for='schoolId'], label.error[for='branchId'], label.error[for='registrationId']").remove();
            
            // 5. 重新触发表格搜索
            $.table.search();
      }

      function editTab() {
          var rows = $.table.selectRows();
          if (rows.length < 0) {
              $.modal.alertWarning("请至少选择一条记录");
              return;
          }else if (rows.length == 1) {

            var data = {
                "id": rows[0].id
            }

            var code;

            $.ajax({
                url: prefix + "/checkStudyCenterIsSyn",
                type: "post",
                processData: false,
                contentType: 'application/json',
                async: false,
                data: JSON.stringify(data),
                success: function(response) {
                    if (response.code == 500) {
                        code = response.code;
                        $.modal.alertWarning(response.msg);
                    }
                }
            })

            if (code == 500) {
                event.stopPropagation();
                return false;
            }

              if (rows[0].studyCenterIsSyn == 0) {
                  $.operate.editTab();
              }else{
                  superviseTab(rows[0].id);
              }
          }
      }

      function superviseTab(id) {
          var url = "/404.html";
          table.set();
          var id = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
          if (id.length == 0) {
              $.modal.alertWarning("请至少选择一条记录");
              return;
          }
          url=prefix + "/supervise/"+id;
          $.modal.openTab("修改已监管学员",url);
      }

      /* 学员退学 */
      function dropOut() {
          var url = "/404.html";
          var rows = $.table.selectRows();
          if (rows.length < 0) {
              $.modal.alertWarning("请至少选择一条记录");
              return;
          }else if (rows.length == 1) {
              if (rows[0].isQuit == 1) {
                  $.modal.alertWarning("学员已退学，请勿重复操作");
              }else if(rows[0].isCheck == 0) {
              	 $.modal.confirm("学员" + rows[0].name + "未审核，确认退学吗?",function(){
              		 url=prefix + "/dropOut/"+rows[0].id;
                       $.modal.open("学员退学",url,1600,800);
              	 });
                   //$.modal.alertWarning("学员" + rows[0].name + "未审核。");
              }else{
                  url=prefix + "/dropOut/"+rows[0].id;
                  $.modal.open("学员退学",url,1600,800);
              }
          }
      }

      //学员统计数据
      function studentCount() {
          table.set();
          var beginPrepareRegisteDate = $("input[id='beginPrepareRegisteDate']").val(); //预登记开始时间
          var endPrepareRegisteDate = $("input[id='endPrepareRegisteDate']").val(); //预登记结束时间
          _url = prefix + "/studentCount?beginPrepareRegisteDate="+beginPrepareRegisteDate+"&endPrepareRegisteDate="+endPrepareRegisteDate;
          var options = {
                  title: "学员统计数据",
                  width: '',
                  height: '180',
                  url: _url,
                  btn: ['关闭'],
                  yes: function (index, layero) {
                      $.modal.close(index);
                  }
          };
          $.modal.openOptions(options);
      }

      /* 审核学员 */
      function check() {
        var rows = $.table.selectRows();
        if (rows.length == 0) {
          $.modal.alertWarning("请至少选择一条记录");
          return;
        }
        if ($.common.isEmpty(rows[0].studyCenterId) || rows[0].studyCenterId == 0){
          $.modal.alertWarning("学员" + rows[0].name + "未选择学时平台。");
          return;
        }
        if (rows[0].isCheck == 1) {
          $.modal.alertWarning("学员" + rows[0].name + "已经是已审核状态。");
          return;
        }
        $.modal.confirm("确认要审核学员" + rows[0].name + "吗?", function () {
          var param = {id: rows[0].id};
          console.log(param);
          $.ajax({
            url: prefix + "/isMustSignContract",
            data: param,
            type: "post",
            dataType: 'json',
            beforeSend: function () {
              $.modal.loading("正在处理中，请稍候...");
            },
            success: function(result) {
              if (result.code === web_status.SUCCESS) {
                 if(result.data.isMustSignContract===true){
                   var url = prefix + "/check/"+rows[0].id;
                   $.modal.open('审核学员', url, '1024', '700');
                 }else{
                   $.operate.submitPro(prefix + "/oldCheckStudent", "post", "json", param, "学员正在审核中");
                 }
              } else {
                $.modal.alertError(result.msg);
              }
              $.modal.closeLoading();
            }
          })

        });
      }


      function studyCenterSyn() {
          var rows = $.table.selectRows();
          if (rows.length == 0) {
              $.modal.alertWarning("请至少选择一条记录");
              return;
          } else if (rows.length == 1) {
              if (rows[0].studyCenterIsSyn == 1) {
                  $.modal.alertWarning("学员" + rows[0].name + "已经是同步状态。");
              }else if(rows[0].isCheck == 0){
                  $.modal.alertWarning("学员" + rows[0].name + "未审核，请审核之后同步。");
              }else {
                  $.modal.confirm("确认要同步学员学时" + rows[0].name + "吗?", function () {
                      $.operate.submitPro(prefix + "/studyCenterSyn", "post", "json", {ids: $.table.selectColumns("id").join(",")}, "学员正在同步学时中");
                  });
              }
          } else {
              $.modal.confirm("确认要同步选中的学员学时吗?", function () {
                  $.operate.submitPro(prefix + "/studyCenterSyn", "post", "json", {ids: $.table.selectColumns("id").join(",")}, "学员正在同步学时中");
              });
          }
      }

      function view(id,width, height) {
          table.set();
          var _url = $.operate.detailUrl(id);
          var options = {
              title: table.options.modalName + "详细",
              width: width,
              height: height,
              url: _url,
              skin: 'layui-layer-gray',
              btn: ['关闭'],
              yes: function (index, layero) {
                  $.modal.close(index);
              }
          };
          $.modal.openOptions(options);
      }


      // 导出数据
      function exportSelected() {
          var ids = $.table.selectColumns("id");
          var schoolId = $('#schoolId').val(); //驾校id
          var branchId = $('#branchId').val(); //分校id
          var registrationId = $('#registrationId').val(); //报名点id
          var name = $("input[name='name']").val(); //学员名称
          var identity = $("input[name='identity']").val(); //身份证号
          var mobile = $("input[name='mobile']").val(); //手机号
          var isSupervise = $('select[name="isSupervise"]').val(); //监管状态
          var isCheck = $('select[name="isCheck"]').val(); //是否审核
          var beginTime = $('input[id="startTime"]').val(); //报名审核开始时间
          var endTime = $('input[id="endTime"]').val(); //报名审核结束时间
          var beginPrepareRegisteDate = $('input[id="beginPrepareRegisteDate"]').val(); //预登记开始时间
          var endPrepareRegisteDate = $('input[id="endPrepareRegisteDate"]').val(); //预登记结束时间
          var studyCenterIsSyn = $('select[name="studyCenterIsSyn"]').val(); //是否上报计时平台
          var stunum = $('input[name="stunum"]').val();

          let fromData = new FormData();
          fromData.append('schoolId', schoolId != null ? schoolId : '');
          fromData.append('branchId', branchId != null ? branchId : '' );
          fromData.append('registrationId', registrationId != null ? registrationId : '');
          fromData.append('name', name != null ? name : '');
          fromData.append('identity', identity != null ? identity : '');
          fromData.append('mobile', mobile != null ? mobile : '');
          fromData.append('isSupervise', isSupervise != null ? isSupervise : '');
          fromData.append('isCheck', isCheck != null ? isCheck : '');
          fromData.append('beginTime', beginTime != null ? beginTime : '');
          fromData.append('endTime', endTime != null ? endTime : '');
          fromData.append('beginPrepareRegisteDate', beginPrepareRegisteDate != null ? beginPrepareRegisteDate : '');
          fromData.append('endPrepareRegisteDate', endPrepareRegisteDate != null ? endPrepareRegisteDate : '');
          fromData.append('studyCenterIsSyn', studyCenterIsSyn != null ? studyCenterIsSyn : '');
          fromData.append('stunum', stunum != null ? stunum : '');

          var tipMsg = "确定导出所有数据吗？（注意：单次最多允许导出5000条数据，请尽量缩小查询范围）";
          if($.common.isNotEmpty(ids)){
              tipMsg = "确定导出勾选" + ids.length + "条数据吗？（注意：单次最多允许导出5000条数据，请尽量缩小查询范围）";
              fromData.append('ids', ids);
          }

          $.modal.confirm(tipMsg, function() {
              $.ajax({
                  url: prefix + "/export",
                  data: fromData,
                  type: "post",
                  processData: false,
                  contentType: false,
                  async: true,
                  success: function(result) {
                      if (result.code == web_status.SUCCESS) {
                          window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                      } else {
                          $.modal.alertError(result.msg);
                      }
                  }
              })
          });
      }

      /* 批量删除学员 */
      function removeAll() {
          table.set();
          var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
          if (rows.length == 0) {
              $.modal.alertWarning("请至少选择一条记录");
              return;
          }
          let html = "<div style='text-align: center; margin-top: 20px;'><span style='font-size: 16px;'>确认要删除选中的" + rows.length + "条学员数据吗?</span><br/><br/><label class='control-label is-required'>请输入删除原因：</label><input type='text' id='delReason' name='delReason'/></div>"
          layer.open({
                  type: 1,
                  title: '删除学员',
                  area: ['350px', '200px'],
                  btn: ['确认','取消'],
                  offset: 'auto', // 居中显示
                  content: html,
                  yes: function (index, layero) {
                      var delReason = document.getElementById('delReason').value;
                      let fromData = new FormData();
                      fromData.append('ids', rows);
                      fromData.append('delReason', delReason);
                      $.ajax({
                          url: prefix + "/remove",
                          data: fromData,
                          type: "post",
                          processData: false,
                          contentType: false,
                          async: false,
                          success: function(result) {
                              if (result.code == web_status.SUCCESS) {
                                  $.modal.alertSuccess(result.msg);
                                  $.table.refresh()
                                  layer.close(index);
                              } else {
                                  $.modal.alertError(result.msg);
                              }
                          }
                      })
              }
          });
      }

      /* 修改学员同步学时状态 */
      function editIsSyn() {
        table.set();
        var rows = $.table.selectRows();
        var _url = prefix + "/editIsSyn/" + rows[0].id;
        $.modal.open("修改同步学时状态", _url, '', '200');
      }

      /* 查看学员详情 */
      $('#bootstrap-table').on('dbl-click-row.bs.table', function (e, row, $element) {
        if(isJiaxie===1){
          //监管方禁用双击查看详情
          return false;
        }
        table.set();
          var _url = $.operate.detailUrl(row.id);
          var options = {
              title: table.options.modalName + "详细",
              width: "1300",
              height: "",
              url: _url,
              skin: 'layui-layer-gray',
              btn: ['关闭'],
              yes: function (index, layero) {
                  $.modal.close(index);
              }
          };
          $.modal.openOptions(options);
      });

      /* 修改学员编号 */
      function editStunum() {
        table.set();
        var rows = $.table.selectRows();
        var param = "id="+rows[0].id;
        $.ajax({
            url: prefix + "/checkStunum",
            data: param,
            type: "get",
            contentType: 'json',
            success: function(result) {
                if (result.code == web_status.SUCCESS) {
                  var _url = prefix + "/editStunum/" + rows[0].id;
                  $.modal.open("修改学号", _url, '', '200');
                } else {
                    $.modal.alertError(result.msg);
                }
            }
        })
      }

      /* 查询省平台数据 */
      function studyTimeList() {
        table.set();
        var rows = $.table.selectRows();
        var _url = prefix + "/studyTimeList/" + rows[0].id;
        var options = {
              title: "省平台数据",
              width: "1000",
              height: "390",
              url: _url,
              skin: 'layui-layer-gray',
              btn: ['关闭'],
              yes: function (index, layero) {
                  $.modal.close(index);
              }
          };
          $.modal.openOptions(options);
      }

      /* 查看签署合同 */
      function viewContract() {
        var rows = $.table.selectRows();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        // 检查是否签署了合同
        var params = 'id='+rows[0].id;
        var config = {
            url: prefix + "/checkContract",
            type: "post",
            dataType: "json",
            data: params,
            beforeSend: function () {
            $.modal.loading("正在处理中，请稍候...");
            },
            success: function (result) {
            if (result.code == web_status.SUCCESS) {
              var _url = prefix + "/viewContract/" + rows[0].id;
              var options = {
                    title: "查看签署合同",
                    width: "1200",
                    height: "900",
                    url: _url,
                    skin: 'layui-layer-gray',
                    btn: ['关闭'],
                    yes: function (index, layero) {
                        $.modal.close(index);
                    }
                };
              $.modal.openOptions(options);
            } else {
                $.modal.alertError(result.msg);
            }
                $.modal.closeLoading();
            },
        };
        $.ajax(config);
      }


      function grStudentExport() {

      }
    </script>

    <script id="importTpl" type="text/template">
      <form enctype="multipart/form-data" class="mt20 mb10">
          <div class="col-xs-offset-1">
              <input type="file" id="file" name="file"/>
              <font color="red" class="pull-left mt10">
                  提示：仅允许导入“zip”格式的压缩文件！
              </font>
          </div>
      </form>
    </script>
  </body>
</html>
