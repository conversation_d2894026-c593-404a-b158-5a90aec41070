<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('省厅数据列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list">
              <ul>
                <li>
                  <label>姓名：</label>
                  <input type="text" name="name" placeholder="请输入姓名" />
                </li>
                <li>
                  <label>身份证号：</label>
                  <input
                    type="text"
                    name="idcard"
                    placeholder="多个身份证号使用空格进行分隔"
                  />
                </li>
                <li>
                  <label>学号：</label>
                  <input type="text" name="stunum" placeholder="请输入学号" />
                </li>
                <li>
                  <label>驾校编号：</label>
                  <input
                    type="text"
                    name="inscode"
                    placeholder="请输入驾校编号"
                  />
                </li>
                <li>
                  <label style="width: 100px">培训阶段：</label>
                  <select name="trainphase">
                    <option value="">所有</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                  </select>
                </li>
                <li>
                  <label style="width: 100px">是否已释放：</label>
                  <select name="isRelease">
                    <option value="">所有</option>
                    <option value="0">否</option>
                    <option value="1">是</option>
                  </select>
                </li>
                <li class="select-time">
                  <label style="width: 100px">共享时间： </label>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input"
                    id="beginPushTime"
                    placeholder="开始时间"
                    name="beginPushTime"
                  />
                  <span>-</span>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input"
                    id="endPushTime"
                    placeholder="结束时间"
                    name="endPushTime"
                  />
                </li>
                <li class="select-time">
                  <label style="width: 100px">更新时间： </label>
                  <input
                    style="width: 135px"
                    type="text"
                    class="time-input"
                    data-type="datetime"
                    id="beginUpdateTime"
                    data-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="开始时间"
                    name="beginUpdateTime"
                  />
                  <span>-</span>
                  <input
                    style="width: 135px"
                    type="text"
                    class="time-input"
                    data-type="datetime"
                    id="endUpdateTime"
                    data-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="结束时间"
                    name="endUpdateTime"
                  />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:provinceStudyTime:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:provinceStudyTime:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:provinceStudyTime:remove')}]];
      var isSupervise = [[${@dict.getType('is_supervise')}]];
      var releaseType = [[${@dict.getType('release_type')}]];
      var studyStage = [[${@dict.getType('study_stage')}]];
      var prefix = ctx + "business/provinceStudyTime";

      $(function() {
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              importUrl: prefix + "/importData",
              importTemplateUrl: prefix + "/importTemplate",
              exportUrl: prefix + "/export",
              detailUrl: prefix + "/view/{id}",
              showPageGo: true,
              modalName: "省厅数据",
              columns: [{
                  checkbox: true
              },
              {
                  field: 'id',
                  title: '主键',
                  visible: false
              },
              {
                  field: 'stunum',
                  title: '学号'
              },
              {
                  field: 'name',
                  title: '姓名'
              },
              {
                  field: 'idcard',
                  title: '证件号码'
              },
              {
                  field: 'inscode',
                  title: '驾校编号'
              },
              {
                  field: 'traintype',
                  title: '培训车型'
              },
              {
                  field: 'trainphase',
                  title: '培训阶段',
              },
              {
                  field: 'pushtime',
                  title: '共享时间'
              },
              {
                  field: 'updateTime',
                  title: '更新时间'
              },
              {
                  field: 'isRelease',
                  title: '是否已释放',
                  formatter: function(value, row, index) {
                       return $.table.selectDictLabel(isSupervise, value);
                    }
              },
              {
                  field: 'releaseType',
                  title: '释放类型',
                  formatter: function(value, row, index) {
                       return $.table.selectDictLabel(releaseType, value);
                    }
              },]
          };
          $.table.init(options);

          /** 初始化时间组件 */
          layui.use('laydate', function(){
            var laydate = layui.laydate;
                laydate.render({
                    elem: '#beginPushTime',
                    type: 'datetime',
                    trigger: 'click'
              });
                    laydate.render({
                    elem: '#endPushTime',
                    type: 'datetime',
                    trigger: 'click'
              });
          });
      });

      /* 导出省厅数据列表 */
      function exportSelected() {
        var params = $("#formId").serialize();
        var ids = $.table.selectColumns("pushtime");
        var tipMsg = "确定导出所有数据吗？";
        if($.common.isNotEmpty(ids)){
            tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
            params += "&ids="+ids;
        }
        $.modal.confirm(tipMsg, function() {
            var config = {
              url: prefix + "/export",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
        });
      }
    </script>
  </body>
</html>
