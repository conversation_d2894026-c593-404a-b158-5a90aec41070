<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('批量提交列表')" />
    <!-- 交费导入页面 -->
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>

                <!-- <li>
                                <label>分校：</label>
                                <select style="margin-bottom:0px;font-family:inherit;font-size:inherit;line-height:inherit;" id="branchId" name="branchId" class="branchId form-control m-b" data-first-title="选择分校"></select>
                            </li> -->

                <li>
                  <label>批次：</label>
                  <input type="text" name="orderId" />
                </li>
                <li>
                  <label>是否支付：</label>
                  <select name="isPay" id="isPay">
                    <option value="">所有</option>
                    <option value="1">已支付</option>
                    <option value="0">未支付</option>
                  </select>
                </li>
                <li class="select-time">
                  <label>时间： </label>
                  <input
                    type="text"
                    class="time-input"
                    id="startTime"
                    placeholder="开始日期"
                    name="params[beginTime]"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    class="time-input"
                    id="endTime"
                    placeholder="结束日期"
                    name="params[endTime]"
                  />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-success"
            onclick="$.operate.addTab()"
            shiro:hasPermission="business:batchSubmit:add"
          >
            <i class="fa fa-plus"></i> 交费导入
          </a>
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:batchSubmit:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:batchSubmit:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:batchSubmit:remove')}]];
      var prefix = ctx + "business/batchSubmit";

      $(function() {
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              modalName: "批量提交",
              showSearch: false,
              showRefresh: false,
              showToggle: false,
              showColumns: false,
              showPageGo: true,
              columns: [{
                  checkbox: true
              },
              {
                  field: 'schoolId',
                  title: '驾校',
                  formatter: function (value, row, index) {
                      if ($.common.isNotEmpty(value)) {
                          if (row.school != null) {
                              return row.school.name;
                          }
                      }
                  }
              },
              {
                  field: 'orderId',
                  title: '订单号',
              },
              {
                  field: 'createdTime',
                  title: '时间'
              },
              {
                  field: 'studentCount',
                  title: '学员数'
              },
              {
                  field: 'totalFee',
                  title: '转账总金额'
              },
              {
                  field: 'isPay',
                  title: '支付状态',
                  formatter: function (value, row, index) {
                      if (value) {
                          return `<span class=\"label label-primary\">成功</span>`;
                      } else {
                          return `<span class=\"label label-danger\">未支付</span>`;
                      }
                  }
              },
              {
                  title: '操作',
                  align: 'center',
                  formatter: function(value, row, index) {
                      var actions = [];
                      actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="detail(\'' + row.orderId + '\')">查看详情</a> ');
                      return actions.join('');
                  }
              }]
          };
          $.table.init(options);


          //加载驾校、分校
          $.cxSelect.defaults.url = ctx + "business/school/schoolData";
          $('#cxSelectSchool').cxSelect({
              selects: ['schoolId', 'branchId'],
              jsonValue: 'v',
          });

      });

      /* 查看批量提交学员明细 */
      function detail(id) {
          var url = prefix + '/detail/' + id;

          var options = {
                  title: "批量提交学员明细",
                  url: url,
                  btn: ['关闭'],
                  width: 950,
                  height: 600,
                  yes: function (index, layero) {
                      $.modal.close(index);
                  }
              };
              $.modal.openOptions(options);
      }

      /* 导出批量交费列表 */
      function exportSelected() {
        var params = $("#formId").serialize();
        var ids = $.table.selectColumns("orderId");
        var tipMsg = "确定导出所有数据吗？";
        if($.common.isNotEmpty(ids)){
            tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
            params += "&ids="+ids;
        }
        $.modal.confirm(tipMsg, function() {
            var config = {
              url: prefix + "/export",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
        });
      }
    </script>
  </body>
</html>
