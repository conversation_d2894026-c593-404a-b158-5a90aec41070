<!DOCTYPE html>
<html lang="zh" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('学员交费向导')"/>
    <th:block th:include="include :: jasny-bootstrap-css"/>
    <th:block th:include="include :: jquery-smartwizard-css"/>
    <style type="text/css">
        /* 如果要让工具栏固定在页面底部,使用下面的样式,不需要的可以注释 */
        .sw > .toolbar-bottom {
            z-index: 100;
            bottom: 0px;
            left: 0;
            width: 100%;
            position: fixed;
            text-align: right;
            background: #fff;
            box-shadow: 0 -2px 6px 1px hsla(223, 8%, 83%, .5);
            border-top: 1px solid #e3e4e8;
        }

        /* 如果设置了是否自动调节高度为false,需要加滚动条 */
        .sw > .tab-content {
            overflow-x: hidden;
            overflow-y: auto;
        }

        /* 解决工具栏无法固定底部的问题（如果页面没有animated类可以不写这部分代码） */
        .animated {
            animation-fill-mode: none;
            -webkit-animation-fill-mode: none;
            -moz-animation-fill-mode: none;
            -o-animation-fill-mode: none;
        }

        .nav > li.active {
            /* border-left: 4px solid #19aa8d;
            background: #293846;*/
        }
    </style>
</head>

<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight" style="height: 100%;">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox">
                <div class="ibox-title">
                    <h5>
                        学员交费向导
                    </h5>
                </div>
                <div class="ibox-content">
                    <div id="smartwizard">
                        <ul class="nav">
                            <li class="nav-item">
                                <a class="nav-link" href="#step-1"> 第一步：学员信息导入 </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#step-2"> 第二步：学员信息匹配 </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#step-3"> 第三步 ：确认信息</a>
                            </li>
                        </ul>
                        <div class="tab-content" style="min-height: 400px">
                            <input id="stepNumber" name="stepNumber" type="hidden" value="1">
                            <div id="step-1" class="tab-pane" role="tabpanel" aria-labelledby="step-1">
                                <form class="form form-horizontal m-t" id="form-step-1">
                                    <h4 class="form-header h4">学员信息导入</h4>

                                    <div id="cxSelectSchool" class="form-group">
                                        <label class="col-sm-3 control-label is-required">所属学校：</label>
                                        <div class="col-sm-4">
                                            <select id="schoolId" name="schoolId" class="schoolId form-control m-b"
                                                    data-first-title="所属驾校" required></select>
                                        </div>
                                        <!-- <div class="col-sm-4">
                                            <select id="branchId" name="branchId" class="branchId form-control m-b"
                                                    data-first-title="所属分校"></select>
                                        </div> -->
                                    </div>

                                    <div class="form-group">
                                        <label class="col-sm-3 control-label is-required">导入学员信息表格：</label>
                                        <div class="col-sm-8">
                                            <div class="fileinput fileinput-new input-group"
                                                 data-provides="fileinput">
                                                <div class="form-control" data-trigger="fileinput">
                                                    <i class="glyphicon glyphicon-file fileinput-exists"></i>
                                                    <span class="fileinput-filename"></span>
                                                </div>
                                                <span class="input-group-addon btn btn-white btn-file">
                                                        <span class="fileinput-new">选择文件</span>
                                                        <span class="fileinput-exists">更改</span>
                                                        <input type="file" name="file" accept=".xls,.xlsx" required>
                                                    </span>
                                                <a href="#" class="input-group-addon btn btn-white fileinput-exists"
                                                   data-dismiss="fileinput">清除</a>
                                            </div>
                                            <span class="help-block m-b-none">
                                                    <i class="fa fa-info-circle"></i>
                                                    仅允许导入“xls”或“xlsx”格式文件
                                                </span>
                                            <div class="pt5">
                                                <a onclick="importTemplate()" class="btn btn-default btn-xs">
                                                    <i class="fa fa-file-excel-o"></i> 下载模板
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div id="step-2" class="tab-pane" role="tabpanel" aria-labelledby="step-2">
                                <form class="form form-horizontal m-t" id="form-step-2">
                                    <div class="tabs-container">
                                        <ul class="nav nav-pills">
                                            <li class="nav-item active"><a data-toggle="tab" href="#tab-1"
                                                                           aria-expanded="true">匹配学员</a></li>
                                            <li class="nav-item"><a data-toggle="tab" href="#tab-2"
                                                                    aria-expanded="false">未匹配学员</a></li>
                                        </ul>
                                        <div class="tab-content">
                                            <div id="tab-1" class="tab-pane active">
                                                <div class="panel-body">
													
                                                    <div class="row">
                                                        <div class="col-sm-12 select-table table-bordered">
                                                            <table id="bootstrap-table-1"></table>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                            <div id="tab-2" class="tab-pane">
                                                <div class="panel-body">
													<div class="row">
														<a class="btn btn-warning" onclick="exportError()">
										                    <i class="fa fa-download"></i> 导出
										                </a>
													</div>
                                                    <div class="row">
                                                        <div class="col-sm-12 select-table table-bordered">
                                                            <table id="bootstrap-table-2"></table>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>


                                    </div>
                                </form>
                            </div>
                            <div id="step-3" class="tab-pane" role="tabpanel" aria-labelledby="step-3">
                                <form class="form form-horizontal m-t" id="form-step-3">
                                    <div class="row">
                                        <div class="col-sm-12 select-table table-bordered">
                                            <table id="bootstrap-table-3"></table>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: jasny-bootstrap-js"/>
<th:block th:include="include :: jquery-cxselect-js"/>
<th:block th:include="include :: jquery-smartwizard-js"/>
<script th:inline="javascript">
    var prefix = ctx + "business/pay";

    var licenseTypeDatas = [[${@dict.getType('license_types') }]];
    var businessTypeDatas = [[${@dict.getType('business_type') }]];

    var schoolId = "";
    var branchId = "";

    $(document).ready(function () {
        // 工具栏按钮
        var btnFinish = $('<a id="btn-finish"></a>').text('确认提交到银行交费').addClass('btn btn-info').on('click', function () {
            submitHandler();
        });
        var btnCancel = $('<a id="btn-cancel"></a>').text('取消').addClass('btn btn-danger').on('click', function () {
            $('#smartwizard').smartWizard("reset");
            refreshTab();
            schoolId = "";
            branchId = "";
        });
        // 下面两个按钮是为了因为插件默认的是botton,这里换成<a>,也可以选择用样式替换,或者不替换
        var btnNext = $('<a id="btn-next"></a>').text('下一步').addClass('btn btn-info').on('click', function () {
            $('#smartwizard').smartWizard("next");
        });
        var btnPrev = $('<a id="btn-prev"></a>').text('上一步').addClass('btn btn-success disabled').on('click', function () {
            $('#smartwizard').smartWizard("prev");
        });
        // 初始化表单向导组件
        $('#smartwizard').smartWizard({
            theme: 'dots', // default, arrows, dots, progress
            autoAdjustHeight: false, // 自动调整高度, 默认true
            enableURLhash: false, //开启URL hash,开启后点击浏览器前进后退按钮会执行下一步和上一步操作
            transition: {
                animation: 'slide-horizontal', // Effect on navigation, none/fade/slide-horizontal/slide-vertical/slide-swing
            },
            toolbarSettings: {
                showNextButton: false,// 因为上面自定义了下一步按钮, 所以隐藏掉插件自带的按钮, 如果不使用自定义按钮, 需要改为true或者去掉该属性
                showPreviousButton: false,// 因为上面自定义了上一步按钮, 所以隐藏掉插件自带的按钮, 如果不使用自定义按钮, 需要改为true或者去掉该属性
                toolbarExtraButtons: [btnCancel, btnPrev, btnNext, btnFinish]// 扩展的按钮集合
            }
        });


        //加载驾校、分校
        $.cxSelect.defaults.url = ctx + "business/school/schoolData";
        $('#cxSelectSchool').cxSelect({
            selects: ['schoolId', 'branchId'],
            jsonValue: 'v',
        });


    });

    $("#form-step-1").validate({
        onkeyup: false,
        focusCleanup: true,
        rules: {
            'file': {
                required: true
            }
        }
    });

    function submitHandler() {
        var stepNumber = $("#stepNumber").val();
        var form = $("#step-" + stepNumber).find('.form');
        console.log(form)
        if (form.length > 0) {
            if (form.validate().form()) {
                if (stepNumber == 1) {
                    saveStudentPay();
                } else {
                    //submit();
                    showPayInfo();
                }
            }
        }
    };

    //学员信息交费导入
    function saveStudentPay() {
        var stepNumber = $("#stepNumber").val()
        var allData = new FormData();
        var $form = $("#form-step-" + stepNumber);
        for (var index = 0; index < $form.length; index++) {
            var form = $form[index];
            if (!$(form).validate().form()) {
                return false;
            }
            var formData = new FormData(form);
            for (var key of formData.keys()) {
                var value = formData.get(key);
                if (value) {
                    allData.append(key, value);
                }
            }
        }
        allData.append("stepNumber", stepNumber);

        var config = {
            url: prefix + "/importStudentPayData",
            type: "post",
            dataType: "json",
            contentType: false,
            processData: false,
            data: allData,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                if (typeof callback == "function") {
                    callback(result);
                }
                if (result.code == web_status.SUCCESS) {
                    $.modal.alertWarning(result.data.msg);
                    insertSuccessStudentRow(result.data.successStudentList);
                    insertErrorStudentRow(result.data.errorStudentList);
                    studentInfoBank(result.data.successStudentList);

                    schoolId = result.data.account.schoolId;
                    branchId = result.data.account.branchId;

                } else if (result.code == web_status.WARNING) {
                    $.modal.msgWarning(result.msg);
                    $('#smartwizard').smartWizard("reset");
                } else {
                    $.modal.alertError(result.msg);
                    $('#smartwizard').smartWizard("reset");
                }
                $.modal.closeLoading();
            }
        };
        $.ajax(config);
    };

    function submit(transType,chnlNo,showPayInfoIndex) {
        table.set("bootstrap-table-3");
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            //获取全部数据(本页)
            var allTableData = $("#bootstrap-table-3").bootstrapTable('getData');
            if (allTableData.length == 0) {
                $.modal.alertWarning("请选择要操作的记录");
                return;
            }
            var ids = new Array();
            $.each(allTableData, function (i, e) {
                //a_barcode是table下columns的field
                ids.push(e.id);
            })
            rows = ids;
        }
        $.modal.confirm("确认提交到银行交费选中的" + rows.length + "条数据吗?", function (confirmIndex) {
            var url = ctx + "business/batchSubmit/addUnion";
            var data = {
                "studentIds": rows.join(),
                "schoolId": schoolId,
                "branchId": branchId,
                "transType": transType,
                "chnlNo": chnlNo
            };
            // let loadIndex = $.modal.loading("正在处理中，请稍候...");
            $.ajax({
                url: url,
                type: "post",
                dataType: "json",
                data: data,
                success: function (res) {
                    if (res.code === 0) {
						if(transType==2){
							var qrurl = res.data.qrurl;
							var html = '<img src='+qrurl+' width=300px height=300px/></br><span style=font-size:16px;>&nbsp;&nbsp请使用微信，支付宝，或者银联APP扫码支付&nbsp;&nbsp;</span><br/>';
							layer.open({
		                       type:1,//可传入的值有：0（信息框，默认）1（页面层）2（iframe层）3（加载层）4（tips层）
		                       shade: 0.6,
		                       maxmin: true,
		                       anim: 1,
		                       title: '扫码支付',
		                       btn: ['已支付', '未支付'],
		                       area: ['auto', 'auto'],
		                       // skin: 'layui-layer-nobg', //没有背景色
		                       shadeClose: true,
		                       content: html,
		                       yes:function(index,layero){
								   layer.close(index);
								   //layer.close(showPayInfoIndex);
								   $.operate.successTabCallback(res);
							   },function(index,layero){
								   layer.close(index);
								   $.modal.close(confirmIndex);
								   //layer.close(showPayInfoIndex);
							   }
		                   });
						}else{
                         window.open(res.data.url)
                         top.layer.confirm("请问是否已经支付？", {
                             icon: 3,
                             title: "系统提示",
                             btn: ['已支付', '未支付'],
                         }, function () {
                             $.operate.successTabCallback(res);
                         }, function (index) {
                             // top.layer.close(loadIndex)
                             $.modal.close(confirmIndex);
                             $.modal.close(index);
                         });
                       }
                    } else {
                        $.modal.alertError(res.msg);
                    }
                }
            })
        });
    }

    //匹配学员
    function insertSuccessStudentRow(data) {
        var options = {
            id: "bootstrap-table-1",
            data: data,
            sidePagination: "client",
            pageSize: 100,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                {
                    field: 'id',
                    title: '学生id',
                    visible: false
                },
                {
                    field: 'schoolId',
                    title: '驾校',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.school != null) {
                                return row.school.name;
                            }
                        }
                    }
                },
                {
                    field: 'branchId',
                    title: '分校',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.branch != null) {
                                return row.branch.name;
                            }
                        }
                    }
                },
                {
                    field: 'registrationId',
                    title: '报名点',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.registration != null) {
                                return row.registration.name;
                            }
                        }
                    }
                },
                {
                    field: 'licenseType',
                    title: '学车类型',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(licenseTypeDatas, value);
                    }
                },
                {
                    field: 'businessType',
                    title: '业务类型',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(businessTypeDatas, value);
                    }
                },
                {
                    field: 'name',
                    title: '学员姓名'
                },
                {
                    field: 'identity',
                    title: '证件号',
                },
            ]
        };
        $.table.init(options);
    };

    //未匹配学员
    function insertErrorStudentRow(data) {
        var options = {
            id: "bootstrap-table-2",
            data: data,
            sidePagination: "client",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                {
                    field: 'id',
                    title: '学生id',
                    visible: false
                },
                {
                    field: 'schoolId',
                    title: '驾校',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.school != null) {
                                return row.school.name;
                            }
                        }
                    }
                },
                {
                    field: 'branchId',
                    title: '分校',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.branch != null) {
                                return row.branch.name;
                            }
                        }
                    }
                },
                {
                    field: 'registrationId',
                    title: '报名点',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.registration != null) {
                                return row.registration.name;
                            }
                        }
                    }
                },
                {
                    field: 'licenseType',
                    title: '学车类型',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(licenseTypeDatas, value);
                    }
                },
                {
                    field: 'businessType',
                    title: '业务类型',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(businessTypeDatas, value);
                    }
                },
                {
                    field: 'name',
                    title: '学员姓名'
                },
                {
                    field: 'identity',
                    title: '证件号',
                },
                {
                    field: 'remark',
                    title: '未匹配原因',

                }]
        };
        $.table.init(options);
    }
    function exportError(){
		var allTableData = $("#bootstrap-table-2").bootstrapTable('getData');
        if (allTableData.length == 0) {
            $.modal.alertWarning("无数据导出");
            return;
        }
        console.log(allTableData);
        $.ajax({
            url: prefix + "/exportError",
            data: JSON.stringify(allTableData),
            type: "post",
            contentType: 'application/json',
            async: true,
            success: function(result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else {
                    $.modal.alertError(result.msg);
                }
            }
        })
	}

    //学员信息确认提交到银行交费
    function studentInfoBank(data) {
        var options = {
            id: "bootstrap-table-3",
            data: data,
            sidePagination: "client",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            showFooter: true,
            footerStyle: footerStyle,
            columns: [
                {
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '学生id',
                    visible: false
                },
                {
                    field: 'schoolId',
                    title: '驾校',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.school != null) {
                                return row.school.name;
                            }
                        }
                    }
                },
                {
                    field: 'branchId',
                    title: '分校',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.branch != null) {
                                return row.branch.name;
                            }
                        }
                    }
                },
                {
                    field: 'registrationId',
                    title: '报名点',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.registration != null) {
                                return row.registration.name;
                            }
                        }
                    }
                },
                {
                    field: 'licenseType',
                    title: '学车类型',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(licenseTypeDatas, value);
                    }
                },
                {
                    field: 'businessType',
                    title: '业务类型',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(businessTypeDatas, value);
                    }
                },
                {
                    field: 'name',
                    title: '学员姓名'
                },
                {
                    field: 'identity',
                    title: '证件号',
                },
                {
                    field: 'superviseFee',
                    title: '交费',
                    footerFormatter: function (value) {
                        var sumBalance = 0;
                        for (var i in value) {
                            sumBalance += parseFloat(value[i].superviseFee);
                        }
                        return "分页合计：" + sumBalance;
                    }
                }]
        };
        $.table.init(options);
    }

    function footerStyle(column) {
        return {
            superviseFee: {
                css: {color: 'red', 'font-weight': 'normal'}
            }

        }[column.field]
    }

    // 显示步骤时将触发事件
    $("#smartwizard").on("showStep", function (e, anchorObject, stepNumber, stepDirection, stepPosition) {
        $("#stepNumber").val((stepNumber + 1));
        // 下面按钮是快速操作栏的
        $("#prev-btn").removeClass('disabled');
        $("#next-btn").removeClass('disabled');
        // 下面按钮是工具栏的
        $("#btn-prev").removeClass('disabled');
        $("#btn-next").removeClass('disabled');
        $("#btn-finish").removeClass('disabled');
        if (stepPosition === 'first') {
            $("#prev-btn").addClass('disabled');// 快速操作栏（演示用）
            $("#btn-prev").addClass('disabled');
            $("#btn-finish").addClass('disabled');
            $("#btn-finish").hide();
            $("#btn-next").show();
            $("#btn-next").text("下一步")
        } else if (stepPosition === 'last') {
            $("#next-btn").addClass('disabled');// 快速操作栏（演示用）
            $("#btn-next").hide();
            $("#btn-next").addClass('disabled');
            $("#btn-finish").show();
        } else {
            $("#prev-btn").removeClass('disabled');// 快速操作栏（演示用）
            $("#next-btn").removeClass('disabled');// 快速操作栏（演示用）
            $("#btn-next").show();
            $("#btn-prev").removeClass('disabled');
            $("#btn-next").removeClass('disabled');
            $("#btn-next").text("下一步");
            $("#btn-finish").addClass('disabled');
            $("#btn-finish").hide();
        }
    });

    // 该事件在离开某个步骤之前触发
    $("#smartwizard").on("leaveStep", function (e, anchorObject, currentStepNumber, nextStepNumber, stepDirection) {
        if (stepDirection == 'forward') {
            var form = $("#step-" + (currentStepNumber + 1)).find('.form');
            if (form.length > 0) {
                if (currentStepNumber == 0) {
                    if (form.validate().form()) {
                        saveStudentPay();
                    }
                }
                return form.validate().form();
            }
            return true;
        }
        return true;
    });

    $("#theme-selector").on("change", function () {
        var options = {
            theme: $(this).val()
        };
        $('#smartwizard').smartWizard("setOptions", options);
        return true;
    });

    $("#reset-btn").on("click", function () {
        // Reset wizard
        $('#smartwizard').smartWizard("reset");
        return true;
    });

    $("#prev-btn").on("click", function () {
        // Navigate previous
        $('#smartwizard').smartWizard("prev");
        return true;
    });

    $("#next-btn").on("click", function () {
        // Navigate next
        $('#smartwizard').smartWizard("next");
        return true;
    });

    // 第1步模版导入
    function importTemplate() {
        $.get(prefix + "/importStudentTemplate", function (result) {
            if (result.code == web_status.SUCCESS) {
                window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
            } else if (result.code == web_status.WARNING) {
                $.modal.alertWarning(result.msg);
            } else {
                $.modal.alertError(result.msg);
            }
        });
    }

    /* 展示交费明细 */
    function showPayInfo() {
        table.set("bootstrap-table-3");
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            //获取全部数据(本页)
            var allTableData = $("#bootstrap-table-3").bootstrapTable('getData');
            if (allTableData.length == 0) {
                $.modal.alertWarning("请选择要操作的记录");
                return;
            }
            var ids = new Array();
            $.each(allTableData, function (i, e) {
                //a_barcode是table下columns的field
                ids.push(e.id);
            })
            rows = ids;
        }

        let _url = ctx + "business/batchSubmit/showPayInfo?studentIds="+rows.join()+"&schoolId="+schoolId+"&branchId="+branchId;
        layer.open({
            type: 2,
            content: _url,
            title: '确认提交到银行交费',
            area: ['800px', '450px'], //宽，高
            btn: ['确认提交','取消'],
            offset: 'auto', // 居中显示
            yes: function (index, layero) {
                //获取下拉框的值
                var iframeWindow = window[layero.find('iframe')[0]['name']];
                var transType = iframeWindow.$('#transType').val();
                var chnlNo = iframeWindow.$('#chnlNo').val();
                if($.trim(transType).length==0){
                	$.modal.alertWarning("请选择交易类型");
                	return;
                }
                if(transType==0 || transType==1){
	                if($.trim(chnlNo).length==0){
	                	$.modal.alertWarning("请选择付款银行");
	                	return;
	                }
                }
                submit(transType,chnlNo,index);
                
            },
        });
    }

</script>
</body>

</html>