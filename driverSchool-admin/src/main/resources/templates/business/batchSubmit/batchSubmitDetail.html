<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('批量提交学员明细列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <input type="hidden" id="orderId" name="orderId" th:value="${superviseBatchSubmit.orderId}"/>
                </form>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "business/batchSubmitDetail";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "批量提交学员明细",
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                queryParams: queryParams,
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'orderId',
                    title: '订单号'
                },
                {
                    field: 'studentId',
                    title: '学员',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.schoolStudent != null) {
                                return row.schoolStudent.name;
                            }
                        }
                    }
                },
                {
                    field: 'studentId',
                    title: '联系号码',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.schoolStudent != null) {
                                return basecclusion(row.schoolStudent.mobile,3,4);
                            }
                        }
                    }
                },

                {
                   field: 'totalFee',
                   title: '监管金额'
                },
                {
                   field: 'commission',
                   title: '手续费'
                },
                {
                   field: 'superviseFee',
                   title: '剩余监管金额'
                },
                {
                    field: 'createdTime',
                    title: '时间'
                }
                ]
            };
            $.table.init(options);
        });

        function queryParams(params) {
            var search = $.table.queryParams(params);
            search.orderId = $("#orderId").val();
            return search;
        }
    </script>
</body>
</html>