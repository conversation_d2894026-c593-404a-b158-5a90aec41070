<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('展示交费明细')" />
    <th:block th:include="include :: upload-img-css" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-teacher-edit" th:object="${showPayInfoVo}">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">交易类型：</label>
                <div class="col-sm-8">
                    <select id="transType" name="transType" class="form-control m-b" onchange="selectChnlNo(this)" required>
                        <option value="">请选择</option>
                        <option value="0">个人网银</option>
                        <option value="1">企业网银</option>
                        <option value="2">扫码支付</option>
                    </select>
                </div>
            </div>
            <div class="form-group" id="divBank">
                <label class="col-sm-3 control-label is-required">选择支付银行：</label>
                <div class="col-sm-8">
                    <select id="chnlNo" name="chnlNo" class="form-control m-b" th:with="type=${@dict.getType('unionBank_person_chnlNo')}" required>
                        <option value="">请选择</option>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
<!--            <div class="form-group">
                <label class="col-sm-3 control-label">监管金额：</label>
                <div class="col-sm-8">
                	<span style="font-size:20px; color:red; font-weight:bold" th:text="*{entryAmtDcl}"></span> 元
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">手续费：</label>
                <div class="col-sm-8">
                	<span style="font-size:20px; color:red; font-weight:bold" th:text="*{commission}"></span> 元
                </div>
            </div>-->

            <div class="form-group">
                <label class="col-sm-3 control-label">总费用：</label>
                <div class="col-sm-8">
                	<span style="font-size:20px; color:red; font-weight:bold" th:text="*{totalAmt}"></span> 元
                </div>
            </div>
            
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: upload-img-js" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
    
    	var personBank = [[${@dict.getType('unionBank_person_chnlNo') }]];
    	var enterpriseBank = [[${@dict.getType('unionBank_enterprise_chnlNo') }]];
    	
    	$(function(){
			var commission = [[${showPayInfoVo.commission}]];
			if(commission<=20){
				$('#transType option[value=1]').remove();
			}
			var specialSchool = [[${showPayInfoVo.specialSchool}]];
			if(specialSchool){
				if(specialSchool.isSupportWeixin==1 && specialSchool.isSupportZFB==0){
					$('#transType option[value=2]').text("微信扫码支付");
				}
				if(specialSchool.isSupportZFB==1 && specialSchool.isSupportWeixin==0){
					$('#transType option[value=2]').text("支付宝扫码支付");
				}
				if(specialSchool.isSupportWeixin==0 && specialSchool.isSupportZFB==0){
					$('#transType option[value=2]').remove();
				}
			}
		});
    
		function selectChnlNo(e){
			var transType = $(e).val();
			$('#chnlNo').empty();
			var option = "<option value=''>请选择</option>";
			$('#chnlNo').append(option);
			if(transType==0){
				$('#divBank').show();
				for(var i=0;i<personBank.length;i++){
					option = "<option value="+personBank[i].dictValue+">"+personBank[i].dictLabel+"</option>";
					$('#chnlNo').append(option);
				}
			}else if(transType==1){
				$('#divBank').show();
				for(var i=0;i<enterpriseBank.length;i++){
					option = "<option value="+enterpriseBank[i].dictValue+">"+enterpriseBank[i].dictLabel+"</option>";
					$('#chnlNo').append(option);
				}
			}else if(transType==2){
				$('#divBank').hide();
			}
		}
    </script>
</body>
</html>