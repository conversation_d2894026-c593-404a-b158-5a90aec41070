<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('资讯内容列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>标题：</label>
                                <input type="text" name="title"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="business:article:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="business:article:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="business:article:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning multiple disabled" onclick="check()" shiro:hasPermission="business:article:check">
                    审核
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:article:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:article:remove')}]];
        var isShowDatas = [[${@dict.getType('is_show_article')}]];
        var prefix = ctx + "business/article/serviceCenter";

        $(function() {
            var options = {
                url: "/business/article/list?category=4",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: ctx + "business/article/remove",
                modalName: "服务中心",
                showPageGo: true,
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '学车指引key为“study_guidence”',
                    visible: false
                },
                {
                    field: 'title',
                    title: '标题'
                },
                    {
                        field: 'content',
                        title: '内容',
                        formatter: function(value, row, index) {
                            return $.table.tooltip(getNoMarkupStr(value));
                        }
                    },
                {
                    field: 'isShow',
                    title: '是否显示',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(isShowDatas, value);
                    }
                },
                    {
                        field: 'isCheck',
                        title: '是否审核',
                        formatter: function(value, row, index) {
                            if(value == 0){
                                return "未审核";
                            }else if (value == 1) {
                                return "已审核";
                            }else{
                                return "-";
                            }
                        }
                    },
                    // {
                    //     title: '审核状态',
                    //     align: 'center',
                    //     formatter: function (value, row, index) {
                    //         return statusTools(row);
                    //     }
                    // },
                {
                    field: 'orderNo',
                    title: '排序'
                },
                {
                    field: 'updatedTime',
                    title: '更新时间'
                },
                ]
            };
            $.table.init(options);
        });


        function check() {
            var rows = $.table.selectRows();
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            } else if (rows.length == 1) {
                if (rows[0].isCheck == 1) {
                    $.modal.alertWarning("资讯内容" + rows[0].title + "已经是已审核状态。");
                } else {
                    $.modal.confirm("确认要审核服务中心" + rows[0].title + "吗?", function () {
                        $.operate.submitPro(ctx + "business/article/checkBasicArticle", "post", "json", {ids: $.table.selectColumns("id").join(",")}, "正在审核中");
                    });
                }
            } else {
                $.modal.confirm("确认要审核选中的服务中心吗?", function () {
                    $.operate.submitPro(ctx + "business/article/checkBasicArticle", "post", "json", {ids: $.table.selectColumns("id").join(",")}, "正在审核中");
                });
            }
        }


        /*状态显示 */
        function statusTools(row) {
            if (row.isCheck == 0) {
                return '<i class=\"fa fa-toggle-off text-info fa-2x\" onclick="enable(\'' + row.id + '\')"></i> ';
            } else {
                return '<i class=\"fa fa-toggle-on text-info fa-2x\" onclick="disable(\'' + row.id + '\')"></i> ';
            }
        }

        /* 资讯内容-停用 */
        function disable(id) {
            $.modal.confirm("确认要停用资讯内容吗？", function() {
                $.operate.post(ctx + "business/article/changeStatus", { "id": id, "isCheck": 0 });
            })
        }
        /* 资讯内容启用 */
        function enable(id) {
            $.modal.confirm("确认要启用资讯内容吗？", function() {
                $.operate.post(ctx + "business/article/changeStatus", { "id": id, "isCheck": 1 });
            })
        }

    </script>
</body>
</html>