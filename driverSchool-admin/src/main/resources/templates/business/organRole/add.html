<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增机构角色信息')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-organRole-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">角色名称：</label>
                <div class="col-sm-8">
                    <input name="roleName" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">显示顺序：</label>
                <div class="col-sm-8">
                    <input name="roleSort" class="form-control" type="text" required>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <label class="toggle-switch switch-solid">
                        <input type="checkbox" id="status" checked>
                        <span></span>
                    </label>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">数据范围：</label>
                <div class="col-sm-8">
                    <input name="dataScope" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "business/organRole"
        $("#form-organRole-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                add()
            }
        }

        function add() {
            var roleName = $("input[name='roleName']").val();
            var roleSort = $("input[name='roleSort']").val();
            var status = $("input[id='status']").is(':checked') == true ? 0 : 1;
            var remark = $("input[name='remark']").val();
            $.ajax({
                cache : true,
                type : "POST",
                url : prefix + "/add",
                data : {
                    "roleName": roleName,
                    "roleSort": roleSort,
                    "status": status,
                    "remark": remark,
                },
                async : false,
                error : function(request) {
                    $.modal.alertError("系统错误");
                },
                success : function(data) {
                    $.operate.successCallback(data);
                }
            });
        }

    </script>
</body>
</html>