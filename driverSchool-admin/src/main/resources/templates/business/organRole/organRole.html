<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('机构角色信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>账号类型：</label>
                                <input type="text" name="roleName"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

           <div class="btn-group-sm" id="toolbar" role="group" th:if="${@config.getKey('sys.setOrganRole.type') == '1'}">
               <a class="btn btn-success" onclick="set()">
                   <i class="fa fa-bars"></i> 配置机构用户菜单
               </a>
           </div>

            <!--<div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="business:organRole:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="business:organRole:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
            </div>-->
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:organRole:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:organRole:remove')}]];
        var prefix = ctx + "business/organRole";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "机构角色信息",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                columns: [{
                    checkbox: true,
                    visible: false
                },
                {
                    field: 'roleId',
                    title: '角色ID',
                    visible: false
                },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    visible: true,
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },

                {
                    field: 'roleName',
                    title: '账号类型'
                },
                {
                    title: '菜单权限设置',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.roleId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function set() {
            table.set();
            $.modal.open("配置机构用户菜单",prefix+"/setOrganRole");
        }

    </script>
</body>
</html>