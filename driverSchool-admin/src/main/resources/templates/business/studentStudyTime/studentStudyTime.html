<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('学时数据列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>
                <li>
                  <label>分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="branchId"
                    name="branchId"
                    class="branchId form-control m-b"
                    data-first-title="选择分校"
                  ></select>
                </li>
                <li>
                  <label>报名点：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="registrationId"
                    name="registrationId"
                    class="registrationId form-control m-b"
                    data-first-title="所属报名点"
                  ></select>
                </li>

                <li>
                  <label>姓名：</label>
                  <input type="text" name="params[studentName]" />
                </li>
                <li>
                  <label>身份证号：</label>
                  <input
                    type="text"
                    name="params[identity]"
                    placeholder="多个身份证请用空格间隔"
                  />
                </li>

                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:studentStudyTime:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
        </div>
        <div class="col-sm-12 select-table table-bordered">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:studentStudyTime:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:studentStudyTime:remove')}]];
      var subjectIsPassData= [[${@dict.getType('subject_is_pass')}]];

      var prefix = ctx + "business/studentStudyTime";

      $(function () {
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              modalName: "学时数据",
              showSearch: false,
              showRefresh: false,
              showToggle: false,
              showColumns: false,
              columns:[

                  [
                      {
                          title : '基本信息',
                          align : 'center',
                          colspan: 9,
                          rowspan: 1
                      },
                      {
                      title : '课目一',
                      align : 'center',
                      colspan : 3,
                          rowspan: 1
                      },
                      {
                          title : '课目二',
                          align : 'center',
                          colspan : 3,
                          rowspan: 1
                      },
                      {
                          title : '课目三',
                          align : 'center',
                          colspan : 3,
                          rowspan: 1
                      },
                      {
                          title : '课目四',
                          align : 'center',
                          colspan : 3,
                          rowspan: 1
                      }

                  ],

                  [
                      {
                          checkbox: true,
                      },
                      {
                        field: 'id',
                        title: '主键',
                        visible: false
                        },
                      {
                          field: 'studentId',
                          title: '驾校',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if(row.schoolStudent != null){
                                      if (row.schoolStudent.school != null) {
                                          return row.schoolStudent.school.name;
                                      }
                                  }
                              }
                          },
                      },
                      {
                          field: 'studentId',
                          title: '分校',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if(row.schoolStudent != null){
                                      if (row.schoolStudent.branch != null) {
                                          return row.schoolStudent.branch.name;
                                      }
                                  }

                              }
                          }
                      },
                      {
                          field: 'studentId',
                          title: '报名点',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if(row.schoolStudent != null){
                                      if (row.schoolStudent.registration != null) {
                                          return row.schoolStudent.registration.name;
                                      }
                                  }
                              }
                          }
                      },
                      {
                          field: 'studentId',
                          title: '学员',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if (row.schoolStudent != null) {
                                      return row.schoolStudent.name;
                                  }
                              }
                          }
                      },

                      {
                          field: 'studentId',
                          title: '身份证号',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if (row.schoolStudent != null) {
                                      return row.schoolStudent.identity;
                                  }
                              }
                          }
                      },

                      {
                          field: 'studentId',
                          title: '业务类型',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if (row.schoolStudent != null) {
                                      return row.schoolStudent.businessType;
                                  }
                              }
                          }
                      },
                      {
                          field: 'studentId',
                          title: '驾驶证',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if (row.schoolStudent != null) {
                                      return row.schoolStudent.licenseType;
                                  }
                              }
                          }
                      },

                      {
                          field: 'subject1CheckValidStudyTime',
                          title: '有效时长',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  var time=formatSeconds(value);
                                  return '<a href="javascript:void(0)" onclick="detail(\'' + row.studentId + '\',\'1\')">' + time + '</a>';
                              }
                          }
                      },
                      {
                          field: 'subject1ExamineStudyTime',
                          title: '考核时长',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  var time=row.subject1ExamineStudyTime
                                  return formatSeconds(time);
                              }
                          }
                      },
                      {
                          field: 'subject1IsPass',
                          title: '是否合格',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  return $.table.selectDictLabel(subjectIsPassData, row.subject1IsPass);
                              }
                          }
                      },



                      {
                          field: 'subject2CheckValidStudyTime',
                          title: '有效时长',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  var time=formatSeconds(row.subject2CheckValidStudyTime);
                                  return '<a href="javascript:void(0)" onclick="detail(\'' + row.studentId + '\',\'2\')">' + time + '</a>';
                              }
                          }
                      },
                      {
                          field: 'subject2ExamineStudyTime',
                          title: '考核时长',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  var time=row.subject2ExamineStudyTime
                                  return formatSeconds(time);
                              }
                          }
                      },
                      {
                          field: 'subject2IsPass',
                          title: '是否合格',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  return $.table.selectDictLabel(subjectIsPassData, row.subject2IsPass);
                              }
                          }
                      },


                      {
                          field: 'subject3CheckValidStudyTime',
                          title: '有效时长',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  var time=formatSeconds(row.subject3CheckValidStudyTime);
                                  return '<a href="javascript:void(0)" onclick="detail(\'' + row.studentId + '\',\'3\')">' + time + '</a>';
                              }
                          }
                      },
                      {
                          field: 'subject3ExamineStudyTime',
                          title: '考核时长',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  var time=row.subject3ExamineStudyTime
                                  return formatSeconds(time);
                              }
                          }
                      },
                      {
                          field: 'subject3IsPass',
                          title: '是否合格',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  return $.table.selectDictLabel(subjectIsPassData, row.subject3IsPass);
                              }
                          }
                      },



                      {
                          field: 'subject4CheckValidStudyTime',
                          title: '有效时长',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  var time=formatSeconds(row.subject4CheckValidStudyTime);
                                  return '<a href="javascript:void(0)" onclick="detail(\'' + row.studentId + '\',\'4\')">' + time + '</a>';
                              }
                          }
                      },
                      {
                          field: 'subject4ExamineStudyTime',
                          title: '考核时长',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  var time=row.subject4ExamineStudyTime
                                  return formatSeconds(time);
                              }
                          }
                      },
                      {
                          field: 'subject4IsPass',
                          title: '是否合格',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  return $.table.selectDictLabel(subjectIsPassData, row.subject4IsPass);
                              }
                          }
                      },

                  ]

              ]
          };
          $.table.init(options);


          //加载驾校、分校
          $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
          $('#cxSelectSchool').cxSelect({
              selects: ['schoolId', 'branchId','registrationId'],
              jsonValue: 'v',
          });

      });

      function detail(id,subjectType) {
          var url = prefix + '/detail/' + id+"/"+subjectType;
          $.modal.openTab("学时数据有效时长", url);
      }


      // 导出数据
      function exportSelected() {
            var params = $("#formId").serialize();
            var ids = $.table.selectColumns("studentId");
            var tipMsg = "确定导出所有数据吗？";
            if($.common.isNotEmpty(ids)){
                tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
                params += "&ids="+ids;
            }
            $.modal.confirm(tipMsg, function() {
                var config = {
                url: prefix + "/export",
                type: "post",
                dataType: "json",
                data: params,
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍候...");
                },
                success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                },
                };
                $.ajax(config);
            });
        }
    </script>
  </body>
</html>
