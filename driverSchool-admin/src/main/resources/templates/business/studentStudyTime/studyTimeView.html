<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('有效时长列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">

            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <input type="hidden" id="studentId" name="studentId" th:value="${studentId}"/>
                    <input type="hidden" id="subjectName" name="subjectName" th:if="${subjectType == '1'}" th:value="课目1"/>
                    <input type="hidden" id="subjectName" name="subjectName" th:if="${subjectType == '2'}" th:value="课目2"/>
                    <input type="hidden" id="subjectName" name="subjectName" th:if="${subjectType == '3'}" th:value="课目3"/>
                    <input type="hidden" id="subjectName" name="subjectName" th:if="${subjectType == '4'}" th:value="课目4"/>
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>教练名称：</label>
                                <input type="text" name="teacherName"/>
                            </li>
                            <li>
                                <label>开始时间：</label>
                                <input type="text" class="time-input" placeholder="请选择开始时间" name="beginTime"/>
                            </li>
                            <li>
                                <label>结束时间：</label>
                                <input type="text" class="time-input" placeholder="请选择结束时间" name="endTime"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>



            <div class="col-sm-12 select-table table-bordered">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var checkStatusDatas = [[${@dict.getType('study_time_check_status')}]];
        var prefix = ctx + "business/studentStudyTime";

        $(function() {
            var options = {
                url: prefix + "/studentStudyTimeDetailList",
                modalName: "学时数据有效时长",
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                queryParams: queryParams,
                columns: [
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'studentId',
                    title: '学员',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.schoolStudent != null) {
                                return row.schoolStudent.name;
                            }
                        }
                    }
                },
                {
                    field: 'teacherName',
                    title: '教练名称'
                },
                {
                    field: 'carNo',
                    title: '车牌号'
                },
                {
                    field: 'classType',
                    title: '课时类型'
                },
                    {
                        field: 'subjectName',
                        title: '考试课目'
                    },
                {
                    field: 'classSubType',
                    title: '课时子类'
                },
                {
                    field: 'beginTime',
                    title: '开始时间'
                },
                {
                    field: 'endTime',
                    title: '结束时间'
                },
                {
                    field: 'studyTime',
                    title: '未审时长'
                },
                {
                    field: 'checkStatus',
                    title: '审核状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(checkStatusDatas, value);
                    }
                },
                {
                    field: 'checkValidStudyTime',
                    title: '审核有效时长'
                },
                {
                    field: 'checkInvalidStudyTime',
                    title: '审核无效时长'
                },
                ]
            };
            $.table.init(options);
        });

        function queryParams(params) {
            var search = $.table.queryParams(params);
            search.studentId = $("#studentId").val();
            search.subjectName = $("#subjectName").val();
            return search;
        }
    </script>
</body>
</html>