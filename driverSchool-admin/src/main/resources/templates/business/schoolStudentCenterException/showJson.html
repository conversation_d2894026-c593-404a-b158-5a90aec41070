<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <th:block th:include="include :: header('查看提交数据')" />
  </head>
  <body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
      <form
        class="form-horizontal m"
        id="form-replyTemplate-edit"
        th:object="${schoolStudentCenterException}"
      >
        <input name="id" th:field="*{id}" type="hidden" />
        <div class="form-group">
          <div class="col-sm-8">
            <textarea
              id="submitParams"
              name="submitParams"
              rows="20"
              cols="100"
              th:text="*{submitParams}"
            ></textarea>
          </div>
        </div>
        <div class="form-group">
          <div class="col-sm-8">
            <a id="copyButton" class="btn btn-success"> 复制 </a>
          </div>
        </div>
      </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
      /* 复制文本 */
      $("#copyButton").click(function () {
        var textToCopy = $("#submitParams").val();
        var clipboardItem = new ClipboardItem({
          "text/plain": new Blob([textToCopy], { type: "text/plain" }),
        });
        navigator.clipboard
          .write([clipboardItem])
          .then(function () {
            $.modal.alertSuccess("已复制");
          })
          .catch(function (err) {
            console.error("复制失败:", err);
          });
      });
    </script>
  </body>
</html>
