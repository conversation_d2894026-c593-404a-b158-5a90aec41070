<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('上报学时中心异常数据列表')" />
    <th:block th:include="include :: select2-css"/>
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>学校：</label>
                  <select
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="请选择"
                  ></select>
                </li>
                <li>
                  <label>学时中心：</label>
                  <select
                    class="form-control"
                    id="centerName"
                    name="centerName"
                  >
                    <option value="">请选择</option>
                    <option
                      th:each="dict : ${centerNameList}"
                      th:text="${dict}"
                      th:value="${dict}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label>姓名：</label>
                  <input
                    type="text"
                    name="studentName"
                    placeholder="请输入姓名"
                  />
                </li>
                <li>
                  <label>身份证号：</label>
                  <input
                    type="text"
                    name="studentIdentity"
                    placeholder="请输入精准身份证号，多个用空格隔开"
                  />
                </li>
                <li class="select-time">
                  <label>提交时间： </label>
                  <input
                    type="text"
                    class="time-input"
                    data-type="datetime"
                    id="beginSynDate"
                    data-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="开始时间"
                    name="beginSynDate"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    class="time-input"
                    data-type="datetime"
                    id="endSynDate"
                    data-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="结束时间"
                    name="endSynDate"
                  />
                </li>
                <li>
                  <label>是否成功：</label>
                  <select
                    name="isSuccess"
                    th:with="type=${@dict.getType('is_yes_no_2')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="resetQueryForm()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:schoolStudentCenterException:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js"/>
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:schoolStudentCenterException:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:schoolStudentCenterException:remove')}]];
      var isYesNo = [[${@dict.getType('is_yes_no_2')}]];
      var prefix = ctx + "business/schoolStudentCenterException";

      $(function() {
          //初始化搜索框
          $("#schoolId").select2({

          });
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              importUrl: prefix + "/importData",
              importTemplateUrl: prefix + "/importTemplate",
              exportUrl: prefix + "/export",
              detailUrl: prefix + "/view/{id}",
              showPageGo: true,
              modalName: "上报学时中心异常数据",
              columns: [{
                  checkbox: true
              },
              {
                  field: 'id',
                  title: '主键',
                  visible: false
              },
              {
                  field: 'studentName',
                  title: '学员姓名'
              },
              {
                  field: 'studentIdentity',
                  title: '证件号码'
              },
              {
                  field: '',
                  title: '学校名称',
                  formatter: function(value, row, index) {
                       return row.school.name;
                    }
              },
              {
                  field: 'centerName',
                  title: '学时中心'
              },
              {
                  field: 'requestUrl',
                  title: '提交地址'
              },
              {
                  field: 'isSuccess',
                  title: '是否成功',
                  formatter: function(value, row, index) {
                       return $.table.selectDictLabel(isYesNo, value);
                    }
              },
              {
                  field: '',
                  title: '返回数据',
                  formatter: function(value, row, index) {
                        if (row.isSuccess == 1) {
                            return row.successReason;
                        }else {
                            return row.failReason;
                        }
                    }
              },
              {
                  field: 'synDate',
                  title: '提交时间',
              },
              {
                  field: '',
                  title: '查看',
                  formatter: function(value, row, index) {
                        var actions = [];
                        var value = "查看提交的数据";
                        actions.push(' <div style="width: 100px;"><a href="javascript:void(0)" onclick="showJson(\'' + row.id + '\')">查看提交的数据</a></div> ');
                       return actions.join('');
                    }
              },]
          };
          $.table.init(options);

          //加载驾校、分校
        $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
          $('#cxSelectSchool').cxSelect({
              selects: ['schoolId'],
              jsonValue: 'v',
          });

          /** 初始化时间组件 */
          layui.use('laydate', function(){
            var laydate = layui.laydate;
                laydate.render({
                    elem: '#beginSynDate',
                    type: 'datetime',
                    trigger: 'click'
              });
                    laydate.render({
                    elem: '#endSynDate',
                    type: 'datetime',
                    trigger: 'click'
              });
          });
      });
        //重置搜索条件
        function resetQueryForm(){
            // 1. 先移除验证
            $('#formId').validate().resetForm();
            
            // 2. 重置表单
            $('#formId')[0].reset();
            
            // 3. 重置 select2 字段
            $("#schoolId").val('').trigger('change');
            $("#branchId").empty().trigger('change');
            $("#registrationId").empty().trigger('change');
            
            // 4. 清除错误样式
            $("#schoolId, #branchId, #registrationId").removeClass('error');
            $("label.error[for='schoolId'], label.error[for='branchId'], label.error[for='registrationId']").remove();
            
            // 5. 重新触发表格搜索
            $.table.search();
        }

        /* 查看提交的数据 */
       function showJson(id) {
        var url = prefix + "/showJson/"+id;
        var options = {
            title: "查看提交的数据",
            width: '',
            height: '',
            url: url,
            skin: 'layui-layer-gray',
            btn: ['关闭'],
            yes: function (index, layero) {
                $.modal.close(index);
            }
        };
        $.modal.openOptions(options);
       }

       /* 导出同步学时数据列表 */
      function exportSelected() {
        var params = $("#formId").serialize();
        var ids = $.table.selectColumns("id");
        var tipMsg = "确定导出所有数据吗？";
        if($.common.isNotEmpty(ids)){
            tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
            params += "&ids="+ids;
        }
        $.modal.confirm(tipMsg, function() {
            var config = {
              url: prefix + "/export",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
        });
      }
    </script>
  </body>
</html>
