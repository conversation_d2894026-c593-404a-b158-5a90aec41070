<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增用户管理（运营）')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-operateUser-add">
            <input id="organType" name="organType"  type="hidden" value="7">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">登录用户名：</label>
                <div class="col-sm-8">
                    <input id="username" name="username" class="form-control" type="text" required>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">密码：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="password" name="password" id="password" required>
                    <th:block th:with="chrtype=${@config.getKey('sys.account.chrtype')}">
                        <th:block th:if="${chrtype != '0'}">
                            <span class="help-block m-b-none">
                                <th:block th:if="${chrtype == '1'}"><i class="fa fa-info-circle" style="color: red;"></i>  密码只能为0-9数字 </th:block>
                                <th:block th:if="${chrtype == '2'}"><i class="fa fa-info-circle" style="color: red;"></i>  密码只能为a-z和A-Z字母</th:block>
                                <th:block th:if="${chrtype == '3'}"><i class="fa fa-info-circle" style="color: red;"></i>  密码必须包含（字母，数字）</th:block>
                                <th:block th:if="${chrtype == '4'}"><i class="fa fa-info-circle" style="color: red;"></i>  密码必须包含（字母，数字，特殊字符!@#$%^&*()-=_+）</th:block>
                            </span>
                        </th:block>
                    </th:block>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">再次确认：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="password" name="confirmPassword" id="confirmPassword">
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请再次输入您的密码</span>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label is-required" >真实姓名：</label>
                <div class="col-sm-8">
                    <input name="realName" class="form-control" type="text" required>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label is-required">联系方式：</label>
                <div class="col-sm-8">
                    <input name="tel" class="form-control" type="text" required>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label is-required">是否启用：</label>
                <div class="col-sm-8">
                    <select name="status" class="form-control m-b" th:with="type=${@dict.getType('organ_user_status')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">角色：</label>
                <div class="col-sm-8">
                    <select name="roleId" class="form-control m-b" th:with="type=${@dict.getType('organ_user_status')}" required>
                        <option th:each="role : ${roles}" th:text="${role.roleName}" th:value="${role.roleId}"  th:disabled="${role.status == '1'}"></option>
                    </select>
                </div>
            </div>

        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/operateUser"
        $("#form-operateUser-add").validate({
            rules:{
                username:{
                    minlength: 2,
                    maxlength: 20,
                    remote: {
                        url: prefix + "/checkUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "organType":function () {
                                return $.common.trim($("#organType").val());
                            },
                            "username": function() {
                                return $.common.trim($("#username").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },

                password: {
                    required: true,
                    isPassWord:true,
                    minlength: 5,
                    maxlength: 20
                },
                confirmPassword: {
                    required: true,
                    equalTo: "#password"
                },
                tel:{
                    isPhoneOrTel: true
                }
            },
            messages: {
                "username": {
                    remote: "登录用户名已经存在"
                },
                password: {
                    required: "请输入新密码",
                    minlength: "密码不能小于5个字符",
                    maxlength: "密码不能大于20个字符"
                },
                confirmPassword: {
                    required: "请再次输入新密码",
                    equalTo: "两次密码输入不一致"
                }

            },
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-operateUser-add').serialize());
            }
        }
    </script>
</body>
</html>