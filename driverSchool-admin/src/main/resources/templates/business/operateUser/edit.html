<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改用户管理（运营）')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-operateUser-edit" th:object="${organUser}">
            <input name="id" th:field="*{id}" type="hidden">
            <input name="organType" th:field="*{organType}" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">登录用户名：</label>
                <div class="col-sm-8">
                    <input name="username" th:field="*{username}" class="form-control" type="text"  disabled="disabled">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">真实姓名：</label>
                <div class="col-sm-8">
                    <input name="realName" th:field="*{realName}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">联系方式：</label>
                <div class="col-sm-8">
                    <input name="tel" th:field="*{tel}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">是否启用：</label>
                <div class="col-sm-8">
                    <select name="status" class="form-control m-b" th:with="type=${@dict.getType('organ_user_status')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{status}"></option>
                    </select>
                </div>
            </div>


            <div class="form-group">
                <label class="col-sm-3 control-label">角色：</label>
                <div class="col-sm-8">
                    <select name="roleId" th:field="*{roleId}" class="form-control m-b" th:with="type=${@dict.getType('organ_user_status')}" required>
                        <option th:each="role : ${roles}" th:text="${role.roleName}" th:value="${role.roleId}"  th:disabled="${role.status == '1'}"></option>
                    </select>
                </div>
            </div>

        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/operateUser";
        $("#form-operateUser-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-operateUser-edit').serialize());
            }
        }
    </script>
</body>
</html>