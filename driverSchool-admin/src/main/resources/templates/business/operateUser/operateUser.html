<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('用户管理（运营）列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>用户名：</label>
                                <input type="text" name="username"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="business:operateUser:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="business:operateUser:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="business:operateUser:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning single disabled" onclick="resetPwd()" shiro:hasPermission="business:operateUser:resetPwd">
                    <i class="fa fa-key"></i> 重置密码
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:operateUser:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:operateUser:remove')}]];
        var organTypeDatas = [[${@dict.getType('user_organ_type')}]];
        var statusDatas = [[${@dict.getType('organ_user_status')}]];
        var prefix = ctx + "business/operateUser";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "用户管理（运营）",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'username',
                    title: '登录用户名'
                },
                {
                    field: 'realName',
                    title: '真实姓名'
                },
                {
                    field: 'tel',
                    title: '联系方式'
                },
                {
                    field: 'organType',
                    title: '账号类型',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(organTypeDatas, value);
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field: 'createdTime',
                    title: '创建时间'
                }
                ]
            };
            $.table.init(options);
        });


        /* 用户管理-重置密码 */
        function resetPwd() {
            table.set();
            var id = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
            if (id.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            var url = prefix + '/resetPwd/' + id;
            $.modal.open("重置密码", url, '800', '300');
        }
    </script>
</body>
</html>