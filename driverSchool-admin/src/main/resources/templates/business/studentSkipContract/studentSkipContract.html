<!DOCTYPE html>
<html
        lang="zh"
        xmlns:th="http://www.thymeleaf.org"
        xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
<head>
    <th:block th:include="include :: header('学员跳过合同签署报名列表')" />
    <th:block th:include="include :: select2-css"/>
    <link rel="shortcut icon" href="favicon.ico" />
    <link
            href="../static/css/bootstrap.min.css"
            th:href="@{/css/bootstrap.min.css}"
            rel="stylesheet"
    />
    <link
            href="../static/css/font-awesome.min.css"
            th:href="@{/css/font-awesome.min.css}"
            rel="stylesheet"
    />
    <link
            href="../static/css/animate.min.css"
            th:href="@{/css/animate.min.css}"
            rel="stylesheet"
    />
    <link
            href="../static/css/style.min.css"
            th:href="@{/css/style.min.css}"
            rel="stylesheet"
    />
</head>
<body class="gray-bg">
<!-- 隐藏的选择框，用于在模态框中使用 -->
<div id="switchSkipContractModalContent" style="display: none;">
    <form id="switchSkipContractForm">
        <div style='text-align: center; margin-top: 20px;'>
            <span style='font-size: 20px; color: #b94a48'>确认要切换跳过合同报名、签署的参数吗？</span>
            <br/><br/>
            <span style='font-size: 15px;'>点击【开启】时，未生成合同或未签署合同的学员能直接进行报名后缴费。点击【关闭】时，按正常流程进行。</span>
            <br/><br/>
        </div>
    </form>

</div>
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list" id="cxSelectSchool">
                    <ul>
                        <li>
                            <label>当前跳过合同签署开关状态：</label>
                            <select disabled name="switchSkipContract" id="switchSkipContractSelect">
                                <!-- 这里会动态填充选项 -->
                            </select>
                        </li>
                        <li>
                            <label>驾校：</label>
                            <select
                                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                                    id="schoolId"
                                    name="schoolId"
                                    class="schoolId form-control m-b "
                                    data-first-title="选择驾校"
                                    required
                            ></select>
                        </li>
                        <li>
                            <label>分校：</label>
                            <select
                                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                                    id="branchId"
                                    name="branchId"
                                    class="branchId form-control m-b"
                                    data-first-title="选择分校"
                            ></select>
                        </li>
                        <li>
                            <label>报名点：</label>
                            <select
                                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                                    id="registrationId"
                                    name="registrationId"
                                    class="registrationId form-control m-b"
                                    data-first-title="所属报名点"
                            ></select>
                        </li>
                        <li>
                            <label>姓名：</label>
                            <input type="text" name="name" placeholder="请输入姓名" />
                        </li>
                        <li>
                            <label>身份证号：</label>
                            <input
                                    type="text"
                                    name="identity"
                                    placeholder="多个身份证号使用空格进行分隔"
                            />
                        </li>
                        <li>
                            <label>手机号：</label>
                            <input type="text" name="mobile" placeholder="请输入手机号" />
                        </li>
                        <li>
                            <label>是否已重签合同：</label>
                            <select
                                    name="isFinishResign"
                                    th:with="type=${@dict.getType('is_sign_contract')}"
                            >
                                <option value="">所有</option>
                                <option
                                        th:each="dict : ${type}"
                                        th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"
                                ></option>
                            </select>
                        </li>
                        <li class="select-time">
                            <label style="width: 100px">触发跳过签署合同时间： </label>
                            <input
                                    type="text"
                                    class="time-input"
                                    id="startTime"
                                    placeholder="开始时间"
                                    name="params[beginTriggerSkipDate]"
                            />
                            <span>-</span>
                            <input
                                    type="text"
                                    class="time-input"
                                    id="endTime"
                                    placeholder="结束时间"
                                    name="params[endTriggerSkipDate]"
                            />
                        </li>

                        <li>
                            <a
                                    class="btn btn-primary btn-rounded btn-sm"
                                    onclick="$.table.search()"
                            ><i class="fa fa-search"></i>&nbsp;搜索</a
                            >
                            <a
                                    id="reset"
                                    class="btn btn-warning btn-rounded btn-sm"
                                    onclick="resetSearchForm()"
                            ><i class="fa fa-refresh"></i>&nbsp;重置</a
                            >
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <!--            <a-->
            <!--                    class="btn btn-primary single disabled"-->
            <!--                    onclick="editTab()"-->
            <!--                    shiro:hasPermission="business:studentSkipContract:edit"-->
            <!--            >-->
            <!--                <i class="fa fa-edit"></i> 修改-->
            <!--            </a>-->
            <!--            <a-->
            <!--                    class="btn btn-primary single disabled"-->
            <!--                    onclick="view('',1300,'')"-->
            <!--                    shiro:hasPermission="business:studentSkipContract:view"-->
            <!--            >-->
            <!--                查看-->
            <!--            </a>-->

            <a
                    class="btn btn-danger multiple disabled"
                    onclick="$.operate.removeAll()"
                    shiro:hasPermission="business:studentSkipContract:remove"
            >
                <i class="fa fa-remove"></i> 删除
            </a>
            <!--            <a-->
            <!--                    class="btn btn-info"-->
            <!--                    onclick="exportSelected()"-->
            <!--                    shiro:hasPermission="business:studentSkipContract:export"-->
            <!--            >-->
            <!--                <i class="fa fa-download"></i> 导出-->
            <!--            </a>-->
            <a
                    class="btn btn-info"
                    onclick="sendReSignNotice()"
                    shiro:hasPermission="business:studentSkipContract:sendNotice"
            >
                发送补签提醒
            </a>
            <a
                    class="btn btn-danger"
                    onclick="switchSkipContract()"
                    shiro:hasPermission="business:studentSkipContract:switch"
            >
                跳过合同生成签署开关
            </a>
            <a
                    class="btn btn-success single disabled"
                    onclick="viewContract()"
                    shiro:hasPermission="business:student:viewContract"
            >
                查看签署合同
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: select2-js"/>
<th:block th:include="include :: jquery-cxselect-js" />
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('business:student:edit')}]];
    var removeFlag = [[${@permission.hasPermi('business:student:remove')}]];
    var isSignContractDatas = [[${@dict.getType('is_sign_contract')}]];
    var prefix = ctx + "business/studentSkipContract";
    var importResignUrl= ctx +"business/resign-contract/importData";
    //跳过合同签署开关
    const switchSkipContractDatas = [
        { dictValue: '1', dictLabel: '关闭' },
        { dictValue: '2', dictLabel: '开启' }
    ];
    $(function() {
        $("#schoolId").select2({

        });
        // 假设这是跳过合同签署开关状态的字典数据
        const switchSkipContractDatas = [
            { dictValue: '1', dictLabel: '关闭' },
            { dictValue: '2', dictLabel: '开启' }
        ];

        // 获取 select 元素
        const selectElement = $('#switchSkipContractSelect');

        // 动态填充 option 选项
        switchSkipContractDatas.forEach(data => {
            const option = $('<option></option>');
            option.val(data.dictValue);
            option.text(data.dictLabel);
            selectElement.append(option);
        });

        // 假设这里有一个函数来获取当前状态的值
        function getCurrentSwitchSkipContractValue(callback) {
            var queryParam = 3; //为0则是查询当前参数
            $.ajax({
                url: prefix + "/switchOrQuery",
                type: "post",
                contentType: 'application/json',
                data: JSON.stringify(queryParam), // 使用 JSON.stringify 确保参数正确传递
                success: function(response) {
                    if(response.code===0){
                        callback(response.data.param); // 使用回调函数返回结果
                    }
                },
                error: function(error) {
                    console.error('请求出错:', error);
                }
            });
        }

// 设置选中的值
        getCurrentSwitchSkipContractValue(function(currentValue) {
            selectElement.val(currentValue);
            // 如果 currentValue 为 2，修改选中选项的样式
            if (currentValue === '2') {
                //2为开启
                selectElement.find('option:selected').css({
                    'color': 'red',
                    'font-weight': 'bold'
                });
            }
        });


        var options = {
            id: "bootstrap-table",
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            detailUrl: prefix + "/view/{id}",
            modalName: "学员",
            showPageGo: true,
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'schoolId',
                    title: '驾校',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.school != null) {
                                return row.school.name;
                            }
                        }
                    }
                },
                {
                    field: 'branchId',
                    title: '分校',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.branch != null) {
                                return row.branch.name;
                            }
                        }
                    }
                },
                {
                    field: 'registrationId',
                    title: '报名点',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            if (row.registration != null) {
                                return row.registration.name;
                            }
                        }
                    }
                },
                {
                    field: 'name',
                    title: '学员姓名'
                },
                {
                    field: 'identity',
                    title: '身份证号',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            return basecclusion(value,6,4);
                        }
                    }
                },
                {
                    field: 'mobile',
                    title: '手机号',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            return basecclusion(value,3,4);
                        }
                    }
                },
                {
                    field: 'triggerSkipDate',
                    title: '触发跳过合同签署时间',
                },
                {
                    field: 'recentNoticeDate',
                    title: '最近提醒重签时间',
                },
                {
                    field: 'sendNoticeCount',
                    title: '提醒重签次数',
                },
                {
                    field: 'newContractPath',
                    title: '新合同地址',
                    formatter: function(value, row, index) {
                        if(value==null || value===''){
                            value =0; //为空则是未生成
                        }else{
                            value =1;
                        }
                        return $.table.selectDictLabel(isSignContractDatas, value);
                    }
                },
                {
                    field: 'isFinishResign',
                    title: '是否完成合同重签',
                    formatter: function(value, row, index) {
                        if(value==null || value===''){
                            value =0; //为空则是未签署
                        }
                        return $.table.selectDictLabel(isSignContractDatas, value);
                    }
                },
            ]
        };
        $.table.init(options);


        //加载驾校、分校
        $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
        $('#cxSelectSchool').cxSelect({
            selects: ['schoolId', 'branchId','registrationId'],
            jsonValue: 'v',
        });

        /** 初始化时间组件 */
        layui.use('laydate', function(){
            var laydate = layui.laydate;
            laydate.render({
                elem: '#beginPrepareRegisteDate',
                type: 'datetime',
                trigger: 'click'
            });
            laydate.render({
                elem: '#endPrepareRegisteDate',
                type: 'datetime',
                trigger: 'click'
            });
        });
    });
    function importResignData(){
        table.options.useDefaultimportUrl = true;
        $.table.importExcel('','','')
    }
    //重置搜索条件
    function resetSearchForm(){
        // 1. 先移除验证
        $('#formId').validate().resetForm();

        // 2. 重置表单
        $('#formId')[0].reset();

        // 3. 重置 select2 字段
        $("#schoolId").val('').trigger('change');
        $("#branchId").empty().trigger('change');
        $("#registrationId").empty().trigger('change');

        // 4. 清除错误样式
        $("#schoolId, #branchId, #registrationId").removeClass('error');
        $("label.error[for='schoolId'], label.error[for='branchId'], label.error[for='registrationId']").remove();

        // 5. 重新触发表格搜索
        $.table.search();
    }

    function editTab() {
        var rows = $.table.selectRows();
        if (rows.length < 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }else if (rows.length == 1) {

            var data = {
                "id": rows[0].id
            }

            var code;

            $.ajax({
                url: prefix + "/checkStudyCenterIsSyn",
                type: "post",
                processData: false,
                contentType: 'application/json',
                async: false,
                data: JSON.stringify(data),
                success: function(response) {
                    if (response.code == 500) {
                        code = response.code;
                        $.modal.alertWarning(response.msg);
                    }
                }
            })

            if (code == 500) {
                event.stopPropagation();
                return false;
            }

            if (rows[0].studyCenterIsSyn == 0) {
                $.operate.editTab();
            }else{
                superviseTab(rows[0].id);
            }
        }
    }
    /* 切换跳过合同缴费参数*/
    function switchSkipContract() {
        // 获取当前选中的值
        var currentValue = $('#switchSkipContractSelect').val();
        let html = $("#switchSkipContractModalContent").html();
        layer.open({
            title: '切换跳过合同签署参数',
            content: html,
            btn: ['开启','关闭', '取消'],
            btn1: function (index, layero) {
                // 通过 id 选取模态框里的 select 元素
                var setParam =2
                $.ajax({
                    url: ctx + "business/studentSkipContract/switchOrQuery",
                    type: "post",
                    contentType: 'application/json',
                    data: JSON.stringify(setParam),
                    success: function (response) {
                        if (response.code === 0) {
                            $.modal.alertSuccess(response.msg);
                            refreshTab();
                        } else {
                            $.modal.alertWarning(response.msg);
                        }
                    }
                });
            },btn2: function (index, layero) {
                // 通过 id 选取模态框里的 select 元素
                var setParam =1
                $.ajax({
                    url: ctx + "business/studentSkipContract/switchOrQuery",
                    type: "post",
                    contentType: 'application/json',
                    data: JSON.stringify(setParam),
                    success: function (response) {
                        if (response.code === 0) {
                            $.modal.alertSuccess(response.msg);
                            refreshTab();
                        } else {
                            $.modal.alertWarning(response.msg);
                        }
                    }
                });
            },btn3: function (index, layero) {
               //关闭弹窗
                layer.close(index);
            },success: function (layero, index) {
                $(layero).find('.layui-layer-btn0').css({
                    'background-color': '#D2691E',//按钮背景
                    'color': '#000' // 按钮文字颜色
                });
                $(layero).find('.layui-layer-btn1').css({
                    'background-color': '#90EE90',
                    'color': '#000' // 按钮文字颜色
                });
            }
        });
    }

    /* 发送重签提醒 */
    function sendReSignNotice() {
        var rows = $.table.selectRows();
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var ids = [];
        var hasFinishedResign = false;

        // 遍历选中的记录
        $.each(rows, function(i, item) {
            if (item.isFinishResign === 1) {
                hasFinishedResign = true;
                $.modal.alertWarning("学员 " + item.name + " 已完成合同重签，无需发送消息进行提醒。");
            } else {
                ids.push(item.id); // 将未完成重签的 ID 加入数组
            }
        });

        // 如果所有记录都已完成重签，直接返回
        if (ids.length === 0) {
            return;
        }

        // 发送 AJAX 请求
        var param = { ids: ids.join(",") };
        $.modal.confirm("确认要给"+ids.length+"位学员发送重签合同提醒吗？",function (){
            $.ajax({
                url: prefix + "/notice",
                data: param,
                type: "post",
                dataType: 'json',
                success: function (response) {
                    if(response.data){
                        $.modal.alertSuccess(response.data.msg);
                    }
                }
            })
        })
    }

    function view(id,width, height) {
        table.set();
        var _url = $.operate.detailUrl(id);
        var options = {
            title: table.options.modalName + "详细",
            width: width,
            height: height,
            url: _url,
            skin: 'layui-layer-gray',
            btn: ['关闭'],
            yes: function (index, layero) {
                $.modal.close(index);
            }
        };
        $.modal.openOptions(options);
    }



    // 导出数据
    function exportSelected() {

    }

    /* 查看学员详情 */
    $('#bootstrap-table').on('dbl-click-row.bs.table', function (e, row, $element) {
        table.set();
        var _url = $.operate.detailUrl(row.id);
        var options = {
            title: table.options.modalName + "详细",
            width: "1300",
            height: "",
            url: _url,
            skin: 'layui-layer-gray',
            btn: ['关闭'],
            yes: function (index, layero) {
                $.modal.close(index);
            }
        };
        $.modal.openOptions(options);
    });

    /* 查看签署合同 */
    function viewContract() {
        var rows = $.table.selectRows();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        // 检查是否签署了合同
        var params = 'id='+rows[0].studentId;
        var config = {
            url: ctx + "business/student/checkContract",
            type: "post",
            dataType: "json",
            data: params,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
            },
            success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    var _url = ctx + "business/student/viewContract/" + rows[0].studentId;
                    var options = {
                        title: "查看签署合同",
                        width: "1200",
                        height: "900",
                        url: _url,
                        skin: 'layui-layer-gray',
                        btn: ['关闭'],
                        yes: function (index, layero) {
                            $.modal.close(index);
                        }
                    };
                    $.modal.openOptions(options);
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            },
        };
        $.ajax(config);
    }


    function grStudentExport() {

    }
</script>

<script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“zip”格式的压缩文件！
            </font>
        </div>
    </form>
</script>
</body>
</html>
