<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('结算凭证列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:src="@{/js/view.img.js}"></script>
    <script th:inline="javascript">
      var prefix = ctx + "business/calculateFlow";

      $(function () {
        var options = {
          url: prefix + "/list",
          createUrl: prefix + "/add",
          updateUrl: prefix + "/edit/{id}",
          removeUrl: prefix + "/remove",
          exportUrl: prefix + "/export",
          modalName: "结算凭证",
          columns: [
            {
              field: "id",
              title: "主键",
              visible: false,
            },
            {
              field: "",
              title: "统计周期",
              formatter: function (value, row, index) {
                return row.beginCountTime + " 至 " + row.endCountTime;
              },
            },
            {
              field: "calculateBatchNo",
              title: "结算批次",
            },
            {
              field: "calculateProofImage",
              title: "结算凭证",
              formatter: function (value, row, index) {
                if (value != null) {
                  var tag = $.common.sprintf(
                    "<img id='img' class='img-circle img-xs' data-height='%s' data-width='%s' data-target='%s' src='%s'/>",
                    300,
                    800,
                    null,
                    value
                  );
                  var actions = [];
                  actions.push(
                    '<a href="javascript:void(0)" onclick="viewImage(\'' +
                      value +
                      "')\">" +
                      tag +
                      "</a>"
                  );
                  return actions.join("");
                } else {
                  return "-";
                }
              },
            },
            {
              title: "操作",
              align: "center",
              align: "left",
              formatter: function (value, row, index) {
                var actions = [];
                actions.push(
                  '<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="remove(\'' +
                    row.id +
                    '\')"><i class="fa fa-remove"></i> 删除</a> '
                );
                return actions.join("");
              },
            },
          ],
        };
        $.table.init(options);
      });

      function remove(id) {
        var tipMsg = "确定要删除这条结算凭证吗？";
        $.modal.confirm(tipMsg, function () {
          var params = "id=" + id;
          var config = {
            url: prefix + "/remove",
            type: "post",
            dataType: "json",
            data: params,
            beforeSend: function () {
              $.modal.loading("正在处理中，请稍候...");
            },
            success: function (result) {
              if (result.code == web_status.SUCCESS) {
                $.modal.alertSuccess(result.msg);
                $.table.refresh();
              } else {
                $.modal.alertError(result.msg);
              }
              $.modal.closeLoading();
            },
          };
          $.ajax(config);
        });
      }
    </script>
  </body>
</html>
