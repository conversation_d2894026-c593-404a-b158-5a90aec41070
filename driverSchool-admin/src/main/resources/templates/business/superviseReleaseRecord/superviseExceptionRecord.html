<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('资金异常记录列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>

                <li>
                  <label>分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="branchId"
                    name="branchId"
                    class="branchId form-control m-b"
                    data-first-title="选择分校"
                    required
                  ></select>
                </li>

                <li>
                  <label>门店：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="registrationId"
                    name="registrationId"
                    class="registrationId form-control m-b"
                    data-first-title="选择门店"
                    required
                  ></select>
                </li>

                <li>
                  <label>姓名：</label>
                  <input
                    type="text"
                    name="studentName"
                    id="name"
                    placeholder="请输入学员姓名"
                  />
                </li>
                <li>
                  <label>身份证号：</label>
                  <input
                    type="text"
                    name="identity"
                    id="identity"
                    placeholder="身份证6位 多个用空格隔开"
                  />
                </li>

                <li class="select-time">
                  <label style="width: 100px">交易时间： </label>
                  <input
                          style="width: 135px"
                          type="text"
                          class="layui-input"
                          id="beginTime"
                          placeholder="开始时间"
                          name="beginTime"
                  />
                  <span>-</span>
                  <input
                          style="width: 135px"
                          type="text"
                          class="layui-input"
                          id="endTime"
                          placeholder="结束时间"
                          name="endTime"
                  />
                </li>

                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <!-- <a class="btn btn-success single disabled" onclick="view('','1000','')" shiro:hasPermission="business:superviseReleaseRecord:view">
                    查看
                </a> -->
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:superviseReleaseRecord:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
          var isSuccessDatas = [[${@dict.getType('supervise_is_success')}]];
          var isPassDatas = [[${@dict.getType('subject_is_pass')}]];
          var prefix = ctx + "business/superviseReleaseRecord";

          $(function() {
              var options = {
                  url: prefix + "/superviseExceptionRecordList",
                  detailUrl: prefix + "/viewException/{id}",
                  exportUrl: prefix + "/export",
                  modalName: "资金异常记录",
                  columns: [{
                      checkbox: true
                  },
                  {
                      field: 'id',
                      title: '',
                      visible: false
                  },
                  {
                      field: 'schoolId',
                      title: '驾校',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              if (row.school != null) {
                                  return row.school.name;
                              }
                          }
                      }
                  },

                  {
                      field: 'branchId',
                      title: '分校',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              if (row.branch != null) {
                                  return row.branch.name;
                              }
                          }
                      }
                  },

                  {
                      field: 'registrationId',
                      title: '报名点',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              if (row.registration != null) {
                                  return row.registration.name;
                              }
                          }
                      }
                  },

                      {
                          field: 'studentId',
                          title: '学员',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if (row.schoolStudent != null) {
                                      return row.schoolStudent.name;
                                  }
                              }
                          }
                      },

                      {
                          field: 'studentId',
                          title: '身份证号',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if (row.schoolStudent != null) {
                                      return basecclusion(row.schoolStudent.identity,6,4);
                                  }
                              }
                          }
                      },

                      // {
                      //     field: 'superviseDate',
                      //     title: '监管金额到帐时间',
                      // },

                      {
                          field: 'studentId',
                          title: '监管金额',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if (row.schoolStudent != null) {
                                      return row.schoolStudent.superviseFee;
                                  }
                              }
                          }
                      },

                      {
                          field: 'releaseFee',
                          title: '释放金额'
                      },

                      {
                          field: 'releaseDate',
                          title: '释放金额时间',
                      },

                      // {
                      //     field: 'releaseDay',
                      //     title: '释放间隔天',
                      // },
                      // {
                      //     field: 'day',
                      //     title: '释放间隔天阀值',
                      // },

                      {
                          field: 'schoolId',
                          title: '释放账号',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if (row.school != null) {
                                      return row.school.payAcctNo;
                                  }
                              }
                          }
                      },
                      {
                          field: 'schoolId',
                          title: '释放账户',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if (row.school != null) {
                                      return row.school.bankAcctName;
                                  }
                              }
                          }
                      },
                      {
                          field: 'schoolId',
                          title: '关联银行',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if (row.school != null) {
                                      return row.school.bankName;
                                  }
                              }
                          }
                      },
                      {
                          field: 'exceptionReason',
                          title: '异常原因',
                      }
                  ]
              };
              $.table.init(options);

              //加载驾校、分校
              $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
              $('#cxSelectSchool').cxSelect({
                  selects: ['schoolId', 'branchId','registrationId'],
                  jsonValue: 'v',
              });

          });



          function view(id,width, height) {
              table.set();
              var rows = $.table.selectRows();
              if (rows.length == 0) {
                  $.modal.alertWarning("请至少选择一条记录");
                  return;
              }
              var _url = $.operate.detailUrl(rows[0].releaseRecordId);
              var options = {
                  title: table.options.modalName + "详细",
                  width: width,
                  height: height,
                  url: _url,
                  skin: 'layui-layer-gray',
                  btn: ['关闭'],
                  yes: function (index, layero) {
                      $.modal.close(index);
                  }
              };
              $.modal.openOptions(options);
          }

         /* 导出资金释放异常列表 */
      function exportSelected() {
            var params = $("#formId").serialize();
            var ids = $.table.selectColumns("id");
            var tipMsg = "确定导出所有数据吗？";
            if($.common.isNotEmpty(ids)){
                tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
                params += "&ids="+ids;
            }
            $.modal.confirm(tipMsg, function() {
                var config = {
                  url: prefix + "/exportExceptionRecord",
                  type: "post",
                  dataType: "json",
                  data: params,
                  beforeSend: function () {
                    $.modal.loading("正在处理中，请稍候...");
                  },
                  success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                  },
                };
                $.ajax(config);
            });
          }
    </script>
  </body>
</html>
