<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('释放记录列表')" />
    <th:block th:include="include :: select2-css"/>
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                          style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                          id="schoolId"
                          name="schoolId"
                          class="schoolId form-control m-b "
                          data-first-title="选择驾校"
                          required
                  ></select>
                </li>
                <li>
                  <label>分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="branchId"
                    name="branchId"
                    class="branchId form-control m-b"
                    data-first-title="选择分校"
                    required
                  ></select>
                </li>

                <li>
                  <label>门店：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="registrationId"
                    name="registrationId"
                    class="registrationId form-control m-b"
                    data-first-title="选择门店"
                    required
                  ></select>
                </li>
                <li>
                  <label>姓名：</label>
                  <input
                    type="text"
                    name="studentName"
                    placeholder="请输入学员姓名"
                  />
                </li>
                <li>
                  <label>身份证号：</label>
                  <input type="text" name="identity" placeholder="身份证6位 多个空格隔开" />
                </li>
                <li class="select-time">
                  <label>资金监管日期： </label>
                  <input
                          type="text"
                          class="time-input"
                          id="startSuperviseTime"
                          placeholder="开始时间"
                          name="params[startSuperviseTime]"
                  />
                  <span>-</span>
                  <input
                          type="text"
                          class="time-input"
                          id="endSuperviseTime"
                          placeholder="结束时间"
                          name="params[endSuperviseTime]"
                  />
                </li>



                <li>
                  <label>科目：</label>
                  <select name="subjectName">
                    <option value="">所有</option>
                    <option value="科目2">科目2</option>
                    <option value="科目3">科目3</option>
                    <option value="退学">退学</option>
                    <option value="其他">其他</option>
                  </select>
                </li>

                <li class="select-time">
                  <label>释放时间： </label>
                  <input
                    type="text"
                    class="time-input"
                    id="startTime"
                    placeholder="开始时间"
                    name="beginTime"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    class="time-input"
                    id="endTime"
                    placeholder="结束时间"
                    name="endTime"
                  />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="resetSearchForm()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-success single disabled"
            onclick="view('','1000','')"
            shiro:hasPermission="business:superviseReleaseRecord:view"
          >
            查看
          </a>
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:superviseReleaseRecord:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js"/>
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:superviseReleaseRecord:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:superviseReleaseRecord:remove')}]];
        var isSuccessDatas = [[${@dict.getType('supervise_is_success')}]];
        var isPassDatas = [[${@dict.getType('subject_is_pass')}]];
        var prefix = ctx + "business/superviseReleaseRecord";

        $(function() {
          $("#schoolId").select2({

          });
            var options = {
                url: prefix + "/list?isSuccess=1",
                detailUrl: prefix + "/view/{id}",
                exportUrl: prefix + "/export",
                showFooter: true,
                footerStyle: footerStyle,
                modalName: "释放记录",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                    {
                        field: 'schoolId',
                        title: '驾校',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.school != null) {
                                    return row.school.name;
                                }
                            }
                        }
                    },
                    {
                      field: 'branchId',
                      title: '分校',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              if (row.branch != null) {
                                  return row.branch.name;
                              }
                          }
                      }
                  },

                  {
                      field: 'registrationId',
                      title: '报名点',
                      formatter: function (value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              if (row.registration != null) {
                                  return row.registration.name;
                              }
                          }
                      }
                  },
                    {
                        field: 'studentId',
                        title: '学员',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.schoolStudent != null) {
                                    return row.schoolStudent.name;
                                }
                            }
                        }
                    },
                    {
                        field: 'studentId',
                        title: '身份证号',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.schoolStudent != null) {

                                    return basecclusion(row.schoolStudent.identity,6,4);

                                }
                            }
                        }
                    },
                    {
                        field: 'superviseFee',
                        title: '监管金额'
                    },
                  {
                    field: 'studentId',
                    title: '监管日期',
                    formatter: function (value, row, index) {
                      if ($.common.isNotEmpty(value)) {
                        if (row.schoolStudent != null) {
                          return basecclusion(row.schoolStudent.superviseDate);

                        }
                      }
                    }
                  },
                    {
                        field: 'releaseFee',
                        title: '释放金额',
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                sumBalance += parseFloat(value[i].releaseFee);
                            }
                            return "释放金额分页合计：" + sumBalance;
                        }
                    },
                    {
                        field: 'releaseDate',
                        title: '释放时间'
                    },
                    {
                        field: 'payAcctNo',
                        title: '释放账号'
                    },
                    {
                        field: 'bankAcctName',
                        title: '释放账户名'
                    },
                    {
                        field: 'bankName',
                        title: '关联银行'
                    },
                    {
                        field: 'subjectName',
                        title: '归属科目'

                    }

                ]
            };
            $.table.init(options);

            //加载驾校、分校
            $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
            $('#cxSelectSchool').cxSelect({
                selects: ['schoolId', 'branchId','registrationId'],
                jsonValue: 'v',
            });

        });

        function footerStyle(column) {
            return {
                releaseFee: {
                    css: { color: 'red', 'font-weight': 'normal' }
                }
            }[column.field]
        }


        function view(id,width, height) {
            table.set();
            var _url = $.operate.detailUrl(id);
            var options = {
                title: table.options.modalName + "详细",
                width: width,
                height: height,
                url: _url,
                skin: 'layui-layer-gray',
                btn: ['关闭'],
                yes: function (index, layero) {
                    $.modal.close(index);
                }
            };
            $.modal.openOptions(options);
        }

        /* 查看资金释放记录详情 */
      $('#bootstrap-table').on('dbl-click-row.bs.table', function (e, row, $element) {
        table.set();
          var _url = $.operate.detailUrl(row.id);
          var options = {
              title: table.options.modalName + "详细",
              width: "1000",
              height: "",
              url: _url,
              skin: 'layui-layer-gray',
              btn: ['关闭'],
              yes: function (index, layero) {
                  $.modal.close(index);
              }
          };
          $.modal.openOptions(options);
      });

        //重置搜索条件
        function resetSearchForm(){
          // 1. 先移除验证
          $('#formId').validate().resetForm();

          // 2. 重置表单
          $('#formId')[0].reset();

          // 3. 重置 select2 字段
          $("#schoolId").val('').trigger('change');
          $("#branchId").empty().trigger('change');
          $("#registrationId").empty().trigger('change');

          // 4. 清除错误样式
          $("#schoolId, #branchId, #registrationId").removeClass('error');
          $("label.error[for='schoolId'], label.error[for='branchId'], label.error[for='registrationId']").remove();

          // 5. 重新触发表格搜索
          $.table.search();
        }

      /* 导出资金释放记录 */
      function exportSelected() {
        var params = $("#formId").serialize();
        var ids = $.table.selectColumns("id");
        var tipMsg = "确定导出所有数据吗？";
        if($.common.isNotEmpty(ids)){
            tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
            params += "&ids="+ids;
        }
        $.modal.confirm(tipMsg, function() {
            var config = {
              url: prefix + "/export",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
        });
      }
    </script>
  </body>
</html>
