<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('查看释放详情')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-teacher-edit" th:object="${superviseReleaseRecord}">
            <h4 class="form-header h4">基本信息</h4>

            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">学员姓名：</label>
                        <div class="form-control-static"  th:text="*{schoolStudent.name}"></div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">释放金额：</label>
                        <div class="form-control-static"  th:text="*{releaseFee}"></div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">释放账号：</label>
                        <div class="form-control-static" th:text="*{payAcctNo}"></div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">关联银行：</label>
                        <div class="form-control-static" th:text="*{bankName}"></div>
                    </div>


                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">身份证号：</label>
                        <div class="form-control-static"  th:text="*{schoolStudent.identity}"></div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">释放时间：</label>
                        <div class="form-control-static"  th:text="*{#dates.format(releaseDate, 'yyyy-MM-dd HH:mm:ss')}"></div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">释放账户：</label>
                        <div class="form-control-static"  th:text="*{bankAcctName}"></div>
                    </div>

                </div>

            </div>

            <h4 class="form-header h4">达标情况</h4>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">所学课目：</label>
                        <div class="form-control-static"  th:text="*{subjectName}"></div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">是否达标：</label>
                        <div class="form-control-static"  th:text="已达标"></div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="col-sm-5 control-label">省平台达标时间：</label>
                        <div class="form-control-static"  th:text="*{#dates.format(pushTime, 'yyyy-MM-dd HH:mm:ss')}"></div>
                    </div>
                    <!-- <div class="form-group">
                        <label class="col-sm-3 control-label">有效学时：</label>
                        <div class="form-control-static"  th:text="${checkValidStudyTimeStr}"></div>
                    </div> -->
                    <!-- <div class="form-group">
                        <label class="col-sm-3 control-label">法定学时：</label>
                        <div class="form-control-static"  th:text="${examineStudyTimeStr}"></div>
                    </div> -->
                </div>


                <!-- <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>学时数据</h5>
                        <div class="ibox-tools">
                        </div>
                    </div>
                    <div class="ibox-content">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th>学员姓名</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>是否审核</th>
                                <th>审核时长</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="studyTime : ${schoolStudentStudyTimeList}">
                                <td th:text="${studyTime.schoolStudent.name}">张三</td>
                                <td th:text="${#dates.format(studyTime.beginTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                <td th:text="${#dates.format(studyTime.endTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                <td th:text="${studyTime.checkStatus == 1 || studyTime.checkStatus == 2} ? |否|:|是|"></td>
                                <td th:text="${studyTime.checkValidStudyTimeStr}">23</td>
                            </tr>
                            </tbody>
                        </table>

                    </div>
                </div> -->


                <!-- <span class="help-block m-b-none">
                    <i class="fa fa-info-circle" style="color: red;"></i> 详细学时数据请在学时数据菜单中搜索查看
                </span> -->

            </div>


        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
    </script>
</body>
</html>