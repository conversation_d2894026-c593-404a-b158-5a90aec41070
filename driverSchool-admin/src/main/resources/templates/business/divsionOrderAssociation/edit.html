<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改分账总订单')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-divsionOrderAssociation-edit" th:object="${divsionOrderAssociation}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">：</label>
                <div class="col-sm-8">
                    <input name="orderId" th:field="*{orderId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">订单号：</label>
                <div class="col-sm-8">
                    <input name="orderNo" th:field="*{orderNo}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">订单时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="orderTime" th:value="${#dates.format(divsionOrderAssociation.orderTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">学校ID：</label>
                <div class="col-sm-8">
                    <input name="schoolId" th:field="*{schoolId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">学校名称：</label>
                <div class="col-sm-8">
                    <input name="schoolName" th:field="*{schoolName}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">分校ID：</label>
                <div class="col-sm-8">
                    <input name="branchId" th:field="*{branchId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">分校名字：</label>
                <div class="col-sm-8">
                    <input name="branchName" th:field="*{branchName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">门店ID：</label>
                <div class="col-sm-8">
                    <input name="registrationId" th:field="*{registrationId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">门店名称：</label>
                <div class="col-sm-8">
                    <input name="registrationName" th:field="*{registrationName}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">学生ID：</label>
                <div class="col-sm-8">
                    <input name="studentId" th:field="*{studentId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">学生报名时姓名：</label>
                <div class="col-sm-8">
                    <input name="studentName" th:field="*{studentName}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">学生报名时电话：</label>
                <div class="col-sm-8">
                    <input name="studentMobile" th:field="*{studentMobile}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">学生报名时身份证号：</label>
                <div class="col-sm-8">
                    <input name="studentIdcard" th:field="*{studentIdcard}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">银行单号：</label>
                <div class="col-sm-8">
                    <input name="bankOrderNo" th:field="*{bankOrderNo}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">平台费：</label>
                <div class="col-sm-8">
                    <input name="associationFee" th:field="*{associationFee}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">清算时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="checkTime" th:value="${#dates.format(divsionOrderAssociation.checkTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="createdTime" th:value="${#dates.format(divsionOrderAssociation.createdTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="updatedTime" th:value="${#dates.format(divsionOrderAssociation.updatedTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/divsionOrderAssociation";
        $("#form-divsionOrderAssociation-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-divsionOrderAssociation-edit').serialize());
            }
        }

        $("input[name='orderTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='checkTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='createdTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='updatedTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>