<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增机构用户')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-organUser-add">


            <th:block th:if="${sysOrganType == 3}">
                <input name="schoolId" th:field="${sysOrganUse.schoolId}" type="hidden">
                <input name="branchId" th:field="${sysOrganUse.branchId}" type="hidden">
                <input name="registrationId" th:field="${sysOrganUse.registrationId}" type="hidden">
            </th:block>

            <div class="form-group">
                <label class="col-sm-3 control-label is-required">登录用户名：</label>
                <div class="col-sm-8">
                    <input id="username" name="username" class="form-control" type="text" required>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">密码：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="password" name="password" id="password" required>
                    <th:block th:with="chrtype=${@config.getKey('sys.account.chrtype')}">
                        <th:block th:if="${chrtype != '0'}">
                            <span class="help-block m-b-none">
                                <th:block th:if="${chrtype == '1'}"><i class="fa fa-info-circle" style="color: red;"></i>  密码只能为0-9数字 </th:block>
                                <th:block th:if="${chrtype == '2'}"><i class="fa fa-info-circle" style="color: red;"></i>  密码只能为a-z和A-Z字母</th:block>
                                <th:block th:if="${chrtype == '3'}"><i class="fa fa-info-circle" style="color: red;"></i>  密码必须包含（字母，数字）</th:block>
                                <th:block th:if="${chrtype == '4'}"><i class="fa fa-info-circle" style="color: red;"></i>  密码必须包含（字母，数字，特殊字符!@#$%^&*()-=_+）</th:block>
                            </span>
                        </th:block>
                    </th:block>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">再次确认：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="password" name="confirmPassword" id="confirmPassword">
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请再次输入您的密码</span>
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">真实姓名：</label>
                <div class="col-sm-8">
                    <input name="realName" class="form-control" type="text" required placeholder="不强制要求真实姓名，可填写登录用户名">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">联系方式：</label>
                <div class="col-sm-8">
                    <input name="tel" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group" th:if="${sysOrganType == 7}">
                <label class="col-sm-3 control-label is-required">角色：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('organ_user_role')}">
                        <input type="radio" th:id="${'organType_' + dict.dictCode}" name="organType" th:value="${dict.dictValue}" th:checked="${dict.default}" required>
                        <label th:for="${'organType_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>

            <th:block th:if="${sysOrganType == 1}">
            <div class="form-group" >
                <label class="col-sm-3 control-label is-required">角色：</label>
                <div class="col-sm-8">
                    <label class="radio-box"> <input type="radio" name="organType" value="1" th:checked="true" /> 驾校方 </label>
                    <label class="radio-box"> <input type="radio" name="organType" value="2"/> 分校方 </label>
                    <label class="radio-box"> <input type="radio" name="organType" value="3"/> 报名点方 </label>
                </div>
            </div>
            </th:block>

            <div class="form-group"  th:if="${sysOrganType == 2}">
                <label class="col-sm-3 control-label is-required">角色：</label>
                <div class="col-sm-8">
                    <label class="radio-box"> <input type="radio" name="organType" value="2"  th:checked="true" /> 分校方 </label>
                    <label class="radio-box"> <input type="radio" name="organType" value="3"/> 报名点方 </label>
                </div>
            </div>

            <div class="form-group"  th:if="${sysOrganType == 3}">
                <label class="col-sm-3 control-label is-required">角色：</label>
                <div class="col-sm-8">
                    <label class="radio-box"> <input type="radio" name="organType" value="3"  th:checked="true" /> 报名点方 </label>
                </div>
            </div>



            <div class="form-group"  th:if="${sysOrganType == 4}">
                <label class="col-sm-3 control-label is-required">角色：</label>
                <div class="col-sm-8">
                    <label class="radio-box"> <input type="radio" name="organType" value="4" th:checked="true" /> 移动方 </label>
                </div>
            </div>


            <div class="form-group"  th:if="${sysOrganType == 5}">
                <label class="col-sm-3 control-label is-required">角色：</label>
                <div class="col-sm-8">
                    <label class="radio-box"> <input type="radio" name="organType" value="5" th:checked="true" /> 监管方 </label>
                </div>
            </div>


            <div id="cxSelectSchool" class="form-group" th:if="${sysOrganType == 7}">
                <label class="col-sm-3 control-label">所属学校：</label>
                <div class="col-sm-3">
                    <select id="schoolId" name="schoolId" class="schoolId form-control m-b" data-first-title="所属驾校" required>
                    </select>
                </div>
                <div class="col-sm-3" id="branchDiv">
                    <select id="branchId" name="branchId" class="branchId form-control m-b" data-first-title="所属分校">
                    </select>
                </div>
                <div class="col-sm-3" id="registrationDiv">
                    <select id="registrationId" name="registrationId" class="registrationId form-control m-b" data-first-title="所属报名点">
                    </select>
                </div>
            </div>



            <div id="cxSelectSchool" class="form-group" th:if="${sysOrganType == 1}">
                <label class="col-sm-3 control-label is-required">所属学校：</label>
                <div class="col-sm-3">
                    <select id="schoolId" name="schoolId" class="schoolId form-control m-b" data-first-title="所属驾校" required>
                    </select>
                </div>
                <div class="col-sm-3" id="branchDiv">
                    <select id="branchId" name="branchId" class="branchId form-control m-b" data-first-title="所属分校">
                    </select>
                </div>
                <div class="col-sm-3" id="registrationDiv">
                    <select id="registrationId" name="registrationId" class="registrationId form-control m-b" data-first-title="所属报名点">
                    </select>
                </div>
            </div>


            <div id="cxSelectSchool" class="form-group" th:if="${sysOrganType == 2}">
                <label class="col-sm-3 control-label is-required">所属学校：</label>
                <div class="col-sm-3">
                    <select id="schoolId" name="schoolId" class="schoolId form-control m-b" data-first-title="所属驾校" required>
                    </select>
                </div>
                <div class="col-sm-3" id="branchDiv">
                    <select id="branchId" name="branchId" class="branchId form-control m-b" data-first-title="所属分校" required>
                    </select>
                </div>
                <div class="col-sm-3" id="registrationDiv">
                    <select id="registrationId" name="registrationId" class="registrationId form-control m-b" data-first-title="所属报名点">
                    </select>
                </div>
            </div>


            <div class="form-group">
                <label class="col-sm-3 control-label is-required">是否启用：</label>
                <div class="col-sm-8">
                    <select name="status" class="form-control m-b" th:with="type=${@dict.getType('organ_user_status')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/organUser"
        $("#form-organUser-add").validate({
            rules:{
                username:{
                    minlength: 2,
                    maxlength: 20,
                    remote: {
                        url: prefix + "/checkUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "schoolId":function () {
                                return $.common.trim($.form.selectSelects("schoolId"));
                            },
                            "branchId":function () {
                                return $.common.trim($.form.selectSelects("branchId"));
                            },
                            "registrationId":function () {
                                return $.common.trim($.form.selectSelects("registrationId"));
                            },
                            "organType":function () {
                                return $.common.trim( $('input[name="organType"]:checked').val());
                            },
                            "username": function() {
                                return $.common.trim($("#username").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },

                password: {
                    required: true,
                    isPassWord:true,
                    minlength: 5,
                    maxlength: 20
                },
                confirmPassword: {
                    required: true,
                    equalTo: "#password"
                },
                tel:{
                    isPhoneOrTel: true
                }
            },
            messages: {
                "username": {
                    remote: "登录用户名已经存在"
                },
                password: {
                    required: "请输入新密码",
                    minlength: "密码不能小于5个字符",
                    maxlength: "密码不能大于20个字符"
                },
                confirmPassword: {
                    required: "请再次输入新密码",
                    equalTo: "两次密码输入不一致"
                }

            },
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-organUser-add').serialize());
            }
        }

        $(function() {
            //加载驾校、分校
            $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
            $('#cxSelectSchool').cxSelect({
                selects: ['schoolId', 'branchId','registrationId'],
                jsonValue: 'v',
            });

            var sysOrganType=[[${sysOrganType}]];
            if (sysOrganType == 1 || sysOrganType == 7){
                $("#branchDiv").hide();
                $("#registrationDiv").hide();
            }else if (sysOrganType == 2) {
                $("#registrationDiv").hide();
            }else if(sysOrganType == 3){
                $("#cxSelectSchool").hide();
            }

            $('input').on('ifChecked', function(event){
                var organType = $(event.target).val();
                if (organType == "1") {
                    $("#cxSelectSchool").show();
                    $("#branchDiv").hide();
                    $("#registrationDiv").hide();
                }else if(organType == "2"){
                    $("#cxSelectSchool").show();
                    $("#branchDiv").show();
                    $("#registrationDiv").hide();
                }else if(organType == "3"){
                    $("#cxSelectSchool").show();
                    $("#branchDiv").show();
                    $("#registrationDiv").show();
                }else if (organType == "4") {
                    $("#cxSelectSchool").hide();
                } else if (organType == "5") {
                    $("#cxSelectSchool").hide();
                }
            });

        });

    </script>
</body>
</html>