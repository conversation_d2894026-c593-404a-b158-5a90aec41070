<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <!-- 驾校管理-账号管理页 -->
    <th:block th:include="include :: header('机构用户列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <div>
                                <div id="cxSelectSchool" class="select-list" th:if="${organUser.organType == 7 || organUser.organType == 1}">
                                    <li>
                                        <label>驾校：</label>
                                        <select style="margin-bottom:0px;font-family:inherit;font-size:inherit;line-height:inherit;" id="schoolId" name="schoolId" class="schoolId form-control m-b" data-first-title="选择驾校" required></select>
                                    </li>
                                    <li>
                                        <label>分校：</label>
                                        <select style="margin-bottom:0px;font-family:inherit;font-size:inherit;line-height:inherit;" id="branchId" name="branchId" class="branchId form-control m-b" data-first-title="选择分校"></select>
                                    </li>
                                    <li>
                                        <label>报名点：</label>
                                        <select style="margin-bottom:0px;font-family:inherit;font-size:inherit;line-height:inherit;" id="registrationId" name="registrationId" class="registrationId form-control m-b" data-first-title="所属报名点">
                                        </select>
                                    </li>
                                </div>
                                <li>
                                    <label>用户名：</label>
                                    <input type="text" name="username"/>
                                </li>
                                <li>
                                    <label>账号类型：</label>
                                    <select name="organType">
                                        <option value="">所有</option>
                                        <option value="1">驾校方</option>
                                        <option value="2">分校方</option>
                                        <option value="3">报名点</option>
                                        <option value="4">移动方</option></option>
                                        <option value="5">监管方</option>
                                    </select>
                                </li>
                                <li>
                                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                </li>
                            </div>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="business:organUser:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="business:organUser:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="business:organUser:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning single disabled" onclick="resetPwd()" shiro:hasPermission="business:organUser:resetPwd">
                    <i class="fa fa-key"></i> 重置密码
                </a>

            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:organUser:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:organUser:remove')}]];
        var organTypeDatas = [[${@dict.getType('user_organ_type')}]];
        var statusDatas = [[${@dict.getType('organ_user_status')}]];
        var prefix = ctx + "business/organUser";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "机构用户",
                showPageGo: true,
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'username',
                    title: '登录用户名'
                },
                {
                    field: 'realName',
                    title: '真实姓名'
                },
                {
                    field: 'tel',
                    title: '联系方式'
                },
                {
                    field: 'organType',
                    title: '账号类型',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(organTypeDatas, value);
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                    {
                        field: 'schoolId',
                        title: '驾校',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.school != null) {
                                    return row.school.name;
                                }
                            }
                        }
                    },
                    {
                        field: 'branchId',
                        title: '分校',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.branch != null) {
                                    return row.branch.name;
                                }
                            }
                        }
                    },
                    {
                        field: 'registrationId',
                        title: '报名点',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.registration != null) {
                                    return row.registration.name;
                                }
                            }
                        }
                    },
                    {
                        field: 'updatedTime',
                        title: '创建时间'
                    }
                ]
            };
            $.table.init(options);

            //加载驾校、分校、报名点
            $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
            $('#cxSelectSchool').cxSelect({
                selects: ['schoolId', 'branchId','registrationId'],
                jsonValue: 'v',
            });
        });


        /* 用户管理-重置密码 */
        function resetPwd() {
            table.set();
            var id = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
            if (id.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            var url = prefix + '/resetPwd/' + id;
            $.modal.open("重置密码", url, '800', '300');
        }
    </script>
</body>
</html>