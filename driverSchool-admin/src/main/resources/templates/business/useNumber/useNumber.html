<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('已使用号码列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list">
              <ul>
                <li>
                  <label>学员姓名：</label>
                  <input
                    type="text"
                    name="studentName"
                    placeholder="请输入学员姓名"
                  />
                </li>
                <li>
                  <label>号码：</label>
                  <input type="text" name="number" placeholder="请输入号码" />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:useNumber:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:useNumber:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:useNumber:remove')}]];
      var prefix = ctx + "business/useNumber";

      $(function() {
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              sortName: "useTime",
              sortOrder: "desc",
              modalName: "已使用号码",
              columns: [{
                  checkbox: true
              },
              {
                  field: 'id',
                  title: '主键',
                  visible: false
              },
              {
                  field: '',
                  title: '号码',
                  formatter: function(value, row, index) {
                    if (row.unicomNumber != null) {
                        return row.unicomNumber.number;
                    }
                }
              },
              {
                  field: '',
                  title: '学员姓名',
                  formatter: function(value, row, index) {
                    if (row.schoolStudent != null) {
                        return row.schoolStudent.name;
                    }
                }
              },
              {
                  field: '',
                  title: '联系电话',
                  formatter: function(value, row, index) {
                    if (row.schoolStudent != null) {
                        return row.schoolStudent.mobile;
                    }
                }
              },
              {
                  field: 'useTime',
                  title: '开卡时间'
              },]
          };
          $.table.init(options);
      });

      /* 导出已使用号码 */
      function exportSelected() {
        var params = $("#formId").serialize();
        var ids = $.table.selectColumns("id");
        var tipMsg = "确定导出所有数据吗？";
        if($.common.isNotEmpty(ids)){
            tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
            params += "&ids="+ids;
        }
        $.modal.confirm(tipMsg, function() {
            var config = {
              url: prefix + "/export",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
        });
      }
    </script>
  </body>
</html>
