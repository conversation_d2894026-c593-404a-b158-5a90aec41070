<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('学员优惠')" />
    <th:block th:include="include :: summernote-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-article-student-add" th:object="${basicArticle}">
            <input name="id" th:field="*{id}" type="hidden">
            <input id="category" name="category" class="form-control" type="hidden" th:value="7">

            <div class="form-group">
                <div class="col-sm-8">
                    <input id="title" name="title" class="form-control" type="hidden" value="学员优惠">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">优惠说明：</label>
                <div class="col-sm-9">
                    <input type="hidden" class="form-control" name="content" th:field="*{content}">
                    <div class="summernote" id="summernote"></div>
                </div>
            </div>

        </form>
    </div>


    <div class="row">
        <div class="col-sm-offset-5 col-sm-10">
            <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
            <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭 </button>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: summernote-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/article"
        $("#form-article-student-add").validate({
            onkeyup: false,
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                if ($("#summernote").summernote('isEmpty')){
                    $.modal.msgError("内容不能为空");
                    return;
                }
                $.operate.saveModal(prefix + "/saveStudentDiscount", $('#form-article-student-add').serialize());
            }
        }


        $(function() {
            $('#summernote').summernote({
                lang: 'zh-CN',
                height : 402,
                followingToolbar: false,
                dialogsInBody: true,
                toolbar: [
                    ['font', ['bold', 'italic', 'underline']],
                    ['fontsize',['fontsize']],
                    ['fontname', ['fontname']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']],
                    ['insert', ['table', 'link', 'picture']],
                    ['view', ['fullscreen', 'help', 'preview']],
                ],
                callbacks: {
                    onChange: function(contents, $edittable) {
                        $("input[name='content']").val(contents);
                    },
                    onImageUpload: function(files) {
                        var obj = this;
                    	var data = new FormData();
                    	data.append("file", files[0]);
                    	$.ajax({
                            type: "post",
                            url: ctx + "common/upload",
                    		data: data,
                    		cache: false,
                    		contentType: false,
                    		processData: false,
                    		dataType: 'json',
                    		success: function(result) {
                    		    if (result.code == web_status.SUCCESS) {
                    		        $('#' + obj.id).summernote('insertImage', result.url);
                    		    } else {
                    		        $.modal.alertError(result.msg);
                    		    }
                    		},
                    		error: function(error) {
                    		    $.modal.alertWarning("图片上传失败。");
                    		}
                    	});
                    }
                }
            });

            var content = $("input[name='content']").val();
            $('#summernote').summernote('code', content);

        });
    </script>
</body>
</html>