<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('驾校结息流水列表(留存金额)')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      width: 150px;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>
                <li class="select-time">
                  <label>结算时间： </label>
                  <input
                    type="text"
                    class="time-input"
                    id="beginCountTime"
                    placeholder="开始日期"
                    name="beginCountTime"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    class="time-input"
                    id="endCountTime"
                    placeholder="结束日期"
                    name="endCountTime"
                  />
                </li>
                <li>
                  <label>结算状态：</label>
                  <select
                    name="calculateStatus"
                    th:with="type=${@dict.getType('calculate_status')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label>结算批次：</label>
                  <input
                    type="text"
                    name="calculateBatchNo"
                    placeholder="请输入结算批次"
                  />
                </li>
                <li>
                  <label>系统单号：</label>
                  <input
                    type="text"
                    name="sysCalculateNo"
                    placeholder="请输入结算系统单号"
                  />
                </li>
                <li>
                  <label>银行单号：</label>
                  <input
                    type="text"
                    name="calculateBankNo"
                    placeholder="请输入结算银行单号"
                  />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:schoolCalculateFlowAmount:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
          <a
            class="btn btn-success multiple disabled"
            onclick="submitCalculate()"
            shiro:hasPermission="business:schoolCalculateFlowAmount:submitCalculate"
          >
            提交结算
          </a>
          <a
            class="btn btn-warning"
            onclick="viewProof()"
            shiro:hasPermission="business:calculateFlow:view"
          >
            查看凭证
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:schoolCalculateFlowAmount:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:schoolCalculateFlowAmount:remove')}]];
      var calculateStatus = [[${@dict.getType('calculate_status')}]];
      var prefix = ctx + "business/schoolCalculateFlowAmount";
      var prefix2 = ctx + "business/calculateFlow"

      $(function() {
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              modalName: "驾校结息流水（留存金额）",
              columns: [{
                  checkbox: true
              },
              {
                  field: 'id',
                  title: '主键',
                  visible: false
              },
              {
                  field: '',
                  title: '驾校',
                  formatter: function(value, row, index) {
                    if (row.school != null) {
                        return row.school.name;
                    }
                }
              },
              {
                  field: '',
                  title: '统计周期',
                  formatter: function(value, row, index) {
                    return row.beginCountTime +" 至 "+ row.endCountTime;
                }
              },
              {
                  field: 'interestCount',
                  title: '利息总数'
              },
              {
                  field: 'allSchoolRetainedAmount',
                  title: '所有驾校留存金额'
              },
              {
                  field: 'schoolRetainedAmount',
                  title: '有效留存金额'
              },
              {
                  field: 'ratio',
                  title: '所占比例',
                  formatter: function(value, row, index) {
                    return value + "%";
                }
              },
              {
                  field: 'calculateAmount',
                  title: '结算金额'
              },
              {
                  field: 'countTime',
                  title: '结息统计时间'
              },
              {
                  field: 'startCalculateTime',
                  title: '系统发起结算时间'
              },
              {
                  field: 'sysCalculateNo',
                  title: '结算系统单号'
              },
              {
                  field: 'calculateBankNo',
                  title: '结算银行单号'
              },
              {
                  field: 'calculateStatus',
                  title: '结算状态',
                  formatter: function(value, row, index) {
                    return $.table.selectDictLabel(calculateStatus, value);
                  }
              },
              {
                  field: 'calculateBatchNo',
                  title: '结算批次'
              },]
          };
          $.table.init(options);

          //加载驾校、分校
          $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
          $('#cxSelectSchool').cxSelect({
              selects: ['schoolId', 'branchId','registrationId'],
              jsonValue: 'v',
          });
      });

      /* 导出驾校结息流水列表 */
      function exportSelected() {
        var params = $("#formId").serialize();
        var ids = $.table.selectColumns("id");
        var tipMsg = "确定导出所有数据吗？";
        if($.common.isNotEmpty(ids)){
            tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
            params += "&ids="+ids;
        }
        $.modal.confirm(tipMsg, function() {
            var config = {
              url: prefix + "/export",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
        });
      }

      /* 查看结息凭证 */
      function viewProof() {
        var options = {
            title: "结息凭证",
            width: '',
            height: '',
            url: prefix2,
            skin: 'layui-layer-gray',
            btn: ['关闭'],
            yes: function (index, layero) {
                $.modal.close(index);
            }
        };
        $.modal.openOptions(options);
      }

      /* 提交结算 */
      function submitCalculate() {
        var tipMsg = "确定要提交结算吗？";
        $.modal.confirm(tipMsg, function () {
          var ids = $.table.selectColumns("id");
          var _url = prefix + "/submitCalculate";
          var params = "ids="+ids;
          var config = {
              url: _url,
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    $.modal.alertSuccess(result.msg);
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
        });
      }
    </script>
  </body>
</html>
