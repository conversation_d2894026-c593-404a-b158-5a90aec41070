<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('重新开卡')" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-student-sim" th:object="${studentSIM}">
        <input name="studentId" th:value="*{studentId}"  type="hidden">
        <input name="id" th:value="*{id}"  type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label">请选择一个号码：</label>
            <div class="col-sm-9">
                <a class="btn btn-primary" onclick="refresh()">
                    <i class="fa fa-refresh"></i> 换一批
                </a>
            </div>
        </div>

        <div id="simList">
            <div class="form-group" th:if="${simInfo.code == 0}" th:fragment="fragment-simList">
                <label class="col-sm-3 control-label"></label>
                <div class="col-sm-9">
                    <label th:each="sim:${simInfo.sim}" class="check-box">
                        <input name="simMobile" type="radio" th:value="${sim.mobileno}" th:simProductCode="${sim.commid}" th:text="${sim.mobileno}" required>
                    </label>
                </div>
            </div>
        </div>

        <div id="cxSelect" class="form-group">
            <label class="col-sm-3 control-label is-required">地址：</label>
            <div class="col-sm-3">
                <select id="deliverProvince" name="deliverProvince" class="province form-control" data-first-title="所属省份" required>
                    <option value="广东省" selected>广东省</option>
                </select>
            </div>
            <div class="col-sm-3">
                <select id="deliverCity" name="deliverCity" class="city form-control"  data-first-title="所属城市" required>
                    <option value="东莞市" selected>东莞市</option>
                </select>
            </div>
            <div class="col-sm-3" >
                <select id="deliverTown" name="deliverTown" class="town form-control" data-first-title="所属县/镇/区">
                    <option value="">所属县/镇/区</option>
                </select>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-3 control-label is-required">具体地址：</label>
            <div class="col-sm-9">
                <input id="deliverAddress" name="deliverAddress" class="form-control" type="text" required>
            </div>
        </div>
    </form>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: jquery-cxselect-js" />
<script th:inline="javascript">
    var prefix = ctx + "business/studentSim";

    $("#form-student-sim").validate({
        onkeyup: false,
        rules:{
            simMobile:{
                required:true,
            },
            deliverAddress:{
                maxlength:50
            }
        },
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            var simProductCode= $("input[name='simMobile']:checked").attr('simProductCode');
            var data = $("#form-student-sim").serializeArray();
            data.push({"name": "simProductCode", "value": simProductCode});
            $.operate.save(prefix + "/openCard", data);
        }
    }
    function refresh(){
        $.ajax({
            type: "post",
            url: prefix + "/localRefresh/sim",
            data: {
                "fragment":'fragment-simList'
            },
            success: function(data) {
                $("#simList").html(data);
                $(".check-box:not(.noicheck),.radio-box:not(.noicheck)").each(function() {
                    $(this).iCheck({
                        checkboxClass: 'icheckbox-blue',
                        radioClass: 'iradio-blue',
                    })
                })
            }
        });
    }


    $(function() {
        // 加载省市区
        $.cxSelect.defaults.url = ctx + "business/area/areaData";
        $('#cxSelect').cxSelect({
            selects: ['province', 'city', 'town'],
            nodata: 'none'
        });

    });

</script>
</body>
</html>