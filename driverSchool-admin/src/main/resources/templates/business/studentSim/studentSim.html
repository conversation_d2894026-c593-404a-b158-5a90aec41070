<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('运营数据列表')" />
    <th:block th:include="include :: bootstrap-editable-css" />
    <link
      href="../static/css/bootstrap.min.css"
      th:href="@{/css/bootstrap.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/font-awesome.min.css"
      th:href="@{/css/font-awesome.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/animate.min.css"
      th:href="@{/css/animate.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/style.min.css"
      th:href="@{/css/style.min.css}"
      rel="stylesheet"
    />
    <style>
      .bootstrap-table .fixed-table-container .fixed-table-body {
        overflow-x: unset;
        overflow-y: unset;
      }
    </style>
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label style="width: 78px; display: inline-block"
                    >学习进度：</label
                  >
                  <select name="isRegiste">
                    <option value="">所有</option>
                    <option value="1">预登记</option>
                    <option value="2">已报名</option>
                  </select>
                </li>

                <li>
                  <label style="width: 65px; display: inline-block"
                    >是否开卡：</label
                  >
                  <select name="isNotOpenCard">
                    <option value="">所有</option>
                    <option value="0">未发起</option>
                    <option value="1">已发起</option>
                    <option value="2">不开卡</option>
                  </select>
                </li>

                <li>
                  <label style="width: 91px; display: inline-block"
                    >开卡是否外呼：</label
                  >
                  <select name="isCallOut">
                    <option value="">所有</option>
                    <option value="0">否</option>
                    <option value="1">是</option>
                  </select>
                </li>

                <li>
                  <label style="width: 91px; display: inline-block"
                    >返现是否外呼：</label
                  >
                  <select name="returnCashIsCallOut">
                    <option value="">所有</option>
                    <option value="0">否</option>
                    <option value="1">是</option>
                  </select>
                </li>

                <li>
                  <label>有无补贴：</label>
                  <select name="isReturnCash">
                    <option value="">所有</option>
                    <option value="0">否</option>
                    <option value="1">是</option>
                  </select>
                </li>

                <li>
                  <label>订单状态：</label>
                  <select name="activeStatus">
                    <option value="">所有</option>
                    <option value="待办理">待办理</option>
                    <option value="激活成功">激活成功</option>
                    <option value="订单取消">订单取消</option>
                  </select>
                </li>

                <li>
                  <label>姓名：</label>
                  <input type="text" name="name" id="studentName" />
                </li>

                <li>
                  <label>身份证号：</label>
                  <input type="text" name="identity" placeholder="身份证6位" />
                </li>

                <li>
                  <label>学员卡号：</label>
                  <input type="text" name="simMobile" placeholder="卡号6位" />
                </li>

                <li>
                  <label>异常学员：</label>
                  <select name="exceptionStudent">
                    <option value="0">请选择</option>
                    <option value="1">仅看驾校/分校/报名点空值学员</option>
                  </select>
                </li>

                <li>
                  <label>快递单号：</label>
                  <input
                    type="text"
                    name="expressNo"
                    placeholder="快递单号6位"
                  />
                </li>

                <li>
                  <label>联系号码：</label>
                  <input
                    type="text"
                    name="mobile"
                    placeholder="请输入联系号码"
                  />
                </li>

                <li class="select-time">
                  <label>报名时间： </label>
                  <input
                    type="text"
                    class="time-input"
                    id="startTime"
                    placeholder="开始时间"
                    name="params[beginTime]"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    class="time-input"
                    id="endTime"
                    placeholder="结束时间"
                    name="params[endTime]"
                  />
                </li>

                <li class="select-time">
                  <label style="width: 78px; display: inline-block"
                    >预登记时间：
                  </label>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input dateTime"
                    id="prepareRegisteDateStartTime"
                    placeholder="开始时间"
                    name="params[prepareRegisteDateStartTime]"
                  />
                  <span>-</span>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input dateTime"
                    id="prepareRegisteDateEndTime"
                    placeholder="结束时间"
                    name="params[prepareRegisteDateEndTime]"
                  />
                </li>

                <li class="select-time">
                  <label style="width: 91px; display: inline-block"
                    >配送完成时间：
                  </label>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input dateTime"
                    id="activeDateStartTime"
                    placeholder="开始时间"
                    name="params[activeDateStartTime]"
                  />
                  <span>-</span>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input dateTime"
                    id="activeDateEndTime"
                    placeholder="结束时间"
                    name="params[activeDateEndTime]"
                  />
                </li>

                <li>
                  <label style="width: 39px; display: inline-block"
                    >驾校：</label
                  >
                  <select
                    style="width: 160px; display: inline-block"
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>
                <li>
                  <label style="width: 39px; display: inline-block"
                    >分校：</label
                  >
                  <select
                    style="width: 160px; display: inline-block"
                    id="branchId"
                    name="branchId"
                    class="branchId form-control m-b"
                    data-first-title="选择分校"
                  ></select>
                </li>
                <li>
                  <label style="width: 39px; display: inline-block"
                    >门店：</label
                  >
                  <select
                    style="width: 180px; display: inline-block"
                    id="registrationId"
                    name="registrationId"
                    class="registrationId form-control m-b"
                    data-first-title="所属报名点"
                  ></select>
                </li>
                <li class="select-time">
                  <label style="width: 78px; display: inline-block"
                    >开卡时间：
                  </label>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input dateTime"
                    id="beginOpenSimTime"
                    placeholder="开始时间"
                    name="beginOpenSimTime"
                  />
                  <span>-</span>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input dateTime"
                    id="endOpenSimTime"
                    placeholder="结束时间"
                    name="endOpenSimTime"
                  />
                </li>
                <li>
                  <label>收货号码：</label>
                  <input
                    type="text"
                    name="recipientMobile"
                    placeholder="请输入收货电话"
                  />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-info"
            onclick="importStudentSimData()"
            shiro:hasPermission="business:studentSim:import"
          >
            <i class="fa fa-upload"></i> 导入
          </a>

          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:studentSim:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>

          <a
            class="btn btn-info"
            id="turnOn"
            th:if="${isOpenMobile== '0'}"
            shiro:hasPermission="business:studentSim:turnOnLight"
          >
            <i class="fa fa-toggle-on"></i> 开启开卡
          </a>

          <a
            class="btn btn-danger"
            id="turnOff"
            th:if="${isOpenMobile== '1'}"
            shiro:hasPermission="business:studentSim:turnOffLight"
          >
            <i class="fa fa-power-off"></i> 关闭开卡
          </a>

          <a
            class="btn btn-primary"
            id="turnOnSkip"
            th:if="${isOpenMobileSkip== '0'}"
            shiro:hasPermission="business:studentSim:turnOnSkip"
          >
            <i class="fa fa-toggle-on"></i> 开启开卡环节
          </a>

          <a
            class="btn btn-danger"
            id="turnOffSkip"
            th:if="${isOpenMobileSkip== '1'}"
            shiro:hasPermission="business:studentSim:turnOffSkip"
          >
            <i class="fa fa-power-off"></i> 关闭开卡环节
          </a>

          <a
            class="btn btn-danger single disabled"
            id="sendSimToYiDong"
            shiro:hasPermission="business:studentSim:sendSimToYiDong"
          >
            <i class="fa fa-credit-card"></i> 发起开卡
          </a>

          <a
            class="btn btn-danger single disabled"
            id="notOpenCard"
            shiro:hasPermission="business:studentSim:notOpenCard"
          >
            <i class="fa fa-credit-card"></i> 不开卡
          </a>

          <a
            class="btn btn-primary single disabled"
            onclick="openSim('',1200,'')"
            shiro:hasPermission="business:studentSim:openSim"
          >
            重新开卡
          </a>

          <a
            class="btn btn-danger"
            id="batchSendSimToYiDong"
            onclick="$.table.importExcel()"
            shiro:hasPermission="business:studentSim:batchSendSimToYiDong"
          >
            <i class="fa fa-credit-card"></i> 批量开卡
          </a>

          <a
            class="btn btn-danger"
            id="batchNoOpenCard"
            onclick="importNoOpenSimExcel()"
            shiro:hasPermission="business:studentSim:batchNoOpenSim"
          >
            <i class="fa fa-credit-card"></i> 批量不开卡
          </a>

          <a
            class="btn btn-danger"
            id="batchNoInitSim"
            onclick="importNoInitSimExcel()"
            shiro:hasPermission="business:studentSim:batchNoInitSim"
          >
            批量未发起
          </a>

          <a
            class="btn btn-warning single disabled"
            onclick="expressInfo('',1200,'')"
            shiro:hasPermission="business:studentSim:expressInfo"
          >
            查询物流
          </a>

          <a
            class="btn btn-info fa fa-area-chart"
            onclick="studentSimCount()"
            shiro:hasPermission="business:studentSim:count"
          >
            统计数据
          </a>

          <a
            class="btn btn-danger multiple disabled"
            onclick="$.operate.removeAll()"
            shiro:hasPermission="business:studentSim:remove"
          >
            <i class="fa fa-remove"></i> 删除
          </a>
        </div>
        <div
          id="top_fix_table"
          class="col-sm-12 select-table table-striped table table-hover"
          style="overflow: auto"
        >
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-table-editable-js" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
          var editFlag = [[${@permission.hasPermi('business:studentSim:edit')}]];
          var removeFlag = [[${@permission.hasPermi('business:studentSim:remove')}]];
          var studentStatusDatas =[[${@dict.getType('student_status')}]]

          var prefix = ctx + "business/studentSim";

          $(function() {

              var options = {
                  url: prefix + "/list",
                  createUrl: prefix + "/add",
                  updateUrl: prefix + "/edit/{id}",
                  removeUrl: prefix + "/remove",
                  importUrl: prefix + "/importData",
                  importTemplateUrl: prefix + "/importTemplate",
                  exportUrl: prefix + "/export",
                  detailUrl: prefix + "/expressInfo/{id}",
                  modalName: "运营开卡",
                  onEditableSave: onEditableSave,
                  showPageGo: true,
                  columns: [{
                      checkbox: true
                  },

                      {
                          field: 'id',
                          title: '',
                          visible: false
                      },

                      {
                          field: 'studentId',
                          title: '驾校',
                          formatter: function (value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if (row.schoolStudent != null) {
                                      return '<div style="width:120px;">' + row.schoolName + '</div>';
                                  }
                              }
                          },
                      },
                      {
                          field: 'studentId',
                          title: '分校',
                          formatter: function (value, row, index) {
                              if (row.schoolBranch != null) {
                                  return '<div style="width:120px;">' + row.schoolBranch.name + '</div>';
                              }
                          }
                      },
                      {
                          field: 'studentId',
                          title: '报名点',
                          formatter: function (value, row, index) {
                              if (row.schoolRegistration != null) {
                                      return '<div style="width:120px;">' + row.schoolRegistration.name + '</div>';
                              }
                          }
                      },
                      {
                          field: 'name',
                          title: '学员',
                          formatter: function (value, row, index) {
                              return '<div style="width:120px;">' + value + '</div>';
                          }
                      },

                      {
                          field: 'identity',
                          title: '身份证号',
                          formatter: function (value, row, index) {
                              return '<div style="width:150px;">' + basecclusion(value,6,4) + '</div>';
                          }
                      },

                      {
                          field: 'mobile',
                          title: '联系号码',
                          formatter: function (value, row, index) {
                              return '<div style="width:120px;">' + value + '</div>';
                          }
                      },

                      {
                          field: 'studentId',
                          // title: '报名时间',
                          title: '报名审核时间',
                          formatter: function (value, row, index) {
                              if (row.schoolStudent != null) {
                                      if ($.common.isNotEmpty(value) && $.common.isNotEmpty(row.schoolStudent.registeDate)) {
                                          return '<div style="width:120px;">' + row.schoolStudent.registeDate + '</div>';
                                  }else {
                                      return '<div style="width:120px;">-</div>';
                                  }
                              }
                          }
                      },
                      {
                          field: 'studentId',
                          title: '预登记时间',
                          formatter: function (value, row, index) {
                              if (row.schoolStudent != null) {
                                      if ($.common.isNotEmpty(value) && $.common.isNotEmpty(row.schoolStudent.prepareRegisteDate)) {
                                          return '<div style="width:200px;">' + row.schoolStudent.prepareRegisteDate + '</div>';
                                  }else {
                                      return '<div style="width:200px;">-</div>';
                                  }
                              }
                          }
                      },
                      {
                          field: 'studentId',
                          title: '学习进度',
                          formatter: function(value, row, index) {
                              if ($.common.isNotEmpty(value)) {
                                  if (row.schoolStudent != null) {
                                      return '<div style="width:120px;">' + $.table.selectDictLabel(studentStatusDatas, row.schoolStudent.status) + '</div>';
                                  }
                              }
                          }
                      },

                  {
                      field: 'simMobile',
                      title: '学员卡号码',
                      formatter: function (value, row, index) {
                              return '<div style="width:120px;">' + value + '</div>';
                          }
                  },
                  {
                  field: 'recipientMobile',
                  title: '收货号码',
                  formatter: function(value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              return '<div style="width:120px;">' + value + '</div>';
                          }else {
                              return '<div style="width:120px;">-</div>';
                          }
                      }
                  },
                  {
                      field: 'deliverAddress',
                      title: '配送地址',
                      formatter: function(value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              if (row.deliverProvince!= null && row.deliverCity!= null && row.deliverTown!= null){
                                  return '<div style="width:180px;">' + row.deliverProvince+row.deliverCity+row.deliverTown+value + '</div>';
                              }else{
                                  return '<div style="width:180px;">' + value + '</div>';
                              }
                          }
                      }
                  },
                  {
                      field: 'loginCount',
                      title: '登录小程序次数'
                  },
                  {
                      field: 'deliverStatus',
                      title: '派送状态',
                      formatter: function(value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              return '<div style="width:120px;">' + value + '</div>';
                          }else {
                              return '<div style="width:120px;">-</div>';
                          }
                      }
                  },
                  {
                      field: 'activeStatus',
                      title: '订单状态',
                      formatter: function(value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              return '<div style="width:120px;">' + value + '</div>';
                          }else {
                              return '<div style="width:120px;">-</div>';
                          }
                      }
                  },
                  {
                      field: 'orderNo',
                      title: '订单号',
                      formatter: function(value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              return '<div style="width:120px;">' + value + '</div>';
                          }else {
                              return '<div style="width:120px;">-</div>';
                          }
                      }
                  },
                  {
                      field: 'expressNo',
                      title: '快递单号',
                      formatter: function(value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              return '<div style="width:120px;">' + value + '</div>';
                          }else {
                              return '<div style="width:120px;">-</div>';
                          }
                      }
                  },
                  {
                      field: 'activeDate',
                      title: '完成时间',
                      formatter: function(value, row, index) {
                          if ($.common.isNotEmpty(value)) {
                              return '<div style="width:120px;">' + value + '</div>';
                          }else {
                              return '<div style="width:120px;">-</div>';
                          }
                      }
                  },
                  {
                      field: 'isNotOpenCard',
                      title: '是否已发起开卡',
                      formatter: function(value, row, index) {
                          if(value == 0){
                              return '<div style="width:120px;">未发起</div>';
                          }else if (value == 1) {
                              return '<div style="width:120px;">已发起</div>';
                          }else if (value == 2) {
                              return '<div style="width:120px;">不开卡</div>';
                          }else{
                              return '-';
                          }
                      }
                  },
                  {
                      field: 'openSimTime',
                      title: '开卡时间',
                      formatter: function(value, row, index) {
                          if($.common.isNotEmpty(value)) {
                              return '<div style="width:180px;">' + value + '</div>';
                          }else {
                              return '<div style="width:180px;">-</div>';
                          }
                      }
                  },
                  {
                      field: 'synFailMsg',
                      title: '开卡反馈',
                      formatter: function(value, row, index) {
                          if($.common.isNotEmpty(value)) {
                              return '<div style="width:120px;">' + value + '</div>';
                          }else {
                              return '<div style="width:120px;">-</div>';
                          }
                      }
                  },
                  {
                      field: 'isAddWechat',
                      title: '是否加企微',
                      formatter: function(value, row, index) {
                              if(value == 0){
                                  return '<div style="width:80px;">否</div>';
                              }else if (value == 1) {
                                  return '<div style="width:80px;">是</div>';
                              }else{
                                  return '<div style="width:80px;">-</div>';
                              }
                          }
                  },
                  {
                      field: 'isOpenCardCallOut',
                      title: '开卡是否需要外呼',
                      visible: false,
                      formatter: function(value, row, index) {
                          if (row.schoolStudent != null && $.common.isNotEmpty(row.schoolStudent.registeDate)) {
                                  let registeDateStr = row.schoolStudent.registeDate;
                                  let registeDate = new Date(Date.parse(registeDateStr.replace(/-/g, "/")));
                                  let today = new Date();
                                  let daysDiff = daysBetween(today, registeDate);
                                  if(row.activeStatus != '激活成功' && daysDiff >= 11) {
                                      return '<div style="width:120px;">需要</div>';
                                  }else {
                                      return '<div style="width:120px;">-</div>';
                                  }
                              }
                      }
                  },
                  {
                      field: 'isCallOut',
                      title: '有无开卡外呼',
                      editable : {
      		type : 'select',
      		title : '编辑',
                          emptytext: '请选择',
      		source : [{
      			value : 0,
      			text : '无'
      		}, {
      			value : 1,
      			text : '有'
      		}]
      	}
                  },
                  {
                      field: 'callOutInfo',
                      title: '外呼情况说明',
                      width: 280,
                      editable : {
      		type : 'text',
      		title : '编辑',
                          model: 'inline',
                          emptytext: '点击编辑',
      		// source : [
                          // {
      		// 	value : 1,
      		// 	text : "学员拒接"
      		// },
                          // {
      		// 	value : 2,
      		// 	text : "学员拒绝开卡"
      		// },
                          // {
      		// 	value : 3,
      		// 	text : "学员不知情"
      		// },
                          // {
      		// 	value : 4,
      		// 	text : "学员同意"
      		// },
                          // {
      		// 	value : 5,
      		// 	text : "满五户"
      		// },
                          // {
      		// 	value : 6,
      		// 	text : "黑户"
      		// }]
      	}
                  },
                  {
                      field: 'callOutPeople',
                      title: '外呼操作员'
                  },
                  {
                      field: 'isResetSim',
                      title: '是否重新派卡',
                      formatter: function(value, row, index) {
                              if (value == 0) {
                                  return '否';
                              }else if (value == 1) {
                                  return '是';
                              }else {
                                  return '-';
                              }
                      }
                  },
                  {
                      field: 'failReason',
                      title: '失败原因归类',
                      visible: false,
                      editable : {
      		type : 'select',
      		title : '编辑',
                          model: 'inline',
                          emptytext: '请选择',
      		source : [
                          {
      			value : 1,
      			text : "学员原因"
      		},
                          {
      			value : 2,
      			text : "系统原因"
      		},
                          {
      			value : 3,
      			text : "快递原因"
      		},
                          {
      			value : 4,
      			text : "门店原因"
      		}]
      	}
                  },
                  {
                      field: 'isReturnCash',
                      title: '是否充值',
                      model: 'inline',
                      editable : {
      		type : 'select',
      		title : '编辑',
                          emptytext: '请选择',
      		source : [
                          {
      			value : 0,
      			text : '否'
      		},
                          {
      			value : 1,
      			text : '是'
      		}]
      	}
                  },
                  {
                      field: 'rechargeTime',
                      title: '充值时间',
                      formatter: function(value, row, index) {
                          if($.common.isNotEmpty(value)) {
                              return '<div style="width:180px;">' + value + '</div>';
                          }else {
                              return '<div style="width:180px;">-</div>';
                          }
                      }
                  },
                  {
                      field: 'rechargeOperator',
                      title: '充值操作员',
                      formatter: function(value, row, index) {
                          if($.common.isNotEmpty(value)) {
                              return '<div style="width:80px;">' + value + '</div>';
                          }else {
                              return '<div style="width:80px;">-</div>';
                          }
                      }
                  },
                  {
                      field: 'returnCashFailReason',
                      title: '返现失败原因',
                      visible: false,
                      editable : {
      		type : 'select', //输入框类型
      		title : '编辑', //标题
                          model: 'inline',    //编辑模式
                          emptytext: '请选择',  //无对应的值时显示的文字
                          /** 动态显示编辑状态 */
                          noEditFormatter: function(value, row, index){
                              if(row.isReturnCash == 0){
                                  return false;
                              } else {
                                  return null;
                              }
                          },
      		source : [
                          {
      			value : 1,
      			text : "学员未充值"
      		},
                          {
      			value : 2,
      			text : "学员分开充值"
      		},
                          {
      			value : 3,
      			text : "学员不加企微"
      		}]
      	}
                  },
                  {
                      field: 'isReturnCashCallOut',
                      title: '返现是否需要外呼',
                      visible: false,
                      formatter: function(value, row, index) {
                          if ($.common.isNotEmpty(row.activeDate)) {
                                  let activeDateStr = row.activeDate;
                                  let activeDate = new Date(Date.parse(activeDateStr.replace(/-/g, "/")));
                                  let today = new Date();
                                  let daysDiff = daysBetween(today, activeDate);
                                  if(row.isReturnCash == 0 && daysDiff >= 11) {
                                      return "需要";
                                  }else {
                                      return "-";
                                  }
                              }
                          }
                  },
                  {
                      field: 'returnCashIsCallOut',
                      title: '有无返现外呼',
                      visible: false,
                      editable : {
      		type : 'select',
      		title : '编辑',
                          model: 'inline',
                          emptytext: '请选择',
      		source : [
                          {
      			value : 0,
      			text : "无"
      		},
                          {
      			value : 1,
      			text : "有"
      		}]
      	}
                  },
                  {
                      field: 'isDynamic',
                      title: '是否活跃',
                      editable : {
      		type : 'select',
      		title : '编辑',
                          model: 'inline',
                          emptytext: '请选择',
      		source : [
                          {
      			value : 0,
      			text : "否"
      		},
                          {
      			value : 1,
      			text : "是"
      		}]
      	}
                  },
                  {
                      field: 'simCardStatus',
                      title: '学员卡状态',
                      formatter: function(value, row, index) {
                          if($.common.isNotEmpty(value)) {
                              return '<div style="width:120px;">' + value + '</div>';
                          }else {
                              return '<div style="width:120px;">-</div>';
                          }
                      }
                  },
                  {
                      field: 'isAddCallOutSale',
                      title: '是否二呼',
                      editable : {
      		type : 'select',
      		title : '编辑',
                          model: 'inline',
                          emptytext: '请选择',
      		source : [
                          {
      			value : 0,
      			text : "否"
      		},
                          {
      			value : 1,
      			text : "是"
      		}]
      	}
                  },
                  {
                      field: 'recallDate',
                      title: '二呼日期',
                      formatter: function(value, row, index) {
                          if($.common.isNotEmpty(value)) {
                              return '<div style="width:80px;">' + value + '</div>';
                          }else {
                              return '<div style="width:80px;">-</div>';
                          }
                      }
                  },
                  {
                      field: '',
                      title: '是否不开卡',
                      visible: false,
                      formatter: function(value, row, index) {
                              if(value == 0){
                                  return '<div style="width:120px;">-</div>';
                              }else if (value == 1) {
                                  return '<div style="width:120px;">是</div>';
                              }else{
                                  return '<div style="width:120px;">-</div>';
                              }
                          }
                  },
                  ]
              };
              $.table.init(options);


              $('#sendSimToYiDong').click(function(){
              	var rows = $.table.selectRows();
                  if (rows.length < 0) {
                      $.modal.alertWarning("请至少选择一条记录");
                      return;
                  }

                  var id = rows[0].id;
                  var formData = new FormData();
                  formData.append('id', id);
                  var code;

                  $.ajax({
                          url: prefix + "/checkOpenStatus",
                          type: "post",
                          processData: false,
                          contentType: false,
                          async: false,
                          data: formData,
                          success: function(response) {
                              if (response.code == 500) {
                                  code = response.code;
                                  $.modal.alertWarning(response.msg);
                              }
                          }
                      })

                      if (code == 500) {
                          //如果学员被设为不开卡，则中止
                          event.stopPropagation();
                          return false;
                      }

                  $.modal.confirm("确认要对号码" + rows[0].simMobile + "发起开卡吗?", function () {
                      $.operate.submitPro(prefix + "/sendSimToYiDong", "post", "json", {id: rows[0].id}, "正在开卡中...");
                  });
              });

              $("#turnOn").click(function () {
                  $.modal.confirm("确认开启报名时移动开卡吗?", function () {
                      $.operate.submitPro(prefix + "/turnOnLight", "post", "json",null, "移动开卡正在打开中...");
                      refreshTab();
                  });

              })

              //关闭
              $("#turnOff").click(function () {
                  $.modal.confirm("确认关闭报名时移动开卡吗?", function () {
                      $.operate.submitPro(prefix + "/turnOffLight", "post", "json",null, "移动开卡正在关闭中");
                      refreshTab();
                  });

              })


              $("#turnOnSkip").click(function () {
                  $.modal.confirm("确认打开跳过开卡环节吗?", function () {
                      $.operate.submitPro(prefix + "/turnOnSkip", "post", "json",null, "正在打开中...");
                      refreshTab();
                  });

              })

              //关闭
              $("#turnOffSkip").click(function () {
                  $.modal.confirm("确认关闭跳过开卡环节吗?", function () {
                      $.operate.submitPro(prefix + "/turnOffSkip", "post", "json",null, "正在关闭中");
                      refreshTab();
                  });
              })

              /** 不开卡操作 */
              $('#notOpenCard').click(function(){
              	var rows = $.table.selectRows();
                  if (rows.length < 0) {
                      $.modal.alertWarning("请至少选择一条记录");
                      return;
                  }
                  $.modal.confirm("确认要对号码" + rows[0].simMobile + "设为不开卡吗?", function () {
                      $.operate.submitPro(prefix + "/notOpenCard", "post", "json", {id: rows[0].id}, "处理中...");
                  });
              });

              /** 初始化时间组件 */
              layui.use('laydate', function(){
                  var laydate = layui.laydate;

                      // laydate.render({
                      //     elem: '#activeDateStartTime',
                      //     type: 'datetime',
                      //     trigger: 'click'
         		        // });
                      //    laydate.render({
                      //     elem: '#activeDateEndTime',
                      //     type: 'datetime',
                      //     trigger: 'click'
         		        // });


                  lay('.dateTime').each(function(){
                      laydate.render({
                      elem: this,
                      type: 'datetime',
                      trigger: 'click'
                      })
                  });

                  /** 初始化时间组件 */
              layui.use('laydate', function(){
                  var laydate = layui.laydate;
                    laydate.render({
                          elem: '#beginOpenSimTime',
                          type: 'datetime',
                          trigger: 'click'
                    });
                    laydate.render({
                          elem: '#endOpenSimTime',
                          type: 'datetime',
                          trigger: 'click'
                    });
                  });

              /** 加载驾校、分校 */
              $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
              $('#cxSelectSchool').cxSelect({
                  selects: ['schoolId', 'branchId','registrationId'],
                  jsonValue: 'v',
              });

              });
          });


          /* 导出移动开卡列表 */
          function exportSelected() {
              var ids = $.table.selectColumns("id"); //勾选的行数据id
              var schoolId = $('#schoolId').val(); //驾校id
              var studentName = $("input[id='studentName']").val(); //学生名称
              var identity = $("input[name='identity']").val(); //身份证号
              var simMobile = $("input[name='simMobile']").val(); //学员卡号
              var expressNo = $("input[name='expressNo']").val(); //快递单号
              var startTime = $("input[id='startTime']").val(); //报名开始时间
              var endTime = $("input[id='endTime']").val(); //报名结束时间
              var prepareRegisteDateStartTime = $("input[id='prepareRegisteDateStartTime']").val(); //预登记开始时间
              var prepareRegisteDateEndTime = $("input[id='prepareRegisteDateEndTime']").val(); //预登记开始时间
              var activeDateStartTime = $("input[id='activeDateStartTime']").val(); //配送完成开始时间
              var activeDateEndTime = $("input[id='activeDateEndTime']").val(); //配送完成结束时间
              var isRegiste = $('select[name="isRegiste"]').val(); //是否预报名
              var isNotOpenCard = $('select[name="isNotOpenCard"]').val(); //是否开卡
              var isCallOut = $('select[name="isCallOut"]').val(); //开卡是否外呼
              var returnCashIsCallOut = $('select[name="returnCashIsCallOut"]').val(); //返现是否外呼
              var isReturnCash = $('select[name="isReturnCash"]').val(); //有无补贴
              var schoolId = $('select[id="schoolId"]').val();
              var branchId = $('select[id="branchId"]').val();
              var registrationId = $('select[id="registrationId"]').val();
              var exceptionStudent = $('select[name="exceptionStudent"]').val(); //异常学员
              var activeStatus = $('select[name="activeStatus"]').val(); //订单状态
              var beginOpenSimTime = $('select[id="beginOpenSimTime"]').val();
              var endOpenSimTime = $('select[id="endOpenSimTime"]').val();
              var mobile = $("input[name='mobile']").val();
              var recipientMobile = $("input[name='recipientMobile']").val();

              var tipMsg = "确定导出所有数据吗？注意：每次仅能导出50000条数据，超出部分请缩短查询结果的范围。";

              let fromData = new FormData();
              fromData.append('schoolId', schoolId != null ? schoolId : '');
              fromData.append('name', studentName != null ? studentName : '' );
              fromData.append('identity', identity != null ? identity : '');
              fromData.append('simMobile', simMobile != null ? simMobile : '');
              fromData.append('expressNo', expressNo != null ? expressNo : '');
              fromData.append('startTime', startTime != null ? startTime : '');
              fromData.append('endTime', endTime != null ? endTime : '');
              fromData.append('prepareRegisteDateStartTime', prepareRegisteDateStartTime != null ? prepareRegisteDateStartTime : '');
              fromData.append('prepareRegisteDateEndTime', prepareRegisteDateEndTime != null ? prepareRegisteDateEndTime : '');
              fromData.append('activeDateStartTime', activeDateStartTime != null ? activeDateStartTime : '');
              fromData.append('activeDateEndTime', activeDateEndTime != null ? activeDateEndTime : '');
              fromData.append('isRegiste', isRegiste != null ? isRegiste : '');
              fromData.append('isNotOpenCard', isNotOpenCard != null ? isNotOpenCard : '');
              fromData.append('isCallOut', isCallOut != null ? isCallOut : '');
              fromData.append('returnCashIsCallOut', returnCashIsCallOut != null ? returnCashIsCallOut : '');
              fromData.append('isReturnCash', isReturnCash != null ? isReturnCash : '');
              fromData.append('schoolId', schoolId != null ? schoolId : '');
              fromData.append('branchId', branchId != null ? branchId : '');
              fromData.append('registrationId', registrationId != null ? registrationId : '');
              fromData.append('exceptionStudent', exceptionStudent != null ? exceptionStudent : '');
              fromData.append('activeStatus', activeStatus != null ? activeStatus : '');
              fromData.append('beginOpenSimTime', beginOpenSimTime != null ? beginOpenSimTime : '');
              fromData.append('endOpenSimTime', endOpenSimTime != null ? endOpenSimTime : '');
              fromData.append('mobile', mobile != null ? mobile : '');
              fromData.append('recipientMobile', recipientMobile != null ? recipientMobile : '');

              if($.common.isNotEmpty(ids)){
                  tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
                  fromData.append('ids', ids);
              }
              $.modal.confirm(tipMsg, function() {
                  $.ajax({
                  url: prefix + "/export",
                  data: fromData,
                  type: "post",
                  processData: false,
                  contentType: false,
                  success: function(result) {
                      if (result.code == web_status.SUCCESS) {
                              window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                      } else {
                              $.modal.alertError(result.msg);
                      }
                  }
              })
          });
      }

          function expressInfo(id,width, height) {
              table.set();
              var _url =  $.operate.detailUrl(id);
              var options = {
                  title: table.options.modalName + "详细",
                  width: width,
                  height: height,
                  url: _url,
                  skin: 'layui-layer-gray',
                  btn: ['关闭'],
                  yes: function (index, layero) {
                      $.modal.close(index);
                  }
              };
              $.modal.openOptions(options);
          }

          function openSim() {

              table.set();
              var rows = $.table.selectRows();
              var id = rows[0].id;
              var formData = new FormData();
              formData.append('id', id);
              var code;

              $.ajax({
                  url: prefix + "/checkOpenStatus",
                  type: "post",
                  processData: false,
                  contentType: false,
                  async: false,
                  data: formData,
                  success: function(response) {
                      code = response.code;
                  if (response.code == 500) {
                      $.modal.alertWarning(response.msg);
                      }
                  }
              })

              if (code == web_status.SUCCESS) {
                  var id = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
                  if (id.length == 0) {
                      $.modal.alertWarning("请至少选择一条记录");
                      return;
                  }
                  url = prefix +"/openSim/{id}".replace("{id}", id);
                  $.modal.open("重新开卡" , url);
              }else if (code == 500) {
                  //如果学员被设为不开卡，则中止
                  event.stopPropagation();
                  return false;
              }
          }


          //学员统计数据
          function studentSimCount() {
              table.set();

              var startTime = $("input[id='startTime']").val(); //报名开始时间
              var endTime = $("input[id='endTime']").val(); //报名结束时间
              var prepareRegisteDateStartTime = $("input[id='prepareRegisteDateStartTime']").val(); //预登记开始时间
              var prepareRegisteDateEndTime = $("input[id='prepareRegisteDateEndTime']").val(); //预登结束时间
              var activeDateStartTime = $("input[id='activeDateStartTime']").val(); //配送完成开始时间
              var activeDateEndTime = $("input[id='activeDateEndTime']").val(); //配送完成结束时间
              var studyCenterIsSyn = $('select[name="studyCenterIsSyn"]').val(); //是否上报计时平台
              var isRegiste = $('select[name="isRegiste"]').val(); //是否上报计时平台

              _url = prefix + "/studentSimCount?isRegiste="+isRegiste;
              var options = {
                      title: "开卡统计数据",
                      width: '1000',
                      height: '180',
                      url: _url,
                      btn: ['关闭'],
                      yes: function (index, layero) {
                          $.modal.close(index);
                      }
                  };
                  $.modal.openOptions(options);
          }

          /** 比较时间 */
          function daysBetween(date1, date2) {
              const ONE_DAY_MS = 1000 * 60 * 60 * 24;
              const timeDiff = Math.abs(date2.getTime() - date1.getTime());
              return Math.floor(timeDiff / ONE_DAY_MS);
          }

          /** 表格行内编辑事件 */
          function onEditableSave (field, row, rowIndex, oldValue, $el) {
              $.modal.loading('修改中...');
              let fromData = new FormData();
              fromData.append('id', row.id);
              fromData.append('isAddWechat', row.isAddWechat != null ? row.isAddWechat : '' );
              fromData.append('isCallOut', row.isCallOut != null ? row.isCallOut : '');
              fromData.append('callOutInfo', row.callOutInfo != null ? row.callOutInfo : '');
              fromData.append('failReason', row.failReason != null ? row.failReason : '');
              fromData.append('isReturnCash', row.isReturnCash != null ? row.isReturnCash : '');
              fromData.append('returnCashFailReason', row.returnCashFailReason != null ? row.returnCashFailReason : '');
              fromData.append('returnCashIsCallOut', row.returnCashIsCallOut != null ? row.returnCashIsCallOut : '');
              fromData.append('isDynamic', row.isDynamic != null ? row.isDynamic : '');
              fromData.append('simCardStatus', row.simCardStatus != null ? row.simCardStatus : '');
              fromData.append('isDynamic', row.isDynamic != null ? row.isDynamic : '');
              fromData.append('simCardStatus', row.simCardStatus != null ? row.simCardStatus : '');
              $.ajax({
                  url: prefix + "/update",
                  data: fromData,
                  type: "post",
                  processData: false,
                  contentType: false,
                  success: function(result) {
                      if (result.code == web_status.SUCCESS) {
                          $.modal.alertSuccess(result.msg);
                      } else {
                          $.modal.alertError(result.msg);
                      }
                      $.table.refresh();
                      $.modal.closeLoading();
                  }
              })
          }

          /* 批量不开卡 */
          function importNoOpenSimExcel() {
              table.set();
                  top.layer.open({
                      type: 1,
                      area: ['400px', '230px'],
                      fix: false,
                      //不固定
                      maxmin: true,
                      shade: 0.3,
                      title: '导入批量不开卡数据',
                      content: $('#imporNoOpenSimExcel').html(),
                      btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
                      // 弹层外区域关闭
                      shadeClose: true,
                      btn1: function(index, layero){
                          var file = layero.find('#file').val();
                          if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                              $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                              return false;
                          }
                          var index = top.layer.load(2, {shade: false});
                          $.modal.disable();
                          var formData = new FormData(layero.find('form')[0]);
                          $.ajax({
                              url: prefix + '/importNoOpenSimData',
                              data: formData,
                              cache: false,
                              contentType: false,
                              processData: false,
                              timeout : 180000,
                              type: 'POST',
                              success: function (result) {
                                  if (result.code == web_status.SUCCESS) {
                                  	$.modal.close(index);
                                      $.modal.closeAll();
                                      $.modal.alertSuccess(result.msg);
                                      $.table.refresh();
                                  } else if (result.code == web_status.WARNING) {
                                  	$.modal.close(index);
                                      $.modal.enable();
                                      $.modal.alertWarning(result.msg)
                                  } else {
                                      $.modal.close(index);
                                      $.modal.enable();
                                      $.modal.alertError(result.msg);
                                  }
                              },
                              complete: function () {
                              	layero.find('#file').val('');
                              }
                          });
                      }
                  });
          }

          /* 导入运营数据 */
          function importStudentSimData() {
              table.set();
                  top.layer.open({
                      type: 1,
                      area: ['400px', '230px'],
                      fix: false,
                      //不固定
                      maxmin: true,
                      shade: 0.3,
                      title: '导入运营数据',
                      content: $('#imporData').html(),
                      btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
                      // 弹层外区域关闭
                      shadeClose: true,
                      btn1: function(index, layero){
                          var file = layero.find('#file').val();
                          if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                              $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                              return false;
                          }
                          var index = top.layer.load(2, {shade: false});
                          $.modal.disable();
                          var formData = new FormData(layero.find('form')[0]);
                          $.ajax({
                              url: prefix + '/importStudentSimData',
                              data: formData,
                              cache: false,
                              contentType: false,
                              processData: false,
                              timeout : 180000,
                              type: 'POST',
                              success: function (result) {
                                  if (result.code == web_status.SUCCESS) {
                                  	$.modal.close(index);
                                      $.modal.closeAll();
                                      $.modal.alertSuccess(result.msg);
                                      $.table.refresh();
                                  } else if (result.code == web_status.WARNING) {
                                  	$.modal.close(index);
                                      $.modal.enable();
                                      $.modal.alertWarning(result.msg)
                                  } else {
                                      $.modal.close(index);
                                      $.modal.enable();
                                      $.modal.alertError(result.msg);
                                  }
                              },
                              complete: function () {
                              	layero.find('#file').val('');
                              }
                          });
                      }
                  });
          }

          /* 导入批量未发起 */
          function importNoInitSimExcel() {
              table.set();
                  top.layer.open({
                      type: 1,
                      area: ['400px', '230px'],
                      fix: false,
                      maxmin: true,
                      shade: 0.3,
                      title: '导入批量未发起数据',
                      content: $('#imporNoInitSimExcel').html(),
                      btn: ['<i class="fa fa-check"></i> 导入', '<i class="fa fa-remove"></i> 取消'],
                      // 弹层外区域关闭
                      shadeClose: true,
                      btn1: function(index, layero){
                          var file = layero.find('#file').val();
                          if (file == '' || (!$.common.endWith(file, '.xls') && !$.common.endWith(file, '.xlsx'))){
                              $.modal.msgWarning("请选择后缀为 “xls”或“xlsx”的文件。");
                              return false;
                          }
                          var index = top.layer.load(2, {shade: false});
                          $.modal.disable();
                          var formData = new FormData(layero.find('form')[0]);
                          var config = {
                            url: prefix + "/importNoInitSim",
                            type: "post",
                            data: formData,
                            contentType: false,
                            processData: false,
                            success: function (result) {
                              if (result.code == web_status.SUCCESS) {
                                $.modal.closeAll();
                                $.table.refresh();
                                $.modal.alertSuccess(result.msg);
                              } else {
                                  $.modal.enable();
                                  $.modal.alertError(result.msg);
                              }
                              $.modal.close(index);
                            },
                            complete: function () {
                              	layero.find('#file').val('');
                            },
                          };
                          $.ajax(config);
                      }
                  });
          }
    </script>
  </body>
  <!--运营数据导入区域-->
  <script id="imporData" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                <a onclick="$.table.importNoOpenSimTemplate('/business/studentSim/importDataTemplate')" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
  </script>

  <!--批量开卡导入区域-->
  <script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                <a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：每次导入最多支持50条数据，仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
  </script>

  <!--批量不开卡导入区域-->
  <script id="imporNoOpenSimExcel" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                <a onclick="$.table.importNoOpenSimTemplate('/business/studentSim/importNoOpenSimTemplate')" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
  </script>

  <!--批量未发起导入区域-->
  <script id="imporNoInitSimExcel" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <div class="mt10 pt5">
                <a onclick="$.table.importTemplatePro('/business/studentSim/importNoInitSimTemplate')" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
            <font color="red" class="pull-left mt10">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </font>
        </div>
    </form>
  </script>
</html>
