<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('开卡统计数据')" />
    <style type="text/css">
        table.tftable {
            font-size: 12px;
            color: #333333;
            width: 100%;
            border-width: 1px;
            border-color: #729ea5;
            border-collapse: collapse;
        }

        table.tftable th {
            font-size: 12px;
            background-color: #9fe3f8;
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #729ea5;
            text-align: center;
        }

        table.tftable tr {
            background-color: #ffffff;
        }

        table.tftable td {
            font-size: 12px;
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #729ea5;
        }
        
        td {
            text-align: center;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <table id="tfhover" class="tftable" border="1">
                <tr>
                    <th>总报名人数</th>
                    <th>预登记人数</th>
                    <th>已给补贴人数</th>
                    <th>未给补贴人数</th>
                    <th>已成功激活人数</th>
                    <th>未成功激活人数</th>
                    <th>已发起开卡人数</th>
                    <th>未发起开卡人数</th>
                    <th>重新派卡人数</th>
                    <th>外呼开卡失败人数</th>
                <tr>
                    <td th:text="${studentSimCount.registeCount}">0</td>
                    <td th:text="${studentSimCount.prepareRegisteCount}">0</td>
                    <td th:text="${studentSimCount.activeSuccessCount}">0</td>
                    <td th:text="${studentSimCount.subsidySuccessCount}">0</td>
                    <td th:text="${studentSimCount.subsidyFailCount}">0</td>
                    <td th:text="${studentSimCount.activeFailCount}">0</td>
                    <td th:text="${studentSimCount.openSimCount}">0</td>
                    <td th:text="${studentSimCount.unOpenSimCount}">0</td>
                    <td th:text="${studentSimCount.resetSimCount}">0</td>
                    <td th:text="${studentSimCount.callOutFailCount}">0</td>
                </tr>
            </table>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">

        var prefix = ctx + "business/studentSim";

        $(function() {
            
        });

    </script>
</body>
</html>

<script type="text/javascript">
    window.onload = function () {
        var tfrow = document.getElementById('tfhover').rows.length;
        var tbRow = [];
        for (var i = 1; i < tfrow; i++) {
            tbRow[i] = document.getElementById('tfhover').rows[i];
            tbRow[i].onmouseover = function () {
                this.style.backgroundColor = '#e6e6e6';
            };
            tbRow[i].onmouseout = function () {
                this.style.backgroundColor = '#ffffff';
            };
        }
    };
</script>