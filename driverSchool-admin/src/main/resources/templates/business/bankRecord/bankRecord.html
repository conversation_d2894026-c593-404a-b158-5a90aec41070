<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('银行交易流水列表')" />
    <link
      href="../static/css/bootstrap.min.css"
      th:href="@{/css/bootstrap.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/font-awesome.min.css"
      th:href="@{/css/font-awesome.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/animate.min.css"
      th:href="@{/css/animate.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/style.min.css"
      th:href="@{/css/style.min.css}"
      rel="stylesheet"
    />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>
                <li>
                  <label>分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="branchId"
                    name="branchId"
                    class="branchId form-control m-b"
                    data-first-title="选择分校"
                  ></select>
                </li>
                <li>
                  <label>报名点：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="registrationId"
                    name="registrationId"
                    class="registrationId form-control m-b"
                    data-first-title="所属报名点"
                  ></select>
                </li>
                <li>
                  <label>学员姓名：</label>
                  <input type="text" name="studentName" />
                </li>
                <li>
                  <label>交易金额：</label>
                  <input type="text" name="amount" />
                </li>
                <li>
                  <label>消息提示：</label>
                  <input type="text" name="errMsg" />
                </li>
                <li>
                  <label>银行单号：</label>
                  <input type="text" name="bankOrderId" />
                </li>
                <li>
                  <label>系统单号：</label>
                  <input type="text" name="orderId" />
                </li>
                <li>
                  <label>类型：</label>
                  <select name="type">
                    <option value="">请选择</option>
                    <option value="PAY">支付</option>
                    <option value="WITHDRAW">提现</option>
                    <option value="LIXI">结息</option>
                    <option value="DIVSION">分账</option>
                  </select>
                </li>
                <li class="select-time">
                  <label style="width: 100px">交易时间： </label>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input"
                    id="beginPayTime"
                    placeholder="开始时间"
                    name="params[beginPayTime]"
                  />
                  <span>-</span>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input"
                    id="endPayTime"
                    placeholder="结束时间"
                    name="params[endPayTime]"
                  />
                </li>
                <li>
                  <label>交易状态：</label>
                  <select
                          name="status"
                          id="status"
                  >
                    <option value="">所有</option>
                    <option value=true>成功</option>
                    <option value=false>失败</option>
                  </select>
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <!--<a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="business:bankRecord:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="business:bankRecord:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="business:bankRecord:remove">
                <i class="fa fa-remove"></i> 删除
            </a>-->
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:bankRecord:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:bankRecord:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:bankRecord:remove')}]];
      var prefix = ctx + "business/bankRecord";

      $(function () {
          let typeMap = {
              "PAY": "支付",
              "WITHDRAW": "提现",
              "LIXI": "结息",
              "DIVSION":"分账"
          }
          var options = {
              url: prefix + "/list",
              exportUrl: prefix + "/export",
              modalName: "银行交易流水",
              columns: [{
                  checkbox: true
              },
                  {
                      field: 'bankOrderId',
                      title: '银行单号'
                  },
                  {
                      field: 'orderId',
                      title: '系统单号'
                  },
                  {
                      field: 'studentName',
                      title: '学员姓名'
                  },
                  {
                      field: 'schoolName',
                      title: '驾校'
                  },
                  {
                      field: 'branchName',
                      title: '分校'
                  },
                  {
                      field: 'registrationName',
                      title: '报名点'
                  },
                  {
                      field: 'amount',
                      title: '交易金额(元)'
                  },
                  {
                      field: 'type',
                      title: '类型',
                      formatter: function (value, row, index) {
                          return typeMap[value];
                      }
                  },
                  {
                      field: 'status',
                      title: '交易状态',
                      formatter: function (value, row, index) {
                          if (value) {
                              return `<span class=\"label label-primary\">成功</span>`;
                          } else {
                              return `<span class=\"label label-danger\">失败</span>`;
                          }
                      }
                  },
                  /* {
                      field: 'bankStatus',
                      title: '银行状态',
                      formatter: function (value, row, index) {
                          if (value === '0' || value === 'TRADE_SUCCESS' || value === 'SUCCESS') {
                              return `<span class=\"label label-primary\">成功</span>`;
                          } else {
                              return `<span class=\"label label-danger\">失败</span>`;
                          }
                      }
                  },*/
                  {
                      field: 'createTime',
                      title: '交易时间'
                  },
                  {
                      field: 'errMsg',
                      title: '消息提示'
                  },
                  /*{
                      field: 'fromJson',
                      title: '提交的json表单'
                  },*/
                  /*{
                      field: 'extendData',
                      title: '拓展数据，因为每个类型需要的都不一样'
                  },*/
                  /*{
                      title: '操作',
                      align: 'center',
                      formatter: function (value, row, index) {
                          var actions = [];
                          actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.studentId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                          actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.studentId + '\')"><i class="fa fa-remove"></i>删除</a>');
                          return actions.join('');
                      }
                  }*/]
          };
          $.table.init(options);


          //加载驾校、分校
          $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
          $('#cxSelectSchool').cxSelect({
              selects: ['schoolId', 'branchId', 'registrationId'],
              jsonValue: 'v',
          });

        /** 初始化时间组件 */
        layui.use("laydate", function () {
            var laydate = layui.laydate;
            laydate.render({
            elem: "#beginPayTime",
            type: "datetime",
            trigger: "click",
            });
            laydate.render({
            elem: "#endPayTime",
            type: "datetime",
            trigger: "click",
            });
        });
      });

      // 导出数据
      function exportSelected() {
        var params = $("#formId").serialize();
        var ids = $.table.selectColumns("id");
        var tipMsg = "确定导出所有数据吗？";
        if($.common.isNotEmpty(ids)){
            tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
            params += "&ids="+ids;
        }
        $.modal.confirm(tipMsg, function() {
            var config = {
              url: prefix + "/export",
              type: "post",
              dataType: "json",
              data: params,
              beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
              },
              success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
              },
            };
            $.ajax(config);
        });
      }
    </script>
  </body>
</html>
