<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('训练场地列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>

                <li>
                  <label>分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="branchId"
                    name="branchId"
                    class="branchId form-control m-b"
                    data-first-title="选择分校"
                  ></select>
                </li>
                <li>
                  <label style="width: 100px">训练场名称：</label>
                  <input type="text" name="name" />
                </li>
                <li>
                  <label style="width: 100px">备案号：</label>
                  <input type="text" name="recNo" />
                </li>
                <li>
                  <label>状态：</label>
                  <select
                    name="status"
                    th:with="type=${@dict.getType('t_school_status')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label  style="width: 110px">二维码审核状态：</label>
                  <select
                          name="qrcodeAuditStatus"
                          th:with="type=${@dict.getType('qrcode_audit_status')}"
                  >
                    <option value="">所有</option>
                    <option
                            th:each="dict : ${type}"
                            th:text="${dict.dictLabel}"
                            th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li class="select-time">
                  <label style="width: 120px; display: inline-block"
                  >二维码审核时间：
                  </label>
                  <input
                          style="width: 135px"
                          type="text"
                          class="layui-input dateTime"
                          id="beginLastAuditTime"
                          placeholder="开始时间"
                          name="params[beginLastAuditTime]"
                  />
                  <span>-</span>
                  <input
                          style="width: 135px"
                          type="text"
                          class="layui-input dateTime"
                          id="endLastAuditTime"
                          placeholder="结束时间"
                          name="params[endLastAuditTime]"
                  />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-success"
            onclick="add()"
            shiro:hasPermission="business:trainingGround:add"
          >
            <i class="fa fa-plus"></i> 添加
          </a>
          <a
            class="btn btn-primary single disabled"
            onclick="edit()"
            shiro:hasPermission="business:trainingGround:edit"
          >
            <i class="fa fa-edit"></i> 修改
          </a>
          <a
            class="btn btn-danger multiple disabled"
            onclick="$.operate.removeAll()"
            shiro:hasPermission="business:trainingGround:remove"
          >
            <i class="fa fa-remove"></i> 删除
          </a>
          <a
            class="btn btn-warning single disabled"
            onclick="view('',1300,'')"
            shiro:hasPermission="business:trainingGround:view"
          >
            查看
          </a>
          <a
            class="btn btn-success"
            onclick="exportSelected()"
            shiro:hasPermission="business:trainingGround:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
          <a class="btn btn-info"
             onclick="$.table.importExcel()"
             shiro:hasPermission="business:trainingGround:import"
          >
            <i class="fa fa-upload"></i> 导入
          </a>
          <a class="btn btn-info multiple disabled"
             onclick="submitAudit()"
             shiro:hasPermission="business:trainingGround:submitaudit"
          >
            <i class="fa fa-address-book"></i> 提交二维码审核申请
          </a>
          <a class="btn btn-warning multiple disabled"
             onclick="audit()"
             shiro:hasPermission="business:trainingGround:audit"
          >
            <i class="fa fa-address-book"></i> 审核二维码
          </a>
          <a class="btn btn-warning multiple disabled"
             onclick="downloadQrcode()"
             shiro:hasPermission="business:trainingGround:downloadqrcode"
          >
            <i class="fa fa-address-book"></i> 下载二维码
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:trainingGround:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:trainingGround:remove')}]];
        var statusDatas = [[${@dict.getType('t_school_status')}]];
        var qrcodeAuditStatus = [[${@dict.getType('qrcode_audit_status')}]];
        var prefix = ctx + "business/trainingGround";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                detailUrl: prefix + "/view/{id}",
                exportUrl: prefix + "/export",
                importTemplateUrl: prefix + "/importTemplate",
                downloadOfficialTemplate: prefix+"/downloadOfficialTemplate",
                importUrl: prefix + "/importData",
                submitAuditUrl: prefix + "/submitAudit",
                auditStatusUrl: prefix+"/audit",
                downloadQrcode: prefix+"/downloadQrcode",
                modalName: "训练场地",
                showPageGo: true,
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'name',
                    title: '训练场名称'
                },
                  {
                    field: 'simpleName',
                    title: '场地简称'
                  },
                  {
                    field: 'licenseTypes',
                    title: '培训车型',
                    formatter: function(value, row, index) {
                      // 检查 value 是否为 undefined 或者空字符串
                      if (typeof value === 'undefined' || value === '') {
                        return ''; // 或者返回一个默认值，比如 'N/A' 或者 ''
                      }

                      try {
                        var json = JSON.parse(value);
                      } catch (e) {
                        console.error('Parsing error:', e);
                        return ''; // 或者返回一个错误信息，比如 'Invalid JSON'
                      }

                      var actions = [];
                      $.each(json, function(i, item) {
                        var licenseType = item;
                        if ($.common.isNotEmpty(licenseType)) {
                          actions.push(licenseType);
                        }
                      });
                      return actions.join('，');
                    }
                  },
                {
                    field: 'contact',
                    title: '联系人'
                },
                {
                    field: 'tel',
                    title: '联系电话'
                },

                {
                    field: 'address',
                    title: '地址'
                },

                    {
                        field: 'schoolId',
                        title: '驾培机构名称',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.school != null){
                                    return row.school.name;
                                }
                            }
                        }
                    },

                    {
                        field: 'branchId',
                        title: '所属分校',
                        formatter: function(value, row, index) {
                            if ($.common.isNotEmpty(value)){
                                if (row.branch != null){
                                    return row.branch.name;
                                }
                            }
                        }
                    },

                {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                  {
                    field: 'qrcodeAuditStatus',
                    title: '二维码申请状态',
                    formatter: function (value,row,index){
                      return $.table.selectDictLabel(qrcodeAuditStatus,value);
                    }
                  },
                  {
                    field: 'lastAuditTime',
                    title: '最近审核时间'
                  },
                  {
                    field: 'recNo',
                    title: '备案号'
                  },
                  {
                    field: 'recDate',
                    title: '备案时间'
                  }

                ]
            };
            $.table.init(options);

            //加载驾校、分校
            $.cxSelect.defaults.url = ctx + "business/school/schoolData";
            $('#cxSelectSchool').cxSelect({
                selects: ['schoolId', 'branchId'],
                jsonValue: 'v',
            });

        });


        function add(id) {
            table.set();
            $.modal.open("添加" + table.options.modalName, $.operate.addUrl(id),1300);
        }

        function edit(id) {
            table.set();
            if ($.common.isEmpty(id) && table.options.type == table_type.bootstrapTreeTable) {
                var row = $("#" + table.options.id).bootstrapTreeTable('getSelections')[0];
                if ($.common.isEmpty(row)) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                var url = table.options.updateUrl.replace("{id}", row[table.options.uniqueId]);
                $.modal.open("修改" + table.options.modalName, url);
            } else {
                $.modal.open("修改" + table.options.modalName, $.operate.editUrl(id),1300);
            }
        }

        function view(id,width, height) {
            table.set();
            var _url = $.operate.detailUrl(id);
            var options = {
                title: table.options.modalName + "详细",
                width: width,
                height: height,
                url: _url,
                skin: 'layui-layer-gray',
                btn: ['关闭'],
                yes: function (index, layero) {
                    $.modal.close(index);
                }
            };
            $.modal.openOptions(options);
        }
        //驾校提交审核申请
        function submitAudit(){
          table.set();
          var rows = $.common.isEmpty(table.options.uniqueId)
                  ? $.table.selectFirstColumns()
                  : $.table.selectColumns(table.options.uniqueId);
          if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
          }
          var dataList = $('#bootstrap-table').bootstrapTable('getSelections');
          var flag = false;
          //已提交审核的数据不能重复审核
          try {
            dataList.forEach(function(item) {
              if(item.qrcodeAuditStatus ===1) {
                flag = true;
                throw new Error('auditStatus error')
              }
            });
          }catch (e){

          }
          if(flag) {
            $.modal.alertWarning("选中的行记录中，存在一条申请状态为【待审核】的数据，请勿重复提交申请");
            return;
          }
          $.modal.confirm(
                  "确认要提交" + rows.length + "条数据来申请审核吗?",
                  function () {
                    var url = table.options.submitAuditUrl;
                    var data = { ids: rows.join() };
                    $.operate.submit(url, "post", "json", data);
                  }
          );
        }

        //管理员进行审核
        function audit(){
          table.set();
          var rows = $.common.isEmpty(table.options.uniqueId)
                  ? $.table.selectFirstColumns()
                  : $.table.selectColumns(table.options.uniqueId);
          if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
          }
          //获取选中行
          var dataList = $('#bootstrap-table').bootstrapTable('getSelections');
          var flag = false;
          //已提交审核的数据不能重复审核
          try {
            dataList.forEach(function(item) {
              if(item.qrcodeAuditStatus !== 1) {
                flag = true;
                throw new Error('auditStatus error')
              }
            });
          }catch (e){

          }
          if(flag) {
            $.modal.alertWarning("选中的行记录中，存在申请状态不是【待审核】的数据，请确认。");
            return;
          }
          layer.open({
            id:'001',
            title: '批量审核二维码申请',
            area: ['500px', '300px'],
            content:  "即将对" + rows.length + "条数据进行审核", //这里content是一个普通的String
            btn: ['批量合格','批量不合格','关闭'],
            yes:function (index){
              $.modal.confirm(
                      "确定要将所选的数据审核为【通过】吗？",
                      function () {
                        var url = table.options.auditStatusUrl;
                        var data = { ids: rows.join(),auditStatus: 2 };
                        $.operate.submit(url, "post", "json", data);
                      }
              );
              layer.closeAll();
            },
            btn2:function (index) {
              $.modal.confirm(
                      "确定要将所选的数据审核为【不合格】吗？",
                      function () {
                        var url = table.options.auditStatusUrl;
                        var data = { ids: rows.join(),auditStatus: 3 };
                        $.operate.submit(url, "post", "json", data);
                      }
              );
              layer.closeAll();
            },
            btn3: function (index, layero) {
              //关闭弹窗
              $.modal.close(index);
            }
          });
        }

        //导出二维码
        function downloadQrcode(){
          table.set();
          var rows = $.common.isEmpty(table.options.uniqueId)
                  ? $.table.selectFirstColumns()
                  : $.table.selectColumns(table.options.uniqueId);
          if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
          }
          //获取选中行
          var dataList = $('#bootstrap-table').bootstrapTable('getSelections');
          var flag = false;
          //已提交审核的数据不能重复审核
          try {
            dataList.forEach(function(item) {
              if(item.qrcodeAuditStatus !== 2) {
                flag = true;
                throw new Error('auditStatus error')
              }
            });
          }catch (e){

          }
          if(flag) {
            $.modal.alertWarning("只能导出二维码审核状态为【通过】的图片");
            return;
          }
          $.modal.confirm(
                  "确认要下载" + rows.length + "条数据来申请审核吗?",
                  function () {
                    //loading层
                    var loadingIndex = layer.load(2, { //icon支持传入0-2
                      shade: [0.5, '#fff'], //0.5透明度的灰色背景
                      content: '正在为您导出二维码图片，图片较大，请耐心等候...',
                      success: function (layero) {
                        layero.find('.layui-layer-content').css({
                          'padding-top': '39px',
                          'width': '160px'
                        });
                      }
                    });
                    $.ajax({
                      type: "post",
                      url:  table.options.downloadQrcode,
                      data: {
                        "ids":rows.join()
                      },
                      xhrFields: {
                        responseType: 'blob'  // 告诉 jQuery 把响应作为二进制数据（Blob）
                      },
                      success: function (blob, status, xhr) {
                        // 关闭 layui 遮罩层
                        layer.close(loadingIndex);

                        // 从 Content-Disposition 获取文件名
                        var disposition = xhr.getResponseHeader('Content-Disposition');
                        var fileName = disposition ? disposition.split('filename=')[1] : 'downloaded-file';

                        // 如果文件名包含引号，移除引号
                        if (fileName && fileName.indexOf('"') === 0) {
                          fileName = fileName.substring(1, fileName.length - 1);
                        }

                        // 创建 Blob URL
                        var url = window.URL.createObjectURL(blob);

                        // 创建临时下载链接
                        var a = document.createElement('a');
                        a.href = url;
                        a.download = fileName;
                        document.body.appendChild(a);
                        a.click();  // 触发下载
                        document.body.removeChild(a);  // 下载完成后移除临时链接
                        window.URL.revokeObjectURL(url);  // 释放内存
                      },
                      error: function (xhr, status, error) {
                        // 关闭 layui 遮罩层
                        layer.close(loadingIndex);
                        console.error("文件下载失败：", error);
                      }
                    });
                  }
          );
        }

        // 导出数据
        function exportSelected() {
            var ids = $.table.selectColumns("id");
            var schoolId = $('#schoolId').val(); //驾校id
            var branchId = $('#branchId').val(); //分校id
            var name = $("input[name='name']").val(); //训练场名称
            var status = $('select[name="status"]').val(); //是否审核

            let fromData = new FormData();
            fromData.append('schoolId', schoolId != null ? schoolId : '');
            fromData.append('branchId', branchId != null ? branchId : '' );
            fromData.append('name', name != null ? name : '');
            fromData.append('status', status != null ? status : '');

            var tipMsg = "确定导出所有数据吗？";
            if($.common.isNotEmpty(ids)){
                tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
                fromData.append('ids', ids);
            }
            $.modal.confirm(tipMsg, function() {
                $.ajax({
                    url: prefix + "/export",
                    data: fromData,
                    type: "post",
                    processData: false,
                    contentType: false,
                    async: true,
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    }
                })
            });
        }

        /* 查看训练场详情 */
      $('#bootstrap-table').on('dbl-click-row.bs.table', function (e, row, $element) {
        table.set();
          var _url = $.operate.detailUrl(row.id);
          var options = {
              title: table.options.modalName + "详细",
              width: "1300",
              height: "",
              url: _url,
              skin: 'layui-layer-gray',
              btn: ['关闭'],
              yes: function (index, layero) {
                  $.modal.close(index);
              }
          };
          $.modal.openOptions(options);
      });
        // 下载模板
        function importTemplateByUrl() {
          $.get(
                  activeWindow().options.downloadOfficialTemplate,
                  function (result) {
                    if (result.code == web_status.SUCCESS) {
                      window.location.href =
                              ctx +
                              "common/download?fileName=" +
                              encodeURI(result.msg) +
                              "&delete=" +
                              true;
                    } else if (result.code == web_status.WARNING) {
                      $.modal.alertWarning(result.msg);
                    } else {
                      $.modal.alertError(result.msg);
                    }
                  }
          );
        }
    </script>
  </body>
  <!-- 导入区域 -->
  <script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
      <div class="col-xs-offset-1">
        <input type="file" id="file" name="file"/>
        <div class="mt10 pt5">
          <a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
        </div>
        <font color="red" class="pull-left mt10">
          提示：仅允许导入“xls”或“xlsx”格式文件！
        </font>
      </div>
    </form>
  </script>
<style>
  th,tr {
    text-align: center;
  }
</style>
</html>
