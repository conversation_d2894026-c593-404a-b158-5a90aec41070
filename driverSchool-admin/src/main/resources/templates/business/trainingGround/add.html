<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增训练场地')" />
    <th:block th:include="include :: upload-img-css" />
    <th:block th:include="include :: datetimepicker-css" />
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJsCode:'7a9e030c8ccd0c69f4dc506c177e53f9',
        }
    </script>
    <script src="https://webapi.amap.com/maps?v=2.0&key=e7be917da311ee7c80cf7865191e1ca4&plugin=AMap.PolygonEditor"></script>
    <script src="https://a.amap.com/jsapi_demos/static/demo-center/js/demoutils.js"></script>
    <link th:href="@{/css/wind_info_css.css}" rel="stylesheet"/>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-trainingGround-add">
            <input id="longitude" name="longitude"  class="form-control" type="hidden">
            <input id="latitude" name="latitude"  class="form-control" type="hidden">

            <div class="row">
                <div class="col-sm-12" style="height: 680px;">
                    <div class="col-sm-7">
                        <div id="container"></div>
                    </div>
                    <div class="col-sm-5">
                        <h4 class="form-header h4">训练场管理信息</h4>
                        <div id="cxSelectSchool" class="form-group">
                            <label class="col-sm-3 control-label is-required">所属学校：</label>
                            <div class="col-sm-4">
                                <select id="schoolId" name="schoolId" class="schoolId form-control m-b" data-first-title="所属驾校" required>
                                </select>
                            </div>
                            <div class="col-sm-4">
                                <select id="branchId" name="branchId" class="branchId form-control m-b" data-first-title="所属分校">
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">训练场名称：</label>
                            <div class="col-sm-8">
                                <input id="name" name="name" class="form-control" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">训练场简称：</label>
                            <div class="col-sm-8">
                                <input id="simpleName" name="simpleName" class="form-control" type="text" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required"
                            >培训车型：</label
                            >
                            <div
                                    class="col-sm-8"
                                    th:with="type=${@dict.getType('license_types')}"
                            >
                                <label th:each="dict : ${type}" class="check-box">
                                    <input
                                            name="licenseTypes"
                                            type="checkbox"
                                            th:value="${dict.dictValue}"
                                            th:text="${dict.dictLabel}"
                                            required
                                    />
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">联系人：</label>
                            <div class="col-sm-8">
                                <input id="contact" name="contact" class="form-control" type="text"  maxlength="20">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">联系电话：</label>
                            <div class="col-sm-8">
                                <input name="tel" class="form-control" type="text" placeholder="固话或者手机号码"  >
                            </div>
                        </div>

                        <div id="cxSelect" class="form-group">
                            <label class="col-sm-3 control-label is-required">地址：</label>
                            <div class="col-sm-3">
                                <select id="province" name="province" class="province form-control" data-first-title="所属省份" required>
                                    <option value="广东省" selected>广东省</option>
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <select id="city" name="city" class="city form-control" data-first-title="所属城市" required>
                                    <option value="东莞市" selected>东莞市</option>
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <select id="town" name="town" class="town form-control" data-first-title="所属县/镇/区" required>
                                    <option value="">所属县/镇/区</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">具体地址：</label>
                            <div class="col-sm-8">
                                <input id="address" name="address" class="form-control" type="text" required>
                            </div>
                            <button id="location" class="btn btn-danger btn-circle" type="button"><i class="fa fa-map-marker"></i></button>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">备案号：</label>
                            <div class="col-sm-8">
                                <input name="recNo"  class="form-control" type="text" placeholder="请输入备案号">
                            </div>
                        </div>


                        <div class="form-group date">
                            <label class="col-sm-3 control-label">备案时间：</label>
                            <div class="col-sm-8">
                                <input
                                        id="recDate"
                                        name="recDate"
                                        class="form-control"
                                        data-date-format="YYYY-MM-DD"
                                        placeholder="请选择备案时间"
                                        type="text"
                                />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">图片：</label>
                            <div class="col-sm-8">
                                <div class="image-box">
                                </div>
                                <a class="btn btn-success" href="javascript:addImage();">
                                    <label>选择文件</label>
                                    <input id="add-input" type="file" accept="image/*" style="display: none" onchange="selectImage(this);">
                                </a>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">状态：</label>
                            <div class="col-sm-8">
                                <select name="status" class="form-control m-b" th:with="type=${@dict.getType('t_school_status')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: upload-img-js" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:src="@{/ruoyi/map.js}"></script>
    <script th:inline="javascript">
        var prefix = ctx + "business/trainingGround"
        var maxImage = 5;
        var currentla ;
        var currentlg ;
        var address ;
        var circle ;
        var circleName ;
        var circleId ;
        var la ;
        var lg ;
        var center;

        //日期格式化
        $("input[name='recDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("#form-trainingGround-add").validate({
            onkeyup: false,
            focusCleanup: true,
            rules:{
                name:{
                    maxlength:60,
                    remote: {
                        url: prefix + "/checkUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "schoolId":function () {
                                return $.common.trim($.form.selectSelects("schoolId"));
                            },
                            "branchId":function () {
                                return $.common.trim($.form.selectSelects("branchId"));
                            },
                            "name": function() {
                                return $.common.trim($("#name").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },
                'tel':{
                    isPhoneOrTel: true
                }
            },
            messages: {
                "name": {
                    remote: "该训练场名称已经存在"
                }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                submitCustom("/add","schoolImages");
            }
        }

        $(function() {
            //加载驾校、分校
            $.cxSelect.defaults.url = ctx + "business/school/schoolData";
            $('#cxSelectSchool').cxSelect({
                selects: ['schoolId', 'branchId'],
                jsonValue: 'v',
            });
            // 加载省市区
            $.cxSelect.defaults.url = ctx + "business/area/areaData";
            $('#cxSelect').cxSelect({
                selects: ['province', 'city', 'town'],
                nodata: 'none'
            });

        });


    </script>
</body>
</html>