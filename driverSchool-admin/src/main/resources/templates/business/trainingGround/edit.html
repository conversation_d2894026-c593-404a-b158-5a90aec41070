<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改训练场地')" />
    <th:block th:include="include :: upload-img-css" />
    <th:block th:include="include :: datetimepicker-css" />
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJsCode:'7a9e030c8ccd0c69f4dc506c177e53f9',
        }
    </script>
    <script src="https://webapi.amap.com/maps?v=2.0&key=e7be917da311ee7c80cf7865191e1ca4&plugin=AMap.PolygonEditor"></script>
    <script src="https://a.amap.com/jsapi_demos/static/demo-center/js/demoutils.js"></script>
    <link th:href="@{/css/wind_info_css.css}" rel="stylesheet"/>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-trainingGround-edit" th:object="${schoolTrainingGround}">
            <input name="id" th:field="*{id}" type="hidden">
            <input name="longitude" th:field="*{longitude}" type="hidden" >
            <input name="latitude" th:field="*{latitude}"  type="hidden">
            <input th:field="*{schoolId}" type="hidden" th:if="*{school != null}">
            <input th:field="*{branchId}" type="hidden" th:if="*{branch != null}">
            <div class="row">
                <div class="col-sm-12" style="height: 680px;">
                    <div class="col-sm-7">
                        <div id="container"></div>
                    </div>
                    <div class="col-sm-5">
                        <h4 class="form-header h4">训练场管理信息</h4>

                        <div class="form-group" th:if="*{school != null}">
                            <label class="col-sm-3 control-label">所属学校：</label>
                            <div class="col-sm-8">
                                <input  class="form-control" type="text" disabled="disabled"  th:field="*{school.name}">
                            </div>
                        </div>

                        <div class="form-group" th:if="*{branch != null}">
                            <label class="col-sm-3 control-label">所属分校：</label>
                            <div class="col-sm-8">
                                <input  class="form-control" type="text" disabled="disabled" th:field="*{branch.name}">
                            </div>
                        </div>

                        <div id="cxSelectSchool" class="form-group" th:if="*{school == null and branch == null}">
                            <label class="col-sm-3 control-label is-required">所属学校：</label>
                            <div class="col-sm-4">
                                <select id="schoolId" name="schoolId" class="schoolId form-control m-b" data-first-title="所属驾校" required>
                                </select>
                            </div>
                            <div class="col-sm-4">
                                <select id="branchId" name="branchId" class="branchId form-control m-b" data-first-title="所属分校">
                                </select>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-sm-3 control-label">训练场名称：</label>
                            <div class="col-sm-8">
                                <input name="name" th:field="*{name}" class="form-control" type="text" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">训练场简称：</label>
                            <div class="col-sm-8">
                                <input id="simpleName" name="simpleName" th:field="*{simpleName}" class="form-control" type="text" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required"
                            >培训车型：</label
                            >
                            <div class="col-sm-8">
                                <label th:each="dict : ${licenseTypeList}" class="check-box">
                                    <input
                                            name="licenseTypes"
                                            type="checkbox"
                                            th:value="${dict.dictValue}"
                                            th:text="${dict.dictLabel}"
                                            th:checked="${dict.flag}"
                                            required
                                    />
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">联系人：</label>
                            <div class="col-sm-8">
                                <input name="contact" th:field="*{contact}" class="form-control" type="text"  maxlength="20">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">联系电话：</label>
                            <div class="col-sm-8">
                                <input name="tel" th:field="*{tel}" class="form-control" type="text" placeholder="固话或者手机号码">
                            </div>
                        </div>

                        <div id="cxSelect" class="form-group">
                            <label class="col-sm-3 control-label is-required">地址：</label>
                            <div class="col-sm-3">
                                <select id="province" name="province" class="province form-control" data-first-title="所属省份" required>
                                    <option value="">所属省份</option>
                                    <option th:if="*{province != null and province != ''}" th:value="*{province}" th:text="*{province}" selected></option>
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <select id="city" name="city" class="city form-control" data-first-title="所属城市" required>
                                    <option value="">所属城市</option>
                                    <option th:if="*{city != null and city != ''}" th:value="*{city}" th:text="*{city}" selected></option>
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <select id="town" name="town" class="town form-control" data-first-title="所属县/镇/区">
                                    <option value="">所属县/镇/区</option>
                                    <option th:if="*{town != null and town != ''}" th:value="*{town}" th:text="*{town}" selected></option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">具体地址：</label>
                            <div class="col-sm-8">
                                <input name="address" th:field="*{address}" class="form-control" type="text" required>
                            </div>
                            <button id="location" class="btn btn-danger btn-circle" type="button"><i class="fa fa-map-marker"></i></button>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">备案号：</label>
                            <div class="col-sm-8">
                                <input name="recNo" th:field="*{recNo}" class="form-control" type="text" placeholder="请输入备案号">
                            </div>
                        </div>


                        <div class="form-group date">
                            <label class="col-sm-3 control-label">备案时间：</label>
                            <div class="col-sm-8">
                            <input
                                    id="recDate"
                                    name="recDate"
                                    class="form-control"
                                    th:value="*{#calendars.format(recDate,'yyyy-MM-dd')}"
                                    data-date-format="YYYY-MM-DD"
                                    placeholder="请选择备案时间"
                                    type="text"
                            />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">图片：</label>
                            <div class="col-sm-8">
                                <div class="image-box">
                                    <div class="image-item" th:each="file : *{imageFileList}" th:style="|background-image: url('${file.webPath}');|">
                                        <img class="delete-img" src="/img/delete.png" onclick="deleteImage(this);">
                                        <input type="hidden" name="imageIds" th:value="${file.id}">
                                    </div>
                                </div>
                                <a class="btn btn-success" href="javascript:addImage();">
                                    <label>选择图片</label>
                                    <input id="add-input" type="file" accept="image/*" style="display: none" onchange="selectImage(this);">
                                </a>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">状态：</label>
                            <div class="col-sm-8">
                                <select name="status" class="form-control m-b" th:field="*{status}" th:with="type=${@dict.getType('t_school_status')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: upload-img-js" />
    <th:block th:include="include :: jquery-cxselect-js" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:src="@{/ruoyi/map.js}"></script>

    <script th:inline="javascript">
        var prefix = ctx + "business/trainingGround";

        var maxImage = 5;
        var currentla ;
        var currentlg ;
        var address ;
        var circle ;
        var circleName ;
        var circleId ;

        var la =[[${schoolTrainingGround.longitude}]];
        var lg =[[${schoolTrainingGround.latitude}]];
        var center;

        if (la){
            //loadMapAddress(la,lg) ;
        }
        //日期格式化
        $("input[name='recDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("#form-trainingGround-edit").validate({
            onkeyup: false,
            focusCleanup: true,
            rules:{
                name:{
                    maxlength:60,
                    remote: {
                        url: prefix + "/checkUnique",
                        type: "post",
                        dataType: "json",
                        data: {
                            "id": function() {
                                return $.common.trim($("#id").val());
                            },
                            "schoolId":function () {
                                return $.common.trim($("#schoolId").val());
                            },
                            "branchId":function () {
                                return $.common.trim($("#branchId").val());
                            },
                            "name": function() {
                                return $.common.trim($("#name").val());
                            }
                        },
                        dataFilter: function(data, type) {
                            return $.validate.unique(data);
                        }
                    }
                },
                'tel':{
                    isPhoneOrTel: true
                }
            },
            messages: {
                "name": {
                    remote: "该训练场名称已经存在"
                }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                submitCustom("/edit","schoolImages");
            }
        }
        $(function() {

            //加载驾校、分校
            $.cxSelect.defaults.url = ctx + "business/school/schoolData";
            $('#cxSelectSchool').cxSelect({
                selects: ['schoolId', 'branchId'],
                jsonValue: 'v',
            });
            // 加载省市区
            $.cxSelect.defaults.url = ctx + "business/area/areaData";
            $('#cxSelect').cxSelect({
                selects: ['province', 'city', 'town'],
                nodata: 'none'
            });

        });
    </script>
</body>
</html>