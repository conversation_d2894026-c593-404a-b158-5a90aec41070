<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">

<head>
    <th:block th:include="include :: header('监管统计数据')" />
    <style type="text/css">
        table.tftable {
            font-size: 12px;
            color: #333333;
            width: 100%;
            border-width: 1px;
            border-color: #729ea5;
            border-collapse: collapse;
        }

        table.tftable th {
            font-size: 12px;
            background-color: #9fe3f8;
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #729ea5;
            text-align: center;
        }

        table.tftable tr {
            background-color: #ffffff;
        }

        table.tftable td {
            font-size: 12px;
            border-width: 1px;
            padding: 8px;
            border-style: solid;
            border-color: #729ea5;
        }
        
        td {
            text-align: center;
        }
    </style>
</head>

<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <table id="tfhover" class="tftable" border="1">
                <tr>
                    <th colspan="4"></th>
                    <th>总监管人数</th>
                    <th>总监管金额</th>
                    <th>总释放金额</th>
                    <th>待释放金额</th>
                    <th colspan="4"></th>
                <tr>
                    <td colspan="4"></td>
                    <td th:text="${superviseCount.superviseNumbers}">0</td>
                    <td th:text="${superviseCount.superviseFee}">0</td>
                    <td th:text="${superviseCount.releaseFee}">0</td>
                    <td th:text="${superviseCount.restSuperviseFee}">0</td>
                    <td colspan="4"></td>
                </tr>
                <tr>
                    <th colspan="3">科目一</th>
                    <th colspan="3">科目二</th>
                    <th colspan="3">科目三</th>
                    <th colspan="3">科目四</th>
                </tr>
                <tr>
                    <th>监管金额</th>
                    <th>释放金额</th>
                    <th>剩余释放金额</th>
                    <th>监管金额</th>
                    <th>释放金额</th>
                    <th>剩余释放金额</th>
                    <th>监管金额</th>
                    <th>释放金额</th>
                    <th>剩余释放金额</th>
                    <th>监管金额</th>
                    <th>释放金额</th>
                    <th>剩余释放金额</th>
                </tr>
                <tr>
                    <td th:text="${superviseCount.subject1SuperviseFee}">0</td>
                    <td th:text="${superviseCount.subject1AmountRelease}">0</td>
                    <td th:text="${superviseCount.subject1RestSuperviseFee}">0</td>
                    <td th:text="${superviseCount.subject2SuperviseFee}">0</td>
                    <td th:text="${superviseCount.subject2AmountRelease}">0</td>
                    <td th:text="${superviseCount.subject2RestSuperviseFee}">0</td>
                    <td th:text="${superviseCount.subject3SuperviseFee}">0</td>
                    <td th:text="${superviseCount.subject3AmountRelease}">0</td>
                    <td th:text="${superviseCount.subject3RestSuperviseFee}">0</td>
                    <td th:text="${superviseCount.subject4SuperviseFee}">0</td>
                    <td th:text="${superviseCount.subject4AmountRelease}">0</td>
                    <td th:text="${superviseCount.subject4RestSuperviseFee}">0</td>
                </tr>
            </table>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">

        $(function () {
            
        });

    </script>
</body>

</html>

<script type="text/javascript">
    window.onload = function () {
        var tfrow = document.getElementById('tfhover').rows.length;
        var tbRow = [];
        for (var i = 1; i < tfrow; i++) {
            tbRow[i] = document.getElementById('tfhover').rows[i];
            tbRow[i].onmouseover = function () {
                this.style.backgroundColor = '#e6e6e6';
            };
            tbRow[i].onmouseout = function () {
                this.style.backgroundColor = '#ffffff';
            };
        }
    };
</script>