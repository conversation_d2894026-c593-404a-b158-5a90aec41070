<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('驾校交易流水列表')" />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list">
              <ul>
                <li>
                  <label style="width: 39px; display: inline-block"
                    >驾校：</label
                  >
                  <select id="schoolId" name="schoolId">
                    <option value="">选择驾校</option>
                    <option
                      th:each="dict : ${schoolList}"
                      th:text="${dict.name}"
                      th:value="${dict.id}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label>学员：</label>
                  <input
                    type="text"
                    name="params[studentName]"
                    placeholder="输入学员姓名"
                  />
                </li>
                <li>
                  <label>身份证：</label>
                  <input
                    type="text"
                    name="params[identity]"
                    placeholder="身份证6位 多个用空格隔开"
                  />
                </li>
                <li class="select-time">
                  <label style="width: 50px">时间： </label>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input"
                    id="beginTime"
                    placeholder="开始时间"
                    name="params[beginTime]"
                  />
                  <span>-</span>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input"
                    id="endTime"
                    placeholder="结束时间"
                    name="params[endTime]"
                  />
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="$.form.reset()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <!-- <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="business:schoolDealFlow:add">
                    <i class="fa fa-plus"></i> 添加
                </a> -->
          <!-- <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="business:schoolDealFlow:edit">
                    <i class="fa fa-edit"></i> 修改
                </a> -->
          <!-- <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="business:schoolDealFlow:remove">
                    <i class="fa fa-remove"></i> 删除
                </a> -->
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:schoolDealFlow:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
      var editFlag = [[${@permission.hasPermi('business:schoolDealFlow:edit')}]];
      var removeFlag = [[${@permission.hasPermi('business:schoolDealFlow:remove')}]];
      var prefix = ctx + "business/schoolDealFlow";

      $(function() {
          var options = {
              url: prefix + "/list",
              createUrl: prefix + "/add",
              updateUrl: prefix + "/edit/{id}",
              removeUrl: prefix + "/remove",
              exportUrl: prefix + "/export",
              sortName: "createdTime",
              sortOrder: "desc",
              modalName: "驾校交易流水",
              columns: [{
                  checkbox: true
              },
              {
                  field: 'id',
                  title: '主键',
                  visible: false
              },
              {
                  field: 'schoolId',
                  title: '驾校名',
                  formatter: function (value, row, index) {
                      if ($.common.isNotEmpty(value)) {
                          if (row.school != null) {
                              return row.school.name;
                          }
                      }
                  }
              },
              {
                  field: 'createdTime',
                  title: '时间'
              },
              {
                  field: 'action',
                  title: '动作',
                  formatter: function (value, row, index) {
                      if (value == 0) {
                          return '释放';
                      }else if (value == 1) {
                          return '存入监管';
                      }else if(value==2){
                      	return '释放失败回滚';
                      }else if(value==3){
                      	return '存入监管失败回滚';
                      }
                  }
              },
              {
                  field: 'lastBalance',
                  title: '上期结余'
              },
              {
                  field: 'theAmt',
                  title: '本期发生额'
              },
              {
                  field: 'theBalance',
                  title: '本期结余'
              },
              {
                  field: 'bankInfo',
                  title: '银行信息'
              },
              {
                  field: 'bankCardNo',
                  title: '支付卡号'
              },
              {
                  field: 'targetOrderId',
                  title: '平台单号'
              },
              {
                  field: 'bankOrderId',
                  title: '交易流水'
              },
              {
                  field: 'studentId',
                  title: '关联学员',
                  formatter: function (value, row, index) {
                      console.log(row);
                      if ($.common.isNotEmpty(value)) {
                          if (row.schoolStudent != null) {
                              return row.schoolStudent.name;
                          }
                      }
                  }
              },
              {
                  field: 'studentId',
                  title: '身份证号',
                  formatter: function (value, row, index) {
                      if ($.common.isNotEmpty(value)) {
                          if (row.schoolStudent != null) {
                              return row.schoolStudent.identity;
                          }
                      }
                  }
              },
              {
                  field: 'relationSubject',
                  title: '关联科目'
              }]
          };
          $.table.init(options);
      });

      /** 初始化时间组件 */
      layui.use("laydate", function () {
            var laydate = layui.laydate;
            laydate.render({
            elem: "#beginTime",
            type: "datetime",
            trigger: "click",
            });
            laydate.render({
            elem: "#endTime",
            type: "datetime",
            trigger: "click",
            });
        });

      /* 导出数据 */
      function exportSelected() {
              var ids = $.table.selectColumns("id");
              var dataParam = $("#formId").serializeArray();
              var tipMsg = "确定导出所有数据吗？";
              if ($.common.isNotEmpty(ids)) {
                  tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
                  dataParam.push({"name": "ids", "value": ids});
              }
              $.modal.confirm(tipMsg, function () {
                  $.post(prefix + "/export", dataParam, function (result) {
                      if (result.code == web_status.SUCCESS) {
                          window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                      } else {
                          $.modal.alertError(result.msg);
                      }
                  });
              });
      }
    </script>
  </body>
</html>
