<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改每日留存人数')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-retainedNumber-edit" th:object="${retainedNumber}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">驾校id：</label>
                <div class="col-sm-8">
                    <input name="schoolId" th:field="*{schoolId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">统计日期：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="countTime" th:value="${#dates.format(retainedNumber.countTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">昨日监管人数：</label>
                <div class="col-sm-8">
                    <input name="yesterdaySuperviseCount" th:field="*{yesterdaySuperviseCount}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">当日导入监管人数：</label>
                <div class="col-sm-8">
                    <input name="todaySuperviseCount" th:field="*{todaySuperviseCount}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">当日释放人数：</label>
                <div class="col-sm-8">
                    <input name="todayReleaseCount" th:field="*{todayReleaseCount}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">留存人数：</label>
                <div class="col-sm-8">
                    <input name="retainedCount" th:field="*{retainedCount}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">当日留存人数比例：</label>
                <div class="col-sm-8">
                    <input name="todayRetainedRatio" th:field="*{todayRetainedRatio}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">是否结算：0=未结算，1=已结算：</label>
                <div class="col-sm-8">
                    <input name="isPay" th:field="*{isPay}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="createdTime" th:value="${#dates.format(retainedNumber.createdTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">更新时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="updatedTime" th:value="${#dates.format(retainedNumber.updatedTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "business/retainedNumber";
        $("#form-retainedNumber-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-retainedNumber-edit').serialize());
            }
        }

        $("input[name='countTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='createdTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='updatedTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>