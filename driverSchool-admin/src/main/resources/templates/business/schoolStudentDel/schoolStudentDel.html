<!DOCTYPE html>
<html
  lang="zh"
  xmlns:th="http://www.thymeleaf.org"
  xmlns:shiro="http://www.pollix.at/thymeleaf/shiro"
>
  <head>
    <th:block th:include="include :: header('学员删除列表')" />
    <th:block th:include="include :: select2-css"/>
    <link
      href="../static/css/bootstrap.min.css"
      th:href="@{/css/bootstrap.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/font-awesome.min.css"
      th:href="@{/css/font-awesome.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/animate.min.css"
      th:href="@{/css/animate.min.css}"
      rel="stylesheet"
    />
    <link
      href="../static/css/style.min.css"
      th:href="@{/css/style.min.css}"
      rel="stylesheet"
    />
  </head>
  <body class="gray-bg">
    <div class="container-div">
      <div class="row">
        <div class="col-sm-12 search-collapse">
          <form id="formId">
            <div class="select-list" id="cxSelectSchool">
              <ul>
                <li>
                  <label>驾校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="schoolId"
                    name="schoolId"
                    class="schoolId form-control m-b"
                    data-first-title="选择驾校"
                    required
                  ></select>
                </li>
                <li>
                  <label>分校：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="branchId"
                    name="branchId"
                    class="branchId form-control m-b"
                    data-first-title="选择分校"
                  ></select>
                </li>
                <li>
                  <label>报名点：</label>
                  <select
                    style="
                      margin-bottom: 0px;
                      font-family: inherit;
                      font-size: inherit;
                      line-height: inherit;
                    "
                    id="registrationId"
                    name="registrationId"
                    class="registrationId form-control m-b"
                    data-first-title="所属报名点"
                  ></select>
                </li>
                <li>
                  <label>姓名：</label>
                  <input type="text" name="name" />
                </li>
                <li>
                  <label>身份证号：</label>
                  <input
                    type="text"
                    name="params[identity]"
                    placeholder="多个身份证号使用空格进行分隔"
                  />
                </li>
                <li>
                  <label>手机号：</label>
                  <input type="text" name="mobile" />
                </li>
                <li>
                  <label>监管状态：</label>
                  <select
                    name="isSupervise"
                    th:with="type=${@dict.getType('student_is_supervise')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <label>是否审核：</label>
                  <select
                    name="isCheck"
                    th:with="type=${@dict.getType('student_is_check')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li class="select-time">
                  <label style="width: 100px">报名审核时间： </label>
                  <input
                    type="text"
                    class="time-input"
                    id="startTime"
                    placeholder="开始时间"
                    name="params[beginTime]"
                  />
                  <span>-</span>
                  <input
                    type="text"
                    class="time-input"
                    id="endTime"
                    placeholder="结束时间"
                    name="params[endTime]"
                  />
                </li>
                <li class="select-time">
                  <label style="width: 100px">预登记时间： </label>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input"
                    id="beginPrepareRegisteDate"
                    placeholder="开始时间"
                    name="params[beginPrepareRegisteDate]"
                  />
                  <span>-</span>
                  <input
                    style="width: 135px"
                    type="text"
                    class="layui-input"
                    id="endPrepareRegisteDate"
                    placeholder="结束时间"
                    name="params[endPrepareRegisteDate]"
                  />
                </li>
                <li>
                  <label>是否上报计时平台：</label>
                  <select
                    name="studyCenterIsSyn"
                    th:with="type=${@dict.getType('study_center_is_syn')}"
                  >
                    <option value="">所有</option>
                    <option
                      th:each="dict : ${type}"
                      th:text="${dict.dictLabel}"
                      th:value="${dict.dictValue}"
                    ></option>
                  </select>
                </li>
                <li>
                  <a
                    class="btn btn-primary btn-rounded btn-sm"
                    onclick="$.table.search()"
                    ><i class="fa fa-search"></i>&nbsp;搜索</a
                  >
                  <a
                    class="btn btn-warning btn-rounded btn-sm"
                    onclick="resetQueryForm()"
                    ><i class="fa fa-refresh"></i>&nbsp;重置</a
                  >
                </li>
              </ul>
            </div>
          </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
          <a
            class="btn btn-primary single disabled"
            onclick="view('',1300,'')"
            shiro:hasPermission="business:schoolStudentDel:view"
          >
            查看
          </a>
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:schoolStudentDel:export"
          >
            <i class="fa fa-download"></i> 导出
          </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
          <table id="bootstrap-table"></table>
        </div>
      </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js"/>
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('business:schoolStudentDel:edit')}]];
        var removeFlag = [[${@permission.hasPermi('business:schoolStudentDel:remove')}]];
        var identityTypeDatas = [[${@dict.getType('identity_type')}]];
        var genderDatas = [[${@dict.getType('sys_user_sex')}]];
        var nationDatas = [[${@dict.getType('nation_type')}]];
        var educationDatas = [[${@dict.getType('education')}]];
        var licenseTypeDatas = [[${@dict.getType('license_types')}]];
        var businessTypeDatas = [[${@dict.getType('business_type')}]];
        var oldLicenseTypeDatas = [[${@dict.getType('license_types')}]];
        var isSuperviseDatas = [[${@dict.getType('student_is_supervise')}]];
        var isQuitDatas = [[${@dict.getType('student_is_quit')}]];
        var studentStatusDatas =[[${@dict.getType('student_status')}]];
        var originDatas=[[${@dict.getType('origin_data')}]];
        var prefix = ctx + "business/schoolStudentDel";

        $(function() {
          //初始化搜索框
          $("#schoolId").select2({

          });
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                detailUrl: prefix + "/view/{id}",
                modalName: "学员删除",
                showPageGo: true,
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                    {
                        field: 'schoolId',
                        title: '驾校',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.school != null) {
                                    return row.school.name;
                                }
                            }
                        }
                    },
                    {
                        field: 'branchId',
                        title: '分校',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.branch != null) {
                                    return row.branch.name;
                                }
                            }
                        }
                    },
                    {
                        field: 'registrationId',
                        title: '报名点',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                if (row.registration != null) {
                                    return row.registration.name;
                                }
                            }
                        }
                    },
                {
                    field: 'name',
                    title: '学员'
                },
                    {
                        field: 'gender',
                        title: '性别',
                        formatter: function(value, row, index) {
                            return $.table.selectDictLabel(genderDatas, value);
                        }
                    },
                {
                    field: 'identity',
                    title: '身份证号',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            return basecclusion(value,6,4);
                        }
                    }
                },
                {
                    field: 'mobile',
                    title: '手机号',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(value)) {
                            return basecclusion(value,3,4);
                        }
                    }
                },


                {
                    field: 'licenseType',
                    title: '学车类型',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(licenseTypeDatas, value);
                    }
                },

                    {
                        field: 'originData',
                        title: '来源',
                        formatter: function(value, row, index) {
                            return $.table.selectDictLabel(originDatas, value);
                        }
                    },

                {
                    field: 'prepareRegisteDate',
                    title: '预登记时间'
                 },

                {
                    field: 'registeDate',
                    title: '报名审核时间'
                },
                {
                    field: 'status',
                    title: '学习状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(studentStatusDatas, value);
                    }
                },
                    {
                        field: 'studyCenterIsSyn',
                        title: '上报计时平台',
                        formatter: function(value, row, index) {
                            if(value == 0){
                                return "否";
                            }else if (value == 1) {
                                return "是";
                            }else{
                                return "-";
                            }
                        }
                    },
                    {
                        field: 'isCheck',
                        title: '是否审核',
                        formatter: function(value, row, index) {
                            if(value == 0){
                                return "未审核";
                            }else if (value == 1) {
                                return "已审核";
                            }else{
                                return "-";
                            }
                        }
                    },

                    {
                        field: 'superviseFeeIsOk',
                        title: '监管资金状态',
                        formatter: function(value, row, index) {
                            if(value == 0){
                                return "未到帐";
                            }else if (value == 1) {
                                return "已到帐";
                            }else{
                                return "-";
                            }
                        }
                    },

                {
                    field: 'isSupervise',
                    title: '监管状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(isSuperviseDatas, value);
                    }
                },
                {
                    field: 'isQuit',
                    title: '是否退学',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(isQuitDatas, value);
                    }
                },
                {
                    field: 'delReason',
                    title: '删除原因'
                }
                ]
            };
            $.table.init(options);

            //加载驾校、分校
            $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
            $('#cxSelectSchool').cxSelect({
                selects: ['schoolId', 'branchId','registrationId'],
                jsonValue: 'v',
            });

            /** 初始化时间组件 */
            layui.use('laydate', function(){
                var laydate = layui.laydate;
                    laydate.render({
                        elem: '#beginPrepareRegisteDate',
                        type: 'datetime',
                        trigger: 'click'
       		        });
                       laydate.render({
                        elem: '#endPrepareRegisteDate',
                        type: 'datetime',
                        trigger: 'click'
       		        });
                });
        });

                  //重置搜索条件
          function resetQueryForm(){
            // 1. 先移除验证
            $('#formId').validate().resetForm();
            
            // 2. 重置表单
            $('#formId')[0].reset();
            
            // 3. 重置 select2 字段
            $("#schoolId").val('').trigger('change');
            $("#branchId").empty().trigger('change');
            $("#registrationId").empty().trigger('change');
            
            // 4. 清除错误样式
            $("#schoolId, #branchId, #registrationId").removeClass('error');
            $("label.error[for='schoolId'], label.error[for='branchId'], label.error[for='registrationId']").remove();
            
            // 5. 重新触发表格搜索
            $.table.search();
          }

        /* 查看详情 */
        function view(id,width, height) {
            table.set();
            var _url = $.operate.detailUrl(id);
            var options = {
                title: table.options.modalName + "详细",
                width: width,
                height: height,
                url: _url,
                skin: 'layui-layer-gray',
                btn: ['关闭'],
                yes: function (index, layero) {
                    $.modal.close(index);
                }
            };
            $.modal.openOptions(options);
        }

        // 导出数据
        function exportSelected() {
            var ids = $.table.selectColumns("id");
            var schoolId = $('#schoolId').val(); //驾校id
            var branchId = $('#branchId').val(); //分校id
            var registrationId = $('#registrationId').val(); //报名点id
            var name = $("input[name='name']").val(); //学员名称
            var identity = $("input[name='params[identity]']").val(); //身份证号
            var mobile = $("input[name='mobile']").val(); //手机号
            var isSupervise = $('select[name="isSupervise"]').val(); //监管状态
            var isCheck = $('select[name="isCheck"]').val(); //是否审核
            var beginTime = $('input[id="startTime"]').val(); //报名审核开始时间
            var endTime = $('input[id="endTime"]').val(); //报名审核结束时间
            var beginPrepareRegisteDate = $('input[id="beginPrepareRegisteDate"]').val(); //预登记开始时间
            var endPrepareRegisteDate = $('input[id="endPrepareRegisteDate"]').val(); //预登记结束时间
            var studyCenterIsSyn = $('select[name="studyCenterIsSyn"]').val(); //是否上报计时平台

            let fromData = new FormData();
            fromData.append('schoolId', schoolId != null ? schoolId : '');
            fromData.append('branchId', branchId != null ? branchId : '' );
            fromData.append('registrationId', registrationId != null ? registrationId : '');
            fromData.append('name', name != null ? name : '');
            fromData.append('identity', identity != null ? identity : '');
            fromData.append('mobile', mobile != null ? mobile : '');
            fromData.append('isSupervise', isSupervise != null ? isSupervise : '');
            fromData.append('isCheck', isCheck != null ? isCheck : '');
            fromData.append('beginTime', beginTime != null ? beginTime : '');
            fromData.append('endTime', endTime != null ? endTime : '');
            fromData.append('beginPrepareRegisteDate', beginPrepareRegisteDate != null ? beginPrepareRegisteDate : '');
            fromData.append('endPrepareRegisteDate', endPrepareRegisteDate != null ? endPrepareRegisteDate : '');
            fromData.append('studyCenterIsSyn', studyCenterIsSyn != null ? studyCenterIsSyn : '');

            var tipMsg = "确定导出所有数据吗？";
            if($.common.isNotEmpty(ids)){
                tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
                fromData.append('ids', ids);
            }

            $.modal.confirm(tipMsg, function() {
                $.ajax({
                    url: prefix + "/export",
                    data: fromData,
                    type: "post",
                    processData: false,
                    contentType: false,
                    async: true,
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    }
                })
            });
        }

        /* 查看删除学员详情 */
      $('#bootstrap-table').on('dbl-click-row.bs.table', function (e, row, $element) {
        table.set();
          var _url = $.operate.detailUrl(row.id);
          var options = {
              title: table.options.modalName + "详细",
              width: "1300",
              height: "",
              url: _url,
              skin: 'layui-layer-gray',
              btn: ['关闭'],
              yes: function (index, layero) {
                  $.modal.close(index);
              }
          };
          $.modal.openOptions(options);
      });
    </script>
  </body>
</html>
