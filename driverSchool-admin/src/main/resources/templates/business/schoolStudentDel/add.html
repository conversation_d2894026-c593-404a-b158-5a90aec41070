<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增学员删除')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-ISchoolStudentDel-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">姓名：</label>
                <div class="col-sm-8">
                    <input name="name" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">头像路径：</label>
                <div class="col-sm-8">
                    <input name="headImage" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">身份证号：</label>
                <div class="col-sm-8">
                    <input name="identity" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">手机号：</label>
                <div class="col-sm-8">
                    <input name="mobile" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">性别，0-女，1-男：</label>
                <div class="col-sm-8">
                    <input name="gender" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">生日：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="birthday" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">民族：</label>
                <div class="col-sm-8">
                    <input name="nation" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">学历：</label>
                <div class="col-sm-8">
                    <input name="education" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">现住具体地址：</label>
                <div class="col-sm-8">
                    <input name="address" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">现住省份：</label>
                <div class="col-sm-8">
                    <input name="province" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">现住城市：</label>
                <div class="col-sm-8">
                    <input name="city" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">现住镇区/县：</label>
                <div class="col-sm-8">
                    <input name="town" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">户籍具体地址：</label>
                <div class="col-sm-8">
                    <input name="hujiAddress" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">户籍省：</label>
                <div class="col-sm-8">
                    <input name="hujiProvince" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">户籍城市：</label>
                <div class="col-sm-8">
                    <input name="hujiCity" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">户籍镇区/县：</label>
                <div class="col-sm-8">
                    <input name="hujiTown" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">驾校ID：</label>
                <div class="col-sm-8">
                    <input name="schoolId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">分校ID：</label>
                <div class="col-sm-8">
                    <input name="branchId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">报名点ID：</label>
                <div class="col-sm-8">
                    <input name="registrationId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">原驾照号码：</label>
                <div class="col-sm-8">
                    <input name="oldLicenseNo" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">原领驾照日期：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="oldLicenseDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <input name="remark" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">报名时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="registeDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">预登记时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="prepareRegisteDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否付款0-未付1-已付：</label>
                <div class="col-sm-8">
                    <input name="isPay" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">交费码，交费成功之后银行返回：</label>
                <div class="col-sm-8">
                    <input name="payCode" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">来自于机构用户中organ_type=6时的ID：</label>
                <div class="col-sm-8">
                    <input name="studyCenterId" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">学时中心数据是否已传输，0-未传输入，1-已传输：</label>
                <div class="col-sm-8">
                    <input name="studyCenterIsSyn" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">学时中心同步时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="studyCenterSynDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">第一次付款日期：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="firstPayDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">付款方式：</label>
                <div class="col-sm-8">
                    <input name="chargeMode" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">对接Id：</label>
                <div class="col-sm-8">
                    <input name="customerId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="createdTime" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">更新时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="updatedTime" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">0-不监管，1-监管，：</label>
                <div class="col-sm-8">
                    <input name="isSupervise" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">监管金额，银行给消息时，改变该值：</label>
                <div class="col-sm-8">
                    <input name="superviseFee" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">监管资金是否到账：</label>
                <div class="col-sm-8">
                    <input name="superviseFeeIsOk" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">受监管时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="superviseDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">是否退学:0=否,1=是：</label>
                <div class="col-sm-8">
                    <input name="isQuit" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">退学数据是否同步：</label>
                <div class="col-sm-8">
                    <input name="quitIsSyn" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">退学时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="quitDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">数据来源：</label>
                <div class="col-sm-8">
                    <input name="originData" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">微信openId：</label>
                <div class="col-sm-8">
                    <input name="openId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="virtualAccountNo" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="virtualAccountName" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否审核：0-未审核，1-已审核：</label>
                <div class="col-sm-8">
                    <input name="isCheck" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">真实身份证上省份：</label>
                <div class="col-sm-8">
                    <input name="realProvince" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">真实身份证上城市：</label>
                <div class="col-sm-8">
                    <input name="realCity" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">真实身份证上镇区：</label>
                <div class="col-sm-8">
                    <input name="realTown" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">真实身份证上地址：</label>
                <div class="col-sm-8">
                    <input name="realAddress" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">原驾驶证电子版图片：</label>
                <div class="col-sm-8">
                    <input name="oldLicenseImage" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">删除原因：</label>
                <div class="col-sm-8">
                    <input name="delReason" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/ISchoolStudentDel"
        $("#form-ISchoolStudentDel-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-ISchoolStudentDel-add').serialize());
            }
        }

        $("input[name='birthday']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='oldLicenseDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='registeDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='prepareRegisteDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='studyCenterSynDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='firstPayDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='createdTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='updatedTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='superviseDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='quitDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>