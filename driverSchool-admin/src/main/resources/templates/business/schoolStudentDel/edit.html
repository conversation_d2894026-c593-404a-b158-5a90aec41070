<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改学员删除')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-ISchoolStudentDel-edit" th:object="${schoolStudentDel}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">姓名：</label>
                <div class="col-sm-8">
                    <input name="name" th:field="*{name}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">头像路径：</label>
                <div class="col-sm-8">
                    <input name="headImage" th:field="*{headImage}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">身份证号：</label>
                <div class="col-sm-8">
                    <input name="identity" th:field="*{identity}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">手机号：</label>
                <div class="col-sm-8">
                    <input name="mobile" th:field="*{mobile}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">性别，0-女，1-男：</label>
                <div class="col-sm-8">
                    <input name="gender" th:field="*{gender}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">生日：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="birthday" th:value="${#dates.format(schoolStudentDel.birthday, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">民族：</label>
                <div class="col-sm-8">
                    <input name="nation" th:field="*{nation}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">学历：</label>
                <div class="col-sm-8">
                    <input name="education" th:field="*{education}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">现住具体地址：</label>
                <div class="col-sm-8">
                    <input name="address" th:field="*{address}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">现住省份：</label>
                <div class="col-sm-8">
                    <input name="province" th:field="*{province}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">现住城市：</label>
                <div class="col-sm-8">
                    <input name="city" th:field="*{city}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">现住镇区/县：</label>
                <div class="col-sm-8">
                    <input name="town" th:field="*{town}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">户籍具体地址：</label>
                <div class="col-sm-8">
                    <input name="hujiAddress" th:field="*{hujiAddress}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">户籍省：</label>
                <div class="col-sm-8">
                    <input name="hujiProvince" th:field="*{hujiProvince}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">户籍城市：</label>
                <div class="col-sm-8">
                    <input name="hujiCity" th:field="*{hujiCity}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">户籍镇区/县：</label>
                <div class="col-sm-8">
                    <input name="hujiTown" th:field="*{hujiTown}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">驾校ID：</label>
                <div class="col-sm-8">
                    <input name="schoolId" th:field="*{schoolId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">分校ID：</label>
                <div class="col-sm-8">
                    <input name="branchId" th:field="*{branchId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">报名点ID：</label>
                <div class="col-sm-8">
                    <input name="registrationId" th:field="*{registrationId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">原驾照号码：</label>
                <div class="col-sm-8">
                    <input name="oldLicenseNo" th:field="*{oldLicenseNo}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">原领驾照日期：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="oldLicenseDate" th:value="${#dates.format(schoolStudentDel.oldLicenseDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <input name="remark" th:field="*{remark}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">报名时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="registeDate" th:value="${#dates.format(schoolStudentDel.registeDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">预登记时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="prepareRegisteDate" th:value="${#dates.format(schoolStudentDel.prepareRegisteDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否付款0-未付1-已付：</label>
                <div class="col-sm-8">
                    <input name="isPay" th:field="*{isPay}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">交费码，交费成功之后银行返回：</label>
                <div class="col-sm-8">
                    <input name="payCode" th:field="*{payCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">来自于机构用户中organ_type=6时的ID：</label>
                <div class="col-sm-8">
                    <input name="studyCenterId" th:field="*{studyCenterId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">学时中心数据是否已传输，0-未传输入，1-已传输：</label>
                <div class="col-sm-8">
                    <input name="studyCenterIsSyn" th:field="*{studyCenterIsSyn}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">学时中心同步时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="studyCenterSynDate" th:value="${#dates.format(schoolStudentDel.studyCenterSynDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">第一次付款日期：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="firstPayDate" th:value="${#dates.format(schoolStudentDel.firstPayDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">付款方式：</label>
                <div class="col-sm-8">
                    <input name="chargeMode" th:field="*{chargeMode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">对接Id：</label>
                <div class="col-sm-8">
                    <input name="customerId" th:field="*{customerId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="createdTime" th:value="${#dates.format(schoolStudentDel.createdTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">更新时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="updatedTime" th:value="${#dates.format(schoolStudentDel.updatedTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">0-不监管，1-监管，：</label>
                <div class="col-sm-8">
                    <input name="isSupervise" th:field="*{isSupervise}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">监管金额，银行给消息时，改变该值：</label>
                <div class="col-sm-8">
                    <input name="superviseFee" th:field="*{superviseFee}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">监管资金是否到账：</label>
                <div class="col-sm-8">
                    <input name="superviseFeeIsOk" th:field="*{superviseFeeIsOk}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">受监管时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="superviseDate" th:value="${#dates.format(schoolStudentDel.superviseDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">是否退学:0=否,1=是：</label>
                <div class="col-sm-8">
                    <input name="isQuit" th:field="*{isQuit}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">退学数据是否同步：</label>
                <div class="col-sm-8">
                    <input name="quitIsSyn" th:field="*{quitIsSyn}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">退学时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="quitDate" th:value="${#dates.format(schoolStudentDel.quitDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">数据来源：</label>
                <div class="col-sm-8">
                    <input name="originData" th:field="*{originData}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">微信openId：</label>
                <div class="col-sm-8">
                    <input name="openId" th:field="*{openId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="virtualAccountNo" th:field="*{virtualAccountNo}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="virtualAccountName" th:field="*{virtualAccountName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否审核：0-未审核，1-已审核：</label>
                <div class="col-sm-8">
                    <input name="isCheck" th:field="*{isCheck}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">真实身份证上省份：</label>
                <div class="col-sm-8">
                    <input name="realProvince" th:field="*{realProvince}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">真实身份证上城市：</label>
                <div class="col-sm-8">
                    <input name="realCity" th:field="*{realCity}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">真实身份证上镇区：</label>
                <div class="col-sm-8">
                    <input name="realTown" th:field="*{realTown}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">真实身份证上地址：</label>
                <div class="col-sm-8">
                    <input name="realAddress" th:field="*{realAddress}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">原驾驶证电子版图片：</label>
                <div class="col-sm-8">
                    <input name="oldLicenseImage" th:field="*{oldLicenseImage}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">删除原因：</label>
                <div class="col-sm-8">
                    <input name="delReason" th:field="*{delReason}" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/ISchoolStudentDel";
        $("#form-ISchoolStudentDel-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-ISchoolStudentDel-edit').serialize());
            }
        }

        $("input[name='birthday']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='oldLicenseDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='registeDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='prepareRegisteDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='studyCenterSynDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='firstPayDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='createdTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='updatedTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='superviseDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='quitDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>