<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('计时校验详情')" />
	<link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
	<link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
	<link href="../static/css/animate.min.css" th:href="@{/css/animate.min.css}" rel="stylesheet"/>
	<link href="../static/css/style.min.css" th:href="@{/css/style.min.css}" rel="stylesheet"/>
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list" id="cxSelectSchool">
                        <ul>
                            <li>
                                <label>驾校：</label>
                                <select style="margin-bottom:0px;font-family:inherit;font-size:inherit;line-height:inherit;" id="schoolId" name="schoolId" class="schoolId" data-first-title="选择驾校" required></select>
                            </li>
                            <li>
                                <label>报名点：</label>
                                <input type="text" name="params[registrationName]"/>
                            </li>
                            <li>
                                <label>科目名称：</label>
                                <input type="text" name="params[subjectName]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <!-- <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-primary single disabled" onclick="view('',1300,'')" shiro:hasPermission="business:quitStudent:view">
                    查看
                </a>
                <a class="btn btn-info" onclick="exportSelected()" shiro:hasPermission="business:quitStudent:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div> -->

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
     <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">

        var prefix = ctx + "system/schoolExceptionDetail";

        $(function() {
            var options = {
                url: prefix + "/list",
                exportUrl: prefix + "/export",
                detailUrl: prefix + "/view/{id}",
                modalName: "计时校验",
                showPageGo: true,
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'name',
                    title: '驾校',
                },
                {
                    field: '',
                    title: '联系人',
                    formatter: function (value, row, index) {
                        if (row.schoolContactList.length > 0) {
                            return row.schoolContactList[0].name;
                        }
                    }
                },
                {
                    field: '',
                    title: '联系电话',
                    formatter: function (value, row, index) {
                        if (row.schoolContactList.length > 0) {
                            return row.schoolContactList[0].tel;
                        }
                    }
                },
                {
                    field: 'address',
                    title: '具体地址',
                },
                {
                    field: '',
                    title: '学员数',
                    formatter: function (value, row, index) {
                        return 0;
                    }
                },
                {
                    field: '',
                    title: '状态总数',
                    formatter: function (value, row, index) {
                        return 0;
                    }
                },
                {
                    field: '',
                    title: '正常状态',
                    formatter: function (value, row, index) {
                        return 0;
                    }
                },
                {
                    field: '',
                    title: '异常状态',
                    formatter: function (value, row, index) {
                        return 0;
                    }
                },
                {
                    field: '',
                    title: '异常状态占比',
                    formatter: function (value, row, index) {
                        return 0.00 + '%';
                    }
                },
                {
                    field: '',
                    title: '终端号异常状态',
                    formatter: function (value, row, index) {
                        return 0;
                    }
                },
                {
                    field: '',
                    title: '学员账号异常状态',
                    formatter: function (value, row, index) {
                        return 0;
                    }
                },
                {
                    field: '',
                    title: '打点率异常状态',
                    formatter: function (value, row, index) {
                        return 0;
                    }
                },
                {
                    field: '',
                    title: '打点次数异常状态',
                    formatter: function (value, row, index) {
                        return 0;
                    }
                },
                ]
            };
            $.table.init(options);


            //加载驾校、分校
            $.cxSelect.defaults.url = ctx + "business/school/schoolThreeData";
            $('#cxSelectSchool').cxSelect({
                selects: ['schoolId', 'branchId','registrationId'],
                jsonValue: 'v',
            });
        });

        // 导出数据
        function exportSelected() {
            var ids = $.table.selectColumns("id");
            var dataParam = $("#formId").serializeArray();
            var tipMsg = "确定导出所有数据吗？";
            if($.common.isNotEmpty(ids)){
                tipMsg = "确定导出勾选" + ids.length + "条数据吗？";
                dataParam.push({ "name": "ids", "value": ids });
            }
            $.modal.confirm(tipMsg, function() {
                $.post(prefix + "/export", dataParam, function(result) {
                    if (result.code == web_status.SUCCESS) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }

    </script>
</body>
</html>