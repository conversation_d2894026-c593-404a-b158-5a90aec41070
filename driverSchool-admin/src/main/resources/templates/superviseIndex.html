<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
  <head>
    <meta charset="utf-8" />
    <title>监管端看板</title>
    <th:block th:include="include :: header('监管端看板')" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!--360浏览器优先以webkit内核解析-->
    <title></title>
    <link rel="shortcut icon" href="favicon.ico" />
    <link
      href="../static/css/sta.css"
      th:href="@{/css/sta.css}"
      rel="stylesheet"
    />
  </head>

  <body style="background-color: #f0f2f5">
    <div class="content-box">
      <div class="box-title">
        <div class="title-box">
          <div class="title-bg"></div>
          <div class="titlt-p">数据更新时间：[[${dataUpdateTime}]]</div>
        </div>
      </div>

      <div class="box-qsxy">
        <div class="title-box">
          <div class="title-bg"></div>
          <div class="titlt-p">全市学员信息</div>
        </div>
        <div class="qsxy">
          <div class="list5" style="margin-top: 46px">
            <div class="icon-box" style="margin-left: 10%">
              <img th:src="@{/img/sta/jg-baoming.png}" alt="" />
            </div>
            <div class="msg-box">
              <h2 class="box-h2">
                报名总人数
                <div class="info-tooltip">
                  <img
                    class="question-mark"
                    th:src="@{/img/questionMark.png}"
                    alt=""
                  />
                  <div class="tooltip-text">
                    报名管理-学员信息：总报名人数直接在"学员信息"查看"现在"的数据。
                  </div>
                </div>
              </h2>
              <br />
              <a
                class="box-a"
                id="studentCount"
                th:text="${staDate?.studentCount}"
                >800</a
              >
            </div>
          </div>
          <div class="qsxy-right">
            <div class="list4 list4-bg">
              <div class="info-tooltips info-tooltip">
                <img
                  class="question-mark"
                  th:src="@{/img/questionMark.png}"
                  alt=""
                />
                <div class="tooltip-text">
                  <p>资金监管-监管信息查询 - 资金释放记录：</p>
                  <p>
                    正在监管学员数=监管信息查询数据信息数-资金释放记录信息数。
                  </p>
                </div>
              </div>
              <div class="msg-box" style="margin-top: 10px">
                <h2 class="box-h2">受资金监管人数</h2>
                <br />
                <a
                  class="box-a fontsize24"
                  id="superviseStudentCount"
                  th:text="${staDate?.superviseStudentCount}"
                  >560</a
                >
              </div>
              <div
                style="
                  float: right;
                  width: 64px;
                  height: 64px;
                  margin-right: 20px;
                  margin-top: 30px;
                "
              >
                <div id="jg-chart02" style="height: 64px"></div>
              </div>
            </div>

            <div class="list4 list4-bg">
              <div class="info-tooltips info-tooltip">
                <img
                  class="question-mark"
                  th:src="@{/img/questionMark.png}"
                  alt=""
                />
                <div class="tooltip-text">
                  <p>报名管理-学员信息：</p>
                  <p>
                    （23年9月1号后）报名管理的学员信息中监管资金状态为未到账的学员数。
                  </p>
                </div>
              </div>
              <div class="msg-box" style="margin-top: 10px">
                <h2 class="box-h2">未受资金监管人数</h2>
                <br />
                <a
                  class="box-a fontsize24"
                  id="noSuperviseStudentCount"
                  th:text="${staDate?.noSuperviseStudentCount}"
                  >560</a
                >
              </div>
              <div
                style="
                  float: right;
                  width: 64px;
                  height: 64px;
                  margin-right: 20px;
                  margin-top: 30px;
                "
              >
                <div id="jg-chart03" style="height: 64px"></div>
              </div>
            </div>

            <!-- <div class="list4 list4-bg">
                    <div class="msg-box" style="margin-top: 30px;">
                        <h2 class="box-h2">结业人数</h2><br>
                        <a class="box-a fontsize24" id="finishStudentCount" th:text="${staDate?.finishStudentCount}">200</a>
                    </div>
                    <div style="float: right;width: 64px; height: 64px; margin-right: 20px;margin-top: 30px;">
                        <div id="jg-chart04" style="height: 64px;"></div>
                    </div>
                </div> -->

            <div class="list4 list4-bg">
              <div class="info-tooltips info-tooltip">
                <img
                  class="question-mark"
                  th:src="@{/img/questionMark.png}"
                  alt=""
                />
                <div class="tooltip-text">
                  在"报名管理-退学学员"查看，即退学学员申请的人数。
                </div>
              </div>
              <div class="msg-box" style="margin-top: 10px">
                <h2 class="box-h2">退学人数</h2>
                <br />
                <a
                  class="box-a fontsize24"
                  id="quitStudentCount"
                  th:text="${staDate?.quitStudentCount}"
                  >40</a
                >
              </div>
              <div
                style="
                  float: right;
                  width: 64px;
                  height: 64px;
                  margin-right: 20px;
                  margin-top: 30px;
                "
              >
                <div id="jg-chart05" style="height: 64px"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="box-zjjg">
        <div class="title-box">
          <div class="title-bg"></div>
          <div class="titlt-p">资金监管数据信息</div>
        </div>
        <div class="zjjg">
          <div class="list5">
            <div class="icon-box" style="margin-left: 10%">
              <img th:src="@{/img/sta/jg-zongrenshu.png}" alt="" />
            </div>
            <div class="msg-box">
              <h2 class="box-h2">
                总驾培机构数
                <div class="info-tooltip">
                  <img
                    class="question-mark"
                    th:src="@{/img/questionMark.png}"
                    alt=""
                  />
                  <div class="tooltip-text">
                    在"驾校管理-总校信息"查看，录入的所有驾校数。
                  </div>
                </div>
              </h2>
              <br />
              <a
                class="box-a fontsize24"
                href=""
                th:text="${staDate?.schoolCount}"
                >560</a
              >
            </div>
          </div>
          <div class="list5">
            <div class="icon-box" style="margin-left: 10%">
              <img th:src="@{/img/sta/jg-zongzijin.png}" alt="" />
            </div>
            <div class="msg-box">
              <h2 class="box-h2">
                已监管资金
                <div class="info-tooltip">
                  <img
                    class="question-mark"
                    th:src="@{/img/questionMark.png}"
                    alt=""
                  />
                  <div class="tooltip-text">
                    <p>资金监管-监管信息查询：</p>
                    <p>已监管资金=已监管学员数*固定资金释放金额。</p>
                  </div>
                </div>
              </h2>
              <br />
              <a
                class="box-a fontsize24"
                href=""
                th:text="${staDate?.superviseTotalMoney}"
                >2240000</a
              >
            </div>
          </div>
          <!-- <div class="list5">
                <div class="icon-box" style="margin-left: 10%;"><img th:src="@{/img/sta/jg-zaitu.png}" alt=""></div>
                <div class="msg-box">
                    <h2 class="box-h2">在途监管资金</h2><br>
                    <a class="box-a fontsize24" href="" th:text="${staDate?.superviseRestMoney}">1680000</a>
                </div>
            </div> -->
          <div class="list5">
            <div class="icon-box" style="margin-left: 10%">
              <img th:src="@{/img/sta/jg-weishifang.png}" alt="" />
            </div>
            <div class="msg-box">
              <h2 class="box-h2">
                正在监管资金
                <div class="info-tooltip">
                  <img
                    class="question-mark"
                    th:src="@{/img/questionMark.png}"
                    alt=""
                  />
                  <div class="tooltip-text">
                    <p>资金监管-监管信息查询-资金释放记录：</p>
                    <p>
                      正在监管资金
                      =（监管信息查询数据信息数-资金释放记录信息数）*
                      固定资金释放金额。
                    </p>
                  </div>
                </div>
              </h2>
              <br />
              <a
                class="box-a fontsize24"
                href=""
                th:text="${staDate?.superviseMoney}"
                >880000</a
              >
            </div>
          </div>
          <div class="list5">
            <div class="icon-box" style="margin-left: 10%">
              <img th:src="@{/img/sta/jg-zijinshifang.png}" alt="" />
            </div>
            <div class="msg-box">
              <h2 class="box-h2">
                已释放资金
                <div class="info-tooltip">
                  <img
                    class="question-mark"
                    th:src="@{/img/questionMark.png}"
                    alt=""
                  />
                  <div class="tooltip-text">
                    <p>资金监管-资金释放记录：</p>
                    <p>已释放资金=资金释放信息数*固定资金释放金额。</p>
                  </div>
                </div>
              </h2>
              <br />
              <a
                class="box-a fontsize24"
                href=""
                th:text="${staDate?.superviseReleaseMoney}"
                >800000</a
              >
            </div>
          </div>
        </div>
      </div>

      <!-- <div class="box-jxxx">
        <div class="title-box">
          <div class="title-bg"></div>
          <div class="titlt-p">全市驾校信息</div>
        </div>
        <div class="jxxx">
          <div class="jxxx-left">
            <div class="icon-box">
              <img th:src="@{/img/sta/jg-jigou.png}" alt="" />
            </div>
            <div class="msg-box">
              <h2 class="box-h2">总驾培机构数</h2>
              <br />
              <a class="box-a" href="" th:text="${staDate?.schoolCount}">84</a>
            </div>
          </div>
          <div class="jg-chart01">
            <input
              type="hidden"
              id="superviseSchoolCount"
              th:value="${staDate?.superviseSchoolCount}"
            />
            <input
              type="hidden"
              id="noSuperviseSchoolCount"
              th:value="${staDate?.noSuperviseSchoolCount}"
            />
            <div id="jg-chart01" style="width: 264px; height: 103px"></div>
          </div>
        </div>
      </div> -->

      <!-- <div class="box-jxqk">
        <div class="title-box">
          <div class="title-bg"></div>
          <div class="titlt-p">驾校情况</div>
          <div class="search-box">
            <div class="box-select">
              <lable>驾校选择：</lable>
              <select id="schoolId" name="schoolId">
                <option value="">选择驾校</option>
                <option
                  th:each="dict : ${schoolList}"
                  th:text="${dict.name}"
                  th:value="${dict.id}"
                ></option>
              </select>
            </div>
            <div class="box-ipt">
              <lable>时间选择：</lable>
              <input
                type="text"
                id="staDate"
                class="sta-time-input"
                placeholder="日期"
                autocomplete="off"
              />
            </div>
          </div>
        </div>
        <div class="jxqk">
          <div class="list4">
            <div class="icon0">
              <img th:src="@{/img/sta/jg-mendian.png}" alt="" />
            </div>
            <div class="titlt-p2">门店总数</div>
            <a
              class="jxqk-a"
              href=""
              id="registrationCount"
              th:text="${staDate?.registrationCount}"
              >840</a
            >
          </div>
          <div class="list4">
            <div class="icon0">
              <img th:src="@{/img/sta/jg-xunlianchang.png}" alt="" />
            </div>
            <div class="titlt-p2">训练场总数</div>
            <a
              class="jxqk-a"
              href=""
              id="trainingGroundCount"
              th:text="${staDate?.trainingGroundCount}"
              >128</a
            >
          </div>
          <div class="list4">
            <div class="icon0">
              <img th:src="@{/img/sta/jg-zongjiaolian.png}" alt="" />
            </div>
            <div class="titlt-p2">教练总数</div>
            <a
              class="jxqk-a"
              href=""
              id="teacherCount"
              th:text="${staDate?.teacherCount}"
              >800</a
            >
          </div>
          <div class="list4">
            <div class="icon0">
              <img th:src="@{/img/sta/jg-zongjiaolianche.png}" alt="" />
            </div>
            <div class="titlt-p2">教练车总数</div>
            <a
              class="jxqk-a"
              href=""
              id="carCount"
              th:text="${staDate?.carCount}"
              >320</a
            >
          </div>
        </div>
      </div> -->

      <div class="box-jxjgsj">
        <div class="title-box">
          <div class="title-bg"></div>
          <div class="titlt-p">全市驾校监管数据信息</div>
          <div class="search-box">
            <div class="box-select">
              <lable>驾校选择：</lable>
              <select id="staDateSchoolId" name="staDateSchoolId">
                <option value="">选择驾校</option>
                <option
                  th:each="dict : ${schoolList}"
                  th:text="${dict.name}"
                  th:value="${dict.id}"
                ></option>
              </select>
            </div>
            <div class="box-ipt">
              <lable>时间选择：</lable>
              <input
                type="text"
                id="staDateSchool"
                class="sta-time-input-school"
                placeholder="日期"
                autocomplete="off"
              />
            </div>
          </div>
        </div>
        <div class="jxqk">
          <div class="list4">
            <div class="icon0">
              <img th:src="@{/img/sta/jg-mendian.png}" alt="" />
            </div>
            <div class="titlt-p2">
              门店总数
              <div class="info-tooltip">
                <img
                  class="question-mark"
                  th:src="@{/img/questionMark.png}"
                  alt=""
                />
                <div class="tooltip-text">在"驾校管理-门店信息"查看。</div>
              </div>
            </div>
            <a
              class="jxqk-a"
              href=""
              id="registrationCount"
              th:text="${staDate?.registrationCount}"
              >840</a
            >
          </div>
          <div class="list4">
            <div class="icon0">
              <img th:src="@{/img/sta/jg-xunlianchang.png}" alt="" />
            </div>
            <div class="titlt-p2">
              训练场总数
              <div class="info-tooltip">
                <img
                  class="question-mark"
                  th:src="@{/img/questionMark.png}"
                  alt=""
                />
                <div class="tooltip-text">在"驾校管理-训练场信息"查看。</div>
              </div>
            </div>
            <a
              class="jxqk-a"
              href=""
              id="trainingGroundCount"
              th:text="${staDate?.trainingGroundCount}"
              >128</a
            >
          </div>
          <div class="list4">
            <div class="icon0">
              <img th:src="@{/img/sta/jg-zongjiaolian.png}" alt="" />
            </div>
            <div class="titlt-p2">
              教练总数
              <div class="info-tooltip">
                <img
                  class="question-mark"
                  th:src="@{/img/questionMark.png}"
                  alt=""
                />
                <div class="tooltip-text">在"驾校管理-教练信息"查看。</div>
              </div>
            </div>
            <a
              class="jxqk-a"
              href=""
              id="teacherCount"
              th:text="${staDate?.teacherCount}"
              >800</a
            >
          </div>
          <!-- <div class="list4">
            <div class="icon0">
              <img th:src="@{/img/sta/jg-zongjiaolianche.png}" alt="" />
            </div>
            <div class="titlt-p2">教练车总数</div>
            <a
              class="jxqk-a"
              href=""
              id="carCount"
              th:text="${staDate?.carCount}"
              >320</a
            >
          </div> -->
        </div>
        <div class="col-sm-12 select-table table-striped">
          <div class="titlt-p2" style="width: 100px; margin-left: 15px">
            标题含义
            <div class="info-tooltip">
              <img
                class="question-mark"
                th:src="@{/img/questionMark.png}"
                alt=""
              />
              <div class="tooltip-text">
                <p>
                  已报名人数：报名管理-学员信息中查询"现在"数据。（23年9月1号后）。
                </p>
                <p>已监管人数：监管信息查询实时查询记录条数。</p>
                <p>监管人数占比：(已监管人数/已报名人数)。</p>
                <p>总监管资金：监管信息查询信息数*固定监管金额。</p>
                <p>已释放资金：资金释放记录驾校的信息数*固定释放金额。</p>
                <p>未释放资金：总监管资金-已释放资金。</p>
              </div>
            </div>
          </div>
          <a
            class="btn btn-warning"
            onclick="exportSelected()"
            shiro:hasPermission="business:superviseIndex:export"
            style="margin-top: 5px"
          >
            <i class="fa fa-download"></i> 导出
          </a>
          <table id="bootstrap-table"></table>
        </div>
      </div>

      <!-- <div class="box-kmqk">
        <div class="title-box">
            <div class="title-bg"></div>
            <div class="titlt-p">各科目具体情况（学员人数及对应在学习科目中的占比）</div>
        </div>
        <div class="kmqk">
            <div class="km-list4" style="border-right: 1px solid #DEDEDE;">
                <div class="msg-box" style="position: relative;margin-left: 100px;">
                    <div class="km-title-bg"><img th:src="@{/img/sta/title-bg.png}" alt=""></div>
                    <h2 class="box-h2" style="font-weight: bold;">科目一</h2><br>
                    <a class="box-a fontsize24" id="subject1StudentCount" th:text="${staDate?.subject1StudentCount}">100</a>
                </div>
                <div style="float: right;width: 64px; height: 64px; margin-right: 60px;margin-top: 10px;">
                    <div id="jg-chart06" style="height: 64px;"></div>
                </div>
            </div>
            <div class="km-list4" style="border-right: 1px solid #DEDEDE;">
                <div class="msg-box" style="position: relative;margin-left: 100px;">
                    <div class="km-title-bg"><img th:src="@{/img/sta/title-bg.png}" alt=""></div>
                    <h2 class="box-h2" style="font-weight: bold;">科目二</h2><br>
                    <a class="box-a fontsize24" id="subject2StudentCount" th:text="${staDate?.subject2StudentCount}">100</a>
                </div>
                <div style="float: right;width: 64px; height: 64px; margin-right: 60px;margin-top: 10px;">
                    <div id="jg-chart07" style="height: 64px;"></div>
                </div>
            </div>
            <div class="km-list4" style="border-right: 1px solid #DEDEDE;">
                <div class="msg-box" style="position: relative;margin-left: 100px;">
                    <div class="km-title-bg"><img th:src="@{/img/sta/title-bg.png}" alt=""></div>
                    <h2 class="box-h2" style="font-weight: bold;">科目三</h2><br>
                    <a class="box-a fontsize24" id="subject3StudentCount" th:text="${staDate?.subject3StudentCount}">100</a>
                </div>
                <div style="float: right;width: 64px; height: 64px; margin-right: 60px;margin-top: 10px;">
                    <div id="jg-chart08" style="height: 64px;"></div>
                </div>
            </div>
            <div class="km-list4">
                <div class="msg-box" style="position: relative;margin-left: 100px;">
                    <div class="km-title-bg"><img th:src="@{/img/sta/title-bg.png}" alt=""></div>
                    <h2 class="box-h2" style="font-weight: bold;">科目四</h2><br>
                    <a class="box-a fontsize24" id="subject4StudentCount" th:text="${staDate?.subject4StudentCount}">260</a>
                </div>
                <div style="float: right;width: 64px; height: 64px; margin-right: 60px;margin-top: 10px;">
                    <div id="jg-chart09" style="height: 64px;"></div>
                </div>
            </div>
        </div>
    </div> -->
    </div>
    <th:block th:include="include :: footer" />
    <script th:src="@{/ajax/libs/echarts/echarts.min.js}"></script>
    <script
      src="../static/ruoyi/superviseIndex.js"
      th:src="@{/ruoyi/superviseIndex.js}"
    ></script>
    <script type="text/javascript">
      var prefix = ctx + "business/sta";

      $(function () {
        var options = {
          url: prefix + "/schoolSuperviseData",
          modalName: "",
          showPageGo: true,
          sortName: "rank",
          sortOrder: "asc",
          columns: [
            {
              field: "id",
              title: "",
              visible: false,
            },
            {
              field: "rank",
              title: "排名",
            },
            {
              field: "schoolName",
              title: "驾校",
            },
            {
              field: "registeCount",
              title: "已报名人数",
              sortable: true,
            },
            {
              field: "superviseStudentCount",
              title: "监管人数",
              sortable: true,
            },
            {
              field: "superviseProportion",
              title: "监管人数比例",
              sortable: true,
              formatter: function (value, row, index) {
                if (value != null) {
                  return value + "%";
                } else {
                  return "0%";
                }
              },
            },
            {
              field: "superviseTotalMoney",
              title: "总监管资金",
              
            },
            {
              field: "superviseReleaseMoney",
              title: "已释放资金",
              sortable: true,
            },
            {
              field: "superviseRestMoney",
              title: "未释放资金",
              sortable: true,
            },
          ],
        };
        $.table.init(options);

        jgChart01();
        jgChart02();
        jgChart03();
        jgChart04();
        jgChart05();
        jgChart06();
        jgChart07();
        jgChart08();
        jgChart09();
      });

      //驾校选择查询驾校情况
      $("#schoolId").on("change", function () {
        var schoolId = $("#schoolId").val();
        if ($.common.isNotEmpty(schoolId)) {
          var staDate = $("#staDate").val();
          var formData = new FormData();
          formData.append("schoolId", schoolId);
          if ($.common.isNotEmpty(staDate)) {
            formData.append("staDate", staDate);
          }

          $.ajax({
            url: ctx + "business/sta/selectSchoolData",
            data: formData,
            type: "post",
            processData: false,
            contentType: false,
            success: function (result) {
              if (result.code == web_status.SUCCESS) {
                if (result.data != null) {
                  $("#registrationCount").text(result.data.registrationCount);
                  $("#trainingGroundCount").text(
                    result.data.trainingGroundCount
                  );
                  $("#teacherCount").text(result.data.teacherCount);
                  $("#carCount").text(result.data.carCount);
                } else {
                  $.modal.alertWarning("未查询数据");
                }
              } else if (result.code == web_status.WARNING) {
                $.modal.alertWarning(result.msg);
              } else {
                $.modal.alertError(result.msg);
              }
              $.modal.closeLoading();
            },
          });
        } else {
          var topWindow = $(window.parent.document);
          var currentId = $(".page-tabs-content", topWindow)
            .find(".active")
            .attr("data-id");
          var target = $(
            '.RuoYi_iframe[data-id="' + currentId + '"]',
            topWindow
          );
          var url = target.attr("src");
          target.attr("src", url).ready();
        }
      });

      // laydate time-input 时间控件绑定
      if ($(".sta-time-input").length > 0) {
        layui.use("laydate", function () {
          var com = layui.laydate;
          $(".sta-time-input").each(function (index, item) {
            var time = $(item);
            // 控制控件外观
            var type = time.attr("data-type") || "date";
            // 控制回显格式
            var format = time.attr("data-format") || "yyyy-MM-dd";
            // 控制日期控件按钮
            var buttons = time.attr("data-btn") || "clear|now|confirm",
              newBtnArr = [];
            // 日期控件选择完成后回调处理
            var callback = time.attr("data-callback") || {};
            if (buttons) {
              if (buttons.indexOf("|") > 0) {
                var btnArr = buttons.split("|"),
                  btnLen = btnArr.length;
                for (var j = 0; j < btnLen; j++) {
                  if (
                    "clear" === btnArr[j] ||
                    "now" === btnArr[j] ||
                    "confirm" === btnArr[j]
                  ) {
                    newBtnArr.push(btnArr[j]);
                  }
                }
              } else {
                if (
                  "clear" === buttons ||
                  "now" === buttons ||
                  "confirm" === buttons
                ) {
                  newBtnArr.push(buttons);
                }
              }
            } else {
              newBtnArr = ["clear", "now", "confirm"];
            }
            com.render({
              elem: item,
              theme: "molv",
              trigger: "click",
              type: type,
              format: format,
              btns: newBtnArr,
              done: function (value, data) {
                if (
                  typeof window[callback] != "undefined" &&
                  window[callback] instanceof Function
                ) {
                  window[callback](value, data);
                }
                //
                var schoolId = $("#schoolId").val();
                if ($.common.isEmpty(schoolId)) {
                  $.modal.msgError("驾校不能为空，请选择驾校");
                  return;
                }
                var formData = new FormData();
                formData.append("schoolId", schoolId);
                formData.append("staDate", value);
                $.ajax({
                  url: ctx + "business/sta/selectSchoolData",
                  data: formData,
                  type: "post",
                  processData: false,
                  contentType: false,
                  success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                      console.log();
                      if (result.data != null) {
                        $("#registrationCount").text(
                          result.data.registrationCount
                        );
                        $("#trainingGroundCount").text(
                          result.data.trainingGroundCount
                        );
                        $("#teacherCount").text(result.data.teacherCount);
                        $("#carCount").text(result.data.carCount);
                      } else {
                        $.modal.alertWarning("未查询数据");
                      }
                    } else if (result.code == web_status.WARNING) {
                      $.modal.alertWarning(result.msg);
                    } else {
                      $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                  },
                });
              },
            });
          });
        });
      }

      // 驾校监管数据选择查询驾校情况（全市驾校监管数据查询）
      $("#staDateSchoolId").on("change", function () {
        var schoolId = $("#staDateSchoolId").val();
        if ($.common.isNotEmpty(schoolId)) {
          var staDate = $("#staDateSchool").val();
          var formData = new FormData();
          formData.append("schoolId", schoolId);
          if ($.common.isNotEmpty(staDate)) {
            formData.append("staDate", staDate);
          }
          $.ajax({
            url: ctx + "business/sta/schoolSuperviseData",
            data: formData,
            type: "post",
            processData: false,
            contentType: false,
            success: function (result) {
              if (result.code == web_status.SUCCESS) {
                if (result != null) {
                  $("#bootstrap-table").bootstrapTable("load", result);
                } else {
                  $.modal.alertWarning("未查询数据");
                }
              } else if (result.code == web_status.WARNING) {
                $.modal.alertWarning(result.msg);
              } else {
                $.modal.alertError(result.msg);
              }
              $.modal.closeLoading();
            },
          });

          $.ajax({
            url: ctx + "business/sta/selectSchoolData",
            data: formData,
            type: "post",
            processData: false,
            contentType: false,
            success: function (result) {
              if (result.code == web_status.SUCCESS) {
                if (result.data != null) {
                  $("#registrationCount").text(result.data.registrationCount);
                  $("#trainingGroundCount").text(
                    result.data.trainingGroundCount
                  );
                  $("#teacherCount").text(result.data.teacherCount);
                  $("#carCount").text(result.data.carCount);
                } else {
                  $.modal.alertWarning("未查询数据");
                }
              } else if (result.code == web_status.WARNING) {
                $.modal.alertWarning(result.msg);
              } else {
                $.modal.alertError(result.msg);
              }
              $.modal.closeLoading();
            },
          });
        } else {
          var topWindow = $(window.parent.document);
          var currentId = $(".page-tabs-content", topWindow)
            .find(".active")
            .attr("data-id");
          var target = $(
            '.RuoYi_iframe[data-id="' + currentId + '"]',
            topWindow
          );
          var url = target.attr("src");
          target.attr("src", url).ready();
        }
      });

      // laydate time-input 时间控件绑定 （全市驾校监管数据查询）
      if ($(".sta-time-input-school").length > 0) {
        layui.use("laydate", function () {
          var com = layui.laydate;
          $(".sta-time-input-school").each(function (index, item) {
            var time = $(item);
            // 控制控件外观
            var type = time.attr("data-type") || "date";
            // 控制回显格式
            var format = time.attr("data-format") || "yyyy-MM-dd";
            // 控制日期控件按钮
            var buttons = time.attr("data-btn") || "clear|now|confirm",
              newBtnArr = [];
            // 日期控件选择完成后回调处理
            var callback = time.attr("data-callback") || {};
            if (buttons) {
              if (buttons.indexOf("|") > 0) {
                var btnArr = buttons.split("|"),
                  btnLen = btnArr.length;
                for (var j = 0; j < btnLen; j++) {
                  if (
                    "clear" === btnArr[j] ||
                    "now" === btnArr[j] ||
                    "confirm" === btnArr[j]
                  ) {
                    newBtnArr.push(btnArr[j]);
                  }
                }
              } else {
                if (
                  "clear" === buttons ||
                  "now" === buttons ||
                  "confirm" === buttons
                ) {
                  newBtnArr.push(buttons);
                }
              }
            } else {
              newBtnArr = ["clear", "now", "confirm"];
            }
            com.render({
              elem: item,
              theme: "molv",
              trigger: "click",
              type: type,
              format: format,
              btns: newBtnArr,
              done: function (value, data) {
                if (
                  typeof window[callback] != "undefined" &&
                  window[callback] instanceof Function
                ) {
                  window[callback](value, data);
                }
                //
                var schoolId = $("#staDateSchoolId").val();
                if ($.common.isEmpty(schoolId)) {
                  $.modal.msgError("驾校不能为空，请选择驾校");
                  return;
                }
                console.log(schoolId);
                var formData = new FormData();
                formData.append("schoolId", schoolId);
                formData.append("staDate", value);
                $.ajax({
                  url: ctx + "business/sta/schoolSuperviseData",
                  data: formData,
                  type: "post",
                  processData: false,
                  contentType: false,
                  success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                      if (result != null) {
                        $("#bootstrap-table").bootstrapTable("load", result);
                      } else {
                        $.modal.alertWarning("未查询数据");
                      }
                    } else if (result.code == web_status.WARNING) {
                      $.modal.alertWarning(result.msg);
                    } else {
                      $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                  },
                });
                $.ajax({
                  url: ctx + "business/sta/selectSchoolData",
                  data: formData,
                  type: "post",
                  processData: false,
                  contentType: false,
                  success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                      console.log();
                      if (result.data != null) {
                        $("#registrationCount").text(
                          result.data.registrationCount
                        );
                        $("#trainingGroundCount").text(
                          result.data.trainingGroundCount
                        );
                        $("#teacherCount").text(result.data.teacherCount);
                        $("#carCount").text(result.data.carCount);
                      } else {
                        $.modal.alertWarning("未查询数据");
                      }
                    } else if (result.code == web_status.WARNING) {
                      $.modal.alertWarning(result.msg);
                    } else {
                      $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                  },
                });
              },
            });
          });
        });
      }

      /* 导出数据 */
      function exportSelected() {
        var schoolId = $("#staDateSchoolId").val();
        var staDate = $("#staDateSchool").val();
        params = {
          schoolId: schoolId,
          staDate: staDate,
        };
        var tipMsg = "确定导出数据吗？";
        $.modal.confirm(tipMsg, function () {
          $.ajax({
            url: prefix + "/schoolSuperviseData/export",
            data: JSON.stringify(params),
            type: "post",
            contentType: "application/json",
            async: true,
            success: function (result) {
              if (result.code == web_status.SUCCESS) {
                window.location.href =
                  ctx +
                  "common/download?fileName=" +
                  encodeURI(result.msg) +
                  "&delete=" +
                  true;
              } else {
                $.modal.alertError(result.msg);
              }
            },
          });
        });
      }
    </script>
  </body>
</html>
