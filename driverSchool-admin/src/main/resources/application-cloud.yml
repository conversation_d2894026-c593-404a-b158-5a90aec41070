# 项目相关配置
ruoyi:
  # 名称
  name: DriverSchool
  # 版本
  version: 4.7.6
  # 版权年份
  copyrightYear: 2025
  # 实例演示开关
  demoEnabled: true
  # 实例演示开关
  jobEnabled: false
  #定时任务限定IP,多个用逗号隔开，不填或0.0.0.0则为不限制
  jobWhiteIpList: ***************
  # 文件路径 示例（ Windows配置D:/drivingCloud/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /mnt/uploadPath

  contractFile: /mnt/uploadPath/download/contract/contract-template-v3.pdf
  # 获取ip地址开关
  addressEnabled: false
  debug: false
  sendDataToCould: false
  cloudUrl: https://www.guanjiaxie.com:8089
  dikou: false
  xingyeServiceUrl: http://localhost:1127
  # 驾培二维码图片路径
  qrcodeTempleteFile: /mnt/uploadPath/qrcodeTempleteFile.jpg
  #微信二维码域名
  qrcodeDomain: https://guanjiaxie.com:8089

  #驾培二维码字体文件夹路径
  qrcodeFontPath: /mnt/uploadPath

  xuanwu:
    username: dgjp@dgcd
    password: QgMgvAiN
    sign: 东莞驾培
  
  provincePlatform:
    url: http://************:7007/api/ServiceGateway/DataService
    accountName: SJGX000005
    password: 4758193DD1@
    sysId: 97D73E5E-212B-45B2-8A0F-CE0E7EE0789B
  
  bank:
    username: drcbank999
    password: Who@#$********[]
    testNotifyUrl: https://www.guanjiaxie.com:8089/bank/supervise/notify

    appKey: 9d5fbf5cd7234434b16915f01b53c577
    appSecret: 99b14687a3904437a4efa56c01995fe2
    bankPublicKey: CE1644BFF5120B1F960B3FDD4ABFC43B2FE21EF7B208B33E15248A195ECA6CB219852E3B48CC6A8F84649695EBD40A89CD2BB22741A89546D35522EA2E3CCAF3
    privateKey: 44E1B20FD4998494044A9FEE4A9260DD5FAD282BDD71DB154F40E5D71611F2DC
    signKey: C07906429077A080EA3F7E37173A124DD6AB8ADA2EB229C83F93299DF2A2DC699919322936CD824020730ACAAA5A9F914A3BBCAFB18EC1C6565A20DFBC7E15F667ACC0EBF0477A84A7AFCA3D6AEC244DF55628A6D4A19DA6BF55C71E3447EA5E
    verifyKey: 699DF02D020E0109972F9E805A6CB4D6F216882F9C6913D3D5FAE30165E7B8FF2E253D6B5EE1A8C0D13F7A7919DAD323391B5AA9813B83E3A646B4248AE92C57
    serverURL: https://creditdev.ddrcbank.com:63081/gateway

    batchSubmitStudentURL: /openapi/driving.training/1.0.0/insertStudentsInfo
    releaseSuperviseMoneyURL: /openapi/driving.training/1.0.0/transferSupervisionFund
    destroyStudentAccountURL: /openapi/driving.training/1.0.0/deleteSupervisionRelation
    queryAccountInfoURL: /openapi/driving.training/1.0.0/queryAccountInfo

    unionBank:
      # 支付
      appId: 8a81c1bd89b6cadb018a1ae8f8e5031f
      appKey: c7d25f6252bf414586c33db577572367
      #mid: 89844034816APU6
      #tid: APU60001
      from: WWW.GDYSKJYXGS.COM
      systemNum: 35YJ
      notifyUrl: /bank/supervise/unionBankNotify
      notifyScanCodeUrl: /bank/supervise/unionBankScanCodeNotify
      notifyMiniappUrl: /bank/supervise/unionBankMiniappNotify
      notifyMiniappScanCodeUrl: /bank/supervise/unionBankMiniappScanCodeNotify
      #直接回首页
      returnUrl: /
      # 提现
      #clientId: AQ5J511
      #signKey: 5a42c7feb8a35a4e9f16a9ee327cbac6
      #目前只支持兴业银行
      bankType: CIB
      bankName: 兴业银行
      netPayUrl: https://api-mop.chinaums.com/v1/netpay/upg/order
      applyWithdrawalUrl: http://jx.chinayinxun.com/custody/cli/api/applyWithdrawal
      withdrawFlowUrl: http://jx.chinayinxun.com/custody/cli/api/queryWithdrawFlow
      netPayQueryUrl: https://api-mop.chinaums.com/v1/netpay/query
      qrcodeUrl: https://api-mop.chinaums.com/v1/netpay/bills/get-qrcode
      qrcodeQueryUrl: https://api-mop.chinaums.com/v1/netpay/bills/query
      miniappOrderUrl: https://api-mop.chinaums.com/v1/netpay/wx/unified-order

  miniApp:
    publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAshGi7B1oxWVQQ0f8bhiVwqX+dxEPhcZYyk6IJLK/do24teDeqHLybHuRtcvx3YN4GTaiPR31BjTeBn80Af618S1viIUzNpvE0s1/zzSs9plN21cFfUsXV1YQY7NRKHXCr9gW3/Ok/hOjjSGmULo0Bxs/tDYULzseYRBKlCC87Y47L7u0ARxQmeM6NM4fhwqJ6ccTcYo3CDNycnOkhtBIqFq9uUSm4XufHPxn4USpayKHuYP/pcer8XNaRmQ9Y8rcuvskl99ZcGpRhyEMshgvrizHfw6GkfgcXEQRWKmTsHbtr9JvuuLzdCq5tAdTSdHiYPCf+1M0m7ureUO/GxL3aQIDAQAB
    privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCyEaLsHWjFZVBDR/xuGJXCpf53EQ+FxljKTogksr92jbi14N6ocvJse5G1y/Hdg3gZNqI9HfUGNN4GfzQB/rXxLW+IhTM2m8TSzX/PNKz2mU3bVwV9SxdXVhBjs1EodcKv2Bbf86T+E6ONIaZQujQHGz+0NhQvOx5hEEqUILztjjsvu7QBHFCZ4zo0zh+HConpxxNxijcIM3Jyc6SG0EioWr25RKbhe58c/GfhRKlrIoe5g/+lx6vxc1pGZD1jyty6+ySX31lwalGHIQyyGC+uLMd/DoaR+BxcRBFYqZOwdu2v0m+64vN0Krm0B1NJ0eJg8J/7UzSbu6t5Q78bEvdpAgMBAAECggEBAIsiGqHQzNdHtTIM4iEIFqQTXUOdUfqdoyzXtaqu+8jfLU73WCJYRjEbUBuzMdV57vIMbTHB4Xyq/DwpUfKpGXJLpRUPtpdZmK15of690tOPr3TNht2COcek2IN+TdCGSsto1V7BGc7oe2c/vprfEt3mnZzqkFTjJXaD6zHYbqZRp/rR4gF3HuzGPIRCjUMROA8K1/rdnjeqhA8uNjYuar7z6IK583kzqQp+DvHz4uNFO0wfcHntRSrq4HUEgGbR7dRB3is/B7tRBJgVBs9XEh9DeismwGEp5QEJJ80LXVxIM7CXIpBqYvaPFoI/gyvcXAtwkMhBPUYDzCb0UiLKM00CgYEA6DgwKIKoxY891imii1smOs8xVPKbUXGhqWmKgjRTbyGcBDGvB0pIxql219n0S1uQO9HPtb71gLDkD9toHVv7JtOtEkKAb3ancAxhCv7XQF9KcGtTIwz3uul86jAJYx1LNBcoz6P44EkiSVdBATAxU0ZW1edR9MOcNLeVd/CO1WcCgYEAxE3ZVxMKVy8nvxvT3kDTmcRv7JdKuAcpEOQ5bl6u0Xh5RFPVkEwEqmWUAOIR4wMJn1JpNgCmEctzVSgvJ/KkghrMqDzHMydlcaoOMk0+QgKaALxZbfLTOdqnJzUrDwlG3xUOdK7E0rUJ8YDI343MdBau58ZlN9Q88vNpXzjkeq8CgYAAzBiWOwSbUQeUUaZGNjTmZMm1kfOojuxQKZWZJtbWfdYMSaNhMgcX74MB8LSfAZx4KBs/c31iSLkvuMAdwbAWDQn+Ew5f05d1kgVPRVq11N3rjuLFLwk2shCEuU9+8J3Oevnf5J85HecpO0x01ZaFw6sOpvGoFnfV0n+H3uKOpwKBgGApYpZtFVuwFeBgWWE+kGl6+PkFA+0yVMOZiSiv6SNjyMPX7X5KPt7Z866bD/gMvOH3FOMHXlLJPy4wCSmgi0XQqwCfBVXhrqgHjfzGKgFcFOTXX1ek5CQrIEbsnLM48CbHwdUW6APv+3b6h2w7ojQcCIIxhdPbfB/nbW7gB8yxAoGAS4bZqLXro6MkBS9gXbkpm2lbqh5qQyjoYfk0hqlom+uCtGMzMo96r1t8W1jD0aJ4uxaiTEHc8LvaKt1PHn8QmBXoPX62QNgn6ynHoJTQuNZAKqf9mNV1G3qNv8c3Dc27eKynsmUfFxisQEoDfbDMPHB+s4kKKYbJjUfDlzmCwEo=
    # APPId
    appId: wx40a8d17a751f71a0
    # appSecret
    appSecret: 745d325495ffe4d496ab4ac013b394c5
  # 移动开卡对接参数
  yidong:
    # 移动appId,需申请
    appId: 107320
    # 移动请求url前缀
    prefix-url: https://221.179.11.204:443/eaop/rest
    # BOSS 渠道编码
    wayId: DG05EC230154
    # BOSS 工号
    operatedId: DG05ECC211389
    # 私钥
    privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCnZLH2K11TqbYxVSxlQuGe23VIZI33snpkaFSMhZRhXzyvfef33bp6AcH/WYUuDTuOaSeTIGMbp1+bhR0XsavBVFn8xTSzoBU08WZGk2xbbs8ZYGV71uvfWc3l2/NmW9fiDZkDwqR3UqGHj/3K1h9rp1tSCuFfcR4un6eE8evadlQStOS6VCmi1mvbJJSBb8s3AukPXrkVVy9kOPDoOxZDO4pDF60US5Bc/8doWFt2BampngV51V/xvLeO1pECc7+c6wOfiMcCKBJiUHIiXkGjQYOCyVvTk/ndedZq53P2VIcV9gcCpyg7fsmfbDxWrIcrESXFvZYrq3K4tMxF/XS9AgMBAAECggEBAIUrfNDlCuhGOeXhZuJgZPJmkVW4eaE3YluDDNCGbNdSVwseVW9B/NqXj2jAJU3PxTO0wIEx4ObdLxigu9ZfyM6Tdsu7cAP/NR/bdIxtOGU9MJF1uQbeouQbo8xcQbWLF6KFQthU/1bWbB9XFIdL9c9FY0dP1KCDc4mQBkuj0wkGkvjOqQUvNrcOcTrQxi7LLI/07WQJPe5ZuA3xh1SjMjjsqSHeNhmmLH/TtlRCHzECg2LeZD6yhlQSRsTv0QARMu7+HLMT/iyJp4jS48lmNE29s+dQFv6EjT8lPbN3F9lvXH1OhZnPN5rblqsk8PtwacUVDccuqMGp59hMlmbY+hkCgYEA5kYyssEzl7uVz6/odzExyvpzES4eGmhbGar5U6pWYrNQALVRph7OISKJvD0ENUh4n4oFQx1MeHTggEo9DjBH/uVtlX9WZbuTVNIfUIn2+5APDAdw6kP+oMJJgMP3Y0oM5GwNZkLC6jJXT75bXbkJeILcrzU15LdbdUKgo1WAmmsCgYEAuhgdgrl7aAJtwGiexabPOT8ZFRBsz3bFCo9RmS9tAAVVpyimQmQNlTjokxlgF9NBsqYpnCVpK6VlA/Y7gOqctVqdv+bTDNmKH+Y5Ql+VmzwvqPDNu7gfeokIsk+fl8Pwt9elToY5Vhg44S7VR9iUgfyYQrGVFAljVoxRsvGmR3cCgYEA5bj6MuIS+jglSEARBXeKWRhpWqZLWcTpvT2X5iN0cpq4ITzB65unv96Y0bV9UTqvO1mRzlaYR0MyBEoLhvAboup23EFgi7UJUgEdRFeekXAx0qpJtyuGBhJNCax2TBM/sVEqPO41TTZTbDZXJ7TFAfhq+E95KGbTUrPZVZii52kCgYB+lhDc75cGdUB69a+q+4FSi51gOsAJj90RBdSXwy7+KKLPBlOyLaUfsIpkJYq8KjQydd0wHjKgQe2o+EIZu33Fz1InS7icsQ3IDS89+w8lfZdd8A/CdcjT7YtA3/DLp7mDFOmZtKwj6G4on1btfGHo2g4or+ucqZYtNV4Nv1tAmQKBgEZ1dlitVYykGTd8mxPnFoAvRWu29p+os7h+nzz0n+HX3kRvZ7I4lgtYCGjaTo70U2Hbg6MrYtstrdoT3P3Je4S2nXvrrYoYfFmxtF4Adu1V0/x0+Zh5G1nItnfXKnja6xRr0y7fbL1Fv27M5tEEl3GJAyqSrj+RSeaWBODicb0z
    # 商品ID
    productId: 1089970149115600896
    # 专区ID
    recommendAreaId: 789088566873464832
    #一级触点
    wayContactcode: A00001
    #二级触点
    waysubContactcode: A00001_B00004
    
    # sms相关
    sms:
      # 发送短信url
      url: http://************:15000
      # 用户名
      username: xueche
      # 密码
      password: Dgxue@2096
      # 模板ID
      templateId: f1fbffe861b0494bbd52820a397efcac
      # 模板ID
      confirmTemplateId: a50113c514f54b749795052fc7acdcb6
      # 签名
      sign: Tq8qv6Qqe
      # 公司名
      companyName: 生产13东莞移动公司区分公司

  #交通安全平台大屏配置
  trafficPlatform:
    key: 220538ba-9006-fbab-7f6a-ac91daa748df #请求Key

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为80
  port: 9820
  #domain: http://***************:10001
  domain: https://www.guanjiaxie.com:8089

  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.guangren: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码错误{maxRetryCount}次锁定10分钟
    maxRetryCount: 5

# Spring配置
spring:
  redis:
    database: 1
    host: ************
    port: 16379
    password: driverschool
  # 模板引擎
  thymeleaf:
    mode: HTML
    encoding: utf-8
    # 禁用缓存
    cache: false
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: static/i18n/messages
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss

  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true

  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: ***********************************************************************************************************************************************************
        username: driverschool
        password: iwC3wsZhzkZLhLfP@
        # 从数据源开关/默认关闭
      slave:
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: admin
        login-password: Admin@20230214
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

# MyBatis
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.guangren.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Shiro
shiro:
  user:
    # 登录地址
    loginUrl: /login
    # 权限认证失败地址
    unauthorizedUrl: /unauth
    # 首页地址
    indexUrl: /index
    # 验证码开关
    captchaEnabled: true
    # 验证码类型 math 数组计算 char 字符
    captchaType: math
  cookie:
    # 设置Cookie的域名 默认空，即当前访问的域名
    domain:
    # 设置cookie的有效访问路径
    path: /
    # 设置HttpOnly属性
    httpOnly: true
    # 设置Cookie的过期时间，天为单位
    maxAge: 30
    # 设置密钥，务必保持唯一性（生成方式，直接拷贝到main运行即可）Base64.encodeToString(CipherUtils.generateNewKey(128, "AES").getEncoded()) （默认启动生成随机秘钥，随机秘钥会导致之前客户端RememberMe Cookie无效，如设置固定秘钥RememberMe Cookie则有效）
    cipherKey:
  session:
    # Session超时时间，-1代表永不过期（默认30分钟）
    expireTime: 60
    # 同步session到数据库的周期（默认1分钟）
    dbSyncPeriod: 1
    # 相隔多久检查一次session的有效性，默认就是10分钟
    validationInterval: 10
    # 同一个用户最大会话数，比如2的意思是同一个账号允许最多同时两个人登录（默认-1不限制）
    maxSession: -1
    # 踢出之前登录的/之后登录的用户，默认踢出之前登录的用户
    kickoutAfter: false
  rememberMe:
    # 是否开启记住我
    enabled: false

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true



map:
  key: d52fbf4715fbb8eb47599233af3ee2af
  mapRequestUrl: https://restapi.amap.com/v3/place/text?


receive:
  ip: ***************
  port: 8089

bcfirm:
  # 服务url
  serviceUrl: https://************:8024/firmbank/online/PFCFoxSecurities
  # 密钥文件名
  certFileName: 95561E9100037498.pfx
  # 密钥文件密码
  certPassWord: 1

# sa-token配置
sa-token:
  # SSO-相关配置
  sso-client:
    # SSO-Server 端主机地址
    server-url: http://***************:9093/prod-api
    # 使用 Http 请求校验ticket (模式三)
    is-http: true
    auth-url: http://***************:9093/login?model=ticket
    curr-sso-logout-call: https://guanjiaxie.com:8089/sso/logoutCall
    client: 4
    #同步用户信息
    save-user-url: /system/client/user/saveClientUser
    del-user-url: /system/client/user/delClientUser
  sign:
    # API 接口调用秘钥
    secret-key: 8d35f269f166a8ffcdd13221e1d831f1
  appKey: fdgf!@&467?Hng
  appSecret: rfgdDfh@!76dJ?fkj865
  token-name: Authorization


  # 配置Sa-Token单独使用的Redis连接 （此处需要和SSO-Server端连接同一个Redis）
  alone-redis:
    # Redis数据库索引
    database: 1
    # Redis服务器地址
    host: ************
    # Redis服务器连接端口
    port: 16379
    # Redis服务器连接密码（默认为空）
    password: driverschool
    # 连接超时时间
    timeout: 20s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0

sso-client-index:
  url: https://guanjiaxie:8089/

forest:
  # 关闭 forest 请求日志打印
  log-enabled: false
  read-timeout: 60000
  connect-timeout: 60000
  timeout: 60000