package com.guangren.web.controller.business;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.util.StringUtil;
import com.guangren.business.domain.DivsionOrder;
import com.guangren.business.domain.ProvinceStudyTime;
import com.guangren.business.domain.School;
import com.guangren.business.domain.SchoolBranch;
import com.guangren.business.domain.SchoolContract;
import com.guangren.business.domain.SchoolRegistration;
import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.domain.SchoolStudentContract;
import com.guangren.business.domain.SchoolStudyCenter;
import com.guangren.business.domain.SpecialSchool;
import com.guangren.business.service.*;
import com.guangren.business.service.impl.YiDongShuiEQianService;
import com.guangren.business.vo.SchoolStudentExportVo;
import com.guangren.business.vo.StudentCountVo;
import com.guangren.common.annotation.Log;
import com.guangren.common.annotation.RepeatSubmit;
import com.guangren.common.config.RuoYiConfig;
import com.guangren.common.constant.Constants;
import com.guangren.common.core.controller.BaseController;
import com.guangren.common.core.domain.AjaxResult;
import com.guangren.common.core.domain.entity.SysDictData;
import com.guangren.common.core.domain.entity.SysOrganUser;
import com.guangren.common.core.page.TableDataInfo;
import com.guangren.common.core.text.Convert;
import com.guangren.common.enums.BusinessType;
import com.guangren.common.exception.BusinessException;
import com.guangren.common.utils.ImageUtil;
import com.guangren.common.utils.ShiroUtils;
import com.guangren.common.utils.StringUtils;
import com.guangren.common.utils.bean.BeanUtils;
import com.guangren.common.utils.poi.ExcelUtil;
import com.guangren.system.service.ISysConfigService;
import com.guangren.system.service.ISysDictDataService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学员Controller
 * 
 * <AUTHOR>
 * @date 2023-03-11
 */
@Controller
@RequestMapping("/business/student")
public class SchoolStudentController extends BaseController
{
    private String prefix = "business/student";

    @Autowired
    private ISchoolStudentService schoolStudentService;
    @Autowired
    private ISchoolService schoolService;
    @Autowired
    private ISchoolBranchService schoolBranchService;
    @Autowired
    private ISchoolRegistrationService schoolRegistrationService;
    @Autowired
    private ISchoolStudyCenterService studyCenterService;
    @Autowired
	private ISysDictDataService sysDictDataService;
    @Resource
    private ISysConfigService configService;
    @Autowired
    private ISchoolContactService schoolContactService;

    @Autowired
    private ISchoolContractService schoolContractService;
    @Autowired
    private YiDongShuiEQianService yiDongShuiEQianService;
    @Autowired
    private ISchoolStudentContractService schoolStudentContractService;
    @Resource
    private ISpecialSchoolService specialSchoolService;
    @Autowired
    private IDivsionOrderService divsionOrderService;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private IDivisionAccountService divisionAccountService;
    @Autowired
    private IStudentSkipContractService studentSkipContractService;


    @RequiresPermissions("business:student:view")
    @GetMapping()
    public String student(ModelMap mmap)
    {
    	String schoolId = ShiroUtils.getSysOrganUser().getSchoolId();
    	String branchId = ShiroUtils.getSysOrganUser().getBranchId();
    	String registrationId = ShiroUtils.getSysOrganUser().getRegistrationId(); 
    	
    	QueryWrapper<School> schoolQuery = new QueryWrapper<School>();
    	QueryWrapper<SchoolBranch> branchQuery = new QueryWrapper<SchoolBranch>();
    	QueryWrapper<SchoolRegistration> registrationQuery = new QueryWrapper<SchoolRegistration>();
    	if(StringUtils.isNotEmpty(schoolId)){
    		schoolQuery.eq("id", schoolId);
    		branchQuery.eq("school_id", schoolId);
    		registrationQuery.eq("school_id", schoolId);
    	}
    	if(StringUtils.isNotEmpty(branchId)){
        	branchQuery.eq("id", branchId);
        	registrationQuery.eq("branch_id", branchId);
        }
    	if(StringUtils.isNotEmpty(registrationId)){
    		registrationQuery.eq("id", registrationId);
    	}
    	List<School> schoolList= schoolService.list(schoolQuery);
        mmap.put("schoolList", schoolList);
                       
        List<SchoolBranch> schoolBranchList= schoolBranchService.list(branchQuery);
        mmap.put("schoolBranchList", schoolBranchList);
        
        List<SchoolRegistration> registrationList= schoolRegistrationService.list(registrationQuery);
        mmap.put("registrationList", registrationList);

        SysOrganUser sysOrganUser = ShiroUtils.getSysOrganUser();
        int isJIaxie = 0;
        if(sysOrganUser!=null && sysOrganUser.getUsername()!=null &&sysOrganUser.getUsername().equalsIgnoreCase("jiaxie")){
            isJIaxie =1;
        }
        //返回登录人员所处的角色
        mmap.put("isJiaxie",isJIaxie );

        return prefix + "/student";
    }

    @RequiresPermissions("business:student:view")
    @GetMapping("/payCode")
    public String payCode(ModelMap mmap)
    {
        School school= new School();
        super.setManageParams(school);
        List<School> schoolList= schoolService.selectSchoolList(school);
        mmap.put("schoolList", schoolList);
        SchoolBranch branch= new SchoolBranch();
        super.setManageParams(branch);
        List<SchoolBranch> schoolBranchList= schoolBranchService.selectSchoolBranchList(branch);
        mmap.put("schoolBranchList", schoolBranchList);

        SchoolRegistration registration= new SchoolRegistration();
        super.setManageParams(registration);
        List<SchoolRegistration> registrationList= schoolRegistrationService.selectSchoolRegistrationList(registration);
        mmap.put("registrationList", registrationList);
        return prefix + "/payCode";
    }

    /**
     * 查询学员列表
     */
    @RequiresPermissions("business:student:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SchoolStudent schoolStudent)
    {
        Map<String, Object> params = schoolStudent.getParams();
        if (params == null) params = new HashMap<>();
        params.put("schoolId", ShiroUtils.getSysOrganUser().getSchoolId());
        params.put("banchId", ShiroUtils.getSysOrganUser().getBranchId());
        params.put("registrationId", ShiroUtils.getSysOrganUser().getRegistrationId());
        schoolStudent.setParams(params);
        schoolStudent.setIsQuit(0);
        startPage();
        List<SchoolStudent> list = schoolStudentService.customSelectSchoolStudentList(schoolStudent);
        return getDataTable(list);
    }

    /**
     * 导出学员列表
     */
    @RequiresPermissions("business:student:export")
    @Log(title = "学员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SchoolStudent schoolStudent,String ids)
    {
        Map<String, Object> params = schoolStudent.getParams();
        if (params == null) params = new HashMap<>();
        params.put("schoolId", ShiroUtils.getSysOrganUser().getSchoolId());
        params.put("banchId", ShiroUtils.getSysOrganUser().getBranchId());
        params.put("registrationId", ShiroUtils.getSysOrganUser().getRegistrationId());
        schoolStudent.setParams(params);
        schoolStudent.setLimit(5000);
        schoolStudent.setIsQuit(0);
        if (StringUtils.isNotEmpty(ids)) {
            List<String> studentIds = Arrays.asList(ids.split(","));
            if (studentIds.size() > 5000) {
                throw new BusinessException("单次最多允许导出5000条数据，请尽量缩小查询范围");
            }
            schoolStudent.setDropOutIds(studentIds);
        }
        List<SchoolStudent> list = schoolStudentService.customSelectSchoolStudentList(schoolStudent);

        ExcelUtil<SchoolStudent> util = new ExcelUtil<SchoolStudent>(SchoolStudent.class);
        return util.exportExcel(list, "学员数据");
    }

    @Log(title = "学员", businessType = BusinessType.EXPORT)
    @PostMapping("/payCodeExport")
    @ResponseBody
    public AjaxResult payCodeExport(SchoolStudent schoolStudent,String ids){
        Map<String, Object> params = schoolStudent.getParams();
        if (params == null) params = new HashMap<>();
        params.put("schoolId", ShiroUtils.getSysOrganUser().getSchoolId());
        params.put("banchId", ShiroUtils.getSysOrganUser().getBranchId());
        params.put("registrationId", ShiroUtils.getSysOrganUser().getRegistrationId());
        schoolStudent.setParams(params);
        List<SchoolStudent> list = schoolStudentService.selectSchoolStudentList(schoolStudent);

        List<SchoolStudent> useList = new ArrayList<SchoolStudent>(Arrays.asList(new SchoolStudent[list.size()]));
        Collections.copy(useList, list);

        // 条件过滤
        if (StringUtils.isNotEmpty(ids)){
            useList.clear();
            for (String studentId : Convert.toStrArray(ids)){
                for (SchoolStudent item : list)
                {
                    if (item.getId().equals(studentId))
                    {
                        useList.add(item);
                    }
                }
            }
        }

        List<SchoolStudentExportVo> resultList = new ArrayList<SchoolStudentExportVo>();
        for (SchoolStudent item:useList) {
            SchoolStudentExportVo studentExportVo= new SchoolStudentExportVo();
            BeanUtils.copyBeanProp(studentExportVo, item);
            resultList.add(studentExportVo);
        }
        ExcelUtil<SchoolStudentExportVo> util = new ExcelUtil<SchoolStudentExportVo>(SchoolStudentExportVo.class);
        return util.exportExcel(resultList, "学员数据");
    }


    /**
     * 新增学员
     */
    @GetMapping("/add")
    public String add(ModelMap mmap)
    {
        /*School school= new School();
        super.setManageParams(school);
        List<School> schoolList= schoolService.selectSchoolList(school);
        mmap.put("schoolList", schoolList);
        SchoolBranch branch= new SchoolBranch();
        super.setManageParams(branch);
        List<SchoolBranch> schoolBranchList= schoolBranchService.selectSchoolBranchList(branch);
        mmap.put("schoolBranchList", schoolBranchList);

        SchoolRegistration registration= new SchoolRegistration();
        super.setManageParams(registration);
        List<SchoolRegistration> registrationList= schoolRegistrationService.selectSchoolRegistrationList(registration);
        mmap.put("registrationList", registrationList);*/
        mmap.put("sysOrganType",ShiroUtils.getSysOrganUser().getOrganType());
        
        String schoolId = ShiroUtils.getSysOrganUser().getSchoolId();
    	String branchId = ShiroUtils.getSysOrganUser().getBranchId();
    	String registrationId = ShiroUtils.getSysOrganUser().getRegistrationId(); 
    	
    	QueryWrapper<School> schoolQuery = new QueryWrapper<School>();
    	QueryWrapper<SchoolBranch> branchQuery = new QueryWrapper<SchoolBranch>();
    	QueryWrapper<SchoolRegistration> registrationQuery = new QueryWrapper<SchoolRegistration>();
    	if(StringUtils.isNotEmpty(schoolId)){
    		schoolQuery.eq("id", schoolId);
    		branchQuery.eq("school_id", schoolId);
    		registrationQuery.eq("school_id", schoolId);
    	}
    	if(StringUtils.isNotEmpty(branchId)){
        	branchQuery.eq("id", branchId);
        	registrationQuery.eq("branch_id", branchId);
        }
    	if(StringUtils.isNotEmpty(registrationId)){
    		registrationQuery.eq("id", registrationId);
    	}
    	List<School> schoolList= schoolService.list(schoolQuery);
        mmap.put("schoolList", schoolList);
                       
        List<SchoolBranch> schoolBranchList= schoolBranchService.list(branchQuery);
        mmap.put("schoolBranchList", schoolBranchList);
        
        List<SchoolRegistration> registrationList= schoolRegistrationService.list(registrationQuery);
        mmap.put("registrationList", registrationList);
        
        return prefix + "/add";
    }

    /**
     * 新增保存学员
     */
    @RepeatSubmit(interval = 1000, message = "请求过于频繁")
    @RequiresPermissions("business:student:add")
    @Log(title = "学员", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SchoolStudent schoolStudent)
    {
        if (schoolStudent.getIsCheck() == 1){
            schoolStudent.setRegisteDate(new Date());
            schoolStudent.setStatus(2);
        }
        schoolStudent.setIsSupervise(1);
        return toAjax(schoolStudentService.insertSchoolStudent(schoolStudent));
    }

    /**
     * 修改学员
     */
    @RequiresPermissions("business:student:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap)
    {
        SchoolStudent schoolStudent = schoolStudentService.selectSchoolStudentById(id);
        schoolStudent.setHujiProvince(schoolStudent.getRealProvince());
        schoolStudent.setHujiCity(schoolStudent.getRealCity());
        schoolStudent.setHujiTown(schoolStudent.getRealTown());
        if (StringUtils.isNotEmpty(schoolStudent.getRealAddress())) {
            schoolStudent.setHujiAddress(schoolStudent.getRealAddress());
        }else {
            schoolStudent.setHujiAddress("");
        }
        mmap.put("schoolStudent", schoolStudent);

        if (StringUtils.isNotEmpty(schoolStudent.getSchoolId())){
            SchoolStudyCenter schoolStudyCenter = new SchoolStudyCenter();
            schoolStudyCenter.setSchoolId(schoolStudent.getSchoolId());
            List<SchoolStudyCenter> studyCenterList= studyCenterService.selectSchoolStudyCenterList(schoolStudyCenter);
            for (SchoolStudyCenter item:studyCenterList) {
                if (item.getOrganUser() != null){
                    item.setOrganText(item.getOrganUser().getOrganName());
                }
            }
            mmap.put("studyCenterList", studyCenterList);
        }else{
            mmap.put("studyCenterList", new ArrayList<SchoolStudyCenter>());
        }
        return prefix + "/edit";
    }


    /**
     * 查看学员
     */
    @RequiresPermissions("business:student:view")
    @GetMapping("/view/{id}")
    public String view(@PathVariable("id") String id, ModelMap mmap)
    {
        SchoolStudent schoolStudent = schoolStudentService.selectSchoolStudentById(id);
        schoolStudent.setHujiProvince(schoolStudent.getRealProvince());
        schoolStudent.setHujiCity(schoolStudent.getRealCity());
        schoolStudent.setHujiTown(schoolStudent.getRealTown());
        if (StringUtils.isNotEmpty(schoolStudent.getRealAddress())) {
            schoolStudent.setHujiAddress(schoolStudent.getRealAddress());
        }else {
            schoolStudent.setHujiAddress("");
        }
        mmap.put("schoolStudent", schoolStudent);

        if (StringUtils.isNotEmpty(schoolStudent.getSchoolId())){
            SchoolStudyCenter schoolStudyCenter = new SchoolStudyCenter();
            schoolStudyCenter.setSchoolId(schoolStudent.getSchoolId());
            List<SchoolStudyCenter> studyCenterList= studyCenterService.selectSchoolStudyCenterList(schoolStudyCenter);
            for (SchoolStudyCenter item:studyCenterList) {
                if (item.getOrganUser() != null){
                    item.setOrganText(item.getOrganUser().getOrganName());
                }
            }
            mmap.put("studyCenterList", studyCenterList);
        }else{
            mmap.put("studyCenterList", new ArrayList<SchoolStudyCenter>());
        }
        return prefix + "/view";
    }

    /**
     * 修改已监管学员
     */
    @RequiresPermissions("business:student:edit")
    @GetMapping("/supervise/{id}")
    public String supervise(@PathVariable("id") String id, ModelMap mmap)
    {
        SchoolStudent schoolStudent = schoolStudentService.selectSchoolStudentById(id);
        schoolStudent.setHujiProvince(schoolStudent.getRealProvince());
        schoolStudent.setHujiCity(schoolStudent.getRealCity());
        schoolStudent.setHujiTown(schoolStudent.getRealTown());
        if (StringUtils.isNotEmpty(schoolStudent.getRealAddress())) {
            schoolStudent.setHujiAddress(schoolStudent.getRealAddress());
        }else {
            schoolStudent.setHujiAddress("");
        }
        mmap.put("schoolStudent", schoolStudent);
        return prefix + "/supervise";
    }

    /**
     * 学员退学
     */
    @RequiresPermissions("business:student:edit")
    @GetMapping("/dropOut/{id}")
    @RepeatSubmit(interval = 5000)
    public String dropOut(@PathVariable("id") String id, ModelMap mmap)
    {
        SchoolStudent schoolStudent = schoolStudentService.selectSchoolStudentById(id);
        if(schoolStudent.getStatus()==null) {
        	schoolStudent.setStatus(1);
        }
        
        SysDictData query = new SysDictData();
		query.setDictType("student_status");
		query.setStatus("0");
		List<SysDictData> list = sysDictDataService.selectDictDataList(query);
		
		for(SysDictData dict : list) {
			if(dict.getDictValue().equals(schoolStudent.getStatus().toString())) {
				mmap.put("statusLable", dict.getDictLabel());
			}
		}
        mmap.put("schoolStudent", schoolStudent);
        return prefix + "/dropOut";
    }

    /**
     * 修改保存学员
     */
    @RequiresPermissions("business:student:edit")
    @Log(title = "学员", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SchoolStudent schoolStudent)
    {
        schoolStudentService.updateSchoolStudent(schoolStudent);
        return AjaxResult.success("修改成功，请重新审核。");
    }

    /**
    * 验证参数唯一性
    */
    @PostMapping("/checkUnique")
    @ResponseBody
    public String checkUnique(SchoolStudent schoolStudent)
    {
        return schoolStudentService.checkUnique(schoolStudent);
    }
 

    /**
     * 删除学员
     */
    @RequiresPermissions("business:student:remove")
    @Log(title = "学员", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids, String delReason)
    {
        return toAjax(schoolStudentService.deleteSchoolStudentByIds(ids, delReason));
    }

    /**
     * 审核学员
     * <AUTHOR>
     * @date 2024/2/22 11:28
     * @param id *
     * @param modelMap *
     * @return java.lang.String *
     */
    @RequiresPermissions("business:student:check")
    @GetMapping("/check/{id}")
    @RepeatSubmit(interval = 5000)
    public String check(@PathVariable("id") String id, ModelMap modelMap) {
        SchoolStudent schoolStudent = schoolStudentService.getById(id);
        SchoolContract schoolContract = schoolContractService.getById(schoolStudent.getSchoolId());
        if(schoolContract != null && StringUtils.isNotEmpty(schoolContract.getVars())){
            JSONObject varJson = JSON.parseObject(schoolContract.getVars());
            schoolStudent.setAttachedItem(varJson.getString("attachedItem"));
        }
        if(schoolStudent.getPayFee()==null || schoolStudent.getPayFee().compareTo(BigDecimal.ZERO)<=0){
            String trainFee = sysConfigService.selectConfigByKey("student.pay.train.fee");
            schoolStudent.setContractFee(new BigDecimal(trainFee));
            schoolStudent.setPayFee(schoolStudent.getContractFee());
        }
        modelMap.put("schoolStudent", schoolStudent);
        return prefix + "/check";
    }


    @RequiresPermissions("business:student:check")
    @Log(title = "学员", businessType = BusinessType.UPDATE)
    @PostMapping( "/oldCheckStudent")
    @ResponseBody
    public AjaxResult oldCheckStudent(String id){
        SchoolStudent schoolStudent= schoolStudentService.getById(id);
        if(StringUtils.isEmpty(schoolStudent.getStudyCenterId()) || schoolStudent.getStudyCenterId().equals("0")){
            return AjaxResult.warn("学员" + schoolStudent.getName() + "未选择学时平台。");
        }

        if(schoolStudent.getIsCheck() == 1){
            return AjaxResult.warn("学员" + schoolStudent.getName() + "已经是已审核状态。");
        }

        schoolStudent.setIsCheck(1);
        schoolStudent.setRegisteDate(new Date());
        schoolStudent.setStatus(2);
        schoolStudentService.checkStudent(schoolStudent);
        return AjaxResult.success();
    }


    @PostMapping( "/isMustSignContract")
    @ResponseBody
    public AjaxResult isMustSignContract(String id){
        SchoolStudent schoolStudent = schoolStudentService.getById(id);
        SpecialSchool specialSchool = specialSchoolService.getById(schoolStudent.getSchoolId());
        JSONObject jsonObject = new JSONObject();
        if(specialSchool != null){
            jsonObject.put("isMustSignContract",false);
            return success(jsonObject);
        }
        if(schoolStudent.getOriginData() == 2){
            jsonObject.put("isMustSignContract",false);
            return success(jsonObject);
        }
        String expireDate = sysConfigService.selectConfigByKey("import.pay.expire.date");
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isEmpty(expireDate)){
            expireDate = "2024-03-15 00:00:00";
        }
        Date expire = DateUtil.parse(expireDate,"yyyy-MM-dd HH:mm:ss");
        if(schoolStudent.getPrepareRegisteDate().before(expire)){
            jsonObject.put("isMustSignContract",false);
            return success(jsonObject);
        }

        long count = divsionOrderService.count(new LambdaQueryWrapper<>(DivsionOrder.class)
                .eq(DivsionOrder::getStudentId,schoolStudent.getId())
                .eq(DivsionOrder::getIsPay,1));
        if(count > 0){
            jsonObject.put("isMustSignContract",false);
            return success(jsonObject);
        }


        jsonObject.put("isMustSignContract",true);
        return success(jsonObject);
    }

    /**
     * 审核学员保存
     */
    @RequiresPermissions("business:student:check")
    @Log(title = "学员", businessType = BusinessType.UPDATE)
    @PostMapping( "/checkStudent")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult checkStudentSave(SchoolStudent student){
        try {
            schoolStudentService.checkStudentSave(student, true);
            Integer skipContractStatus = studentSkipContractService.querySkipContractStatus();
            boolean isSkipContract = skipContractStatus.equals(Constants.SKIP_CONTRACT_SWITCH_ON);
            if(isSkipContract){
                return success("当前管理员已开启跳过合同签署步骤，可直接提醒学员在小程序上完成缴费。");
            }
            return success("已生成合同文件，提醒学员在小程序上签署。");
        }catch (BusinessException e) {
            logger.error("replaceVariablesInWord error", e);
            return AjaxResult.error(e.getMessage());
        }catch(Exception ex){
            logger.error("replaceVariablesInWord error",ex);
            return AjaxResult.error("生成合同失败:"+ex.getMessage());
        }
    }



    /**
     * 学员退学保存
     */
    @Log(title = "学员", businessType = BusinessType.OTHER)
    @PostMapping("/dropOutSave")
    @ResponseBody
    @RepeatSubmit(interval = 5000)
    public AjaxResult dropOutSave(SchoolStudent schoolStudent)
    {
        try {
            if (schoolStudentService.dropOutSave(schoolStudent)) {
                return AjaxResult.success();
            } else {
                return AjaxResult.error();
            }
        }catch(Exception ex){
            logger.error("退学学员出错",ex);
        }
        return error();
    }


    /**
     * 同步学员学时
     */
    @RequiresPermissions("business:student:check")
    @Log(title = "学员", businessType = BusinessType.UPDATE)
    @PostMapping( "/studyCenterSyn")
    @ResponseBody
    public AjaxResult studyCenterSyn(String ids)
    {
        String arrayIds[] = Convert.toStrArray(ids);
        StringBuffer msg=new StringBuffer();
        List<SchoolStudent> studentList= new ArrayList<SchoolStudent>();
        for (String id : arrayIds) {
            SchoolStudent schoolStudent= schoolStudentService.selectSchoolStudentById(id);
            if (schoolStudent.getIsCheck() ==0){
                msg.append("学员" + schoolStudent.getName() + "未审核，请审核之后同步。");
                msg.append("<br/>");
                continue;
            }
            if (StringUtils.isEmpty(schoolStudent.getStudyCenterId())){
                msg.append("学员" + schoolStudent.getName() + "未设置学时中心，无法同步。");
                msg.append("<br/>");
                continue;
            }
            School school  = schoolService.getById(schoolStudent.getSchoolId());
            if(school == null) {
            	msg.append("学员" + schoolStudent.getName() + "未归属任何学校。");
                msg.append("<br/>");
                continue;
            }
            if(school.getIsSupervise()==1) {
	            if(schoolStudent.getSuperviseFeeIsOk() == 0 && schoolStudent.getOriginData() != 2) {
	            	 msg.append("学员" + schoolStudent.getName() + "监管资金未到账,无法同步到计时平台");
	                 msg.append("<br/>");
	                 continue;
	            }
            }
            if (schoolStudent.getStudyCenterIsSyn() ==1){
                msg.append("学员" + schoolStudent.getName() + "已经上报计时平台状态。");
                msg.append("<br/>");
                continue;
            }
            studentList.add(schoolStudent);
        }
        if (msg.length()>0){
            return AjaxResult.error(msg.toString());
        }

        try {
            schoolStudentService.submitStudentAddDataToStudyCenter(studentList);
        }catch (Exception ex){
            logger.error(ex.getMessage(),ex);
            return AjaxResult.error("上报计时平台失败 "+ex.getMessage()+"，请联系对应驾校处理信息，若联系后无法处理，请反馈对应学时平台具体情况");
        }
        return AjaxResult.success();
    }


    /**
     * 根据身份证识别出生日期、性别
     * @return
     */
    @PostMapping("/getIdentityInfo")
    @ResponseBody
    public AjaxResult getIdentityInfo(SchoolStudent student){
        JSONObject item= new JSONObject();
        int gender = 1;
        String birthday = "19700101";
        if(student.getIdentityType().equals("身份证")){
            if(student.getIdentity().length()==18){
                birthday = student.getIdentity().substring(6, 14);
                gender = Integer.parseInt(student.getIdentity().substring(16, 17));
            }
            if(student.getIdentity().length()==15){
                birthday = "19"+student.getIdentity().substring(6, 12);
                gender = Integer.parseInt(student.getIdentity().substring(14));
            }
        }
        student.setGender(gender % 2 == 0 ? 0 : 1);
        student.setBirthday(DateUtil.format(DateUtil.parse(birthday,"yyyyMMdd"),"yyyy-MM-dd"));
        item.put("gender",student.getGender());
        item.put("birthday",student.getBirthday());
        return success(item);
    }

    /**
     * 学员统计数据
     */
    @GetMapping("/studentCount")
    @RequiresPermissions("business:student:studentCount")
    public String schoolCount(SchoolStudent schoolStudent, ModelMap modelMap) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("schoolId", ShiroUtils.getSysOrganUser().getSchoolId());
        params.put("branchId", ShiroUtils.getSysOrganUser().getBranchId());
        params.put("registrationId", ShiroUtils.getSysOrganUser().getRegistrationId());
        schoolStudent.setParams(params);
        StudentCountVo studentCount = schoolStudentService.getSchoolStudentCount(schoolStudent);
        modelMap.put("studentCount", studentCount);
        return prefix + "/student_count";
    }


    /**
     * 检查学员信息是否同步到学时平台
     */
    @ResponseBody
    @PostMapping("/checkStudyCenterIsSyn")
    public AjaxResult checkStudyCenterIsSyn(@RequestBody SchoolStudent schoolStudent) {
        SchoolStudent student = schoolStudentService.getById(schoolStudent.getId());
        if (student.getStudyCenterIsSyn() == 1) {
            return AjaxResult.error("该学员信息已同步到计时平台，不允许修改。");
        }
        return AjaxResult.success();
    }
    
    @ResponseBody
    @PostMapping("/updateStudyCenterSynStatus")
    public AjaxResult updateStudyCenterSynStatus(@RequestBody SchoolStudent schoolStudent) {
        SchoolStudent student = schoolStudentService.getById(schoolStudent.getId());
        if (student.getStudyCenterIsSyn() == 1) {
            return AjaxResult.error("该学员信息已同步到计时平台，不允许修改。");
        }
        student.setStudyCenterIsSyn(1);
        student.setStudyCenterSynDate(new Date());
        schoolStudentService.updateById(student);
        return AjaxResult.success();
    }

    /**
     * 修改同步学时状态
     * <AUTHOR>
     * @date 2023/10/10 10:57
     * @param id *
     * @param modelMap *
     * @return java.lang.String *
     */
    @RequiresPermissions("business:student:editIsSyn")
    @GetMapping("/editIsSyn/{id}")
    public String editIsSyn(@PathVariable("id") String id, ModelMap modelMap) {
        SchoolStudent schoolStudent = schoolStudentService.getById(id);
        modelMap.put("schoolStudent", schoolStudent);
        return prefix + "/editIsSyn";
    }

    /**
     * 修改同步学时状态
     * <AUTHOR>
     * @date 2023/10/10 11:02
     * @param schoolStudent *
     * @return com.guangren.common.core.domain.AjaxResult *
     */
    @RequiresPermissions("business:student:editIsSyn")
    @Log(title = "学员", businessType = BusinessType.OTHER)
    @ResponseBody
    @PostMapping("/editIsSynSave")
    public AjaxResult editIsSynSave(SchoolStudent schoolStudent) {
        schoolStudentService.updateById(schoolStudent);
        return success();
    }


    /**
     * 检查学员的学号是否存在
     * <AUTHOR>
     * @date 2023/11/3 14:52
     * @param id 学员id
     * @return com.guangren.common.core.domain.AjaxResult *
     */
    @GetMapping("/checkStunum")
    @ResponseBody
    public AjaxResult checkStunum(String id) {
        SchoolStudent student = schoolStudentService.getById(id);
        if (StringUtils.isNotEmpty(student.getStunum())) {
            return error("该学员已有学号，无法修改");
        }
        return success();
    }

    /**
     * 从计时平台获取学员的学号
     * <AUTHOR>
     * @date 2023/11/3 13:59
     * @return com.guangren.common.core.domain.AjaxResult *
     */
    @PostMapping("/getPlatformStudentNumber")
    @ResponseBody
    public AjaxResult getPlatformStudentNumber(@RequestBody SchoolStudent schoolStudent) {
    	try {
	        JSONArray body = schoolStudentService.getStudentInfoFromStudyTimeCenter(0, schoolStudent.getIdentity());
	        if (body != null) {
	            JSONObject result = new JSONObject();
	            String stunum = body.getJSONObject(0).getString("stunum");
	            if(StringUtil.isEmpty(stunum)) {
	            	return error("暂未查询到学号");
	            }
	            result.put("stunum", stunum);
	            return success(result);
	        }
	        return error("查询失败");
    	}catch(Exception ex) {
    		logger.error("getPlatformStudentNumber error",ex);
    		return error("查询接口返回："+ex.getMessage());
    	}
    }

    /**
     * 修改学号
     * <AUTHOR>
     * @date 2023/11/3 10:16
     * @param id *
     * @param modelMap *
     * @return java.lang.String *
     */
    @RequiresPermissions("business:student:editStunum")
    @GetMapping("/editStunum/{id}")
    public String editStunum(@PathVariable("id") String id, ModelMap modelMap) {
        SchoolStudent schoolStudent = schoolStudentService.getById(id);
        modelMap.put("schoolStudent", schoolStudent);
        return prefix + "/editStunum";
    }

    /**
     * 修改学号保存
     * <AUTHOR>
     * @date 2023/11/3 10:20
     * @param schoolStudent *
     * @return com.guangren.common.core.domain.AjaxResult *
     */
    @RequiresPermissions("business:student:editStunum")
    @Log(title = "学员", businessType = BusinessType.OTHER)
    @PostMapping("/editStunumSave")
    @ResponseBody
    public AjaxResult editStunumSave(SchoolStudent schoolStudent) {
    	schoolStudent.setStunum(schoolStudent.getStunum().toUpperCase());
        int row = schoolStudentService.editStunumSave(schoolStudent);
        return toAjax(row);
    }

    /**
     * 查询学员在计时平台的数据
     * <AUTHOR>
     * @date 2023/11/3 17:22
     * @param id 学员id
     * @param modelMap *
     * @return java.lang.String *
     */
    @RequiresPermissions("business:student:studyTimeList")
    @GetMapping("/studyTimeList/{id}")
    public String studyTimeView(@PathVariable("id") String id, ModelMap modelMap) {
        SchoolStudent schoolStudent = schoolStudentService.getById(id);
        modelMap.put("schoolStudent", schoolStudent);
        return prefix + "/studyTimeList";
    }

    /**
     * 查询学员在计时平台的数据
     * <AUTHOR>
     * @date 2023/11/3 15:27
     * @param stunum 学号
     * @return com.guangren.common.core.page.TableDataInfo *
     */
    @RequiresPermissions("business:student:studyTimeList")
    @PostMapping("/studyTimeList")
    @ResponseBody
    public TableDataInfo studyTimeList(String stunum) {
        if (StringUtils.isNotEmpty(stunum) && !"null".equals(stunum)) {
            List<ProvinceStudyTime> provinceStudyTimes = schoolStudentService.getStudentInfoStudyTime(stunum);
            return getDataTable(provinceStudyTimes);
        }else {
            throw new BusinessException("该学员未设置学号，请先设置学号再查询");
        }
    }

    /**
     * 检查是否签署了合同
     * <AUTHOR>
     * @date 2024/2/29 14:59
     * @param id *
     * @return com.guangren.common.core.domain.AjaxResult *
     */
    @PostMapping("/checkContract")
    @ResponseBody
    public AjaxResult checkContract(String id) {
        SchoolStudentContract schoolStudentContract = schoolStudentContractService.getById(id);
        if (schoolStudentContract != null && schoolStudentContract.isSign==1) {
            return success();
        }else {
            return error("请学员签署合同后再查看");
        }
    }

    /**
     * 查看签署合同
     * <AUTHOR>
     * @date 2024/2/29 13:44
     * @param id *
     * @param modelMap *
     * @return java.lang.String *
     */
    @RequiresPermissions("business:student:viewContract")
    @GetMapping("/viewContract/{id}")
    public String viewContract(@PathVariable("id") String id, ModelMap modelMap) {
        SchoolStudent student = schoolStudentService.getById(id);
        //SpecialSchool specialSchool = specialSchoolService.getById(student.getSchoolId());
        SchoolStudentContract schoolStudentContract = schoolStudentContractService.getById(id);
        if(schoolStudentContract != null && schoolStudentContract.getHasView()==0){
            JSONObject contractJson = yiDongShuiEQianService.downloadContract(schoolStudentContract.getContractNumber());
            String filepath = RuoYiConfig.getDownloadPath()+"contract/"+schoolStudentContract.getContractNumber()+".pdf";
            File file = new File(filepath);
            try {
                Base64.decodeToFile(contractJson.getString("file"), file);
                schoolStudentContract.setHasView(1);
                schoolStudentContractService.updateById(schoolStudentContract);
            }catch(Exception ex){
                logger.error("下载合同出错",ex);
                try{
                    file.deleteOnExit();
                    boolean result = file.createNewFile();
                    if(result) {
                        Base64.decodeToFile(contractJson.getString("file"), file);
                        schoolStudentContract.setHasView(1);
                        schoolStudentContractService.updateById(schoolStudentContract);
                    }else{
                        throw new BusinessException("创建文件出错");
                    }
                }catch(Exception e){
                    logger.error("再次下载合同出错",e);
                }
            }
        }
        modelMap.put("schoolStudentContract", schoolStudentContract);
        //modelMap.put("hasSpecialSchool", specialSchool != null);  //好方向先不用在后台支付
        //modelMap.put("hasSpecialSchool", student.getSuperviseFeeIsOk()==0);
        modelMap.put("hasSpecialSchool", false);
        return prefix + "/viewContract";
    }

    /**
     * 产生支付二维码
     * <AUTHOR>
     * @date 2024/2/29 15:09
     * @param id *
     * @param modelMap *
     * @return java.lang.String *
     */
    @GetMapping("/createPayCode/{id}")
    public String createPayCode(@PathVariable("id") String id, ModelMap modelMap) {
        SchoolStudent schoolStudent = schoolStudentService.selectSchoolStudentById(id);
        //SpecialSchool specialSchool = specialSchoolService.getById(schoolStudent.getSchoolId());
        try {
            JSONObject json = divsionOrderService.createMiniappQrcode(schoolStudent);//.createSpecialSchoolQrcode(schoolStudent, specialSchool);
            String qrCodeStr = json.getString("qrCode");
            String path = RuoYiConfig.getProfile() + "/qrcode";
            if(!new File(path).exists()){
                new File(path).mkdirs();
            }
            String filePath = path + "/" + UUID.fastUUID() + ".png";
            File qrFile = new File(filePath);
            if(!qrFile.exists()) {
                qrFile.createNewFile();
            }
            QrCodeUtil.generate(qrCodeStr, 300,300, qrFile);
            String qrCode = ImageUtil.imageToBase64(qrFile);
            modelMap.put("payCode", qrCode);
        }catch (Exception e) {
            logger.error("createPayCode method error:", e);
        }
        return prefix + "/createPayCode";
    }

    /**
     * 导入广仁特殊学员数据
     */
    @RequiresPermissions("business:student:grStudentImport")
    @Log(title = "学员", businessType = BusinessType.IMPORT)
    @PostMapping("/grStudentImport")
    @ResponseBody
    public AjaxResult importData(MultipartFile file) {
        int rows = schoolStudentService.grStudentImport(file);
        return AjaxResult.success("导入成功！共更新" + rows + "条数据");
    }
}
