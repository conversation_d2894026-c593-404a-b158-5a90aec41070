package com.guangren.web.controller.business;

import java.util.List;

import com.guangren.business.vo.AreaData;
import com.guangren.common.utils.OkRequestCompent;
import com.guangren.common.utils.map.PoiResultBean;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.guangren.common.annotation.Log;
import com.guangren.common.enums.BusinessType;
import com.guangren.business.domain.BasicArea;
import com.guangren.business.service.IBasicAreaService;
import com.guangren.common.core.controller.BaseController;
import com.guangren.common.core.domain.AjaxResult;
import com.guangren.common.utils.poi.ExcelUtil;
import com.guangren.common.utils.StringUtils;
import com.guangren.common.core.domain.Ztree;

/**
 * 标准行政区域Controller
 * 
 * <AUTHOR>
 * @date 2023-02-15
 */
@Controller
@RequestMapping("/business/area")
public class BasicAreaController extends BaseController
{
    private String prefix = "business/area";

    @Autowired
    private IBasicAreaService basicAreaService;
    @Autowired
    private OkRequestCompent okRequestCompent;

    @RequiresPermissions("business:area:view")
    @GetMapping()
    public String area()
    {
        return prefix + "/area";
    }

    /**
     * 查询标准行政区域树列表
     */
    @RequiresPermissions("business:area:list")
    @PostMapping("/list")
    @ResponseBody
    public List<BasicArea> list(BasicArea basicArea)
    {
        List<BasicArea> list = basicAreaService.selectBasicAreaList(basicArea);
        return list;
    }

    /**
     * 导出标准行政区域列表
     */
    @RequiresPermissions("business:area:export")
    @Log(title = "标准行政区域", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BasicArea basicArea)
    {
        List<BasicArea> list = basicAreaService.selectBasicAreaList(basicArea);
        ExcelUtil<BasicArea> util = new ExcelUtil<BasicArea>(BasicArea.class);
        return util.exportExcel(list, "标准行政区域数据");
    }

    /**
     * 新增标准行政区域
     */
    @GetMapping(value = { "/add/{id}", "/add/" })
    public String add(@PathVariable(value = "id", required = false) Long id, ModelMap mmap)
    {
        if (StringUtils.isNotNull(id))
        {
            mmap.put("basicArea", basicAreaService.selectBasicAreaById(id));
        }
        return prefix + "/add";
    }

    /**
     * 新增保存标准行政区域
     */
    @RequiresPermissions("business:area:add")
    @Log(title = "标准行政区域", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BasicArea basicArea)
    {
        return toAjax(basicAreaService.insertBasicArea(basicArea));
    }

    /**
     * 修改标准行政区域
     */
    @RequiresPermissions("business:area:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        BasicArea basicArea = basicAreaService.selectBasicAreaById(id);
        mmap.put("basicArea", basicArea);
        return prefix + "/edit";
    }

    /**
     * 修改保存标准行政区域
     */
    @RequiresPermissions("business:area:edit")
    @Log(title = "标准行政区域", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BasicArea basicArea)
    {
        return toAjax(basicAreaService.updateBasicArea(basicArea));
    }

    /**
    * 验证参数唯一性
    */
    @PostMapping("/checkUnique")
    @ResponseBody
    public String checkUnique(BasicArea basicArea)
    {
        return basicAreaService.checkUnique(basicArea);
    }



    /**
     * 删除
     */
    @RequiresPermissions("business:area:remove")
    @Log(title = "标准行政区域", businessType = BusinessType.DELETE)
    @GetMapping("/remove/{id}")
    @ResponseBody
    public AjaxResult remove(@PathVariable("id") Long id)
    {
        return toAjax(basicAreaService.deleteBasicAreaById(id));
    }

    /**
     * 选择标准行政区域树
     */
    @GetMapping(value = { "/selectAreaTree/{id}", "/selectAreaTree/" })
    public String selectAreaTree(@PathVariable(value = "id", required = false) Long id, ModelMap mmap)
    {
        if (StringUtils.isNotNull(id))
        {
            mmap.put("basicArea", basicAreaService.selectBasicAreaById(id));
        }
        return prefix + "/tree";
    }

    /**
     * 加载标准行政区域树列表
     */
    @GetMapping("/treeData")
    @ResponseBody
    public List<Ztree> treeData()
    {
        List<Ztree> ztrees = basicAreaService.selectBasicAreaTree();
        return ztrees;
    }


    @GetMapping("/areaData")
    @ResponseBody
    public List<AreaData> areaData() {
        return basicAreaService.areaData();
    }

    @PostMapping(value = "showProjectAddress")
    @ResponseBody
    public AjaxResult showProjectAddress(String city ,String address){
        List<PoiResultBean> resutls = okRequestCompent.getPois(city, "", address);
        if (resutls==null||resutls.isEmpty()){
            return  AjaxResult.error("未查询到相对应的地址");
        }
        return  AjaxResult.success(resutls.get(0).toNormal(1f));
    }

    

}
