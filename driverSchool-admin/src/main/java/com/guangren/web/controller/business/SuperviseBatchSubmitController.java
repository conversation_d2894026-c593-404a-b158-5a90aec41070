package com.guangren.web.controller.business;

import cn.hutool.core.lang.UUID;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.alibaba.fastjson.JSONObject;
import com.guangren.business.domain.SuperviseBatchSubmit;
import com.guangren.business.service.ISuperviseBatchSubmitService;
import com.guangren.business.vo.ShowPayInfoVo;
import com.guangren.business.vo.SuperviseBatchSubmitVo;
import com.guangren.common.annotation.Log;
import com.guangren.common.config.RuoYiConfig;
import com.guangren.common.constant.Constants;
import com.guangren.common.core.controller.BaseController;
import com.guangren.common.core.domain.AjaxResult;
import com.guangren.common.core.domain.entity.SysOrganUser;
import com.guangren.common.core.page.TableDataInfo;
import com.guangren.common.enums.BusinessType;
import com.guangren.common.utils.ShiroUtils;
import com.guangren.common.utils.StringUtils;
import com.guangren.common.utils.poi.ExcelUtil;
import com.guangren.system.service.ISysDictDataService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 批量提交Controller
 *
 * <AUTHOR>
 * @date 2023-03-22
 */
@Controller
@RequestMapping("/business/batchSubmit")
public class SuperviseBatchSubmitController extends BaseController {
    private String prefix = "business/batchSubmit";

    @Autowired
    private ISuperviseBatchSubmitService superviseBatchSubmitService;
    
    @Autowired
    private ISysDictDataService sysDictDataService;

    @RequiresPermissions("business:batchSubmit:view")
    @GetMapping()
    public String batchSubmit() {
        return prefix + "/batchSubmit";
    }

    /**
     * 查询批量提交列表
     */
    @RequiresPermissions("business:batchSubmit:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SuperviseBatchSubmit superviseBatchSubmit) {
        startPage();
        SysOrganUser sysOrganUser = ShiroUtils.getSysOrganUser();
        Map<String, Object> params = superviseBatchSubmit.getParams();
        params.put("schoolId", sysOrganUser.getSchoolId());
        List<SuperviseBatchSubmit> list = superviseBatchSubmitService.selectSuperviseBatchSubmitList(superviseBatchSubmit);
        return getDataTable(list);
    }

    /**
     * 导出批量提交列表
     */
    @RequiresPermissions("business:batchSubmit:export")
    @Log(title = "批量提交", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SuperviseBatchSubmit superviseBatchSubmit, String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            superviseBatchSubmit.setIds(Arrays.asList(ids.split(",")));
        }
    	SysOrganUser sysOrganUser = ShiroUtils.getSysOrganUser();
        Map<String, Object> params = superviseBatchSubmit.getParams();
        params.put("schoolId", sysOrganUser.getSchoolId());
        List<SuperviseBatchSubmit> list = superviseBatchSubmitService.selectSuperviseBatchSubmitList(superviseBatchSubmit);
        ExcelUtil<SuperviseBatchSubmit> util = new ExcelUtil<SuperviseBatchSubmit>(SuperviseBatchSubmit.class);
        return util.exportExcel(list, "批量提交数据");
    }

    /**
     * 新增批量提交
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    

    /**
     * 批量提交学员到银联
     */
    @Log(title = "批量提交到银联支付", businessType = BusinessType.INSERT)
    @PostMapping("/addUnion")
    @ResponseBody
    public AjaxResult addUnion(SuperviseBatchSubmitVo superviseBatchSubmitVo) {
        try {
            String url = superviseBatchSubmitService.insertUnionBatchSubmit(superviseBatchSubmitVo);
            // 返回支付地址前端跳转
            JSONObject data = new JSONObject();
            data.put("url", url);
            if(superviseBatchSubmitVo.getTransType()==2) {
            	String profile = RuoYiConfig.getProfile();
            	String path = profile+"/qrcode";
            	if(!new File(path).exists()){
                   new File(path).mkdirs();
                }
            	String filePath = path + "/" + UUID.fastUUID() + ".png";
            	File qrFile = new File(filePath);
            	if(!qrFile.exists()) {
            		qrFile.createNewFile();
            	}
            	QrCodeUtil.generate(url, 300,300,qrFile);
            	data.put("qrurl", Constants.RESOURCE_PREFIX+"/qrcode/"+qrFile.getName());
            	
            }
            return success(data);
        } catch (Exception ex) {
        	logger.error("addUnion error",ex);
            String msg = "";
            if (StringUtils.isNotEmpty(ex.getMessage())) {
                msg = ex.getMessage();
            }
            return error("批量提交失败 " + msg);
        }
    }

    /**
     * 测试批量提交学员到银联
     */
    /*@Log(title = "测试批量提交到银联支付", businessType = BusinessType.INSERT)
    @PostMapping("/testAddUnion")
    @ResponseBody
    public AjaxResult testAddUnion(SuperviseBatchSubmitVo superviseBatchSubmitVo) {
        return toAjax(superviseBatchSubmitService.testUnionPay(superviseBatchSubmitVo));
    }*/

    /**
     * 批量提交学员到银行，产生付款的虚假账号
     */
    @Log(title = "批量提交", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SuperviseBatchSubmitVo superviseBatchSubmitVo) {
        int row = 0;
        try {
            row = superviseBatchSubmitService.insertSuperviseBatchSubmit(superviseBatchSubmitVo);
        } catch (Exception ex) {
            String msg = "";
            if (StringUtils.isNotEmpty(ex.getMessage())) {
                msg = ex.getMessage();
            }
            return AjaxResult.error("批量提交失败 " + msg);
        }
        return toAjax(row);
    }

    /**
     * 修改批量提交
     */
    @RequiresPermissions("business:batchSubmit:edit")
    @GetMapping("/edit/{orderId}")
    public String edit(@PathVariable("orderId") String orderId, ModelMap mmap) {
        SuperviseBatchSubmit superviseBatchSubmit = superviseBatchSubmitService.selectSuperviseBatchSubmitByOrderId(orderId);
        mmap.put("superviseBatchSubmit", superviseBatchSubmit);
        return prefix + "/edit";
    }

    /**
     * 修改保存批量提交
     */
    @RequiresPermissions("business:batchSubmit:edit")
    @Log(title = "批量提交", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SuperviseBatchSubmit superviseBatchSubmit) {
        return toAjax(superviseBatchSubmitService.updateSuperviseBatchSubmit(superviseBatchSubmit));
    }

    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id") String id, ModelMap mmap) {
        SuperviseBatchSubmit superviseBatchSubmit = superviseBatchSubmitService.selectSuperviseBatchSubmitByOrderId(id);
        mmap.put("superviseBatchSubmit", superviseBatchSubmit);
        return prefix + "/batchSubmitDetail";
    }

    /**
     * 删除批量提交
     */
    @RequiresPermissions("business:batchSubmit:remove")
    @Log(title = "批量提交", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(superviseBatchSubmitService.deleteSuperviseBatchSubmitByOrderIds(ids));
    }

    /**
     * 展示监管资金交费明细
     */
    @GetMapping(value = "/showPayInfo")
    public String showPayInfo(SuperviseBatchSubmitVo superviseBatchSubmitVo, ModelMap modelMap) {
        ShowPayInfoVo showPayInfoVo = superviseBatchSubmitService.showPayInfo(superviseBatchSubmitVo);
        modelMap.put("showPayInfoVo", showPayInfoVo);
        return prefix + "/showPayInfo";
    }
    

}
