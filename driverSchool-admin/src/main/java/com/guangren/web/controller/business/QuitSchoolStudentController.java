package com.guangren.web.controller.business;

import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.domain.SchoolStudentDropOut;
import com.guangren.business.domain.SchoolStudyCenter;
import com.guangren.business.domain.SpecialSchool;
import com.guangren.business.service.ISchoolStudentDropOutService;
import com.guangren.business.service.ISchoolStudentService;
import com.guangren.business.service.ISchoolStudyCenterService;
import com.guangren.business.service.ISpecialSchoolService;
import com.guangren.business.vo.CheckQuitStudentVo;
import com.guangren.business.vo.DropOutExportVo;
import com.guangren.business.vo.SevenDayDropOutExportVo;
import com.guangren.common.annotation.Log;
import com.guangren.common.annotation.RepeatSubmit;
import com.guangren.common.core.controller.BaseController;
import com.guangren.common.core.domain.AjaxResult;
import com.guangren.common.core.domain.entity.SysDictData;
import com.guangren.common.core.page.TableDataInfo;
import com.guangren.common.enums.BusinessType;
import com.guangren.common.utils.ShiroUtils;
import com.guangren.common.utils.StringUtils;
import com.guangren.common.utils.bean.BeanUtils;
import com.guangren.common.utils.poi.ExcelUtil;
import com.guangren.framework.web.service.PermissionService;
import com.guangren.system.service.ISysDictDataService;

import cn.hutool.core.date.DateUtil;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 退学学员controller
 * @date 2023/7/12
 */
@Controller
@RequestMapping("/business/quitStudent")
public class QuitSchoolStudentController extends BaseController {

    private String prefix = "business/quitStudent";

    @Resource
    private ISchoolStudentService schoolStudentService;
    @Resource
    private ISchoolStudyCenterService studyCenterService;
    @Resource
    private ISchoolStudentDropOutService schoolStudentDropOutService;
    @Resource
    private ISysDictDataService sysDictDataService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private ISpecialSchoolService specialSchoolService;

    @RequiresPermissions("business:quitStudent:view")
    @GetMapping()
    public String quitStudent(ModelMap mmap) {
        //获取当前登录用户的角色
        mmap.put("sysOrganType",ShiroUtils.getSysOrganUser().getOrganType());
        return prefix + "/quitStudent";
    }

    @RequiresPermissions("business:quitStudentSchool:view")
    @GetMapping("/school")
    public String quitStudentSchool(ModelMap mmap) {
        //获取当前登录用户的角色
        mmap.put("sysOrganType",ShiroUtils.getSysOrganUser().getOrganType());
        return prefix + "/quitStudentSchool";
    }

    /**
     * 查询退学学员列表（退学学员）
     */
    @RequiresPermissions("business:quitStudentSchool:list")
    @PostMapping("/school/list")
    @ResponseBody
    public TableDataInfo quitStudentSchoolList(SchoolStudentDropOut schoolStudentDropOut)
    {
        super.setManageParams(schoolStudentDropOut);
        if(schoolStudentDropOut.getParams()!=null){
            //终审结束日期
            if(schoolStudentDropOut.getParams().get("endDropoutDoneTime")!=null && !"".equals(schoolStudentDropOut.getParams().get("endDropoutDoneTime"))){
                //将00：00:00转换为23:59:59,这样才算结束日
                String endDropoutDoneTimeStr = this.convertEndDate(schoolStudentDropOut.getParams().get("endDropoutDoneTime").toString());
                schoolStudentDropOut.getParams().put("endDropoutDoneTime",endDropoutDoneTimeStr);
            }
            //初审结束日期
            if(schoolStudentDropOut.getParams().get("endFirstTrialTime")!=null && !"".equals(schoolStudentDropOut.getParams().get("endFirstTrialTime"))){
                //将00：00:00转换为23:59:59,这样才算结束日
                String endFirstTrialTimeStr = this.convertEndDate(schoolStudentDropOut.getParams().get("endFirstTrialTime").toString());
                schoolStudentDropOut.getParams().put("endFirstTrialTime",endFirstTrialTimeStr);
            }
        }
        //判断是否存在排除查看特殊学校的权限
        boolean hasAnyPermissions = permissionService.hasAnyPermissions("business:quitStudent:excludspecialschool");
        boolean isAdmin = ShiroUtils.getSysOrganUser().isAdmin();
        if(hasAnyPermissions&&!isAdmin){
            //排除掉特殊学校
            schoolStudentDropOut.setIsMember(1);
        }
        startPage();
        List<SchoolStudentDropOut>  list = schoolStudentDropOutService.selectSchoolStudentDropOutList(schoolStudentDropOut);
        return getDataTable(list);
    }

    /**
     * 查询退学学员列表（7天退学学员）
     */
    @RequiresPermissions("business:quitStudent:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo quitStudentList(SchoolStudentDropOut dropOut)
    {
        Map<String, Object> params = dropOut.getParams();
        if (params == null) params = new HashMap<>();
        params.put("schoolId", ShiroUtils.getSysOrganUser().getSchoolId());
        params.put("banchId", ShiroUtils.getSysOrganUser().getBranchId());
        params.put("registrationId", ShiroUtils.getSysOrganUser().getRegistrationId());
        params.put("estatus", 0);
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -7);
        String before7Date = DateUtil.format(cal.getTime(), "yyyy-MM-dd HH:mm:ss");
        params.put("eminCreatedTime", before7Date);
        params.put("sevenDayDropout", true);
        dropOut.setParams(params);
        if(dropOut.getParams()!=null){
            if(dropOut.getParams().get("endDropoutDoneTime")!=null){
                //将00：00:00转换为23:59:59,这样才算结束日
                String endDropoutDoneTimeStr = this.convertEndDate(dropOut.getParams().get("endDropoutDoneTime").toString());
                dropOut.getParams().put("endDropoutDoneTime",endDropoutDoneTimeStr);
            }
        }
        //判断是否存在排除查看特殊学校的权限
        boolean hasAnyPermissions = permissionService.hasAnyPermissions("business:quitStudent:excludspecialschool");
        boolean isAdmin = ShiroUtils.getSysOrganUser().isAdmin();
        if(hasAnyPermissions&&!isAdmin){
            //排除掉特殊学校
            dropOut.setIsMember(1);
        }

        startPage();
        List<SchoolStudentDropOut>  list =
                schoolStudentDropOutService.selectSevenDayDropoutSchoolStudentDropOutList(dropOut);
        return getDataTable(list);
    }

    /**
     * 导出7天退学学员列表
     */
    @RequiresPermissions("business:quitStudent:export")
    @Log(title = "7天退学学员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult exportSevenDayQuitStudent(SchoolStudentDropOut dropOut, String ids)
    {
        Map<String, Object> params = dropOut.getParams();
        if (params == null) params = new HashMap<>();
        params.put("schoolId", ShiroUtils.getSysOrganUser().getSchoolId());
        params.put("banchId", ShiroUtils.getSysOrganUser().getBranchId());
        params.put("registrationId", ShiroUtils.getSysOrganUser().getRegistrationId());
        params.put("estatus", 0);
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -7);
        String before7Date = DateUtil.format(cal.getTime(), "yyyy-MM-dd HH:mm:ss");
        params.put("eminCreatedTime", before7Date);
        params.put("sevenDayDropout", true);
        dropOut.setParams(params);
        if (StringUtils.isNotEmpty(ids)) {
            dropOut.setIds(Arrays.asList(ids.split(",")));
        }
        List<SchoolStudentDropOut>  list = schoolStudentDropOutService.selectSevenDayDropoutSchoolStudentDropOutList(dropOut);
        ArrayList<SevenDayDropOutExportVo> useList = new ArrayList<>();
        list.forEach(schoolStudentDropOut -> {
            SevenDayDropOutExportVo sevenDayDropOutExportVo = new SevenDayDropOutExportVo();
            BeanUtils.copyBeanProp(sevenDayDropOutExportVo, schoolStudentDropOut);
            sevenDayDropOutExportVo.setSchoolName(schoolStudentDropOut.getSchoolName());
            sevenDayDropOutExportVo.setBranchName(schoolStudentDropOut.getBranchName());
            sevenDayDropOutExportVo.setRegistrationName(schoolStudentDropOut.getRegistrationName());
            sevenDayDropOutExportVo.setStudentStatus(schoolStudentDropOut.getStudentStatus());
            sevenDayDropOutExportVo.setIsMember(schoolStudentDropOut.getIsMember());
            useList.add(sevenDayDropOutExportVo);
        });
        ExcelUtil<SevenDayDropOutExportVo> util = new ExcelUtil<>(SevenDayDropOutExportVo.class);
        return util.exportExcel(useList, "7天退学学员数据");
    }

    /**
     * 导出退学学员列表
     */
    @RequiresPermissions("business:quitStudentSchool:export")
    @Log(title = "退学学员", businessType = BusinessType.EXPORT)
    @PostMapping("/quitStudent/export")
    @ResponseBody
    public AjaxResult export(SchoolStudentDropOut dropOut, String ids)
    {
        Map<String, Object> params = dropOut.getParams();
        if (params == null) params = new HashMap<>();
        params.put("schoolId", ShiroUtils.getSysOrganUser().getSchoolId());
        params.put("banchId", ShiroUtils.getSysOrganUser().getBranchId());
        params.put("registrationId", ShiroUtils.getSysOrganUser().getRegistrationId());
        dropOut.setParams(params);
        if (StringUtils.isNotEmpty(ids)) {
            dropOut.setIds(Arrays.asList(ids.split(",")));
        }
        List<SchoolStudentDropOut>  list = schoolStudentDropOutService.selectSchoolStudentDropOutList(dropOut);
        ArrayList<DropOutExportVo> useList = new ArrayList<>();

        // 数据拷贝
        list.forEach(schoolStudentDropOut -> {
            DropOutExportVo dropOutExportVo = new DropOutExportVo();
            BeanUtils.copyBeanProp(dropOutExportVo, schoolStudentDropOut);
            dropOutExportVo.setSchoolName(schoolStudentDropOut.getSchoolName());
            dropOutExportVo.setBranchName(schoolStudentDropOut.getBranchName());
            dropOutExportVo.setRegistrationName(schoolStudentDropOut.getRegistrationName());
            dropOutExportVo.setStudentStatus(schoolStudentDropOut.getStudentStatus());
            switch (schoolStudentDropOut.getStatus()) {
                case 0: dropOutExportVo.setStatus("等待学员确认"); break;
                case 1: dropOutExportVo.setStatus("学员已确认"); break;
                case 2: dropOutExportVo.setStatus("等待驾协确认"); break;
                case 3: packStatus(schoolStudentDropOut, dropOutExportVo); break;
                case 4: dropOutExportVo.setStatus("驾协已确认"); break;
                case 5: dropOutExportVo.setStatus("学员已确认"); break;
                case 6: if (StringUtils.isNotEmpty(schoolStudentDropOut.getRevokeReason())) {
                    dropOutExportVo.setStatus("驾校撤销：" + schoolStudentDropOut.getRevokeReason());
                }else {
                    dropOutExportVo.setStatus("驾校撤销");
                }
                    break;
                case 7: dropOutExportVo.setStatus("审核确认");
                case 8: if (StringUtils.isNotEmpty(schoolStudentDropOut.getReviewerRejectReason())) {
                    dropOutExportVo.setStatus("审核退回：" + schoolStudentDropOut.getReviewerRejectReason());
                }else {
                    dropOutExportVo.setStatus("审核退回");
                }
            }
            useList.add(dropOutExportVo);
        });

        ExcelUtil<DropOutExportVo> util = new ExcelUtil<>(DropOutExportVo.class);
        return util.exportExcel(useList, "退学学员数据");
    }

    /**
     * 数据转换
     * 驾校属于协会成员则导出为驾协退回：......，否则统一为平台退回：......
     * <AUTHOR>
     * @date 2023/12/29 16:33
     * @param schoolStudentDropOut *
     * @param dropOutExportVo  *
     */
    private void packStatus(SchoolStudentDropOut schoolStudentDropOut,DropOutExportVo dropOutExportVo) {
        if (schoolStudentDropOut.getIsMember() == 1) {
            if (StringUtils.isNotEmpty(schoolStudentDropOut.getRejectReason())) {
                dropOutExportVo.setStatus("驾协退回："+schoolStudentDropOut.getRejectReason());
            }else {
                dropOutExportVo.setStatus("驾协退回");
            }
        }else {
            if (StringUtils.isNotEmpty(schoolStudentDropOut.getRejectReason())) {
                dropOutExportVo.setStatus("平台退回："+schoolStudentDropOut.getRejectReason());
            }else {
                dropOutExportVo.setStatus("平台退回");
            }
        }
    }

    /**
     * 查看退学学员
     */
    @RequiresPermissions("business:quitStudent:view")
    @GetMapping("/view/{id}")
    public String view(@PathVariable("id") String id, ModelMap mmap)
    {
        SchoolStudent schoolStudent = schoolStudentService.selectSchoolStudentById(id);
        schoolStudent.setHujiProvince(schoolStudent.getRealProvince());
        schoolStudent.setHujiCity(schoolStudent.getRealCity());
        schoolStudent.setHujiTown(schoolStudent.getRealTown());
        if (StringUtils.isNotEmpty(schoolStudent.getRealAddress())) {
            schoolStudent.setHujiAddress(schoolStudent.getRealAddress());
        }else {
            schoolStudent.setHujiAddress("");
        }
        mmap.put("schoolStudent", schoolStudent);

        if (StringUtils.isNotEmpty(schoolStudent.getSchoolId())){
            SchoolStudyCenter schoolStudyCenter = new SchoolStudyCenter();
            schoolStudyCenter.setSchoolId(schoolStudent.getSchoolId());
            List<SchoolStudyCenter> studyCenterList = studyCenterService.selectSchoolStudyCenterList(schoolStudyCenter);
            for (SchoolStudyCenter item:studyCenterList) {
                if (item.getOrganUser() != null){
                    item.setOrganText(item.getOrganUser().getOrganName());
                }
            }
            mmap.put("studyCenterList", studyCenterList);
        }else{
            mmap.put("studyCenterList", new ArrayList<SchoolStudyCenter>());
        }
        return prefix + "/view";
    }

    /**
     * 检查退学学员状态
     */
    @GetMapping(value = "/check/{id}")
    @ResponseBody
    public AjaxResult checkStudentStatus(@PathVariable("id") String id) {
        schoolStudentDropOutService.checkStudentStatus(id);
        return AjaxResult.success();
    }

    /**
     * 审核7天退学学员
     */
    @RequiresPermissions("business:quitStudent:check")
    @GetMapping(value = "/check")
    public String checkQuitStudent(ModelMap modelMap) {
        return prefix + "/check";
    }

    /**
     * 审核保存7天退学学员
     */
    @RequiresPermissions("business:quitStudent:check")
    @Log(title = "退学学员", businessType = BusinessType.OTHER)
    @PostMapping(value = "/checkSave")
    @ResponseBody
    public AjaxResult checkQuitStudentSave(CheckQuitStudentVo checkQuitStudentVo) {
        if (schoolStudentDropOutService.checkQuitStudentSave(checkQuitStudentVo)) {
            return AjaxResult.success();
        }else {
            return AjaxResult.error();
        }
    }

    /**
     * 重新发起学员退学
     */
    @RequiresPermissions("business:quitStudent:reissue")
    @GetMapping(value = "/reissue/{id}")
    @RepeatSubmit(interval = 5000)
    public String reissueStudentQuit(@PathVariable("id") String id, ModelMap mmap) {

        SchoolStudentDropOut dropOut = schoolStudentDropOutService.getById(id);
        mmap.put("dropOutId", dropOut.getId());
        SchoolStudent schoolStudent = schoolStudentService.selectSchoolStudentById(dropOut.getStudentId());
        schoolStudent.setHujiProvince(schoolStudent.getRealProvince());
        schoolStudent.setHujiCity(schoolStudent.getRealCity());
        schoolStudent.setHujiTown(schoolStudent.getRealTown());
        if (StringUtils.isNotEmpty(schoolStudent.getRealAddress())) {
            schoolStudent.setHujiAddress(schoolStudent.getRealAddress());
        }else {
            schoolStudent.setHujiAddress("");
        }
        mmap.put("schoolStudent", schoolStudent);

        if (StringUtils.isNotEmpty(schoolStudent.getSchoolId())){
            SchoolStudyCenter schoolStudyCenter = new SchoolStudyCenter();
            schoolStudyCenter.setSchoolId(schoolStudent.getSchoolId());
            List<SchoolStudyCenter> studyCenterList = studyCenterService.selectSchoolStudyCenterList(schoolStudyCenter);
            for (SchoolStudyCenter item:studyCenterList) {
                if (item.getOrganUser() != null){
                    item.setOrganText(item.getOrganUser().getOrganName());
                }
            }
            mmap.put("studyCenterList", studyCenterList);
        }else{
            mmap.put("studyCenterList", new ArrayList<SchoolStudyCenter>());
        }

        SysDictData query = new SysDictData();
        query.setDictType("student_status");
        query.setStatus("0");
        List<SysDictData> list = sysDictDataService.selectDictDataList(query);

        for(SysDictData dict : list) {
            if(dict.getDictValue().equals(schoolStudent.getStatus().toString())) {
                mmap.put("statusLable", dict.getDictLabel());
            }
        }
        return prefix + "/reissue";
    }

    /**
     * 重新发起学员退学保存
     */
    @RequiresPermissions("business:quitStudent:reissue")
    @Log(title = "退学学员", businessType = BusinessType.OTHER)
    @PostMapping(value = "/reissueSave/{id}")
    @ResponseBody
    public AjaxResult reissueStudentQuitSave(@PathVariable("id") String id,SchoolStudent schoolStudent) {
        schoolStudentDropOutService.reissueStudentQuitSave(id, schoolStudent);
        return success();
    }

    /**
     * 撤销学员退学
     */
    @RequiresPermissions("business:quitStudent:revokeDropout")
    @GetMapping(value = "/revokeDropout/{id}")
    public String revokeDropout(@PathVariable("id") String id, ModelMap modelMap) {
        SchoolStudentDropOut schoolStudentDropOut = schoolStudentDropOutService.getById(id);
        modelMap.put("schoolStudentDropOut", schoolStudentDropOut);
        return prefix + "/revokeDropout";
    }

    /**
     * 撤销学员退学保存
     */
    @RequiresPermissions("business:quitStudent:revokeDropout")
    @Log(title = "退学学员", businessType = BusinessType.OTHER)
    @PostMapping(value = "/revokeDropoutSave")
    @ResponseBody
    public AjaxResult revokeDropout(SchoolStudentDropOut schoolStudentDropOut) {
        int row = schoolStudentDropOutService.revokeDropoutSave(schoolStudentDropOut);
        return toAjax(row);
    }

    /**
     * 审核退学学员
     */
    @RequiresPermissions("business:quitStudent:reviewer")
    @GetMapping(value = "/reviewer")
    public String reviewer(ModelMap modelMap) {
        return prefix + "/reviewer";
    }
    /**
     * 退学学员初审页面展示
     */
    @RequiresPermissions("business:quitStudent:firstTrial")
    @GetMapping(value = "/firstTrialView")
    public String firstTrialView(ModelMap modelMap) {
        return prefix + "/firstTrialView";
    }

    /**
     * 退学学员初审
     */
    @RequiresPermissions("business:quitStudent:firstTrial")
    @Log(title = "退学学员初审", businessType = BusinessType.OTHER)
    @PostMapping(value = "/firstTrial")
    @ResponseBody
    public AjaxResult quitStudentFirstTrial(CheckQuitStudentVo checkQuitStudentVo) {
        schoolStudentDropOutService.firstTrial(checkQuitStudentVo);
            return AjaxResult.success();
    }

    /**
     * 七天退学学员初审
     */
    @RequiresPermissions("business:quitStudent:firstTrial")
    @Log(title = "七天退学学员初审", businessType = BusinessType.OTHER)
    @PostMapping(value = "/firstTrialBySeven")
    @ResponseBody
    public AjaxResult quitStudentFirstTrialBySeven(CheckQuitStudentVo checkQuitStudentVo) {
        schoolStudentDropOutService.firstTrialBySeven(checkQuitStudentVo);
        return AjaxResult.success();
    }

    /**
     * 审核保存退学学员
     */
    @RequiresPermissions("business:quitStudent:reviewer")
    @Log(title = "退学学员", businessType = BusinessType.OTHER)
    @PostMapping(value = "/reviewerSave")
    @ResponseBody
    public AjaxResult reviewerSave(CheckQuitStudentVo checkQuitStudentVo) {
        schoolStudentDropOutService.reviewerSave(checkQuitStudentVo);
        return success();
    }

    /**
     * 将给定的日期字符串转换为结束日期的字符串格式
     * 转换的目的是为了确保日期时间范围的完整性，即将日期设置为该日的最末时刻
     * 例如，将"2021-01-01"转换为"2021-01-01T23:59:59"
     *
     * @param inputDate 输入的日期字符串，格式为"yyyy-MM-dd"
     * @return 转换后的结束日期字符串，格式为"yyyy-MM-dd'T'HH:mm:ss"
     * 如果输入日期为空或空白字符串，则返回null
     */
    private String convertEndDate(String inputDate) {
        // 检查输入日期是否为空或空白字符串，如果是，则返回null
        if (StringUtils.isBlank(inputDate)) {
            return null;
        }
        // 解析输入的日期字符串为LocalDate对象
        LocalDate date = LocalDate.parse(inputDate, DateTimeFormatter.ISO_DATE);
        // 将日期转换为当日的最末时刻LocalDateTime对象
        LocalDateTime endOfDay = date.atTime(23, 59, 59);
        // 将结束时刻的LocalDateTime对象格式化为字符串，并返回
        return endOfDay.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }

}