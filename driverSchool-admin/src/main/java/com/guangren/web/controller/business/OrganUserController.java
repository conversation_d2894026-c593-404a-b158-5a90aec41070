package com.guangren.web.controller.business;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.guangren.business.domain.OrganUser;
import com.guangren.business.domain.School;
import com.guangren.business.domain.SchoolBranch;
import com.guangren.business.domain.SchoolRegistration;
import com.guangren.business.service.IOrganUserService;
import com.guangren.business.service.ISchoolBranchService;
import com.guangren.business.service.ISchoolRegistrationService;
import com.guangren.business.service.ISchoolService;
import com.guangren.common.annotation.Log;
import com.guangren.common.core.controller.BaseController;
import com.guangren.common.core.domain.AjaxResult;
import com.guangren.common.core.domain.entity.SysOrganUser;
import com.guangren.common.core.page.TableDataInfo;
import com.guangren.common.enums.BusinessType;
import com.guangren.common.utils.ShiroUtils;
import com.guangren.common.utils.StringUtils;
import com.guangren.common.utils.bean.BeanUtils;
import com.guangren.common.utils.poi.ExcelUtil;
import com.guangren.framework.shiro.service.SysPasswordService;
import com.guangren.framework.shiro.util.AuthorizationUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 机构用户Controller
 * 
 * <AUTHOR>
 * @date 2023-03-07
 */
@Controller
@RequestMapping("/business/organUser")
public class OrganUserController extends BaseController
{
    private String prefix = "business/organUser";

    @Autowired
    private IOrganUserService organUserService;
    @Autowired
    private ISchoolRegistrationService schoolRegistrationService;
    @Autowired
    private ISchoolBranchService schoolBranchService;
    @Autowired
    private ISchoolService schoolService;
    @Autowired
    private SysPasswordService passwordService;

    @RequiresPermissions("business:organUser:view")
    @GetMapping()
    public String organUser(ModelMap mmap)
    {
        SysOrganUser user = ShiroUtils.getSysOrganUser();
        mmap.put("organUser", user);
        /*School school= new School();
        super.setManageParams(school);
        List<School> schoolList= schoolService.selectSchoolList(new School());
        mmap.put("schoolList", schoolList);
        SchoolBranch branch= new SchoolBranch();
        super.setManageParams(branch);
        List<SchoolBranch> schoolBranchList= schoolBranchService.selectSchoolBranchList(new SchoolBranch());
        mmap.put("schoolBranchList", schoolBranchList);

        SchoolRegistration registration= new SchoolRegistration();
        super.setManageParams(registration);
        List<SchoolRegistration> registrationList= schoolRegistrationService.selectSchoolRegistrationList(new SchoolRegistration());
        mmap.put("registrationList", registrationList);*/
        
        String schoolId = ShiroUtils.getSysOrganUser().getSchoolId();
    	String branchId = ShiroUtils.getSysOrganUser().getBranchId();
    	String registrationId = ShiroUtils.getSysOrganUser().getRegistrationId(); 
    	
    	QueryWrapper<School> schoolQuery = new QueryWrapper<School>();
    	QueryWrapper<SchoolBranch> branchQuery = new QueryWrapper<SchoolBranch>();
    	QueryWrapper<SchoolRegistration> registrationQuery = new QueryWrapper<SchoolRegistration>();
    	if(StringUtils.isNotEmpty(schoolId)){
    		schoolQuery.eq("id", schoolId);
    		branchQuery.eq("school_id", schoolId);
    		registrationQuery.eq("school_id", schoolId);
    	}
    	if(StringUtils.isNotEmpty(branchId)){
        	branchQuery.eq("id", branchId);
        	registrationQuery.eq("branch_id", branchId);
        }
    	if(StringUtils.isNotEmpty(registrationId)){
    		registrationQuery.eq("id", registrationId);
    	}
    	List<School> schoolList= schoolService.list(schoolQuery);
        mmap.put("schoolList", schoolList);
                       
        List<SchoolBranch> schoolBranchList= schoolBranchService.list(branchQuery);
        mmap.put("schoolBranchList", schoolBranchList);
        
        List<SchoolRegistration> registrationList= schoolRegistrationService.list(registrationQuery);
        mmap.put("registrationList", registrationList);
        
        return prefix + "/organUser";
    }

    @RequiresPermissions("business:schoolOrganUser:view")
    @GetMapping("/schoolOrganUser")
    public String schoolOrganUser(ModelMap mmap)
    {
        SysOrganUser user = ShiroUtils.getSysOrganUser();
        mmap.put("organUser", user);
        /*School school= new School();
        super.setManageParams(school);
        List<School> schoolList= schoolService.selectSchoolList(new School());
        mmap.put("schoolList", schoolList);
        SchoolBranch branch= new SchoolBranch();
        super.setManageParams(branch);
        List<SchoolBranch> schoolBranchList= schoolBranchService.selectSchoolBranchList(new SchoolBranch());
        mmap.put("schoolBranchList", schoolBranchList);

        SchoolRegistration registration= new SchoolRegistration();
        super.setManageParams(registration);
        List<SchoolRegistration> registrationList= schoolRegistrationService.selectSchoolRegistrationList(new SchoolRegistration());
        mmap.put("registrationList", registrationList);*/
        
        String schoolId = ShiroUtils.getSysOrganUser().getSchoolId();
    	String branchId = ShiroUtils.getSysOrganUser().getBranchId();
    	String registrationId = ShiroUtils.getSysOrganUser().getRegistrationId(); 
    	
    	QueryWrapper<School> schoolQuery = new QueryWrapper<School>();
    	QueryWrapper<SchoolBranch> branchQuery = new QueryWrapper<SchoolBranch>();
    	QueryWrapper<SchoolRegistration> registrationQuery = new QueryWrapper<SchoolRegistration>();
    	if(StringUtils.isNotEmpty(schoolId)){
    		schoolQuery.eq("id", schoolId);
    		branchQuery.eq("school_id", schoolId);
    		registrationQuery.eq("school_id", schoolId);
    	}
    	if(StringUtils.isNotEmpty(branchId)){
        	branchQuery.eq("id", branchId);
        	registrationQuery.eq("branch_id", branchId);
        }
    	if(StringUtils.isNotEmpty(registrationId)){
    		registrationQuery.eq("id", registrationId);
    	}
    	List<School> schoolList= schoolService.list(schoolQuery);
        mmap.put("schoolList", schoolList);
                       
        List<SchoolBranch> schoolBranchList= schoolBranchService.list(branchQuery);
        mmap.put("schoolBranchList", schoolBranchList);
        
        List<SchoolRegistration> registrationList= schoolRegistrationService.list(registrationQuery);
        mmap.put("registrationList", registrationList);
        
        return prefix + "/schoolOrganUser";
    }

    /**
     * 查询机构用户列表
     */
    @RequiresPermissions("business:organUser:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(OrganUser organUser)
    {
        Map<String, Object> params = organUser.getParams();
        if (params == null) params = new HashMap<>();
        params.put("schoolId", ShiroUtils.getSysOrganUser().getSchoolId());
        params.put("banchId", ShiroUtils.getSysOrganUser().getBranchId());
        params.put("registrationId", ShiroUtils.getSysOrganUser().getRegistrationId());

        String organTypeStr="";
        int organType =ShiroUtils.getSysOrganUser().getOrganType();
        if (organType == 1){
            organTypeStr="1,2,3";
        }else if(organType == 2){
            organTypeStr="2,3";
        }else if (organType == 3 || organType == 4 || organType == 5){
            organTypeStr=organType+"";
        }
        params.put("organType",organTypeStr);

        organUser.setParams(params);
        startPage();
        List<OrganUser> list = organUserService.selectOrganUserList(organUser);
        return getDataTable(list);
    }

    /**
     * 导出机构用户列表
     */
    @RequiresPermissions("business:organUser:export")
    @Log(title = "机构用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(OrganUser organUser)
    {
        Map<String, Object> params = organUser.getParams();
        if (params == null) params = new HashMap<>();
        params.put("schoolId", ShiroUtils.getSysOrganUser().getSchoolId());
        params.put("banchId", ShiroUtils.getSysOrganUser().getBranchId());
        params.put("registrationId", ShiroUtils.getSysOrganUser().getRegistrationId());
        organUser.setParams(params);

        List<OrganUser> list = organUserService.selectOrganUserList(organUser);
        ExcelUtil<OrganUser> util = new ExcelUtil<OrganUser>(OrganUser.class);
        return util.exportExcel(list, "机构用户数据");
    }

    /**
     * 新增机构用户
     */
    @GetMapping("/add")
    public String add(ModelMap mmap)
    {
        mmap.put("sysOrganType",ShiroUtils.getSysOrganUser().getOrganType());
        mmap.put("sysOrganUse",ShiroUtils.getSysOrganUser());
        School school= new School();
        if (StringUtils.isNotEmpty(ShiroUtils.getSysOrganUser().getSchoolId())){
            school=schoolService.selectSchoolById(ShiroUtils.getSysOrganUser().getSchoolId());
        }
        mmap.put("school",school);
        SchoolBranch branch= new SchoolBranch();
        if (StringUtils.isNotEmpty(ShiroUtils.getSysOrganUser().getBranchId())){
            branch=schoolBranchService.selectSchoolBranchById(ShiroUtils.getSysOrganUser().getBranchId());
        }
        mmap.put("branch",branch);
        return prefix + "/add";
    }

    /**
     * 新增保存机构用户
     */
    @RequiresPermissions("business:organUser:add")
    @Log(title = "机构用户", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(OrganUser organUser)
    {
        organUser.setSalt(ShiroUtils.randomSalt());
        organUser.setPassword(passwordService.encryptPassword(organUser.getUsername(), organUser.getPassword(), organUser.getSalt()));
        return toAjax(organUserService.insertOrganUser(organUser));
    }

    /**
     * 修改机构用户
     */
    @RequiresPermissions("business:organUser:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        OrganUser organUser = organUserService.selectOrganUserById(id);
        mmap.put("organUser", organUser);
        mmap.put("sysOrganType",ShiroUtils.getSysOrganUser().getOrganType());
        return prefix + "/edit";
    }

    /**
     * 修改保存机构用户
     */
    @RequiresPermissions("business:organUser:edit")
    @Log(title = "机构用户", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(OrganUser organUser)
    {
        AuthorizationUtils.clearAllCachedAuthorizationInfo();
        return toAjax(organUserService.updateOrganUser(organUser));
    }


    @RequiresPermissions("business:organUser:resetPwd")
    @GetMapping("/resetPwd/{userId}")
    public String resetPwd(@PathVariable("userId") Long userId, ModelMap mmap)
    {
        mmap.put("user", organUserService.selectOrganUserById(userId));
        return prefix + "/resetPwd";
    }

    @RequiresPermissions("business:organUser:resetPwd")
    @Log(title = "重置密码", businessType = BusinessType.UPDATE)
    @PostMapping("/resetPwd")
    @ResponseBody
    public AjaxResult resetPwdSave(OrganUser user)
    {
        user.setSalt(ShiroUtils.randomSalt());
        user.setPassword(passwordService.encryptPassword(user.getUsername(), user.getPassword(), user.getSalt()));
        if (organUserService.resetUserPwd(user) > 0)
        {
            if (ShiroUtils.getUserId().longValue() == user.getId().longValue())
            {
                OrganUser organUser = organUserService.selectOrganUserById(user.getId());
                SysOrganUser sysOrganUser =new SysOrganUser();
                BeanUtils.copyBeanProp(sysOrganUser, organUser);
                setSysUser(sysOrganUser);
            }
            return success();
        }
        return error();
    }


    /**
    * 验证参数唯一性
    */
    @PostMapping("/checkUnique")
    @ResponseBody
    public String checkUnique(OrganUser organUser)
    {
        return organUserService.checkUnique(organUser);
    }

    /**
     * 删除机构用户
     */
    @RequiresPermissions("business:organUser:remove")
    @Log(title = "机构用户", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(organUserService.deleteOrganUserByIds(ids));
    }
}
