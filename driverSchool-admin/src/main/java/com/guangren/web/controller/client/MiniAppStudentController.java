package com.guangren.web.controller.client;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.guangren.business.config.UnionBankConfiguration;
import com.guangren.business.domain.*;
import com.guangren.business.service.*;
import com.guangren.business.service.impl.UnionBankService;
import com.guangren.business.service.impl.YiDongShuiEQianService;
import com.guangren.business.vo.UnionNotifyVo;
import com.guangren.business.vo.UnionScanCodeNotifyVo;
import com.guangren.common.annotation.RepeatSubmit;
import com.guangren.common.config.RuoYiConfig;
import com.guangren.common.config.WeiXinConfig;
import com.guangren.common.constant.Constants;
import com.guangren.common.constant.DictConst;
import com.guangren.common.core.controller.BaseController;
import com.guangren.common.core.domain.AjaxResult;
import com.guangren.common.core.domain.entity.SysDictData;
import com.guangren.common.core.redis.RedisCache;
import com.guangren.common.exception.BusinessException;
import com.guangren.common.utils.DateUtils;
import com.guangren.common.utils.ImageUtil;
import com.guangren.common.utils.WeiXinUtil;
import com.guangren.common.utils.file.BASE64DecodedMultipartFile;
import com.guangren.common.utils.http.HttpUtils;
import com.guangren.common.utils.uuid.IdUtils;
import com.guangren.system.service.ISysConfigService;
import com.guangren.system.service.ISysDictDataService;
import com.microdone.asn1.E;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.guangren.common.constant.Constants.AUDIT_STATUS_UNAUDITED;

@Slf4j
@Controller
@RequestMapping("/miniApp")
public class MiniAppStudentController extends BaseController{

	@Autowired
	private ISchoolRegistrationService registrationService;
	@Autowired
	private ISchoolBranchService branchService;
	@Autowired
	private ISchoolService schoolService;
	@Autowired
	private ISchoolContactService schoolContactService;

	@Autowired
	private ISchoolStudentService studentService;
	@Autowired
	private ISchoolStudentSIMService studentSIMService;
	@Autowired
	private ISysDictDataService sysDictDataService;
	@Resource
	private IUnicomNumberService unicomNumberService;
	@Autowired
	private RedisCache redisCache;
	@Autowired
	private ISchoolStudentContractService schoolStudentContractService;
	@Autowired
	private YiDongShuiEQianService yiDongShuiEQianService;
	@Autowired
	private IStudentResignContractService studentResignContractService;

	@Autowired
	private ISpecialSchoolService specialSchoolService;
	@Autowired
	private IStudentSkipContractService studentSkipContractService;
	@Autowired
	private ISysConfigService configService;
	@Autowired
	private ISchoolContractService schoolContractService;


	@Value("${ruoyi.profile}")
	private String imagePath;
	@Value("${server.domain}")
	private String domain;
	@Value("${ruoyi.debug}")
	private boolean debug;
	@Value("${ruoyi.yidong.sms.confirmTemplateId}")
	private String confirmTemplateId;

	@Value("${ruoyi.miniApp.appId}")
	private String appId;

	@Value("${ruoyi.miniApp.appSecret}")
	private String appSecret;

	@Autowired
	private ISysConfigService sysConfigService;
	@Autowired
	private ISchoolStudyCenterService schoolStudyCenterService;
	@Autowired
	private IPublicFileService publicFileService;
	@Autowired
	private ISchoolStudentDropOutService schoolStudentDropOutService;

	private Cache<String, String> cache;

	private Cache<String, Integer> sendCodeCountCache;

	@Autowired
	private CacheManager cacheManager;

	@Autowired
	private IDivsionOrderService divsionOrderService;

	@PostConstruct
	public void init() {
		cache = cacheManager.getCache("validateCode");
		sendCodeCountCache = cacheManager.getCache("sendCodeCountCache");
	}

	@PostMapping("/student/registe/")
	@ResponseBody
	@RepeatSubmit(interval = 30000)
	public AjaxResult registe(@RequestAttribute String data){
		try{
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);

			logger.warn("registe json ======== "+json);

			String registrationId = json.getString("schoolId");
			SchoolRegistration registration = registrationService.getById(registrationId);
			if(registration != null){
				SchoolStudent student = new SchoolStudent();
				if (StringUtils.isNotEmpty(json.getString("businessType"))) {
					String businessType = json.getString("businessType");
					if (businessType.equals("增驾")) {
						String oldLicenseDate = json.getString("oldLicenseDate");
						String base64ImgStr = json.getString("oldLicenseImage");
						if (StringUtils.isEmpty(oldLicenseDate)) {
							return error("业务类型为增驾时，请填写原驾驶证领取日期");
						}
						if (StringUtils.isEmpty(base64ImgStr)) {
							return error("业务类型为增驾时，请上传原驾驶证电子版图片");
						}
						student.setOldLicenseDate(DateUtil.parse(oldLicenseDate, "yyyy-MM-dd"));
						MultipartFile file = BASE64DecodedMultipartFile.base64ToMultipartFile("data:image/png;base64," + base64ImgStr);
						PublicFile publicFile = publicFileService.saveFile(file, "0", DictConst.FILE_TYPE_OLD_LICENSE_IMG.getDict());
						student.setOldLicenseImage(publicFile.getWebPath());
					}
					student.setBusinessType(json.getString("businessType"));
				}else {
					student.setBusinessType("初领");
				}
				student.setName(json.getString("name").trim());
				student.setIdentityType(json.getString("identityType"));
				student.setIdentity(json.getString("identity").trim());
				student.setMobile(json.getString("mobile").trim());
				student.setRealAddress(json.getString("realAdress"));
				if(StringUtils.isEmpty(student.getRealAddress())){
					student.setRealAddress(json.getString("realAddress"));
				}
				student.setRealCity(json.getString("realCity"));
				student.setRealProvince(json.getString("realProvince"));
				student.setRealTown(json.getString("realTown"));
				student.setContractFee(json.getBigDecimal("contractFee"));
				student.setPayFee(json.getBigDecimal("payFee"));
				student.setTextTrainFee(getBigDecimalFromJSON("textTrainFee", json));
				student.setServiceFee(getBigDecimalFromJSON("serviceFee", json));
				student.setOperateFee(getBigDecimalFromJSON("operateFee", json));
				student.setOperateFee2(getBigDecimalFromJSON("operateFee2", json));
				student.setStudyTimeFee(getBigDecimalFromJSON("studyTimeFee", json));
				student.setStudyTimeFee2(getBigDecimalFromJSON("studyTimeFee2", json));
				student.setPhase2Fee1(getBigDecimalFromJSON("phase2Fee1", json));
				student.setPhase3Fee1(getBigDecimalFromJSON("phase3Fee1", json));
				student.setJiesongFee(getBigDecimalFromJSON("jiesongFee", json));
				if(StringUtils.isEmpty(student.getName()) || StringUtils.isEmpty(student.getIdentityType())
						|| StringUtils.isEmpty(student.getIdentity()) || StringUtils.isEmpty(student.getMobile())){
					return error(AjaxResult.Type.WARN,"信息不完整");
				}
				if(StringUtils.isEmpty(student.getRealProvince()) || StringUtils.isEmpty(student.getRealCity())
						|| StringUtils.isEmpty(student.getRealTown()) || StringUtils.isEmpty(student.getRealAddress())){
					return error("请填写身份证上详细地址");
				}
				if(StringUtils.isEmpty(json.getString("headImage"))){
					return error("头像未上传，请上传头像");
				}
				if(student.getContractFee() != null && student.getPayFee() != null){
					String trainFeeStr = sysConfigService.selectConfigByKey("student.pay.train.fee");
					if(StringUtils.isEmpty(trainFeeStr)){
						return error("未设置基础培训金额");
					}
					BigDecimal trainFee = new BigDecimal(trainFeeStr);
					if(student.getPayFee().compareTo(trainFee)<0){
						return error("支付金额不正确");
					}
					if(student.getPayFee().compareTo(new BigDecimal(1500))==0
							|| student.getPayFee().compareTo(new BigDecimal(4300))>=0){

					}else{
						return error("支付金额不正确");
					}

					if(student.getContractFee().compareTo(student.getPayFee())<0){
						return error("培训费用合计应该大于或者等于支付金额");
					}
				}

				int gender = 1;
				String birthday = "19700101";
				if(student.getIdentityType().equals("身份证")){
					if(student.getIdentity().length()!=18 && student.getIdentity().length() != 15){
						return error("身份证号必须为18位或者15位");
					}

					if(student.getIdentity().length()==18){
						birthday = student.getIdentity().substring(6, 14);
						gender = Integer.parseInt(student.getIdentity().substring(16, 17));
					}
					if(student.getIdentity().length()==15){
						birthday = "19"+student.getIdentity().substring(6, 12);
						gender = Integer.parseInt(student.getIdentity().substring(14));
					}
				}
				//识别港澳台居住证的出生年月日和性别
				if(student.getIdentityType().equals("港澳台居民居住证")){
					try {
						gender = IdcardUtil.getGenderByIdCard(student.getIdentity());
						birthday = IdcardUtil.getBirthByIdCard(student.getIdentity());
					}catch (Exception e){
						logger.error("学员："+student.getName()+",证件解析失败，将采取默认值");
					}
				}

				student.setGender(gender % 2 > 0 ? 1 : 0);
				student.setBirthday(DateUtil.format(DateUtil.parse(birthday,"yyyyMMdd"),"yyyy-MM-dd"));
				student.setLicenseType(json.getString("licenseType"));
				student.setRegistrationId(registration.getId());
				student.setBranchId(registration.getBranchId());
				student.setSchoolId(registration.getSchoolId());

				student.setStatus(1);
				student.setIsPay(0);

				SysDictData query = new SysDictData();
				query.setDictType("nation_type");
				query.setStatus("0");
				List<SysDictData> list = sysDictDataService.selectDictDataList(query);
				student.setNation(list.size()>0?list.get(0).getDictValue():"汉族");

				List<SchoolStudyCenter> schoolStudentCenterList = schoolStudyCenterService.list(new QueryWrapper<SchoolStudyCenter>()
						.eq("school_id", student.getSchoolId()));
				if(schoolStudentCenterList.size()==0){
					student.setStudyCenterId("0");
				}else{
					boolean isSetDefault = false;
					for(SchoolStudyCenter sc : schoolStudentCenterList){
						if(sc.getIsDefault() != null && sc.getIsDefault()==1){
							student.setStudyCenterId(sc.getOrganId());
							isSetDefault = true;
							break;
						}
					}
					if(!isSetDefault){
						student.setStudyCenterId(schoolStudentCenterList.get(0).getOrganId());
					}
				}
				student.setStudyCenterIsSyn(0);
				student.setIsSupervise(1); // 从9月1日开始默认为需要监管
				student.setSuperviseFeeIsOk(0);
				student.setIsQuit(0);
				student.setQuitIsSyn(0);
				student.setPrepareRegisteDate(new Date());
				student.setOriginData(1);
				student.setVersion(1);

				String filepath = RuoYiConfig.getAvatarPath()+File.separator+DateUtils.datePath();
				String filename = ImageUtil.saveImage(filepath, json.getString("headImage"));
				logger.info("headImage:"+json.getString("headImage"));
				String webFilename = Constants.RESOURCE_PREFIX+"/avatar/"+DateUtils.datePath()+"/"+filename;
				student.setHeadImage(webFilename);

				try{
					if(StringUtils.isNotEmpty(json.getString("jsCode"))){
						String openId = studentService.getOpenId(json.getString("jsCode"));
						student.setOpenId(openId);
						//校验openId是否大于五次
						if(StringUtils.isNotBlank(student.getOpenId())){
							long openIdExistCount = studentService.count(new QueryWrapper<SchoolStudent>().eq("open_id", student.getOpenId())
									.eq("is_quit", 0));
							if (openIdExistCount>=5){
								return error("当前微信号已达到报名上限：5 次，请尝试更换微信进行报名。");
							}
						}
					}
				}catch(Exception ex){
					logger.error("get openid error",ex);
				}

				student.setProvince(json.getString("deliverProvince"));
				student.setCity(json.getString("deliverCity"));
				student.setTown(json.getString("deliverTown"));
				student.setAddress(json.getString("deliverAddress"));

				JSONObject simObject = json.getJSONObject("sim");
				if(simObject != null) {
					Integer mobileType = simObject.getInteger("mobileType");
					String simMobile =  simObject.getString("simMobile");
					if (mobileType!= null && mobileType == 0){
						UnicomNumber unicomNumber = unicomNumberService.getOne(new LambdaQueryWrapper<UnicomNumber>()
								.eq(UnicomNumber::getNumber, simMobile));
						if (unicomNumber != null && unicomNumber.getStatus() != null && unicomNumber.getStatus() == 1) {
							return error(AjaxResult.Type.WARN, "该学员卡号已被其他人选用，请重新获取号码");
						}
					}else{
						SchoolStudentSIM studentSim = studentSIMService.getOne(new QueryWrapper<SchoolStudentSIM>()
								.eq("sim_mobile",simMobile).last("limit 1"));
						if (studentSim != null && studentSim.getIsNotOpenCard() != null && studentSim.getIsNotOpenCard() != 2) {
							return error(AjaxResult.Type.WARN, "该学员卡号已被其他人选用，请重新获取号码");
						}
					}
				}

				try {
					boolean success = studentService.customerSave(student, json);
					if (success) {
						return success();
					}
				}catch(BusinessException ex){
					return error(ex.getMessage());
				}catch(Exception e){
					throw e;
				}
			}

		}catch(Exception ex){
			logger.error("registe error",ex);
		}
		return error();
	}



	@PostMapping("/student/signalOpenCard/")
	@ResponseBody
	public AjaxResult signalOpenCard(@RequestAttribute String data){
		try{
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			SchoolStudentSIM sim = new SchoolStudentSIM();
			sim.setSimMobile(json.getString("simMobile"));
			sim.setSimProductCode(json.getString("simProductCode"));
			sim.setDeliverProvince(json.getString("deliverProvince"));
			sim.setDeliverCity(json.getString("deliverCity"));
			sim.setDeliverTown(json.getString("deliverTown"));
			sim.setDeliverAddress(json.getString("deliverAddress"));
			sim.setLoginCount(0);
			sim.setIsSyn(0);
			sim.setName(json.getString("name"));
			sim.setIdentityType(json.getString("identityType"));
			sim.setIdentity(json.getString("identity"));
			sim.setMobile(json.getString("mobile"));
			sim.setIsSignal(1);
			try{
				if(StringUtils.isNotEmpty(json.getString("jsCode"))){
					String openId = studentService.getOpenId(json.getString("jsCode"));
					sim.setOpenId(openId);
				}
			}catch(Exception ex){
				logger.error("get openid error",ex);
			}

			studentSIMService.save(sim);
			sendCodeCountCache.remove(json.getString("mobile"));
			if(!debug){
				JSONObject response = studentSIMService.addMobileNumber(sim);
				if(response != null && response.getIntValue("respcode")==0){
					sim.setIsSyn(1);
					sim.setSynDate(new Date());
					sim.setIsNotOpenCard(1);
					studentSIMService.updateById(sim);
				}
			}

			return success();
		}catch(Exception ex){
			logger.error("signalOpenCard error",ex);
		}
		return error();
	}

	@PostMapping("/student/detail/")
	@ResponseBody
	public AjaxResult detail(@RequestAttribute String data){
		try{
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String simMobile = json.getString("simMobile");
			SchoolStudentSIM sim = studentSIMService.getOne(new QueryWrapper<SchoolStudentSIM>().eq("sim_mobile", simMobile).last("limit 1"));
			if(sim==null){
				return error(AjaxResult.Type.WARN,"手机号不存在");
			}
			SchoolStudent student = studentService.getById(sim.getStudentId());
			if((sim.getIsSignal() != null && sim.getIsSignal()==1) || student==null){
				return error(AjaxResult.Type.WARN,"此手机号为单独开卡，无驾校报名信息，请查询开卡进度");
			}

			JSONObject stu = new JSONObject();
			stu.put("name", student.getName());
			stu.put("mobile", student.getMobile());
			stu.put("identityType", student.getIdentityType());
			stu.put("identity", student.getIdentity());
			stu.put("licenseType", student.getLicenseType());
			stu.put("simmobile", simMobile);
			stu.put("address", student.getProvince()+student.getCity()+student.getTown()+student.getAddress());
			stu.put("status", student.getStatus());
			stu.put("prepareRegisteDate", DateUtil.format(student.getPrepareRegisteDate(),"yyyy-MM-dd HH:mm:ss"));
			if(student.getRegisteDate() != null){
				stu.put("registeDate", DateUtil.format(student.getRegisteDate(),"yyyy-MM-dd HH:mm:ss"));
			}else{
				Calendar cal = Calendar.getInstance();
				cal.setTime(student.getPrepareRegisteDate());
				cal.add(Calendar.DATE, 5);
				stu.put("registeDate", DateUtil.format(cal.getTime(),"yyyy-MM-dd HH:mm:ss"));
			}

			if(StringUtils.isNotEmpty(student.getHeadImage())){
				stu.put("headImage", domain+"/"+student.getHeadImage());
			}
			if(StringUtils.isNotEmpty(student.getRegistrationId())){
				SchoolRegistration registration = registrationService.getById(student.getRegistrationId());
				if(registration != null){
					stu.put("schoolName", registration.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("registration_id", student.getRegistrationId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}else if(StringUtils.isNotEmpty(student.getBranchId())){
				SchoolBranch branch = branchService.getById(student.getBranchId());
				if(branch != null){
					stu.put("schoolName", branch.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("branch_id", student.getBranchId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}else if(StringUtils.isNotEmpty(student.getSchoolId())){
				School school = schoolService.getById(student.getSchoolId());
				if(school != null){
					stu.put("schoolName", school.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("school_id", student.getSchoolId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}
			return success(stu);

		}catch(Exception ex){
			logger.error("registe error",ex);
		}
		return error();
	}

	@PostMapping("/student/detail2/")
	@ResponseBody
	public AjaxResult detail2(@RequestAttribute String data){
		try{
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String idCode = json.getString("idCode");
			SchoolStudent student = null;
			SchoolStudentDropOut dropOut = null;
			List<SchoolStudent> schoolStudents = studentService.list(new QueryWrapper<SchoolStudent>()
					.eq("identity", idCode));

			// 查询退学表中是否存在退学中的数据
			for (SchoolStudent schoolStudent : schoolStudents) {
				dropOut = schoolStudentDropOutService.getOne(new LambdaQueryWrapper<SchoolStudentDropOut>()
						.eq(SchoolStudentDropOut::getStudentId, schoolStudent.getId())
						.eq(SchoolStudentDropOut::getStatus, 0));
				if (dropOut != null) {
					//如果存在七天未确认但又初审通过的学员也视为退学完成，不需要带出其相关信息
					long betweenDay = DateUtil.between(dropOut.getCreatedTime(), new Date(), DateUnit.DAY);
					if(betweenDay>7){
						if(dropOut.getIsFirstTrial()!=null && (dropOut.getIsFirstTrial()==1||dropOut.getIsFirstTrial()==3)){
							//七天未确认的学员经过初审通过或终审通过就代表退学流程已走完，跳过该记录
							dropOut = null;
							continue;
						}else{
							student = schoolStudent;
							break;
						}
					}else{
						student = schoolStudent;
						break;
					}
				}
			}

			// 退学表不存在退学中的数据则返回正常报名的数据
			if (student == null) {
				List<SchoolStudent> students = schoolStudents.stream()
						.filter(schoolStudent -> schoolStudent.getIsQuit() == 0)
						.collect(Collectors.toList());
				if(students.size()>0) {
					student = students.get(0);
				}
			}

			if(student == null){
				return error(AjaxResult.Type.WARN,"无此学员报名信息");
			}



			JSONObject stu = new JSONObject();
			stu.put("stuId", student.getId());
			stu.put("name", student.getName());
			stu.put("mobile", student.getMobile());
			stu.put("identityType", student.getIdentityType());
			stu.put("identity", student.getIdentity());
			stu.put("licenseType", student.getLicenseType());

			SchoolStudentSIM sim = studentSIMService.getOne(new QueryWrapper<SchoolStudentSIM>().eq("student_id", student.getId()));
			stu.put("simmobile",sim==null ? "" : sim.getSimMobile());

			stu.put("isQuit", dropOut != null);
			stu.put("isPreRegistration", student.getStatus() == 1);
			stu.put("quitStudentId",dropOut==null ? "" : dropOut.getStudentId());
			stu.put("address", student.getRealProvince()+student.getRealCity()+student.getRealTown()+student.getRealAddress());
			stu.put("status", student.getStatus());
			stu.put("isCheck",student.getIsCheck()); //合同改变后，需要重新审核
			stu.put("prepareRegisteDate", DateUtil.format(student.getPrepareRegisteDate(),"yyyy-MM-dd HH:mm:ss"));
			if(student.getRegisteDate() != null){
				stu.put("registeDate", DateUtil.format(student.getRegisteDate(),"yyyy-MM-dd HH:mm:ss"));
			}else{
				Calendar cal = Calendar.getInstance();
				cal.setTime(student.getPrepareRegisteDate());
				cal.add(Calendar.DATE, 5);
				stu.put("registeDate", DateUtil.format(cal.getTime(),"yyyy-MM-dd HH:mm:ss"));
			}

			if(StringUtils.isNotEmpty(student.getHeadImage())){
				stu.put("headImage", domain+"/"+student.getHeadImage());
			}
			if(StringUtils.isNotEmpty(student.getRegistrationId())){
				SchoolRegistration registration = registrationService.getById(student.getRegistrationId());
				if(registration != null){
					stu.put("schoolName", registration.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("registration_id", student.getRegistrationId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}else if(StringUtils.isNotEmpty(student.getBranchId())){
				SchoolBranch branch = branchService.getById(student.getBranchId());
				if(branch != null){
					stu.put("schoolName", branch.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("branch_id", student.getBranchId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}else if(StringUtils.isNotEmpty(student.getSchoolId())){
				School school = schoolService.getById(student.getSchoolId());
				if(school != null){
					stu.put("schoolName", school.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("school_id", student.getSchoolId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}
			if (StringUtils.isNotEmpty(student.getSchoolId())) {
				SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>()
						.eq("school_id", student.getSchoolId())
						.isNull("branch_id")
						.isNull("registration_id")
						.last("limit 1"));
				if (contact != null) {
					stu.put("headSchoolTel", contact.getTel());
				}
			}

			DivsionOrder order = divsionOrderService.getOne(new LambdaQueryWrapper<DivsionOrder>()
					.eq(DivsionOrder::getStudentId,student.getId())
					.orderByDesc(DivsionOrder::getId)
					.last("limit 1"));
			stu.put("isExistOrder",order != null);
			if(order != null){
				JSONObject objectOrder = new JSONObject();
				objectOrder.put("orderNo",order.getOrderNo());
				objectOrder.put("orderTime",order.getOrderTime());
				objectOrder.put("totalFee",order.getTotalFee());
				objectOrder.put("status",order.getIsPay());
				objectOrder.put("registrationName",order.getRegistrationName());
				stu.put("order",objectOrder);
			}
			stu.put("isSignContract",false);
			SchoolStudentContract schoolStudentContract = schoolStudentContractService.getById(student.getId());
			if(schoolStudentContract != null){
				stu.put("isSignContract",schoolStudentContract.getIsSign()==1);
			}

			boolean isOtherUserPay = "1".equals(sysConfigService.selectConfigByKey("other.user.pay"));
			stu.put("isOtherUserPay",isOtherUserPay);

			stu.put("showGotoPay",isShowGotoPay(student.getId()));

			return success(stu);

		}catch(Exception ex){
			logger.error("registe error",ex);
		}
		return error();
	}

	private boolean isShowGotoPay(String studentId){
		SchoolStudent student = studentService.getById(studentId);
		SpecialSchool specialSchool = specialSchoolService.getById(student.getSchoolId());
		if(specialSchool != null){
			return false;
		}
		String expireDate = sysConfigService.selectConfigByKey("import.pay.expire.date");
		if(StringUtils.isEmpty(expireDate)){
			expireDate = "2024-03-15 00:00:00";
		}
		Date expire = DateUtil.parse(expireDate,"yyyy-MM-dd HH:mm:ss");
		if(student.getPrepareRegisteDate().before(expire)){
			return false;
		}
		if(student.getIsCheck()==0 || student.getSuperviseFeeIsOk()==1){
			return false;
		}
		DivsionOrder order = divsionOrderService.getOne(new LambdaQueryWrapper<DivsionOrder>()
				.eq(DivsionOrder::getStudentId,student.getId())
				.eq(DivsionOrder::getIsPay,1)
				.orderByDesc(DivsionOrder::getId)
				.last("limit 1"));
		if (order != null) {
			return false;
		}

		return true;
	}

	@PostMapping("/student/detail3/")
	@ResponseBody
	public AjaxResult detail3(@RequestAttribute String data){
		try{
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String mobile = json.getString("mobile");
			SchoolStudent student = studentService.getOne(new QueryWrapper<SchoolStudent>().eq("mobile", mobile));
			if(student == null){
				return error(AjaxResult.Type.WARN,"无此学员报名信息");
			}
			return success();
		}catch(Exception ex){
			logger.error("detail3 error",ex);
		}
		return error();
	}

	@PostMapping("/student/showQuitInfo/")
	@ResponseBody
	public AjaxResult showQuitInfo(@RequestAttribute String data){
		try{
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String idCode = json.getString("idCode");
			String quitStudentId = json.getString("quitStudentId");
			SchoolStudent student = studentService.getById(quitStudentId);
			if(student == null){
				return error(AjaxResult.Type.WARN,"无此学员报名信息");
			}
			SchoolStudentDropOut dropOut = schoolStudentDropOutService.getOne(new QueryWrapper<SchoolStudentDropOut>()
					.eq("student_id", quitStudentId)
					.eq("status", 0).last("limit 1"));
			if(dropOut == null) {
				return error(AjaxResult.Type.WARN,"无此学员退学信息");
			}

			JSONObject stu = new JSONObject();
			stu.put("name", student.getName());
			stu.put("mobile", student.getMobile());
			stu.put("identityType", student.getIdentityType());
			stu.put("identity", student.getIdentity());
			stu.put("licenseType", student.getLicenseType());
			stu.put("quitTime", dropOut.getCreatedTime());
			stu.put("superviseFeeIsOk",student.getSuperviseFeeIsOk()==null?1:student.getSuperviseFeeIsOk()); //判断监管资金是否到账、未到账的不需要接收验证码,为谨慎起见，为空则也要接收

			List<PublicFile> publicFiles = publicFileService.list(new LambdaQueryWrapper<PublicFile>()
					.eq(PublicFile::getRefrence, student.getId())
					.eq(PublicFile::getRefrenceType, DictConst.FILE_TYPE_DROPOUT_CERT_IMG.getDict()));
			JSONArray quitImages = new JSONArray();
			for(PublicFile file : publicFiles) {
				quitImages.add(domain+file.getWebPath());
			}
			stu.put("quitImages", quitImages);

			if(StringUtils.isNotEmpty(student.getRegistrationId())){
				SchoolRegistration registration = registrationService.getById(student.getRegistrationId());
				if(registration != null){
					stu.put("schoolName", registration.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("registration_id", student.getRegistrationId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}else if(StringUtils.isNotEmpty(student.getBranchId())){
				SchoolBranch branch = branchService.getById(student.getBranchId());
				if(branch != null){
					stu.put("schoolName", branch.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("branch_id", student.getBranchId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}else if(StringUtils.isNotEmpty(student.getSchoolId())){
				School school = schoolService.getById(student.getSchoolId());
				if(school != null){
					stu.put("schoolName", school.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("school_id", student.getSchoolId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}

			return success(stu);
		}catch(Exception ex){
			logger.error("detail3 error",ex);
		}
		return error();
	}

	@PostMapping("/student/confirmQuit/")
	@ResponseBody
	public AjaxResult confirmQuit(@RequestAttribute String data) {
		try {
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String idCode = json.getString("idCode");
			String quitStudentId = json.getString("quitStudentId");
			String mobile = json.getString("mobile");
			String mobileCode = json.getString("mobileCode");

			SchoolStudent student = studentService.getById(quitStudentId);
			if(student == null){
				return error(AjaxResult.Type.WARN,"无此学员报名信息");
			}
			SchoolStudentDropOut dropOut = schoolStudentDropOutService.getOne(new QueryWrapper<SchoolStudentDropOut>()
					.eq("student_id", quitStudentId)
					.eq("status", 0).last("limit 1"));
			if(dropOut == null) {
				return error(AjaxResult.Type.WARN,"无此学员退学信息");
			}
			//有监管金到账了才需要接收验证码
			if(student.getSuperviseFeeIsOk()!=null && student.getSuperviseFeeIsOk()==1){
				if(cache.get(mobile)== null){
					return error(AjaxResult.Type.WARN, "验证码错误或过期");
				}
				if(!cache.get(mobile).equals(mobileCode)){
					return error(AjaxResult.Type.WARN, "验证码错误或过期");
				}
			}
			//List<SchoolStudent> studentList= new ArrayList<>();
			//studentList.add(student);
			//studentService.submitStudentQuitDataToStudyCenter(studentList);  //定时作务释入金额和同步
			student.setIsQuit(1);

			student.setQuitIsSyn(0);
			//student.setQuitDate(new Date());
			studentService.updateById(student);

			dropOut.setStatus(5);
			// 监管资金未到账直接可以退学，已到账则需要审核完才能退学
			if (student.getSuperviseFeeIsOk() == 0) {
				dropOut.setIsDone(1);
				dropOut.setDropoutDoneTime(new Date());
				dropOut.setIsFirstTrial(Constants.FINAL_TRIAL_STATUS_DOWN);
			}
			schoolStudentDropOutService.updateById(dropOut);

			return success();
		}catch(Exception ex) {
			logger.error("confirmQuit error",ex);
		}
		return error();
	}

	@PostMapping("/student/sendCode/")
	@ResponseBody
	public AjaxResult sendCode(@RequestAttribute String data){
		try{
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String idCard = json.getString("idCode");
			String mobile = json.getString("mobile");
			SchoolStudent student = studentService.getOne(new QueryWrapper<SchoolStudent>().eq("identity", idCard).eq("mobile", mobile).last("limit 1"));
			if(student==null){
				return error(AjaxResult.Type.WARN,"手机号不存在");
			}
			String validateCode = RandomUtil.randomNumbers(6);
			//发送退学广告
			boolean success = studentSIMService.xuanwuSendValidateCode(mobile, validateCode,Constants.STUDENT_ACTION_DROP_CONFIRM);
			if(success){
				if(cache.get(mobile)!= null){
					cache.remove(mobile);
				}
				cache.put(mobile, validateCode);
				JSONObject code = new JSONObject();
				code.put("code", validateCode);
				return success(code);
			}

		}catch(Exception ex){
			logger.error("get cand mobile error",ex);
		}
		return error();
	}

	/**
	 * 发起退学
	 * <AUTHOR>
	 * @date 2023/11/16 16:36
	 * @param data *
	 * @return com.guangren.common.core.domain.AjaxResult *
	 */
	@PostMapping("/student/sendDropOut")
	@ResponseBody
	public AjaxResult sendDropOut(@RequestAttribute String data) {
		try {
			//参数开启期间为避免纠纷，禁止退学
			Integer skipContractStatus = studentSkipContractService.querySkipContractStatus();
            boolean isSkipContract = skipContractStatus.equals(Constants.SKIP_CONTRACT_SWITCH_ON);
			if(isSkipContract){
				return success("当前管理员已开启跳过合同签署步骤，暂时无法进行退学。");
			}
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String idCard = json.getString("idCode");
			String mobile = json.getString("mobile");
			String dropOutReason = json.getString("dropOutReason");
			SchoolStudent student = studentService.getOne(new LambdaQueryWrapper<SchoolStudent>()
					.eq(SchoolStudent::getIdentity, idCard)
					.eq(SchoolStudent::getMobile, mobile)
					.eq(SchoolStudent::getIsQuit, 0)
					.eq(SchoolStudent::getStatus, 1)
					.last("LIMIT 1"));
			if (student == null) {
				throw new BusinessException("无此学生报名信息或不在预登记状态");
			}
			schoolStudentDropOutService.sendDropOut(student, dropOutReason);
			return success();
		}catch (Exception e) {
			logger.error("sendDropOut error", e);
		}
		return error();
	}

	/**
	 * 微信授权登录
	 * <AUTHOR>
	 * @date 2023/12/19 17:21
	 * @param data *
	 * @return com.guangren.common.core.domain.AjaxResult *
	 */
	@PostMapping("/wxAuthorizeLogin")
	@ResponseBody
	public AjaxResult wxAuthorizeLogin(@RequestAttribute String data){
		JSONArray array = JSONArray.parseArray(data);
		JSONObject json = array.getJSONObject(0);
		String code = json.getString("code");

		if (com.guangren.common.utils.StringUtils.isNull(code) || StringUtils.isEmpty(code)){
			return AjaxResult.error("code不能为空");
		}
		JSONObject map = new JSONObject();
		try{
			StringBuilder url = new StringBuilder(WeiXinConfig.JSCODE2SESSION_URL);
			url.append("appid=");//appid设置
			url.append(appId);
			url.append("&secret=");//secret设置
			url.append(appSecret);
			url.append("&js_code=");//code设置
			url.append(code);
			url.append("&grant_type=authorization_code");
			String response= HttpUtils.sendGet(url.toString());
			// 解析响应
			JSONObject responseJson = JSON.parseObject(response);
			log.info("====requestbody==== = " + responseJson);
			String sessionKey = responseJson.getString("session_key");
			String openId = responseJson.getString("openid");
			map.put("sessionKey",sessionKey);
			map.put("openId",openId);
		}catch (Exception ex){
			logger.error("login error",ex);
			return AjaxResult.error("微信授权登录失败");
		}
		return AjaxResult.success(map);
	}


	/**
	 * 获取用户手机号
	 * <AUTHOR>
	 * @date 2023/12/19 17:19
	 * @param data *
	 * @return com.guangren.common.core.domain.AjaxResult *
	 */
	@PostMapping("/getWxUserMobile")
	@ResponseBody
	public AjaxResult getWxUserMobile(@RequestAttribute String data) {

		JSONArray array = JSONArray.parseArray(data);
		JSONObject json = array.getJSONObject(0);
		String encryptedData = json.getString("encryptedData");
		String sessionKey = json.getString("sessionKey");
		String iv = json.getString("iv");
		String openid = json.getString("openid");

		if (com.guangren.common.utils.StringUtils.isNull(encryptedData) || StringUtils.isEmpty(encryptedData)){
			return AjaxResult.error("encryptedData不能为空");
		}
		if (com.guangren.common.utils.StringUtils.isNull(iv) || StringUtils.isEmpty(iv)){
			return AjaxResult.error("iv不能为空");
		}
		if (com.guangren.common.utils.StringUtils.isNull(sessionKey) || StringUtils.isEmpty(sessionKey)){
			return AjaxResult.error("sessionKey不能为空");
		}
		if (com.guangren.common.utils.StringUtils.isNull(openid) || StringUtils.isEmpty(openid)){
			return AjaxResult.error("openid不能为空");
		}

		JSONObject object = WeiXinUtil.getUserInfo(encryptedData,sessionKey,iv);
		String phone = object.getString("phoneNumber");
		JSONObject result = new JSONObject();
		result.put("mobile", phone);
		return success(result);
	}

	/**
	 * 验证手机号是否被注册
	 * <AUTHOR>
	 * @date 2023/12/19 16:19
	 * @param data *
	 * @return com.guangren.common.core.domain.AjaxResult *
	 */
	@PostMapping("/student/validateMobile")
	@ResponseBody
	public AjaxResult validateMobile(@RequestAttribute String data) {
		JSONArray array = JSONArray.parseArray(data);
		JSONObject json = array.getJSONObject(0);
		String mobile = json.getString("mobile");
		String code = json.getString("code");
		JSONObject result = new JSONObject();
		if (StringUtils.isEmpty(mobile)) {
			return error(AjaxResult.Type.WARN, "手机号不能为空");
		}
		if (cache.get(mobile)==null || !cache.get(mobile).equals(code)) {
			return error(AjaxResult.Type.WARN, "验证码错误或过期");
		}

		SchoolStudent temp = studentService.getOne(new QueryWrapper<SchoolStudent>().eq("mobile",mobile).eq("is_quit", 0).last("limit 1"));
		if(temp != null){
			return error(AjaxResult.Type.WARN,"该手机号已被使用");
		}

		List<SchoolStudent> tempList = studentService.list(new QueryWrapper<SchoolStudent>().eq("mobile", mobile).eq("is_quit", 1));
		for(SchoolStudent tmp : tempList) {
			SchoolStudentDropOut dropOutStudent = schoolStudentDropOutService.getOne(new QueryWrapper<SchoolStudentDropOut>().eq("student_id", tmp.getId()).last("limit 1"));
			if(dropOutStudent != null && dropOutStudent.getIsDone()==0) {
				return error(AjaxResult.Type.WARN,"该手机号正在退学中，请完成退学流程才能报名");
			}
		}
		result.put("isExist", true);
		return success(result);
	}

	/**
	 * 发送验证码
	 * 检查学生是否存在，存在则发送
	 * <AUTHOR>
	 * @date 2023/12/25 13:43
	 * @param data *
	 * @return com.guangren.common.core.domain.AjaxResult *
	 */
	@PostMapping("/student/isNotNull/sendCode")
	@ResponseBody
	public AjaxResult checkStudentIsNotNullSendMsgCode(@RequestAttribute String data) {
		JSONArray array = JSONArray.parseArray(data);
		JSONObject json = array.getJSONObject(0);
		String mobile = json.getString("mobile");
		SchoolStudent student = studentService.getOne(new LambdaQueryWrapper<SchoolStudent>()
				.eq(SchoolStudent::getMobile, mobile)
				.last("LIMIT 1"));
		JSONObject result = new JSONObject();
		if (student != null) {
			String validateCode = RandomUtil.randomNumbers(6);
			boolean success = this.send(mobile, validateCode,"");
			if (success) {
				result.put("code", validateCode);
				return success(result);
			}
		}
		return error("账号不存在");
	}

	/**
	 * 发送验证码
	 * 检查学生是否存在，不存在则发送
	 * <AUTHOR>
	 * @date 2023/12/25 13:43
	 * @param data *
	 * @return com.guangren.common.core.domain.AjaxResult *
	 */
	@PostMapping("/student/isNull/sendCode")
	@ResponseBody
	public AjaxResult checkStudentIsNullSendMsgCode(@RequestAttribute String data) {
		JSONArray array = JSONArray.parseArray(data);
		JSONObject json = array.getJSONObject(0);
		String mobile = json.getString("mobile");

		SchoolStudent temp = studentService.getOne(new QueryWrapper<SchoolStudent>().eq("mobile", mobile).eq("is_quit", 0).last("limit 1"));
		if(temp != null){
			return error(AjaxResult.Type.WARN,"该手机号已被使用");
		}

		List<SchoolStudent> tempList = studentService.list(new QueryWrapper<SchoolStudent>().eq("mobile", mobile).eq("is_quit", 1));
		for(SchoolStudent tmp : tempList) {
			SchoolStudentDropOut dropOutStudent = schoolStudentDropOutService.getOne(new QueryWrapper<SchoolStudentDropOut>().eq("student_id", tmp.getId()).last("limit 1"));
			if(dropOutStudent != null && dropOutStudent.getIsDone()==0) {
				return error(AjaxResult.Type.WARN,"该手机号正在退学中，请完成退学流程才能报名");
			}
		}

		JSONObject result = new JSONObject();
		String validateCode = RandomUtil.randomNumbers(6);
		boolean success = this.sendMsg(mobile, validateCode);
		if (success) {
			result.put("code", validateCode);
			return success(result);
		}else {
			return error("验证码发送错误，请重新点击发送");
		}
	}

	/**
	 * 发送验证码
	 * <AUTHOR>
	 * @date 2023/12/21 10:25
	 * @param data *
	 * @return com.guangren.common.core.domain.AjaxResult *
	 */
	@PostMapping("/sendCode")
	@ResponseBody
	public AjaxResult sendMsgCode(@RequestAttribute String data) {
		try {
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String mobile = json.getString("mobile");
			String validateCode = RandomUtil.randomNumbers(6);
			if (debug) {
				validateCode = "123456";
			}
			// 第一次使用移动发送验证码，第二次之后都使用玄武发送验证码
			boolean success = this.send(mobile, validateCode,"");
			if (success) {
				if (cache.get(mobile) != null) {
					cache.remove(mobile);
				}
				cache.put(mobile, validateCode);
				JSONObject code = new JSONObject();
				code.put("code", validateCode);
				return success(code);
			}

		} catch (Exception ex) {
			logger.error("get cand mobile error", ex);
		}
		return error();
	}

	private boolean sendMsg(String mobile,String validateCode) {
		boolean success = false;
		try {
			String msg = "您正在进行学车报名，请本人签署电子合同，确保学车权益，验证码为"+validateCode+"，5分钟之内有效，请勿泄露";
			success = studentSIMService.xuanwuSendMsg(mobile, msg);
			if (success) {
				if (cache.get(mobile) != null) {
					cache.remove(mobile);
				}
				cache.put(mobile, validateCode);
			}
			return success;
		}catch (Exception e) {
			log.error("send msg error:", e);
		}
		return success;
	}

	/**
	 * 发送验证码
	 * <AUTHOR>
	 * @date 2023/12/25 13:37
	 * @param mobile *
	 * @param validateCode *
	 * @return java.lang.String *
	 */
	private boolean send(String mobile, String validateCode,String type) {
		boolean success = false;
		try {
			if (sendCodeCountCache.get(mobile) == null) {
				sendCodeCountCache.put(mobile, 1);
				log.info("号码："+ mobile + "使用移动发送");
				success = studentSIMService.xuanwuSendValidateCode(mobile, validateCode,type);
			}else {
				log.info("号码："+ mobile + "使用玄武发送");
				success = studentSIMService.xuanwuSendValidateCode(mobile, validateCode,type);
			}

			if (success) {
				if (cache.get(mobile) != null) {
					cache.remove(mobile);
				}
				cache.put(mobile, validateCode);
			}
			return success;
		}catch (Exception e) {
			log.error("send msg error:", e);
		}
		return success;
	}

	/**
	 * 学员登录查询（根据手机号 + 验证码查）
	 * <AUTHOR>
	 * @date 2023/12/21 14:48
	 * @param data *
	 * @return com.guangren.common.core.domain.AjaxResult *
	 */
	@PostMapping("/student/detail4")
	@ResponseBody
	public AjaxResult detail4(@RequestAttribute String data){
		try{
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String mobile = json.getString("mobile");
			String code = json.getString("code");
			if (cache.get(mobile) == null) {
				return error(AjaxResult.Type.WARN, "验证码错误或过期");
			}
			if (!cache.get(mobile).equals(code)) {
				return error(AjaxResult.Type.WARN, "验证码错误或过期");
			}
			SchoolStudent student = null;
			SchoolStudentDropOut dropOut = null;
			List<SchoolStudent> schoolStudents = studentService.list(new LambdaQueryWrapper<SchoolStudent>()
					.eq(SchoolStudent::getMobile, mobile));

			// 查询退学表中是否存在退学中的数据
			for (SchoolStudent schoolStudent : schoolStudents) {
				dropOut = schoolStudentDropOutService.getOne(new LambdaQueryWrapper<SchoolStudentDropOut>()
						.eq(SchoolStudentDropOut::getStudentId, schoolStudent.getId())
						.eq(SchoolStudentDropOut::getStatus, 0));
				if (dropOut != null) {
					//如果存在七天未确认但又初审通过的学员也视为退学完成，不需要带出其相关信息
					long betweenDay = DateUtil.between(dropOut.getCreatedTime(), new Date(), DateUnit.DAY);
					if(betweenDay>7){
						if(dropOut.getIsFirstTrial()!=null && (dropOut.getIsFirstTrial()==1||dropOut.getIsFirstTrial()==3)){
							//七天未确认的学员经过初审通过或终审通过就代表退学流程已走完，跳过该记录
							dropOut = null;
							continue;
						}else{
							student = schoolStudent;
							break;
						}
					}else{
						student = schoolStudent;
						break;
					}
				}
			}

			// 退学表不存在退学中的数据则返回正常报名的数据
			if (student == null) {
				List<SchoolStudent> students = schoolStudents.stream()
						.filter(schoolStudent -> schoolStudent.getIsQuit() == 0)
						.collect(Collectors.toList());
				if(students.size() > 0) {
					student = students.get(0);
				}
			}

			if(student == null){
				return error(AjaxResult.Type.WARN,"无此学员报名信息");
			}

			JSONObject stu = new JSONObject();
			stu.put("stuId", student.getId());
			stu.put("name", student.getName());
			stu.put("mobile", student.getMobile());
			stu.put("identityType", student.getIdentityType());
			stu.put("identity", student.getIdentity());
			stu.put("licenseType", student.getLicenseType());

			SchoolStudentSIM sim = studentSIMService.getOne(new QueryWrapper<SchoolStudentSIM>().eq("student_id", student.getId()));
			stu.put("simmobile",sim==null ? "" : sim.getSimMobile());

			stu.put("isQuit", dropOut != null);
			stu.put("isPreRegistration", student.getStatus() == 1);
			stu.put("quitStudentId",dropOut==null ? "" : dropOut.getStudentId());
			stu.put("address", student.getRealProvince()+student.getRealCity()+student.getRealTown()+student.getRealAddress());
			stu.put("status", student.getStatus());
			stu.put("prepareRegisteDate", DateUtil.format(student.getPrepareRegisteDate(),"yyyy-MM-dd HH:mm:ss"));
			if(student.getRegisteDate() != null){
				stu.put("registeDate", DateUtil.format(student.getRegisteDate(),"yyyy-MM-dd HH:mm:ss"));
			}else{
				Calendar cal = Calendar.getInstance();
				cal.setTime(student.getPrepareRegisteDate());
				cal.add(Calendar.DATE, 5);
				stu.put("registeDate", DateUtil.format(cal.getTime(),"yyyy-MM-dd HH:mm:ss"));
			}

			if(StringUtils.isNotEmpty(student.getHeadImage())){
				stu.put("headImage", domain+"/"+student.getHeadImage());
			}
			if(StringUtils.isNotEmpty(student.getRegistrationId())){
				SchoolRegistration registration = registrationService.getById(student.getRegistrationId());
				if(registration != null){
					stu.put("schoolName", registration.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("registration_id", student.getRegistrationId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}else if(StringUtils.isNotEmpty(student.getBranchId())){
				SchoolBranch branch = branchService.getById(student.getBranchId());
				if(branch != null){
					stu.put("schoolName", branch.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("branch_id", student.getBranchId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}else if(StringUtils.isNotEmpty(student.getSchoolId())){
				School school = schoolService.getById(student.getSchoolId());
				if(school != null){
					stu.put("schoolName", school.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("school_id", student.getSchoolId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}
			if (StringUtils.isNotEmpty(student.getSchoolId())) {
				SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>()
						.eq("school_id", student.getSchoolId())
						.isNull("branch_id")
						.isNull("registration_id")
						.last("limit 1"));
				if (contact != null) {
					stu.put("headSchoolTel", contact.getTel());
				}
			}
			DivsionOrder order = divsionOrderService.getOne(new LambdaQueryWrapper<DivsionOrder>()
					.eq(DivsionOrder::getStudentId,student.getId())
					.orderByDesc(DivsionOrder::getId)
					.last("limit 1"));
			stu.put("isExistOrder",order != null);
			if(order != null){
				JSONObject objectOrder = new JSONObject();
				objectOrder.put("orderNo",order.getOrderNo());
				objectOrder.put("orderTime",order.getOrderTime());
				objectOrder.put("totalFee",order.getTotalFee());
				objectOrder.put("status",order.getIsPay());
				objectOrder.put("registrationName",order.getRegistrationName());
				stu.put("order",objectOrder);
			}
			stu.put("isSignContract",false);
			SchoolStudentContract schoolStudentContract = schoolStudentContractService.getById(student.getId());
			if(schoolStudentContract != null){
				stu.put("isSignContract",schoolStudentContract.getIsSign()==1);
			}
			boolean isOtherUserPay = "1".equals(sysConfigService.selectConfigByKey("other.user.pay"));
			stu.put("isOtherUserPay",isOtherUserPay);
			stu.put("showGotoPay",isShowGotoPay(student.getId()));
			return success(stu);

		}catch(Exception ex){
			logger.error("registe error",ex);
		}
		return error();
	}

	/**
	 *
	 * 学员登录查询（根据openId查）
	 * <AUTHOR>
	 * @date 2023/12/21 15:33
	 * @param data
	 * @return com.guangren.common.core.domain.AjaxResult *
	 */
	@PostMapping("/student/detail5")
	@ResponseBody
	public AjaxResult detail5(@RequestAttribute String data){
		try{
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String openId = json.getString("openId");
			SchoolStudent student = null;
			SchoolStudentDropOut dropOut = null;
			List<SchoolStudent> schoolStudents = studentService.list(new LambdaQueryWrapper<SchoolStudent>()
					.eq(SchoolStudent::getOpenId, openId)
					.orderByDesc(SchoolStudent::getCreatedTime));

			// 查询退学表中是否存在退学中的数据
			for (SchoolStudent schoolStudent : schoolStudents) {
				dropOut = schoolStudentDropOutService.getOne(new LambdaQueryWrapper<SchoolStudentDropOut>()
						.eq(SchoolStudentDropOut::getStudentId, schoolStudent.getId())
						.eq(SchoolStudentDropOut::getStatus, 0));
				if (dropOut != null) {
					//如果存在七天未确认但又初审通过的学员也视为退学完成，不需要带出其相关信息
					long betweenDay = DateUtil.between(dropOut.getCreatedTime(), new Date(), DateUnit.DAY);
					if(betweenDay>7){
						if(dropOut.getIsFirstTrial()!=null && (dropOut.getIsFirstTrial()==1||dropOut.getIsFirstTrial()==3)){
							//七天未确认的学员经过初审通过或终审通过就代表退学流程已走完，跳过该记录
							dropOut=null;
							continue;
						}else{
							student = schoolStudent;
							break;
						}
					}else{
						student = schoolStudent;
						break;
					}
				}
			}

			// 退学表不存在退学中的数据则返回正常报名的数据
			if (student == null) {
				List<SchoolStudent> students = schoolStudents.stream()
						.filter(schoolStudent -> schoolStudent.getIsQuit() == 0)
						.collect(Collectors.toList());
				if(students.size() > 0) {
					student = students.get(0);
				}
			}

			if(student == null){
				return error(AjaxResult.Type.WARN,"无此学员报名信息");
			}

			JSONObject stu = new JSONObject();
			stu.put("stuId", student.getId());
			stu.put("name", student.getName());
			stu.put("mobile", student.getMobile());
			stu.put("identityType", student.getIdentityType());
			stu.put("identity", student.getIdentity());
			stu.put("licenseType", student.getLicenseType());

			SchoolStudentSIM sim = studentSIMService.getOne(new QueryWrapper<SchoolStudentSIM>().eq("student_id", student.getId()));
			stu.put("simmobile",sim==null ? "" : sim.getSimMobile());

			stu.put("isQuit", dropOut != null);
			stu.put("isPreRegistration", student.getStatus() == 1);
			stu.put("quitStudentId",dropOut==null ? "" : dropOut.getStudentId());
			stu.put("address", student.getRealProvince()+student.getRealCity()+student.getRealTown()+student.getRealAddress());
			stu.put("status", student.getStatus());
			stu.put("prepareRegisteDate", DateUtil.format(student.getPrepareRegisteDate(),"yyyy-MM-dd HH:mm:ss"));
			if(student.getRegisteDate() != null){
				stu.put("registeDate", DateUtil.format(student.getRegisteDate(),"yyyy-MM-dd HH:mm:ss"));
			}else{
				Calendar cal = Calendar.getInstance();
				cal.setTime(student.getPrepareRegisteDate());
				cal.add(Calendar.DATE, 5);
				stu.put("registeDate", DateUtil.format(cal.getTime(),"yyyy-MM-dd HH:mm:ss"));
			}

			if(StringUtils.isNotEmpty(student.getHeadImage())){
				stu.put("headImage", domain+"/"+student.getHeadImage());
			}
			if(StringUtils.isNotEmpty(student.getRegistrationId())){
				SchoolRegistration registration = registrationService.getById(student.getRegistrationId());
				if(registration != null){
					stu.put("schoolName", registration.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("registration_id", student.getRegistrationId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}else if(StringUtils.isNotEmpty(student.getBranchId())){
				SchoolBranch branch = branchService.getById(student.getBranchId());
				if(branch != null){
					stu.put("schoolName", branch.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("branch_id", student.getBranchId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}else if(StringUtils.isNotEmpty(student.getSchoolId())){
				School school = schoolService.getById(student.getSchoolId());
				if(school != null){
					stu.put("schoolName", school.getName());
					SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>().eq("school_id", student.getSchoolId()).last("limit 1"));
					if(contact != null){
						stu.put("schoolTel", contact.getTel());
					}
				}
			}
			if (StringUtils.isNotEmpty(student.getSchoolId())) {
				SchoolContact contact = schoolContactService.getOne(new QueryWrapper<SchoolContact>()
						.eq("school_id", student.getSchoolId())
						.isNull("branch_id")
						.isNull("registration_id")
						.last("limit 1"));
				if (contact != null) {
					stu.put("headSchoolTel", contact.getTel());
				}
			}
			DivsionOrder order = divsionOrderService.getOne(new LambdaQueryWrapper<DivsionOrder>()
					.eq(DivsionOrder::getStudentId,student.getId())
					.orderByDesc(DivsionOrder::getId)
					.last("limit 1"));
			stu.put("isExistOrder",order != null);
			if(order != null){
				JSONObject objectOrder = new JSONObject();
				objectOrder.put("orderNo",order.getOrderNo());
				objectOrder.put("orderTime",order.getOrderTime());
				objectOrder.put("totalFee",order.getTotalFee());
				objectOrder.put("status",order.getIsPay());
				objectOrder.put("registrationName",order.getRegistrationName());
				stu.put("order",objectOrder);
			}
			stu.put("isSignContract",false);
			SchoolStudentContract schoolStudentContract = schoolStudentContractService.getById(student.getId());
			if(schoolStudentContract != null){
				stu.put("isSignContract",schoolStudentContract.getIsSign()==1);
			}
			boolean isOtherUserPay = "1".equals(sysConfigService.selectConfigByKey("other.user.pay"));
			stu.put("isOtherUserPay",isOtherUserPay);
			if(student.getIsCheck()==0){
				stu.put("showGotoPay",false);
			}else {
				if (order != null && order.getIsPay() == 1) {
					stu.put("showGotoPay", false);
				} else {
					stu.put("showGotoPay", true);
				}
			}

			return success(stu);

		}catch(Exception ex){
			logger.error("registe error",ex);
		}
		return error();
	}



	@PostMapping("/student/miniappPay")
	@ResponseBody
	@RepeatSubmit(interval = 10000)
	public AjaxResult miniappPay(@RequestAttribute String data){
		try {
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String studentId = json.getString("stuId");
			String jsCode = json.getString("jsCode");
			if(StringUtils.isEmpty(jsCode)){
				return error("缺少参数jsCode");
			}

			SchoolStudent student = studentService.selectSchoolStudentById(studentId);
			SchoolStudentContract schoolStudentContract = schoolStudentContractService.selectSchoolStudentContractByStudentId(studentId);
			if(schoolStudentContract != null){
				//当前系统是否开启跳过合同签署参数
				Integer skipContractStatus = studentSkipContractService.querySkipContractStatus();
            boolean isSkipContract = skipContractStatus.equals(Constants.SKIP_CONTRACT_SWITCH_ON);
				if(isSkipContract){
					//开启了跳过合同签署参数，判断是否存在合同但未签署的情况，如果存在，需要写入跳过合同签署记录表中
					StudentSkipContract studentSkipContract = studentSkipContractService.selectStudentSkipContractByStudentId(studentId);
					if(studentSkipContract==null && schoolStudentContract.getIsSign()==0){
						studentSkipContract = new StudentSkipContract();
						studentSkipContract.setStudentId(studentId);
						studentSkipContract.setSchoolId(student.getSchoolId());
						studentSkipContract.setBranchId(student.getBranchId());
						studentSkipContract.setRegistrationId(student.getRegistrationId());
						studentSkipContract.setName(student.getName());
						studentSkipContract.setIdentity(student.getIdentity());
						studentSkipContract.setMobile(student.getMobile());
						studentSkipContract.setTriggerSkipDate(new Date());
						studentSkipContract.setNewContractPath(schoolStudentContract.getContractUrl());
						studentSkipContract.setIsFinishResign(0);
						studentSkipContract.setId(IdUtils.fastSimpleUUID());
						studentSkipContract.setCreateBy(student.getName());
						studentSkipContract.setCreateTime(new Date());
						studentSkipContract.setUpdateBy(student.getName());
						studentSkipContract.setUpdateTime(new Date());
						studentSkipContractService.insertStudentSkipContract(studentSkipContract);
					}
				}
				if(schoolStudentContract.getIsSign()!=1 && !isSkipContract){
					return error("学员："+student.getName()+"，未完成合同签署，请先返回或重新进入小程序查看并签署合同。");
				}
			}
			if(student == null){
				return error("学员不存在");
			}

			Date now = new Date();
			Date beforeDate = DateUtil.parse(DateUtil.format(now,"yyyy-MM-dd 08:00:00"),"yyyy-MM-dd HH:mm:ss");
			Date afterDate = DateUtil.parse(DateUtil.format(now,"yyyy-MM-dd 22:00:00"),"yyyy-MM-dd HH:mm:ss");
			School school = schoolService.getById(student.getSchoolId());
			if(school!=null && school.getId().equals("b75421514a9c4150ab6962e2ee3248fa")){
				afterDate = DateUtil.parse(DateUtil.format(now,"yyyy-MM-dd 23:00:00"),"yyyy-MM-dd HH:mm:ss");
			}

			if(now.before(beforeDate) || now.after(afterDate)){
				return error("请在8:00-22:00时间段发起支付");
			}

			if(student.getIsCheck() != 1){
				return error("该学员尚未审核，请审核之后再支付");
			}
			if(student.getIsQuit() == 1){
				return error("该学员已退学，请重新报名之后再支付");
			}
			if(student.getSuperviseFeeIsOk()==1){
				return error("该学员已经通过其他渠道支付成功,请勿重复支付");
			}
			long count = divsionOrderService.count(new LambdaQueryWrapper<DivsionOrder>()
					.eq(DivsionOrder::getStudentId,student.getId())
					.eq(DivsionOrder::getIsPay,1));
			if(count>0){
				return error("该学员已经支付成功,请勿重复支付");
			}

			SpecialSchool specialSchool = specialSchoolService.getById(student.getSchoolId());
			if(specialSchool != null){
				return error("在"+student.getSchool().getName()+"报名的学员，通过驾校后台产生二维码，支付宝扫码支付");
			}

			String expireDate = sysConfigService.selectConfigByKey("import.pay.expire.date");
			if(StringUtils.isEmpty(expireDate)){
				expireDate = "2024-03-15 00:00:00";
			}
			Date expire = DateUtil.parse(expireDate,"yyyy-MM-dd HH:mm:ss");
			if(student.getPrepareRegisteDate().before(expire)){
				return error("在"+expireDate+"之前报名的学员，通过驾校导入支付");
			}
			//支付前校验当前用户的OpenId是否大于三次付款
			String openId = student.getOpenId();
			if(StringUtils.isNotBlank(openId)){
				Integer payCount = redisCache.getCacheObject("payCount"+openId);
				if(payCount != null && payCount>=3){
					throw new BusinessException("同一个微信号支付次数不能超过3次");
				}
			}

			String payStatus = redisCache.getCacheObject(studentId);
			if("creating".equals(payStatus)){
				return error("正在产生订单");
			}
			if("created".equals(payStatus)){
				return error("已产生订单，如果未支付，请在1分钟之后在订单列表中重新发起支付");
			}
			redisCache.setCacheObject(studentId,"creating",60, TimeUnit.SECONDS);
			JSONObject wxpay = divsionOrderService.createMiniappPay(student,jsCode);
			return success(wxpay);

		}catch(Exception ex){
			logger.error("miniappPay error",ex);
			return error(ex.getMessage());
		}
	}

	@PostMapping("/student/reMiniappPay")
	@ResponseBody
	public AjaxResult reMiniappPay(@RequestAttribute String data){
		try {
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String orderNo = json.getString("orderNo");
			DivsionOrder order = divsionOrderService.getOne(new LambdaQueryWrapper<DivsionOrder>()
					.eq(DivsionOrder::getOrderNo,orderNo));
			if(order == null){
				return error("订单不存在");
			}
			SchoolStudent student = studentService.getById(order.getStudentId());
			if(student == null){
				return error("学员不存在");
			}
			Date now = new Date();
			Date beforeDate = DateUtil.parse(DateUtil.format(now,"yyyy-MM-dd 08:00:00"),"yyyy-MM-dd HH:mm:ss");
			Date afterDate = DateUtil.parse(DateUtil.format(now,"yyyy-MM-dd 22:00:00"),"yyyy-MM-dd HH:mm:ss");
			School school = schoolService.getById(student.getSchoolId());
			if(school!=null && school.getId().equals("b75421514a9c4150ab6962e2ee3248fa")){
				afterDate = DateUtil.parse(DateUtil.format(now,"yyyy-MM-dd 23:00:00"),"yyyy-MM-dd HH:mm:ss");
			}
			if(now.before(beforeDate) || now.after(afterDate)){
				return error("请在8:00-22:00时间段发起支付");
			}

			if(order.getIsPay()==1){
				return error("该订单已支付");
			}
			if(order.getIsPay()==2){
				return error("该订单已取消");
			}

			if(student.getSuperviseFeeIsOk()==1){
				return error("该学员已通过其他渠道支付，勿重复支付");
			}
			return success(JSONObject.parseObject(order.getWxpayParams()));
		}catch (Exception ex){
			logger.error("reMiniappPay error",ex);
		}
		return error();
	}

	@PostMapping("/student/otherUserMiniappPay")
	@ResponseBody
	public AjaxResult otherUserMiniappPay(@RequestAttribute String data){
		try {
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String studentId = json.getString("stuId");
			SchoolStudent student = studentService.selectSchoolStudentById(studentId);
			if(student == null){
				return error("学员不存在");
			}
			Date now = new Date();
			Date beforeDate = DateUtil.parse(DateUtil.format(now,"yyyy-MM-dd 08:00:00"),"yyyy-MM-dd HH:mm:ss");
			Date afterDate = DateUtil.parse(DateUtil.format(now,"yyyy-MM-dd 22:00:00"),"yyyy-MM-dd HH:mm:ss");
			School school = schoolService.getById(student.getSchoolId());
			if(school!=null && school.getId().equals("b75421514a9c4150ab6962e2ee3248fa")){
				afterDate = DateUtil.parse(DateUtil.format(now,"yyyy-MM-dd 23:00:00"),"yyyy-MM-dd HH:mm:ss");
			}
			if(now.before(beforeDate) || now.after(afterDate)){
				return error("请在8:00-22:00时间段发起支付");
			}

			if(student.getIsCheck() != 1){
				return error("该学员尚未审核，请审核之后再支付");
			}
			if(student.getSuperviseFeeIsOk()==1){
				return error("该学员已经通过其他渠道支付成功,请勿重复支付");
			}
			long count = divsionOrderService.count(new LambdaQueryWrapper<DivsionOrder>()
					.eq(DivsionOrder::getStudentId,student.getId())
					.eq(DivsionOrder::getIsPay,1));
			if(count>0){
				return error("该学员已经支付成功,请勿重复支付");
			}

			SpecialSchool specialSchool = specialSchoolService.getById(student.getSchoolId());
			if(specialSchool != null){
				return error("在"+student.getSchool().getName()+"报名的学员，通过驾校后台产生二维码，支付宝扫码支付");
			}
			String expireDate = sysConfigService.selectConfigByKey("import.pay.expire.date");
			if(StringUtils.isEmpty(expireDate)){
				expireDate = "2024-03-15 00:00:00";
			}
			Date expire = DateUtil.parse(expireDate,"yyyy-MM-dd HH:mm:ss");
			if(student.getPrepareRegisteDate().before(expire)){
				return error("在"+expireDate+"之前报名的学员，通过驾校导入支付");
			}
			//支付前校验当前用户的OpenId是否大于三次付款
			String openId = student.getOpenId();
			if(StringUtils.isNotBlank(openId)){
				Integer payCount = redisCache.getCacheObject("payCount"+openId);
				if(payCount != null && payCount>=3){
					throw new BusinessException("同一个微信号支付次数不能超过3次");
				}
			}
			String payStatus = redisCache.getCacheObject(studentId);
			if("creating".equals(payStatus)){
				return error("正在产生订单");
			}
			if("created".equals(payStatus)){
				return error("已产生订单，如果未支付，请在1分钟之后在订单列表中重新发起支付");
			}
			redisCache.setCacheObject(studentId,"creating",60, TimeUnit.SECONDS);
			JSONObject jsonObject = divsionOrderService.createMiniappQrcode(student);
			return success(jsonObject);
		}catch (Exception ex){
			logger.error("otherUserMiniappPay error",ex);
			return error(ex.getMessage());
		}
	}

	@PostMapping("/student/getContract")
	@ResponseBody
	public AjaxResult getContract(@RequestAttribute String data){
		try {
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String studentId = json.getString("stuId");
			SchoolStudent student = studentService.selectSchoolStudentById(studentId);
			if (student == null) {
				return error("学员不存在");
			}

			SchoolStudentContract schoolStudentContract = schoolStudentContractService.getById(student.getId());
			if(schoolStudentContract == null || StringUtils.isEmpty(schoolStudentContract.getContractNumber())){
				return error("未获取到相关合同");
			}

			json.clear();
			json.put("contractId",schoolStudentContract.getContractNumber());
			json.put("contractFile",domain+schoolStudentContract.getContractUrl());
			json.put("hasView",schoolStudentContract.getHasView());
			return success(json);

		}catch(Exception ex){
			logger.error("getContract error",ex);
		}

		return error("获取合同出错");
	}

	/**
	 * 学员查看合同
	 * @param data
	 * @return
	 */
	@PostMapping("/student/updateViewContractTime")
	@ResponseBody
	public AjaxResult studentViewContract(@RequestAttribute String data){
		try {
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String studentId = json.getString("stuId");
			SchoolStudent student = studentService.selectSchoolStudentById(studentId);
			if (student == null) {
				return error("学员不存在");
			}
			int res = studentService.updateStudentContract(studentId);
			json.clear();
			json.put("res",res);
			return success(json);
		}catch(Exception ex){
			logger.error("更新学员合同查看时间失败",ex);
			return error(ex.getMessage());
		}
	}

	/**
	 * 判断学员是否需要重签合同
	 * @param data
	 * @return
	 */
	@PostMapping("/student/needResignContract")
	@ResponseBody
	public AjaxResult needResignContract(@RequestAttribute String data){
		boolean needResign = false;
		JSONObject json = new JSONObject();
		try {
			JSONArray array = JSONArray.parseArray(data);
			//如果系统正在开启跳过合同签署的参数，直接跳过检验。
			Integer skipContractStatus = studentSkipContractService.querySkipContractStatus();
            boolean isSkipContract = skipContractStatus.equals(Constants.SKIP_CONTRACT_SWITCH_ON);
			if(isSkipContract){
				json.clear();
				json.put("needResign",false);
				return success(json);
			}
			json = array.getJSONObject(0);
			String studentId = json.getString("stuId");
			List<SchoolStudent> schoolStudents = studentService.list(new QueryWrapper<SchoolStudent>()
					.eq("id", studentId));
			SchoolStudentDropOut dropOut = null;
			SchoolStudent student = null;
			// 查询退学表中是否存在退学中的数据
			for (SchoolStudent schoolStudent : schoolStudents) {
				dropOut = schoolStudentDropOutService.getOne(new LambdaQueryWrapper<SchoolStudentDropOut>()
						.eq(SchoolStudentDropOut::getStudentId, schoolStudent.getId())
						.eq(SchoolStudentDropOut::getStatus, 0));
				if (dropOut != null) {
					//如果存在七天未确认但又初审通过的学员也视为退学完成，不需要带出其相关信息
					long betweenDay = DateUtil.between(dropOut.getCreatedTime(), new Date(), DateUnit.DAY);
					if(betweenDay>7){
						if(dropOut.getIsFirstTrial()!=null && (dropOut.getIsFirstTrial()==1||dropOut.getIsFirstTrial()==3)){
							//七天未确认的学员经过初审通过或终审通过就代表退学流程已走完，跳过该记录
							dropOut = null;
							continue;
						}else{
							student = schoolStudent;
							break;
						}
					}else{
						student = schoolStudent;
						break;
					}
				}
			}
			// 退学表不存在退学中的数据则返回正常报名的数据
			if (student == null) {
				List<SchoolStudent> students = schoolStudents.stream()
						.filter(schoolStudent -> schoolStudent.getIsQuit() == 0)
						.collect(Collectors.toList());
				if(students.size()>0) {
					student = students.get(0);
				}
			}

			//无此学员的报名信息，直接返回false
			if(student == null){
				json.clear();
				json.put("needResign",false);
				return success(json);
			}

			StudentSkipContract studentSkipContractDb = studentSkipContractService.selectStudentSkipContractByStudentId(student.getId());
			if(studentSkipContractDb!=null){
				//查看合同表中是否已完成合同签署
				SchoolStudentContract schoolStudentContract = schoolStudentContractService.getById(student.getId());
				if(schoolStudentContract!=null && schoolStudentContract.getIsSign()==1){
					needResign = false;
					//更新学员跳过合同签署记录表信息
					studentSkipContractDb.setIsFinishResign(1);
					studentSkipContractDb.setNewContractId(schoolStudentContract.getContractNumber());
					studentSkipContractDb.setNewContractPath(schoolStudentContract.getContractUrl());
					studentSkipContractDb.setNewSealId(schoolStudentContract.getSealId());
					studentSkipContractDb.setUpdateBy("system");
					studentSkipContractDb.setUpdateTime(new Date());
					studentSkipContractService.updateById(studentSkipContractDb);
				}else {
                    if (schoolStudentContract != null && schoolStudentContract.getContractUrl() != null && schoolStudentContract.getIsSign() == 0) {
                        needResign = true; //存在未签署的合同。
                    }
                }
			}
			json.clear();
			json.put("needResign",needResign);
			return success(json);
		}catch (Exception ex){
			ex.printStackTrace();
			json.clear();
			needResign = false;
			json.put("needResign",needResign);
			return success(json);
		}
	}
	/**
	 * 是否展示重签、跳过签署的按钮
	 * isShowResignContract 0不展示按钮、1展示生成合同按钮、2展示跳过签署按钮；
	 */
	@PostMapping("/student/isShowResignContract")
	@ResponseBody
	public AjaxResult isShowResignContract(@RequestAttribute String data){
		String msg="";
		Integer isShowResignContract = 0;
		try {
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = new JSONObject();
			//如果系统正在开启跳过合同签署的参数，直接跳过检验。
			Integer skipContractStatus = studentSkipContractService.querySkipContractStatus();
            boolean isSkipContract = skipContractStatus.equals(Constants.SKIP_CONTRACT_SWITCH_ON);
			json = array.getJSONObject(0);
			String studentId = json.getString("stuId");
			if(StringUtils.isBlank(studentId)){
				msg="学员ID为空";
			}
			//查看学员是否存在
			SchoolStudent student = studentService.selectSchoolStudentById(studentId);
			if(student == null){
				msg="学员不存在";
			}
			//生成合同按钮展示逻辑：当学员在重签表、且未生成合同且跳过签署合同报名的参数在关闭情况下才展示;
			if(!isSkipContract){
				StudentSkipContract studentSkipContract = studentSkipContractService.selectStudentSkipContractByStudentId(studentId);
				if(studentSkipContract==null){
					msg="学员未在重签合同表中";
				}
				SchoolStudentContract schoolStudentContract = schoolStudentContractService.getById(studentId);
				if(schoolStudentContract==null){
					msg="学员未生成合同";
					isShowResignContract = 1;
				}
			}
			//生成跳过合同签署按钮：当学员未签署合同且跳过签署合同报名的参数在打开的情况下才展示;
			if(isSkipContract){
				SchoolStudentContract schoolStudentContract = schoolStudentContractService.getById(studentId);
				if(schoolStudentContract!=null&&schoolStudentContract.getIsSign()==0){
					msg="学员未签署合同";
					isShowResignContract = 2; //展示跳过签署合同按钮
				}
			}

		}catch (Exception ex){
			AjaxResult error = AjaxResult.error();
			msg = ex.getMessage();
			logger.error("判断是否展示重签、跳过签署的按钮失败",ex);
			error.put("msg",msg);
			error.put("isShowResignContract",isShowResignContract);
			return error;
		}
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("msg",msg);
		jsonObject.put("isShowResignContract",isShowResignContract);
		return success(jsonObject);
	}

	/**
	 * 判断是否开启重签合同参数
	 * @param data
	 * @return
	 */
	@PostMapping("/student/isOpenSkipSign")
	@ResponseBody
	public AjaxResult isOpenSkipSign(@RequestAttribute String data){
		try {
			boolean isOpenSkipContractSign = false;
			JSONObject json = new JSONObject();
			Integer skipContractStatus = studentSkipContractService.querySkipContractStatus();
			boolean isSkipContract = skipContractStatus.equals(Constants.SKIP_CONTRACT_SWITCH_ON);
			if(isSkipContract){
				isOpenSkipContractSign = true;
			}
			json.put("isOpenSkipContractSign",isOpenSkipContractSign);
			return success(json);
		}catch (Exception ex){
			return error(ex.getMessage());
		}
 	}

	/**
	 * 学员生成合同（适用于跳过合同签署记录表的学员）
	 * @param data
	 * @return
	 * @throws BusinessException
	 */
	@PostMapping("/student/createResignContract")
	@ResponseBody
	public AjaxResult studentCreateContract(@RequestAttribute String data)throws BusinessException{
		JSONObject json = new JSONObject();
		JSONObject resJson = new JSONObject();
		JSONArray array = JSONArray.parseArray(data);
		json = array.getJSONObject(0);
		String studentId = json.getString("stuId");
		String contractUrl= "";
		try {
			//查看跳过重签参数是否打开
			Integer skipContractStatus = studentSkipContractService.querySkipContractStatus();
            boolean isSkipContract = skipContractStatus.equals(Constants.SKIP_CONTRACT_SWITCH_ON);
			if (isSkipContract) {
				throw new BusinessException("管理员已打开【跳过合同签署报名】，暂时无法生成合同。");
			}
			if(StringUtils.isBlank(studentId)){
				throw new BusinessException("学员ID不能为空");
			}
			SchoolStudent student = studentService.selectSchoolStudentById(studentId);
			if(student==null || student.getIsQuit().equals(1)){
				throw new BusinessException("学员表中不存在或学员已退学。");
			}
			//查看学员是否在跳过合同签署记录表中
			StudentSkipContract studentSkipContract = studentSkipContractService.selectStudentSkipContractByStudentId(studentId);
			if(studentSkipContract==null){
				throw new BusinessException("该学员不存在跳过合同签署记录表中");
			}
			//查看学员是否存在合同了
			SchoolStudentContract schoolStudentContract = schoolStudentContractService.getById(studentId);
			if(schoolStudentContract!=null && schoolStudentContract.getContractUrl()!=null){
				resJson.put("contractUrl",schoolStudentContract.getContractUrl());
				resJson.put("msg","该学员已生成合同，请直接签署。");
				return success(resJson);
			}
			//生成合同
			SchoolContract schoolContract = schoolContractService.getById(student.getSchoolId());
			if(schoolContract==null){
				throw new BusinessException("该驾校不存在合同信息");
			}
			//重新设置金额
			String needResignContract = studentResignContractService.createNeedResignContract(student, schoolContract);
			if(StringUtils.isNotBlank(needResignContract)){
				contractUrl = needResignContract;
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.error("学员ID:"+studentId+",在小程序生成重签合同失败，失败原因"+e.getMessage());
			return error("生成重签合同失败，请稍后重试。"+e.getMessage());
		}
		resJson.put("contractUrl",contractUrl);
		resJson.put("msg","生成重签合同成功，请尽快查阅后签署合同。");
		return success(resJson);
	}

	/**
	 * 签署重签的合同
	 * @param data
	 * @return
	 */
	@PostMapping("/student/reSignContract")
	@ResponseBody
	public AjaxResult reSignContract(@RequestAttribute String data){
		try {
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String studentId = json.getString("stuId");
			String sealImage = json.getString("sealImage");
			SchoolStudent student = studentService.selectSchoolStudentById(studentId);
			if (student == null) {
				return error("学员不存在");
			}
			if(StringUtils.isEmpty(sealImage)){
				return error("请上传签名图片");
			}
			SchoolStudentContract schoolStudentContract = schoolStudentContractService.getById(student.getId());
			if(schoolStudentContract == null || StringUtils.isEmpty(schoolStudentContract.getContractNumber())){
				return error("未获取到相关合同");
			}

			String filepath = RuoYiConfig.getDownloadPath()+"contract/"+student.getId()+".png";
			File sealFile = new File(filepath);
			sealFile =  Base64.decodeToFile(sealImage,sealFile);
			if(!sealFile.exists()){
				return error("未获取到签名文件");
			}
			JSONObject sealJson = yiDongShuiEQianService.addPersonSeal(schoolStudentContract.getUserId(),sealFile);
			String sealId = sealJson.getString("sealId");
			yiDongShuiEQianService.signContract(schoolStudentContract.getUserId(),schoolStudentContract.getContractNumber(),
					1,1,sealId,"乙方（签名）",5,-5);

			schoolStudentContract.setIsSign(1);
			schoolStudentContract.setSealId(sealId);
			schoolStudentContract.setSignContractTime(DateUtils.getNowDate()); //添加签署合同的时间

			schoolStudentContractService.updateById(schoolStudentContract);

			//更新重签表
			StudentResignContract studentResignContract = new StudentResignContract();
			studentResignContract.setStudentId(studentId);
			studentResignContract.setNewSealId(sealId);
			studentResignContract.setNewContractSignTime(DateUtils.getNowDate());
			studentResignContract.setIsResign(1);
			studentResignContractService.updateStudentResignContract(studentResignContract);

			return success("签署合同成功");

		}catch(Exception ex){
			logger.error("getContract error",ex);
		}

		return error("签署合同出错");
	}

	/**
	 * 获取学员合同的查看状态
	 * @param data
	 * @return
	 */
	@PostMapping("/student/getContractViewStatus")
	@ResponseBody
	public AjaxResult getContractViewStatus(@RequestAttribute String data){
		try {
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String studentId = json.getString("stuId");
			SchoolStudent student = studentService.selectSchoolStudentById(studentId);
			if (student == null) {
				return error("学员不存在");
			}
			int i = studentService.checkStudentContractHasUpdate(studentId);
			json.clear();
			json.put("contractViewStatus",i);
			return success(json);
		}catch(Exception ex){
			logger.error("获取学员合同查看状态出错",ex);
			return error(ex.getMessage());
		}
	}

	@PostMapping("/student/signContract")
	@ResponseBody
	public AjaxResult signContract(@RequestAttribute String data){
		try {
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String studentId = json.getString("stuId");
			String sealImage = json.getString("sealImage");
			SchoolStudent student = studentService.selectSchoolStudentById(studentId);
			if (student == null) {
				return error("学员不存在");
			}
			if(StringUtils.isEmpty(sealImage)){
				return error("请上传签名图片");
			}
			SchoolStudentContract schoolStudentContract = schoolStudentContractService.getById(student.getId());
			if(schoolStudentContract == null || StringUtils.isEmpty(schoolStudentContract.getContractNumber())){
				return error("未获取到相关合同");
			}

			String filepath = RuoYiConfig.getDownloadPath()+"contract/"+student.getId()+".png";
			File sealFile = new File(filepath);
			sealFile =  Base64.decodeToFile(sealImage,sealFile);
			if(!sealFile.exists()){
				return error("未获取到签名文件");
			}
			JSONObject sealJson = yiDongShuiEQianService.addPersonSeal(schoolStudentContract.getUserId(),sealFile);

//			JSONArray jsonArray = yiDongShuiEQianService.findSealList(schoolStudentContract.getUserId(),1);
//			if(jsonArray==null || jsonArray.isEmpty()){
//				return error("未找到该用户的签名");
//			}
			String sealId = sealJson.getString("sealId");
			yiDongShuiEQianService.signContract(schoolStudentContract.getUserId(),schoolStudentContract.getContractNumber(),
					1,1,sealId,"乙方（签名）",5,-5);

			schoolStudentContract.setIsSign(1);
			schoolStudentContract.setSealId(sealId);
			schoolStudentContract.setSignContractTime(DateUtils.getNowDate()); //添加签署合同的时间

			schoolStudentContractService.updateById(schoolStudentContract);
			return success("签署合同成功");

		}catch(Exception ex){
			logger.error("getContract error",ex);
		}

		return error("签署合同出错");
	}

	/**
	 * 查看已签署合同
	 * @param data
	 * @return
	 */
	@PostMapping("/student/viewSignContract")
	@ResponseBody
	public AjaxResult viewSignContract(@RequestAttribute String data){
		try {
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String studentId = json.getString("stuId");
			SchoolStudent student = studentService.selectSchoolStudentById(studentId);
			if (student == null) {
				return error("学员不存在");
			}
			SchoolStudentContract schoolStudentContract = schoolStudentContractService.selectSchoolStudentContractByStudentId(studentId);
			if(schoolStudentContract==null){
				return error("学员合同不存在，无需下载");
			}
			if(schoolStudentContract.getIsSign()!=1){
				return error("当前学员未签署合同，无需下载");
			}
			if(schoolStudentContract.getHasView()==0){
				JSONObject contractJson = yiDongShuiEQianService.downloadContract(schoolStudentContract.getContractNumber());
				String filepath = RuoYiConfig.getDownloadPath()+"contract/"+schoolStudentContract.getContractNumber()+".pdf";
				File file = new File(filepath);
				try {
					Base64.decodeToFile(contractJson.getString("file"), file);
					schoolStudentContract.setHasView(1);
					schoolStudentContractService.updateById(schoolStudentContract);
				}catch(Exception ex){
					logger.error("下载合同出错",ex);
					try{
						file.deleteOnExit();
						boolean result = file.createNewFile();
						if(result) {
							Base64.decodeToFile(contractJson.getString("file"), file);
							schoolStudentContract.setHasView(1);
							schoolStudentContractService.updateById(schoolStudentContract);
						}else{
							throw new BusinessException("创建文件出错");
						}
					}catch(Exception e){
						logger.error("再次下载合同出错",e);
					}
				}
			}
			json.clear();
			json.put("contractId",schoolStudentContract.getContractNumber());
			json.put("contractFile",domain+schoolStudentContract.getContractUrl());
			return success(json);
		}catch(Exception ex){
			logger.error("getContract error",ex);
		}
		return error("获取已签署合同出错");
	}

	@PostMapping("/student/getOrder")
	@ResponseBody
	public AjaxResult getOrder(@RequestAttribute String data){
		try{
			JSONArray array = JSONArray.parseArray(data);
			JSONObject json = array.getJSONObject(0);
			String studentId = json.getString("stuId");

			json.clear();
			DivsionOrder order = divsionOrderService.getOne(new LambdaQueryWrapper<DivsionOrder>()
					.eq(DivsionOrder::getStudentId,studentId).last("limit 1")
					.orderByDesc(DivsionOrder::getId));
			if(order != null){
				JSONObject objectOrder = new JSONObject();
				objectOrder.put("orderNo",order.getOrderNo());
				objectOrder.put("orderTime",order.getOrderTime());
				objectOrder.put("totalFee",order.getTotalFee());
				objectOrder.put("status",order.getIsPay());
				objectOrder.put("registrationName",order.getRegistrationName());
				json.put("order",objectOrder);
			}

			SchoolStudentContract contract = schoolStudentContractService.getById(studentId);
			if(contract != null && StringUtils.isNotEmpty(contract.getContractNumber())){
				if (contract.getHasView() != null && contract.getHasView() == 0) {
					JSONObject contractJson = yiDongShuiEQianService.downloadContract(contract.getContractNumber());
					String filepath = RuoYiConfig.getDownloadPath()+"contract/"+contract.getContractNumber()+".pdf";
					File file = new File(filepath);
					try {
						Base64.decodeToFile(contractJson.getString("file"), file);
						contract.setHasView(1);
						schoolStudentContractService.updateById(contract);
					}catch(Exception ex){
						logger.error("下载合同出错",ex);
						try{
							file.deleteOnExit();
							boolean result = file.createNewFile();
							if(result) {
								Base64.decodeToFile(contractJson.getString("file"), file);
								contract.setHasView(1);
								schoolStudentContractService.updateById(contract);
							}else{
								throw new BusinessException("创建文件出错");
							}
						}catch(Exception e){
							logger.error("再次下载合同出错",e);
						}
					}
				}
				json.put("contractFile",domain+contract.getContractUrl());
			}
			return success(json);

		}catch(Exception ex){
			logger.error("get order error",ex);
		}
		return error("获取订单失败");
	}

	// 辅助方法，从 JSON 对象中获取 BigDecimal 值，如果字段不存在或无法解析为数字，则返回 BigDecimal.ZERO
	public BigDecimal getBigDecimalFromJSON(String key, JSONObject json) {
		Object value = json.get(key);
		if (value == null || value instanceof String && ((String) value).trim().isEmpty()) {
			return BigDecimal.ZERO;
		} else if (value instanceof Number) {
			return new BigDecimal(((Number) value).toString());
		} else {
			try {
				return new BigDecimal(json.getString(key));
			} catch (NumberFormatException e) {
				return BigDecimal.ZERO;
			}
		}
	}

}
