package com.guangren.business.helper;

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.guangren.common.exception.BusinessException;
import com.guangren.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.font.FontRenderContext;
import java.awt.font.TextLayout;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Component
public class QrCodeGenerateHelper {
    @Value("${ruoyi.qrcodeTempleteFile}")
    String imagePath;

    public BufferedImage generateImage(List<String> texts, String qrCodeText) throws Exception {
        // 验证参数
        if (StringUtils.isBlank(imagePath)) {
            throw new BusinessException("驾培二维码图片源文件地址不能为空");
        }
        File sourceFile = new File(imagePath);
        if (!sourceFile.exists() || !sourceFile.isFile()) {
            throw new BusinessException("驾培二维码图片源文件在" + imagePath + "中，未找到");
        }
        if (StringUtils.isBlank(qrCodeText)) {
            throw new BusinessException("二维码的内容不能为空");
        }
        if (CollectionUtils.isEmpty(texts)) {
            throw new BusinessException("文本内容不能为空");
        }

        // 加载背景图片
        BufferedImage background = ImageIO.read(sourceFile);
        int backgroundWidth = background.getWidth();
        int xCenter = backgroundWidth / 2;

        // 设置绘图上下文
        Graphics2D g2d = background.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 绘制二维码
        QrConfig qrConfig = new QrConfig(1100, 1100);
        qrConfig.setMargin(3);
        qrConfig.setForeColor(Color.BLACK);
        qrConfig.setBackColor(Color.WHITE);
        qrConfig.setErrorCorrection(ErrorCorrectionLevel.M);
        BufferedImage qrBufferImage = QrCodeUtil.generate(qrCodeText, qrConfig);
        int qrCodeCentX = xCenter - qrBufferImage.getWidth() / 2;
        int qrCodeCentY = 1000;
        g2d.drawImage(qrBufferImage, qrCodeCentX, qrCodeCentY, null);

        // 设置字体
        Font font = new Font("Source Han Sans CN", Font.BOLD, 120);
        g2d.setFont(font);
        FontMetrics fm = g2d.getFontMetrics();

        // 设置边距和行间距
        int leftMargin = 100;
        int rightMargin = 100;
        int lineSpacing = 200;
        int maxLineWidth = backgroundWidth - leftMargin - rightMargin;
        int textY = 2300;

        for (String text : texts) {
            if (text == null) continue;

            List<String> lines = splitTextIntoLines(text, maxLineWidth, fm);
            for (String line : lines) {
                drawTextLine(g2d, line, xCenter, textY, fm, font, backgroundWidth, leftMargin, rightMargin);
                textY += lineSpacing;
            }
        }

        g2d.dispose();
        return background;
    }

    private List<String> splitTextIntoLines(String text, int maxWidth, FontMetrics fm) {
        List<String> lines = new ArrayList<>();
        StringBuilder currentLine = new StringBuilder();
        int currentWidth = 0;

        for (char c : text.toCharArray()) {
            int charWidth = fm.charWidth(c);

            // 预判断添加当前字符后是否会超出最大宽度
            // 额外添加字符间距15像素的考虑
            if (currentWidth + charWidth + 15 > maxWidth) {
                // 当前行已经接近最大宽度，需要换行
                if (currentLine.length() > 0) {
                    lines.add(currentLine.toString());
                    currentLine = new StringBuilder();
                    currentWidth = 0;
                }
            }

            currentLine.append(c);
            currentWidth += charWidth + 15; // 考虑字符间距
        }

        // 添加最后一行
        if (currentLine.length() > 0) {
            lines.add(currentLine.toString());
        }

        return lines;
    }

    private void drawTextLine(Graphics2D g2d, String text, int xCenter, int y, FontMetrics fm, Font font, int backgroundWidth, int leftMargin, int rightMargin) {
        int textWidth = fm.stringWidth(text) + (text.length() - 1) * 15; // 考虑字符间距
        int x = Math.max(leftMargin, (backgroundWidth - textWidth) / 2);

        char[] charArray = text.toCharArray();
        int currentX = x;

        for (char c : charArray) {
            FontRenderContext frc = g2d.getFontRenderContext();
            TextLayout textLayout = new TextLayout(String.valueOf(c), font, frc);
            Shape shape = textLayout.getOutline(AffineTransform.getTranslateInstance(currentX, y));

            // 绘制描边
            g2d.setColor(Color.WHITE);
            g2d.setStroke(new BasicStroke(18));
            g2d.draw(shape);

            // 填充文字
            g2d.setColor(Color.RED);
            g2d.fill(shape);

            currentX += fm.charWidth(c) + 15; // 添加固定的字符间距
        }
    }
}