package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolTeacher;
import com.guangren.business.vo.SchoolTeacherImportVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 教练Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface ISchoolTeacherService extends IService<SchoolTeacher>
{
    /**
     * 查询教练
     * 
     * @param id 教练主键
     * @return 教练
     */
    public SchoolTeacher selectSchoolTeacherById(String id);

    /**
     * 查询教练列表
     * 
     * @param schoolTeacher 教练
     * @return 教练集合
     */
    public List<SchoolTeacher> selectSchoolTeacherList(SchoolTeacher schoolTeacher);

    /**
     * 新增教练
     * 
     * @param schoolTeacher 教练
     * @return 结果
     */
    public int insertSchoolTeacher(SchoolTeacher schoolTeacher);

    /**
     * 修改教练
     * 
     * @param schoolTeacher 教练
     * @return 结果
     */
    public int updateSchoolTeacher(SchoolTeacher schoolTeacher);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SchoolTeacher schoolTeacher);

    /**
     * 批量删除教练
     * 
     * @param ids 需要删除的教练主键集合
     * @return 结果
     */
    public int deleteSchoolTeacherByIds(String ids);

    /**
     * 删除教练信息
     * 
     * @param id 教练主键
     * @return 结果
     */
    public int deleteSchoolTeacherById(String id);

    public List<SchoolTeacherImportVo> importTeacher(MultipartFile file);

    /**
     * 导出教练列表
     */
    List<SchoolTeacher> exportSchoolTeacherList(SchoolTeacher schoolTeacher);
}
