package com.guangren.business.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.UseUnicomNumber;

import java.util.List;

/**
 * 联通号码开卡Service接口
 * 
 * <AUTHOR>
 * @date 2023-10-24
 */
public interface IUseUnicomNumberService extends IService<UseUnicomNumber>
{
    /**
     * 查询联通号码开卡
     * 
     * @param id 联通号码开卡主键
     * @return 联通号码开卡
     */
    public UseUnicomNumber selectUseUnicomNumberById(String id);

    /**
     * 查询联通号码开卡列表
     * 
     * @param useUnicomNumber 联通号码开卡
     * @return 联通号码开卡集合
     */
    public List<UseUnicomNumber> selectUseUnicomNumberList(UseUnicomNumber useUnicomNumber);

    /**
     * 导出开卡详情列表
     * <AUTHOR>
     * @date 2023/11/22 11:48
     * @param useUnicomNumber *
     * @return java.util.List<com.guangren.business.domain.UseUnicomNumber> *
     */
    public List<UseUnicomNumber> exportUseUnicomNumberList(UseUnicomNumber useUnicomNumber);

    /**
     * 新增联通号码开卡
     * 
     * @param useUnicomNumber 联通号码开卡
     * @return 结果
     */
    public int insertUseUnicomNumber(UseUnicomNumber useUnicomNumber);

    /**
     * 修改联通号码开卡
     * 
     * @param useUnicomNumber 联通号码开卡
     * @return 结果
     */
    public int updateUseUnicomNumber(UseUnicomNumber useUnicomNumber);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(UseUnicomNumber useUnicomNumber);

    /**
     * 批量删除联通号码开卡
     * 
     * @param ids 需要删除的联通号码开卡主键集合
     * @return 结果
     */
    public int deleteUseUnicomNumberByIds(String ids);

    /**
     * 删除联通号码开卡信息
     * 
     * @param id 联通号码开卡主键
     * @return 结果
     */
    public int deleteUseUnicomNumberById(String id);

    /**
     * 设置开卡状态保存
     * <AUTHOR>
     * @date 2023/10/25 14:13
     * @param useUnicomNumber *
     * @return int *
     */
    int setNumberStatus(UseUnicomNumber useUnicomNumber);
}
