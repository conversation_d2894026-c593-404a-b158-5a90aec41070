package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.domain.StudentSkipContract;

import java.util.List;

public interface IStudentSkipContractService extends IService<StudentSkipContract> {
    //查询列表
    List<StudentSkipContract> selectStudentSkipContractList(StudentSkipContract studentSkipContract);
    //查询
    StudentSkipContract selectStudentSkipContractById(String id);
    //新增
    int insertStudentSkipContract(StudentSkipContract studentSkipContract);
    //修改
    int updateStudentSkipContract(StudentSkipContract studentSkipContract);
    //删除
    int deleteStudentSkipContractById(String id);
    //批量删除
    int deleteStudentSkipContractByIds(String ids);
    //发送补签通知
    String sendNotice(StudentSkipContract studentSkipContract);
    //处理跳过合同请求方法
    void handleSkipContract(StudentSkipContract studentSkipContract);

    StudentSkipContract selectStudentSkipContractByStudentId(String studentId);
    //开启或关闭跳过合同签署
    Integer switchSkipContract(Integer param);

    //查询跳过合同签署开关状态
    Integer querySkipContractStatus();
}
