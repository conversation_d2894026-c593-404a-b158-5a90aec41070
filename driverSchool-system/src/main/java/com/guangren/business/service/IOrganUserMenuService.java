package com.guangren.business.service;

import java.util.List;
import com.guangren.business.domain.OrganUserMenu;

/**
 * 机构用户菜单权限Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface IOrganUserMenuService 
{
    /**
     * 查询机构用户菜单权限
     * 
     * @param organUserId 机构用户菜单权限主键
     * @return 机构用户菜单权限
     */
    public OrganUserMenu selectOrganUserMenuByOrganUserId(String organUserId);

    /**
     * 查询机构用户菜单权限列表
     * 
     * @param organUserMenu 机构用户菜单权限
     * @return 机构用户菜单权限集合
     */
    public List<OrganUserMenu> selectOrganUserMenuList(OrganUserMenu organUserMenu);

    /**
     * 新增机构用户菜单权限
     * 
     * @param organUserMenu 机构用户菜单权限
     * @return 结果
     */
    public int insertOrganUserMenu(OrganUserMenu organUserMenu);

    /**
     * 修改机构用户菜单权限
     * 
     * @param organUserMenu 机构用户菜单权限
     * @return 结果
     */
    public int updateOrganUserMenu(OrganUserMenu organUserMenu);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(OrganUserMenu organUserMenu);

    /**
     * 批量删除机构用户菜单权限
     * 
     * @param organUserIds 需要删除的机构用户菜单权限主键集合
     * @return 结果
     */
    public int deleteOrganUserMenuByOrganUserIds(String organUserIds);

    /**
     * 删除机构用户菜单权限信息
     * 
     * @param organUserId 机构用户菜单权限主键
     * @return 结果
     */
    public int deleteOrganUserMenuByOrganUserId(String organUserId);
}
