package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.OrganUser;

import java.util.List;

public interface IOrganUserService extends IService<OrganUser>{
    /**
     * 查询机构用户
     *
     * @param id 机构用户主键
     * @return 机构用户
     */
    public OrganUser selectOrganUserById(Long id);

    /**
     * 查询机构用户列表
     *
     * @param organUser 机构用户
     * @return 机构用户集合
     */
    public List<OrganUser> selectOrganUserList(OrganUser organUser);

    /**
     * 通过用户名查询用户
     *
     * @return 用户对象信息
     */
    public OrganUser selectUserByLoginName(OrganUser organUser);

    /**
     * 通过手机号查询用户
     *
     * @return 用户对象信息
     */
    public OrganUser selectUserByTel(OrganUser organUser);

    /**
     * 新增机构用户
     *
     * @param organUser 机构用户
     * @return 结果
     */
    public int insertOrganUser(OrganUser organUser);

    /**
     * 修改机构用户
     *
     * @param organUser 机构用户
     * @return 结果
     */
    public int updateOrganUser(OrganUser organUser);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(OrganUser organUser);

    /**
     * 批量删除机构用户
     *
     * @param ids 需要删除的机构用户主键集合
     * @return 结果
     */
    public int deleteOrganUserByIds(String ids);

    /**
     * 删除机构用户信息
     *
     * @param id 机构用户主键
     * @return 结果
     */
    public int deleteOrganUserById(String id);

    public int addOperateUser(OrganUser organUser);

    public int updateOperateUser(OrganUser organUser);

    public int updateUserInfo(OrganUser user);

    public int resetUserPwd(OrganUser user);

    public List<OrganUser> selectOperateUserList(OrganUser organUser);

}
