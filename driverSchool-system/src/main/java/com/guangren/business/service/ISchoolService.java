package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.School;
import com.guangren.business.domain.SchoolBranch;
import com.guangren.business.service.impl.YiDongShuiEQianService;
import com.guangren.business.vo.ContractVo;
import com.guangren.common.core.domain.CxSelect;

import java.util.List;

/**
 * 驾校管理Service接口
 * 
 * <AUTHOR>
 * @date 2023-02-15
 */
public interface ISchoolService extends IService<School>
{
    /**
     * 查询驾校管理
     * 
     * @param id 驾校管理主键
     * @return 驾校管理
     */
    public School selectSchoolById(String id);

    /**
     * 查询驾校管理列表
     * 
     * @param school 驾校管理
     * @return 驾校管理集合
     */
    public List<School> selectSchoolList(School school);

    /**
     * 新增驾校管理
     * 
     * @param school 驾校管理
     * @return 结果
     */
    public int insertSchool(School school);

    /**
     * 修改驾校管理
     * 
     * @param school 驾校管理
     * @return 结果
     */
    public int updateSchool(School school);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(School school);

    /**
     * 批量删除驾校管理
     * 
     * @param ids 需要删除的驾校管理主键集合
     * @return 结果
     */
    public int deleteSchoolByIds(String ids);

    /**
     * 删除驾校管理信息
     * 
     * @param id 驾校管理主键
     * @return 结果
     */
    public int deleteSchoolById(String id);

    public List<CxSelect> schoolData(School school);

    public List<CxSelect> schoolTowData(SchoolBranch school);

    /**
     * 驾校、分校、报名点联动
     */
    public List<CxSelect> schoolThreeData(School school);

    /**
     * 驾校、分校、报名点联动(未被撤销)
     */
    public List<CxSelect>schoolThreeNormalData(School school);

    public int saveAccount(School school);

    /**
     * 导出驾校列表
     */
    List<School> exportSchoolList(School school);

    /**
     * 定时任务用，用于获取合同模板的坐标位置
     * @return
     */
    List<YiDongShuiEQianService.TemplateComponent>getCreateTemplateComponentList();

    /**
     * 审核资金监管账户保存
     * <AUTHOR>
     * @date 2023/10/31 10:41
     * @param school *
     * @return int *
     */
    int checkAccountSave(School school);

    /**
     * 设置合同模板保存
     * <AUTHOR>
     * @date 2024/2/26 8:48
     * @param contractVo  *
     */
    void saveContract(ContractVo contractVo) throws Exception;

    void saveContractArg(ContractVo contractVo) throws Exception;
}
