package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.RegistrationAccountChangeRecord;

import java.util.List;

/**
 * 门店收款账户变更记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
public interface IRegistrationAccountChangeRecordService extends IService<RegistrationAccountChangeRecord>
{
    /**
     * 查询门店收款账户变更记录
     * 
     * @param id 门店收款账户变更记录主键
     * @return 门店收款账户变更记录
     */
    public RegistrationAccountChangeRecord selectRegistrationAccountChangeRecordById(Long id);

    /**
     * 查询门店收款账户变更记录列表
     * 
     * @param registrationAccountChangeRecord 门店收款账户变更记录
     * @return 门店收款账户变更记录集合
     */
    public List<RegistrationAccountChangeRecord> selectRegistrationAccountChangeRecordList(RegistrationAccountChangeRecord registrationAccountChangeRecord);

    /**
     * 新增门店收款账户变更记录
     * 
     * @param registrationAccountChangeRecord 门店收款账户变更记录
     * @return 结果
     */
    public int insertRegistrationAccountChangeRecord(RegistrationAccountChangeRecord registrationAccountChangeRecord);

    /**
     * 修改门店收款账户变更记录
     * 
     * @param registrationAccountChangeRecord 门店收款账户变更记录
     * @return 结果
     */
    public int updateRegistrationAccountChangeRecord(RegistrationAccountChangeRecord registrationAccountChangeRecord);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(RegistrationAccountChangeRecord registrationAccountChangeRecord);

    /**
     * 批量删除门店收款账户变更记录
     * 
     * @param ids 需要删除的门店收款账户变更记录主键集合
     * @return 结果
     */
    public int deleteRegistrationAccountChangeRecordByIds(String ids);

    /**
     * 删除门店收款账户变更记录信息
     * 
     * @param id 门店收款账户变更记录主键
     * @return 结果
     */
    public int deleteRegistrationAccountChangeRecordById(Long id);
}
