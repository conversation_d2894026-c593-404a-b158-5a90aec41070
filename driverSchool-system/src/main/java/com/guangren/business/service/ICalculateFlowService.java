package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.CalculateFlow;

import java.util.List;

/**
 * 结息统计Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-17
 */
public interface ICalculateFlowService extends IService<CalculateFlow>
{
    /**
     * 查询结息统计
     * 
     * @param id 结息统计主键
     * @return 结息统计
     */
    public CalculateFlow selectCalculateFlowById(String id);

    /**
     * 查询结息统计列表
     * 
     * @param calculateFlow 结息统计
     * @return 结息统计集合
     */
    public List<CalculateFlow> selectCalculateFlowList(CalculateFlow calculateFlow);

    /**
     * 新增结息统计
     * 
     * @param calculateFlow 结息统计
     * @return 结果
     */
    public int insertCalculateFlow(CalculateFlow calculateFlow);

    /**
     * 修改结息统计
     * 
     * @param calculateFlow 结息统计
     * @return 结果
     */
    public int updateCalculateFlow(CalculateFlow calculateFlow);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(CalculateFlow calculateFlow);

    /**
     * 批量删除结息统计
     * 
     * @param ids 需要删除的结息统计主键集合
     */
    public void deleteCalculateFlowByIds(String ids);

    /**
     * 删除结息统计信息
     * 
     * @param id 结息统计主键
     * @return 结果
     */
    public int deleteCalculateFlowById(String id);
}
