package com.guangren.business.service;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.BasicArea;
import com.guangren.business.vo.AreaData;
import com.guangren.common.core.domain.Ztree;

/**
 * 标准行政区域Service接口
 * 
 * <AUTHOR>
 * @date 2023-02-15
 */
public interface IBasicAreaService extends IService<BasicArea>
{
    /**
     * 查询标准行政区域
     * 
     * @param id 标准行政区域主键
     * @return 标准行政区域
     */
    public BasicArea selectBasicAreaById(Long id);

    /**
     * 查询标准行政区域列表
     * 
     * @param basicArea 标准行政区域
     * @return 标准行政区域集合
     */
    public List<BasicArea> selectBasicAreaList(BasicArea basicArea);

    /**
     * 新增标准行政区域
     * 
     * @param basicArea 标准行政区域
     * @return 结果
     */
    public int insertBasicArea(BasicArea basicArea);

    /**
     * 修改标准行政区域
     * 
     * @param basicArea 标准行政区域
     * @return 结果
     */
    public int updateBasicArea(BasicArea basicArea);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(BasicArea basicArea);

    /**
     * 批量删除标准行政区域
     * 
     * @param ids 需要删除的标准行政区域主键集合
     * @return 结果
     */
    public int deleteBasicAreaByIds(String ids);

    /**
     * 删除标准行政区域信息
     * 
     * @param id 标准行政区域主键
     * @return 结果
     */
    public int deleteBasicAreaById(Long id);

    /**
     * 查询标准行政区域树列表
     * 
     * @return 所有标准行政区域信息
     */
    public List<Ztree> selectBasicAreaTree();

    public List<AreaData> areaData();

    /**
     * 根据经纬度获取所在街道/镇区信息
     * @param longitude
     * @param latitude
     * @return
     */
    public JSONObject getAreaDataByCoordinate(String longitude, String latitude);
}
