package com.guangren.business.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolBusinessLicense;

/**
 * 学校相关执照Service接口
 * 
 * <AUTHOR>
 * @date 2023-02-15
 */
public interface ISchoolBusinessLicenseService extends IService<SchoolBusinessLicense>
{
    /**
     * 查询学校相关执照
     * 
     * @param id 学校相关执照主键
     * @return 学校相关执照
     */
    public SchoolBusinessLicense selectSchoolBusinessLicenseById(String id);

    /**
     * 查询学校相关执照列表
     * 
     * @param schoolBusinessLicense 学校相关执照
     * @return 学校相关执照集合
     */
    public List<SchoolBusinessLicense> selectSchoolBusinessLicenseList(SchoolBusinessLicense schoolBusinessLicense);

    /**
     * 新增学校相关执照
     * 
     * @param schoolBusinessLicense 学校相关执照
     * @return 结果
     */
    public int insertSchoolBusinessLicense(SchoolBusinessLicense schoolBusinessLicense);

    /**
     * 修改学校相关执照
     * 
     * @param schoolBusinessLicense 学校相关执照
     * @return 结果
     */
    public int updateSchoolBusinessLicense(SchoolBusinessLicense schoolBusinessLicense);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SchoolBusinessLicense schoolBusinessLicense);

    /**
     * 批量删除学校相关执照
     * 
     * @param ids 需要删除的学校相关执照主键集合
     * @return 结果
     */
    public int deleteSchoolBusinessLicenseByIds(String ids);

    /**
     * 删除学校相关执照信息
     * 
     * @param id 学校相关执照主键
     * @return 结果
     */
    public int deleteSchoolBusinessLicenseById(String id);
}
