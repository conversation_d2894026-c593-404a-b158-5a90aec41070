package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolStudentContract;

import java.util.List;

/**
 * 学员合同相关Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-24
 */
public interface ISchoolStudentContractService extends IService<SchoolStudentContract>
{
    /**
     * 查询学员合同相关
     * 
     * @param studentId 学员合同相关主键
     * @return 学员合同相关
     */
    public SchoolStudentContract selectSchoolStudentContractByStudentId(String studentId);

    /**
     * 查询学员合同相关列表
     * 
     * @param schoolStudentContract 学员合同相关
     * @return 学员合同相关集合
     */
    public List<SchoolStudentContract> selectSchoolStudentContractList(SchoolStudentContract schoolStudentContract);

    /**
     * 新增学员合同相关
     * 
     * @param schoolStudentContract 学员合同相关
     * @return 结果
     */
    public int insertSchoolStudentContract(SchoolStudentContract schoolStudentContract);

    /**
     * 修改学员合同相关
     * 
     * @param schoolStudentContract 学员合同相关
     * @return 结果
     */
    public int updateSchoolStudentContract(SchoolStudentContract schoolStudentContract);

    /**
     * 批量删除学员合同相关
     * 
     * @param studentIds 需要删除的学员合同相关主键集合
     * @return 结果
     */
    public int deleteSchoolStudentContractByStudentIds(String studentIds);

    /**
     * 删除学员合同相关信息
     * 
     * @param studentId 学员合同相关主键
     * @return 结果
     */
    public int deleteSchoolStudentContractByStudentId(String studentId);
}
