package com.guangren.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guangren.business.domain.BankAccount;
import com.guangren.business.domain.ProvinceStudyTime;
import com.guangren.business.domain.ReleaseMoneyRecord;
import com.guangren.business.domain.School;
import com.guangren.business.domain.SchoolDealFlow;
import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.domain.SpecialSchool;
import com.guangren.business.domain.SuperviseFlow;
import com.guangren.business.domain.SupervisePay;
import com.guangren.business.domain.SuperviseReleaseRecord;
import com.guangren.business.domain.SuperviseReleaseValidList;
import com.guangren.business.mapper.SchoolMapper;
import com.guangren.business.mapper.SchoolStudentMapper;
import com.guangren.business.mapper.SupervisePayMapper;
import com.guangren.business.service.IProvinceStudyTimeService;
import com.guangren.business.service.IReleaseMoneyRecordService;
import com.guangren.business.service.ISchoolDealFlowService;
import com.guangren.business.service.ISchoolService;
import com.guangren.business.service.ISchoolStudentService;
import com.guangren.business.service.ISpecialSchoolService;
import com.guangren.business.service.ISuperviseFlowService;
import com.guangren.business.service.ISupervisePayService;
import com.guangren.business.service.ISuperviseReleaseRecordService;
import com.guangren.business.service.ISuperviseReleaseValidListService;
import com.guangren.business.vo.SchoolStudentImportVo;
import com.guangren.business.vo.SuperviseCountVo;
import com.guangren.common.constant.UserConstants;
import com.guangren.common.core.text.Convert;
import com.guangren.common.exception.BusinessException;
import com.guangren.common.utils.DateUtil;
import com.guangren.common.utils.ShiroUtils;
import com.guangren.common.utils.StringUtils;
import com.guangren.common.utils.bean.BeanUtils;
import com.guangren.common.utils.poi.ExcelUtil;
import com.guangren.system.service.ISysConfigService;
import com.guangren.system.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 学员交费总Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
@Service
public class SupervisePayServiceImpl extends ServiceImpl<SupervisePayMapper, SupervisePay> implements ISupervisePayService
{
    @Resource
    private SupervisePayMapper supervisePayMapper;
    @Resource
    private SchoolStudentMapper schoolStudentMapper;
    @Autowired
    private ISchoolStudentService schoolStudentService;
    
    @Resource
    private SchoolMapper schoolMapper;
    
    @Autowired
    private ISchoolService schoolService;
    
    
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private ISuperviseReleaseRecordService superviseReleaseRecordService;
    @Autowired
    private ISchoolDealFlowService schoolDealFlowService;
    @Autowired
    private ISuperviseFlowService superviseFlowService;
    @Autowired
    private IProvinceStudyTimeService provinceStudyTimeService;
    @Resource
    private IReleaseMoneyRecordService releaseMoneyRecordService;
    @Resource
    private ISuperviseReleaseValidListService superviseReleaseValidListService;

    @Autowired
    private ISysDictDataService sysDictDataService;

    @Autowired
    private ISpecialSchoolService specialSchoolService;



    
    

    /**
     * 查询学员交费总
     * 
     * @param studentId 学员交费总主键
     * @return 学员交费总
     */
    @Override
    public SupervisePay selectSupervisePayByStudentId(String studentId)
    {
        return supervisePayMapper.selectSupervisePayByStudentId(studentId);
    }

    /**
     * 查询学员交费总列表
     * 
     * @param supervisePay 学员交费总
     * @return 学员交费总
     */
    @Override
    public List<SupervisePay> selectSupervisePayList(SupervisePay supervisePay)
    {
        if(supervisePay!=null && StringUtils.isNotBlank(supervisePay.getIdentity())){
            List<String> identityList = Arrays.asList(supervisePay.getIdentity().split(" "));
            supervisePay.setIdentityList(identityList);
        }
        return supervisePayMapper.selectSupervisePayList(supervisePay);
    }

    /**
     * 导出学员交费总列表
     *
     * @param supervisePay 学员交费总
     * @return 学员交费总
     */
    @Override
    public List<SupervisePay> selectExportSupervisePayList(SupervisePay supervisePay)
    {
        return supervisePayMapper.selectExportSupervisePayList(supervisePay);
    }

    /**
     * 新增学员交费总
     * 
     * @param supervisePay 学员交费总
     * @return 结果
     */
    @Override
    public int insertSupervisePay(SupervisePay supervisePay)
    {
        return supervisePayMapper.insertSupervisePay(supervisePay);
    }

    /**
     * 修改学员交费总
     * 
     * @param supervisePay 学员交费总
     * @return 结果
     */
    @Override
    public int updateSupervisePay(SupervisePay supervisePay)
    {
        return supervisePayMapper.updateSupervisePay(supervisePay);
    }



    /**
     * 验证参数唯一性
     *
     * @param supervisePay 学员交费总
     * @return 学员交费总
     */
    @Override
    public String checkUnique(SupervisePay supervisePay)
    {
        return UserConstants.COMMOM_UNIQUE;
    }


    /**
     * 批量删除学员交费总
     * 
     * @param studentIds 需要删除的学员交费总主键
     * @return 结果
     */
    @Override
    public int deleteSupervisePayByStudentIds(String studentIds)
    {
        return supervisePayMapper.deleteSupervisePayByStudentIds(Convert.toStrArray(studentIds));
    }

    /**
     * 删除学员交费总信息
     * 
     * @param studentId 学员交费总主键
     * @return 结果
     */
    @Override
    public int deleteSupervisePayByStudentId(String studentId)
    {
        return supervisePayMapper.deleteSupervisePayByStudentId(studentId);
    }


    private boolean isAllowImport(SchoolStudent student){
        SpecialSchool specialSchool = specialSchoolService.getById(student.getSchoolId());
        if(specialSchool != null){
            return true;
        }

        String expireDate = sysConfigService.selectConfigByKey("import.pay.expire.date");
        if(StringUtils.isEmpty(expireDate)){
            expireDate = "2024-03-15 00:00:00";
        }
        Date expire = DateUtil.parseDate(expireDate,"yyyy-MM-dd HH:mm:ss");
        if(student.getPrepareRegisteDate().before(expire)){
            return true;
        }
        return false;
    }




    /**
     * 监管资金导入
     */
    @Override
    public Map<String, Object> importStudentPayData(MultipartFile excel,SupervisePay supervisePay) {
    	
    	Map<String, Object> map = new HashMap<String, Object>();
    	
        if (excel == null || excel.isEmpty()) {
        	return map;
        }
        
        try {
        	School school= schoolMapper.selectById(supervisePay.getSchoolId());
        	if(school == null){
        		throw new BusinessException("学校不存在");
        	}
        	if(school.getIsSupervise()==0) {
        		throw new BusinessException(school.getName()+"未设置资金监管，不能导入学员");
        	}
        	if (StringUtils.isEmpty(school.getBankAcctName())){
                throw new BusinessException(school.getName()+"监管资金释放账户名未设置");
            }
            if (StringUtils.isEmpty(school.getPayAcctNo())){
                throw new BusinessException(school.getName()+"监管资金释放账户号未设置");
            }
//            if (StringUtils.isEmpty(school.getBankName())){
//                throw new BusinessException(school.getName()+"监管资金释放开户行未设置");
//            }
            
            BankAccount account= new BankAccount();
            account.setBankAcctName(school.getBankAcctName());
            account.setPayAcctNo(school.getPayAcctNo());
            account.setBankName(school.getBankName());
            account.setSchoolId(supervisePay.getSchoolId());

            String studentSuperviseAmt = sysConfigService.selectConfigByKey("student.supervise.amt");
            if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isEmpty(studentSuperviseAmt)){
                throw new Exception("未设置每位学员应交监管资金");
            }
            BigDecimal amt = BigDecimal.ZERO;
            try{
                amt = new BigDecimal(studentSuperviseAmt);
            }catch(Exception ex){
                throw new Exception("设置的监管资金必须是数字");
            }
            if(amt.compareTo(BigDecimal.ZERO) <= 0){
                throw new Exception("监管资金金额必须大于0");
            }

            // 获取列表
            ExcelUtil<SchoolStudentImportVo> util = new ExcelUtil<SchoolStudentImportVo>(SchoolStudentImportVo.class);
            List<SchoolStudentImportVo> carList = util.importExcel(excel.getInputStream());
            if (StringUtils.isNull(carList) || carList.size() == 0) {
                throw new BusinessException("导入数据不能为空！");
            }
            // 进行遍历
            List<SchoolStudent> resultList = new ArrayList<SchoolStudent>();
            List<SchoolStudent> resultErrorList = new ArrayList<SchoolStudent>();

            for (int i = 0;i < carList.size(); i++) {
                int row = i + 1;
                String info = "第" + row + "条数据，";
                SchoolStudentImportVo studentInfo = carList.get(i);
                if (studentInfo == null) {
                    throw new BusinessException("导入数据不能为空！");
                }

                // 校验信息
                String name=studentInfo.getName();
                String identity= studentInfo.getIdentity();

                if (StringUtils.isEmpty(name)) {
                    throw new BusinessException(info +"学员姓名不能为空！");
                }
                if (StringUtils.isEmpty(identity)) {
                    throw new BusinessException(info +"身份证号不能为空！");
                }

                SchoolStudent querySchoolStudent= new SchoolStudent();
                querySchoolStudent.setName(name);
                querySchoolStudent.setIdentity(identity);
                querySchoolStudent.setIsQuit(0);
                SchoolStudent student=schoolStudentMapper.checkUnique(querySchoolStudent);
                if(student!= null){
                    if (StringUtils.isEmpty(supervisePay.getBranchId())){
                        SchoolStudent query1= new SchoolStudent();
                        query1.setName(name);
                        query1.setIdentity(identity);
                        query1.setSchoolId(supervisePay.getSchoolId());
                        query1.setIsQuit(0);
                        SchoolStudent student1=schoolStudentMapper.checkUnique(query1);
                        if (student1!= null){
                            boolean isAllowImport = isAllowImport(student1);
                            if(!isAllowImport){
                                student1.setRemark("该学员需通小程序支付");
                                resultErrorList.add(student1);
                                continue;
                            }
                            if (student1.getSuperviseFeeIsOk() ==1){
                                student1.setRemark("监管资金已到帐");
                                resultErrorList.add(student1);
                                continue;
                            }else if(student1.getIsSupervise() ==0){
                                student1.setRemark("监管状态未设置需要监管");
                                resultErrorList.add(student1);
                                continue;
                            }else if(student.getIsQuit()==1) {
                            	student1.setRemark("已退学");
                                resultErrorList.add(student1);
                                continue;
                            } else if (student1.getIsCheck() == 0){
                                student1.setRemark("未审核");
                                resultErrorList.add(student1);
                                continue;
                            }else{
                                student1.setSuperviseFee(amt);
                                resultList.add(student1);
                            }
                        }else{
                            student= new SchoolStudent();
                            student.setName(name);
                            student.setIdentity(identity);
                            student.setSchoolId(supervisePay.getSchoolId());
                            student.setRemark("选择的驾校信息未匹配上");
                            resultErrorList.add(student);
                        }
                    }else{
                        SchoolStudent query1= new SchoolStudent();
                        query1.setName(name);
                        query1.setIdentity(identity);
                        query1.setSchoolId(supervisePay.getSchoolId());
                        query1.setBranchId(supervisePay.getBranchId());
                        query1.setIsQuit(0);
                        SchoolStudent student1=schoolStudentMapper.checkUnique(query1);

                        if (student1!= null){
                            boolean isAllowImport = isAllowImport(student1);
                            if(!isAllowImport){
                                student1.setRemark("该学员需通小程序支付");
                                resultErrorList.add(student1);
                                continue;
                            }
                            if (student1.getSuperviseFeeIsOk() ==1){
                                student1.setRemark("监管资金已到帐");
                                resultErrorList.add(student1);
                                continue;
                            }else if(student1.getIsSupervise() ==0){
                                student1.setRemark("监管状态未设置需要监管");
                                resultErrorList.add(student1);
                                continue;
                            }else if(student.getIsQuit()==1) {
                            	student1.setRemark("已退学");
                                resultErrorList.add(student1);
                                continue;
                            }else if (student1.getIsCheck() == 0){
                                student1.setRemark("未审核");
                                resultErrorList.add(student1);
                                continue;
                            }else{
                                student1.setSuperviseFee(amt);
                                resultList.add(student1);
                            }
                        }else{
                            student= new SchoolStudent();
                            student.setName(name);
                            student.setIdentity(identity);
                            student.setSchoolId(supervisePay.getSchoolId());
                            student.setBranchId(supervisePay.getBranchId());
                            student.setRemark("选择的驾校、分校信息未匹配");
                            resultErrorList.add(student);
                        }
                    }
                }else{
                    SchoolStudent student1 = new SchoolStudent();
                    student1.setName(name);
                    student1.setIdentity(identity);
                    student1.setRemark("学员姓名或身份证号不存在，未匹配上");
                    resultErrorList.add(student1);
                }

            }
            //过滤重复数据
            List<SchoolStudent> uniqueList = resultList.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(SchoolStudent::getId))), ArrayList::new));

            StringBuffer msg=new StringBuffer();
            if (resultList.size()>0){
                msg.append("成功导入匹配学员，共" + uniqueList.size() + "条数据。"+"<br/>");
            }
            if (resultErrorList.size()>0){
                msg.append("失败导入未匹配学员，共" + resultErrorList.size() + "条数据。");
            }
            map.put("msg",msg);
            map.put("successStudentList",uniqueList);
            map.put("errorStudentList",resultErrorList);
            map.put("account",account);
            return map;
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 获取监管统计数据
     */
    @Override
    public SuperviseCountVo getSuperviseCount() {
        List<SupervisePay> supervisePays = supervisePayMapper.selectSupervisePayList(new SupervisePay());

        SuperviseCountVo superviseCountVo = new SuperviseCountVo();

        //总监管人数
        superviseCountVo.setSuperviseNumbers(supervisePays.size());

        List<String> studentIds = supervisePays.stream().map(SupervisePay::getStudentId).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(studentIds)) {
            //获取资金监管释放记录
            List<SuperviseReleaseRecord> releaseRecords =
                    superviseReleaseRecordService.list(new LambdaQueryWrapper<SuperviseReleaseRecord>()
                            .in(SuperviseReleaseRecord::getStudentId, studentIds));

            //总监管金额
            BigDecimal superviseFee = supervisePays.stream().map(SupervisePay::getSuperviseFee).reduce(BigDecimal.ZERO, BigDecimal::add);

            //总释放金额
            List<SuperviseReleaseRecord> isSuccessRecord = releaseRecords.stream().filter(val -> val.getIsSuccess() == 1).collect(Collectors.toList());
            BigDecimal releaseFee = isSuccessRecord.stream().map(SuperviseReleaseRecord::getReleaseFee).reduce(BigDecimal.ZERO, BigDecimal::add);

            //总待释放金额
            BigDecimal restSuperviseFee = superviseFee.subtract(releaseFee);

            //科目一统计数据
            List<SuperviseReleaseRecord> subject1Records = releaseRecords.stream().filter(val -> val.getSubjectName().equals("科目1")).collect(Collectors.toList());
            BigDecimal subject1SuperviseFee = subject1Records.stream().map(SuperviseReleaseRecord::getSuperviseFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal subject1AmountRelease = subject1Records.stream().map(SuperviseReleaseRecord::getReleaseFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal subject1RestSuperviseFee = subject1SuperviseFee.subtract(subject1AmountRelease);

            //科目二统计数据
            List<SuperviseReleaseRecord> subject2Records = releaseRecords.stream().filter(val -> val.getSubjectName().equals("科目2")).collect(Collectors.toList());
            BigDecimal subject2SuperviseFee = subject2Records.stream().map(SuperviseReleaseRecord::getSuperviseFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal subject2AmountRelease = subject2Records.stream().map(SuperviseReleaseRecord::getReleaseFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal subject2RestSuperviseFee = subject2SuperviseFee.subtract(subject2AmountRelease);

            //科目三统计数据
            List<SuperviseReleaseRecord> subject3Records = releaseRecords.stream().filter(val -> val.getSubjectName().equals("科目3")).collect(Collectors.toList());
            BigDecimal subject3SuperviseFee = subject3Records.stream().map(SuperviseReleaseRecord::getSuperviseFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal subject3AmountRelease = subject3Records.stream().map(SuperviseReleaseRecord::getReleaseFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal subject3RestSuperviseFee = subject3SuperviseFee.subtract(subject3AmountRelease);

            //科目四统计数据
            List<SuperviseReleaseRecord> subject4Records = releaseRecords.stream().filter(val -> val.getSubjectName().equals("科目4")).collect(Collectors.toList());
            BigDecimal subject4SuperviseFee = subject4Records.stream().map(SuperviseReleaseRecord::getSuperviseFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal subject4AmountRelease = subject4Records.stream().map(SuperviseReleaseRecord::getReleaseFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal subject4RestSuperviseFee = subject4SuperviseFee.subtract(subject4AmountRelease);

            superviseCountVo.setSuperviseFee(superviseFee);
            superviseCountVo.setReleaseFee(releaseFee);
            superviseCountVo.setRestSuperviseFee(restSuperviseFee);
            superviseCountVo.setSubject1SuperviseFee(subject1SuperviseFee);
            superviseCountVo.setSubject1AmountRelease(subject1AmountRelease);
            superviseCountVo.setSubject1RestSuperviseFee(subject1RestSuperviseFee);
            superviseCountVo.setSubject2SuperviseFee(subject2SuperviseFee);
            superviseCountVo.setSubject2AmountRelease(subject2AmountRelease);
            superviseCountVo.setSubject2RestSuperviseFee(subject2RestSuperviseFee);
            superviseCountVo.setSubject3SuperviseFee(subject3SuperviseFee);
            superviseCountVo.setSubject3AmountRelease(subject3AmountRelease);
            superviseCountVo.setSubject3RestSuperviseFee(subject3RestSuperviseFee);
            superviseCountVo.setSubject4SuperviseFee(subject4SuperviseFee);
            superviseCountVo.setSubject4AmountRelease(subject4AmountRelease);
            superviseCountVo.setSubject4RestSuperviseFee(subject4RestSuperviseFee);
        }else {
            BigDecimal zero = new BigDecimal(BigInteger.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP);
            superviseCountVo.setSuperviseFee(zero);
            superviseCountVo.setReleaseFee(zero);
            superviseCountVo.setRestSuperviseFee(zero);
            superviseCountVo.setSubject1SuperviseFee(zero);
            superviseCountVo.setSubject1AmountRelease(zero);
            superviseCountVo.setSubject1RestSuperviseFee(zero);
            superviseCountVo.setSubject2SuperviseFee(zero);
            superviseCountVo.setSubject2AmountRelease(zero);
            superviseCountVo.setSubject2RestSuperviseFee(zero);
            superviseCountVo.setSubject3SuperviseFee(zero);
            superviseCountVo.setSubject3AmountRelease(zero);
            superviseCountVo.setSubject3RestSuperviseFee(zero);
            superviseCountVo.setSubject4SuperviseFee(zero);
            superviseCountVo.setSubject4AmountRelease(zero);
            superviseCountVo.setSubject4RestSuperviseFee(zero);
        }

        return superviseCountVo;
    }

    /**
     * 导出释放记录列表
     */
    @Override
    public List<SupervisePay> exportSupervisePayList(SupervisePay supervisePay) {
        return supervisePayMapper.exportSupervisePayList(supervisePay);
    }

    /**
     * 释放成功操作
     * @param releaseType 释入类型，1-自动释放，2-手动释放
     * */
    @Override
    @Transactional(rollbackFor = Exception.class,isolation = Isolation.READ_COMMITTED)
    public void withdrawSuccess(String studentId,String releaseAccountName,String releaseAccountNo,
    		Integer trainphase,BigDecimal withdrawAmount,
    		Integer releaseType,String releaseOrderNo,String bankOrderNo) throws Exception {

    	SchoolStudent student = schoolStudentService.getById(studentId);
        if(student !=null && student.getResidueSuperviseAmt().compareTo(withdrawAmount)<0){
            throw new BusinessException("释放金额不足");
        }

        String subjectName = sysDictDataService.selectDictLabel("study_stage",String.valueOf(trainphase));

        // 修改监管记录表
        this.update(new LambdaUpdateWrapper<SupervisePay>()
                .eq(SupervisePay::getStudentId,student.getId())
                .set(SupervisePay::getReleaseAccountNo,releaseAccountNo)
                .set(SupervisePay::getReleaseAccountName,releaseAccountName)
                .setSql("`rest_supervise_fee`=`rest_supervise_fee`-"+withdrawAmount)
                .setSql("`total_release_fee`=`total_release_fee`+"+withdrawAmount));

        provinceStudyTimeService.update(new LambdaUpdateWrapper<ProvinceStudyTime>()
                .eq(ProvinceStudyTime::getName,student.getName())
                .eq(ProvinceStudyTime::getIdcard,student.getIdentity())
                .eq(ProvinceStudyTime::getTrainphase,trainphase)
                .eq(ProvinceStudyTime::getTraintype,student.getLicenseType())
                .set(ProvinceStudyTime::getIsRelease,1)
                .set(ProvinceStudyTime::getReleaseType,releaseType));

        //修改学生余额
        schoolStudentService.update(new LambdaUpdateWrapper<SchoolStudent>()
                .eq(SchoolStudent::getId,student.getId())
                .setSql("`residue_supervise_amt`=`residue_supervise_amt`-"+withdrawAmount));

        // 减去驾校总监管金额并更新
        schoolService.update(new LambdaUpdateWrapper<School>()
                .eq(School::getId,student.getSchoolId())
                .setSql("`supervise_amt`=`supervise_amt`-"+withdrawAmount));

        superviseReleaseValidListService.update(new LambdaUpdateWrapper<SuperviseReleaseValidList>()
                .eq(SuperviseReleaseValidList::getStudentId,student.getId())
                .eq(SuperviseReleaseValidList::getSubjectName,trainphase)
                .set(SuperviseReleaseValidList::getReleaseStatus,1)
                .set(SuperviseReleaseValidList::getReleaseBankNo,bankOrderNo)
                .set(SuperviseReleaseValidList::getReleaseTime,new Date())
                .set(SuperviseReleaseValidList::getReleaseOrderNo,releaseOrderNo)
                .set(SuperviseReleaseValidList::getReleaseOperator,
                        ShiroUtils.getSysOrganUser()==null?"":ShiroUtils.getSysOrganUser().getUsername())
                .set(SuperviseReleaseValidList::getFailReason,""));

        // 保存驾校交易流水
        SchoolDealFlow schoolDealFlowLastRecord = schoolDealFlowService.getOne(new LambdaQueryWrapper<SchoolDealFlow>()
                .eq(SchoolDealFlow::getSchoolId, student.getSchoolId())
                .orderByDesc(SchoolDealFlow::getId).last("limit 1"));
        SchoolDealFlow schoolDealFlow = new SchoolDealFlow();
        schoolDealFlow.setSchoolId(student.getSchoolId());
        schoolDealFlow.setAction(0);
        schoolDealFlow.setLastBalance(schoolDealFlowLastRecord==null ? BigDecimal.ZERO :schoolDealFlowLastRecord.getTheBalance());
        schoolDealFlow.setTheAmt(BigDecimal.ZERO.subtract(withdrawAmount));
        schoolDealFlow.setTheBalance(schoolDealFlow.getLastBalance().add(schoolDealFlow.getTheAmt()));
        schoolDealFlow.setStudentId(student.getId());
        schoolDealFlow.setBankOrderId(bankOrderNo);
        schoolDealFlow.setRelationSubject(subjectName);
        schoolDealFlowService.save(schoolDealFlow);

        // 保存监管交易流水
        SuperviseFlow superviseFlowLastRecord = superviseFlowService.getOne(new LambdaQueryWrapper<SuperviseFlow>()
                .orderByDesc(SuperviseFlow::getId).last("limit 1"));
        SuperviseFlow superviseFlow = new SuperviseFlow();
        superviseFlow.setSchoolId(student.getSchoolId());
        superviseFlow.setAction(0);
        superviseFlow.setLastBalance(superviseFlowLastRecord==null ? BigDecimal.ZERO : superviseFlowLastRecord.getTheBalance());
        superviseFlow.setTheAmt(BigDecimal.ZERO.subtract(withdrawAmount));
        superviseFlow.setTheBalance(superviseFlow.getLastBalance().add(superviseFlow.getTheAmt()));
        superviseFlow.setStudentId(student.getId());
        superviseFlow.setRelationSubject(subjectName);
        superviseFlowService.save(superviseFlow);
    }

    @Transactional(rollbackFor = Exception.class,isolation = Isolation.READ_COMMITTED)
    @Override
    public void withdrawFail(String studentId, String releaseAccountName, String releaseAccountNo,
                             Integer trainphase, BigDecimal withdrawAmount, Integer releaseType,
                             String releaseOrderNo,String bankOrderNo,String errMsg) throws Exception {

        SchoolStudent student = schoolStudentService.getById(studentId);
        if(student == null){
            throw new BusinessException("学员不存在");
        }
        String subjectName = sysDictDataService.selectDictLabel("study_stage",String.valueOf(trainphase));


        this.update(new LambdaUpdateWrapper<SupervisePay>()
                .eq(SupervisePay::getStudentId,studentId)
                .set(SupervisePay::getReleaseAccountName,"")
                .set(SupervisePay::getReleaseAccountNo,"")
                .set(SupervisePay::getReleaseBankName,"")
                .set(SupervisePay::getUpdatedTime,new Date())
                .setSql("`rest_supervise_fee`=`rest_supervise_fee`+"+withdrawAmount)
                .setSql("`total_release_fee`=`total_release_fee`-"+withdrawAmount));

        LambdaUpdateWrapper<SchoolStudent> updateWrapper = new LambdaUpdateWrapper<SchoolStudent>()
                .eq(SchoolStudent::getId,studentId)
                .setSql("`residue_supervise_amt`=`residue_supervise_amt`+"+withdrawAmount);
        if(trainphase != null && trainphase==5) {
            updateWrapper.set(SchoolStudent::getQuitIsSyn,0);
        }
        schoolStudentService.update(updateWrapper);


        schoolService.update(new LambdaUpdateWrapper<School>()
                .eq(School::getId,student.getSchoolId())
                .setSql("`supervise_amt`=`supervise_amt`+"+withdrawAmount));

        superviseReleaseValidListService.update(new LambdaUpdateWrapper<SuperviseReleaseValidList>()
                .set(SuperviseReleaseValidList::getReleaseStatus,2)
                .set(SuperviseReleaseValidList::getFailReason,errMsg)
                .eq(SuperviseReleaseValidList::getStudentId,studentId)
                .eq(SuperviseReleaseValidList::getSubjectName,trainphase));


        provinceStudyTimeService.update(new LambdaUpdateWrapper<ProvinceStudyTime>()
                .set(ProvinceStudyTime::getIsRelease, 0)
                .eq(ProvinceStudyTime::getName, student.getName())
                .eq(ProvinceStudyTime::getIdcard, student.getIdentity())
                .eq(ProvinceStudyTime::getTraintype, student.getLicenseType())
                .eq(ProvinceStudyTime::getTrainphase, trainphase));

        // 保存驾校交易流水
        SchoolDealFlow SchoolDealFlowLastRecord = schoolDealFlowService.getOne(new QueryWrapper<SchoolDealFlow>().eq("school_id", student.getSchoolId()).orderByDesc("id").last("limit 1"));
        SchoolDealFlow schoolDealFlow = new SchoolDealFlow();
        schoolDealFlow.setSchoolId(student.getSchoolId());
        schoolDealFlow.setAction(2);
        schoolDealFlow.setLastBalance(SchoolDealFlowLastRecord==null ? BigDecimal.ZERO: SchoolDealFlowLastRecord.getTheBalance());
        schoolDealFlow.setTheAmt(withdrawAmount);
        schoolDealFlow.setTheBalance(schoolDealFlow.getLastBalance().add(schoolDealFlow.getTheAmt()));
        schoolDealFlow.setBankOrderId(bankOrderNo);
        schoolDealFlow.setStudentId(studentId);
        schoolDealFlow.setRelationSubject(subjectName);
        schoolDealFlowService.save(schoolDealFlow);

        SuperviseFlow SuperviseFlowLastRecord = superviseFlowService.getOne(new QueryWrapper<SuperviseFlow>().orderByDesc("id").last("limit 1"));
        SuperviseFlow superviseFlow = new SuperviseFlow();
        superviseFlow.setSchoolId(student.getSchoolId());
        superviseFlow.setAction(2);
        superviseFlow.setLastBalance(SuperviseFlowLastRecord==null ? BigDecimal.ZERO: SuperviseFlowLastRecord.getTheBalance());
        superviseFlow.setTheAmt(withdrawAmount);
        superviseFlow.setTheBalance(superviseFlow.getLastBalance().add(superviseFlow.getTheAmt()));
        superviseFlow.setStudentId(student.getId());
        superviseFlow.setRelationSubject(subjectName);
        superviseFlowService.save(superviseFlow);
    }


    /**
     * 释放资金申请保存
     * <AUTHOR>
     * @date 2023/12/8 16:55
     * @param supervisePay  *
     */
    @Override
    public void releaseMoneyApplySave(SupervisePay supervisePay) {

        SupervisePay supervisePayByDb = supervisePayMapper.selectById(supervisePay.getStudentId());
        ReleaseMoneyRecord releaseMoneyRecord = new ReleaseMoneyRecord();
        BeanUtils.copyBeanProp(releaseMoneyRecord, supervisePay);
        SchoolStudent schoolStudent = schoolStudentService.selectSchoolStudentById(releaseMoneyRecord.getStudentId());
        if(schoolStudent == null) {
        	throw  new BusinessException("不存在的学员");
        }

        releaseMoneyRecord.setSchoolName(schoolStudent.getSchool()==null?"":schoolStudent.getSchool().getName());
        releaseMoneyRecord.setBranchName(schoolStudent.getBranch()==null?"":schoolStudent.getBranch().getName());
        releaseMoneyRecord.setRegistrationName(schoolStudent.getRegistration()==null?"":schoolStudent.getRegistration().getName());
        releaseMoneyRecord.setStudentName(schoolStudent.getName());
        releaseMoneyRecord.setSchoolId(schoolStudent.getSchoolId());
        releaseMoneyRecord.setBranchId(schoolStudent.getBranchId());
        releaseMoneyRecord.setRegistrationId(schoolStudent.getRegistrationId());
        releaseMoneyRecord.setSubjectName(Integer.valueOf(supervisePay.getSubjectName()));
        releaseMoneyRecord.setIdentity(schoolStudent.getIdentity());
        if(releaseMoneyRecord.getSubjectName() != 0){
            if(releaseMoneyRecord.getSubjectName() == 5){
                releaseMoneyRecord.setReleaseMoney(supervisePayByDb.getRestSuperviseFee());
            }else{
                String amtStr = sysConfigService.selectConfigByKey("subject"+releaseMoneyRecord.getSubjectName()+".release.amt");
                if(StringUtils.isEmpty(amtStr)){
                    throw  new BusinessException("未设置释放资金，释放资金申请提交失败");
                }
                BigDecimal subjectRelease = new BigDecimal(amtStr);
                if(schoolStudent.getReleaseVersion()==0) {
                    String commissionRateStr = sysConfigService.selectConfigByKey("supervise.import.commission");
                    if(StringUtils.isEmpty(commissionRateStr)){
                        throw new BusinessException("未设置平台手续费, 暂停释放");
                    }
                    BigDecimal commissionRate = new BigDecimal(commissionRateStr);
                    subjectRelease = subjectRelease.multiply(new BigDecimal(1).subtract(commissionRate));
                }else if(schoolStudent.getReleaseVersion()==2){
                    if (releaseMoneyRecord.getSubjectName() == 3) {
                        subjectRelease = schoolStudent.getResidueSuperviseAmt();
                        BigDecimal srcTrainphase2ReleaseAmt = new BigDecimal(sysConfigService.selectConfigByKey("subject2.release.amt"));
                        if(schoolStudent.getResidueSuperviseAmt().compareTo(srcTrainphase2ReleaseAmt)>0){
                            subjectRelease = schoolStudent.getResidueSuperviseAmt().subtract(srcTrainphase2ReleaseAmt);
                        }
                    }
                }
                releaseMoneyRecord.setReleaseMoney(subjectRelease);
            }
        }
        releaseMoneyRecord.setApplyTime(new Date());
        releaseMoneyRecord.setCheckType(0);
        releaseMoneyRecordService.insertReleaseMoneyRecord(releaseMoneyRecord);
    }
}
