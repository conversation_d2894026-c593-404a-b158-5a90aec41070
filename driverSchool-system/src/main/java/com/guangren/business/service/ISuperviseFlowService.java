package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.School;
import com.guangren.business.domain.SuperviseFlow;

import java.math.BigDecimal;
import java.util.List;

/**
 * 监管账户流水Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-03
 */
public interface ISuperviseFlowService extends IService<SuperviseFlow>
{
    /**
     * 查询监管账户流水
     * 
     * @param id 监管账户流水主键
     * @return 监管账户流水
     */
    public SuperviseFlow selectSuperviseFlowById(String id);

    /**
     * 查询监管账户流水列表
     * 
     * @param superviseFlow 监管账户流水
     * @return 监管账户流水集合
     */
    public List<SuperviseFlow> selectSuperviseFlowList(SuperviseFlow superviseFlow);

    /**
     * 新增监管账户流水
     * 
     * @param superviseFlow 监管账户流水
     * @return 结果
     */
    public int insertSuperviseFlow(SuperviseFlow superviseFlow);

    /**
     * 修改监管账户流水
     * 
     * @param superviseFlow 监管账户流水
     * @return 结果
     */
    public int updateSuperviseFlow(SuperviseFlow superviseFlow);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SuperviseFlow superviseFlow);

    /**
     * 批量删除监管账户流水
     * 
     * @param ids 需要删除的监管账户流水主键集合
     * @return 结果
     */
    public int deleteSuperviseFlowByIds(String ids);

    /**
     * 删除监管账户流水信息
     * 
     * @param id 监管账户流水主键
     * @return 结果
     */
    public int deleteSuperviseFlowById(String id);
    
    public void synData();


    /**
     * 驾校结息提交结算后的保存
     * <AUTHOR>
     * @date 2023/11/18 15:13
     * @param school 驾校
     * @param withdrawAmount 交易金额
     */
    void submitCalculateBySave(School school, BigDecimal withdrawAmount);
}
