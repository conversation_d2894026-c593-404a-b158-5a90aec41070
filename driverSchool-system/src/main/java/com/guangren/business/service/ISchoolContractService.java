package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolContract;

import java.util.List;

/**
 * 驾校合同Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-24
 */
public interface ISchoolContractService extends IService<SchoolContract>
{
    /**
     * 查询驾校合同
     * 
     * @param schoolId 驾校合同主键
     * @return 驾校合同
     */
    public SchoolContract selectSchoolContractBySchoolId(String schoolId);

    /**
     * 查询驾校合同列表
     * 
     * @param schoolContract 驾校合同
     * @return 驾校合同集合
     */
    public List<SchoolContract> selectSchoolContractList(SchoolContract schoolContract);

    /**
     * 新增驾校合同
     * 
     * @param schoolContract 驾校合同
     * @return 结果
     */
    public int insertSchoolContract(SchoolContract schoolContract);

    /**
     * 修改驾校合同
     * 
     * @param schoolContract 驾校合同
     * @return 结果
     */
    public int updateSchoolContract(SchoolContract schoolContract);

    /**
     * 批量删除驾校合同
     * 
     * @param schoolIds 需要删除的驾校合同主键集合
     * @return 结果
     */
    public int deleteSchoolContractBySchoolIds(String schoolIds);

    /**
     * 删除驾校合同信息
     * 
     * @param schoolId 驾校合同主键
     * @return 结果
     */
    public int deleteSchoolContractBySchoolId(String schoolId);
}
