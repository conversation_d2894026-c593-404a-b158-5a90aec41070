package com.guangren.business.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

import javax.annotation.Resource;

import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guangren.business.domain.*;
import org.apache.commons.lang.RandomStringUtils;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guangren.business.config.UnionBankConfiguration;
import com.guangren.business.enumration.BankTransactionRecordType;
import com.guangren.business.mapper.SchoolStudentMapper;
import com.guangren.business.mapper.SuperviseBatchSubmitDetailMapper;
import com.guangren.business.mapper.SuperviseBatchSubmitMapper;
import com.guangren.business.service.IBankService;
import com.guangren.business.service.IBankTransactionRecordService;
import com.guangren.business.service.ISpecialSchoolService;
import com.guangren.business.service.ISchoolDealFlowService;
import com.guangren.business.service.ISuperviseBatchSubmitService;
import com.guangren.business.service.ISuperviseFlowService;
import com.guangren.business.service.ISupervisePayService;
import com.guangren.business.vo.ShowPayInfoVo;
import com.guangren.business.vo.SuperviseBatchSubmitVo;
import com.guangren.business.vo.UnionNotifyVo;
import com.guangren.business.vo.UnionScanCodeNotifyVo;
import com.guangren.common.exception.BusinessException;
import com.guangren.common.utils.DateUtil;
import com.guangren.common.utils.StringUtils;
import com.guangren.system.domain.SysConfig;
import com.guangren.system.service.ISysConfigService;

import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;

/**
 * 收款账户Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-03
 */
@Slf4j
@Service
public class SuperviseBatchSubmitServiceImpl extends ServiceImpl<SuperviseBatchSubmitMapper, SuperviseBatchSubmit> implements ISuperviseBatchSubmitService {
    
	@Autowired
    private UnionBankConfiguration unionBankConfiguration;
    
    
    @Resource
    private SuperviseBatchSubmitMapper superviseBatchSubmitMapper;
    @Resource
    private SuperviseBatchSubmitDetailMapper superviseBatchSubmitDetailMapper;
    @Resource
    private SchoolStudentMapper schoolStudentMapper;
    @Resource
    private IBankService bankService;
    @Autowired
    UnionBankService unionBankService;
    @Autowired
    SuperviseBatchSubmitDetailServiceImpl superviseBatchSubmitDetailService;
    @Autowired
    SchoolServiceImpl schoolService;
    @Autowired
    SchoolStudentServiceImpl schoolStudentService;
    @Autowired
    private ISupervisePayService supervisePayService;
    @Autowired
    IBankTransactionRecordService bankTransactionRecordService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private ISchoolDealFlowService schoolDealFlowService;
    @Resource
    private ISuperviseFlowService superviseFlowService;
    
    private ReentrantLock lock = new ReentrantLock();
    @Resource
    private ISpecialSchoolService specialSchoolService;

    /**
     * 新增批量提交
     *
     * @param superviseBatchSubmit 批量提交
     * @return 结果
     */
    @Override
    public int insertSuperviseBatchSubmit(SuperviseBatchSubmit superviseBatchSubmit) {
        return superviseBatchSubmitMapper.insertSuperviseBatchSubmit(superviseBatchSubmit);
    }


    /**
     * 查询批量提交
     *
     * @param orderId 批量提交主键
     * @return 批量提交
     */
    @Override
    public SuperviseBatchSubmit selectSuperviseBatchSubmitByOrderId(String orderId) {
        return superviseBatchSubmitMapper.selectSuperviseBatchSubmitByOrderId(orderId);
    }

    /**
     * 查询批量提交列表
     *
     * @param superviseBatchSubmit 批量提交
     * @return 批量提交
     */
    @Override
    public List<SuperviseBatchSubmit> selectSuperviseBatchSubmitList(SuperviseBatchSubmit superviseBatchSubmit) {
        return superviseBatchSubmitMapper.selectSuperviseBatchSubmitList(superviseBatchSubmit);
    }

    /**
     * 测试银联支付流程
     *
     * @param superviseBatchSubmitVo
     * @return
     */
     //@Override
    /*public boolean testUnionPay(SuperviseBatchSubmitVo superviseBatchSubmitVo) {
        long amt;
        try {
            amt = unionBankService.checkParams(superviseBatchSubmitVo.getMid(),
                    superviseBatchSubmitVo.getTid(),
                    superviseBatchSubmitVo.getBankAcctName(),
                    superviseBatchSubmitVo.getPayAcctNo(),
                    superviseBatchSubmitVo.getBankName());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String[] studentIdArray = Convert.toStrArray(superviseBatchSubmitVo.getStudentIds());
        // 预设的驾协监管银联商户号
        SysConfig superviseMid = sysConfigService.getOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "supervise.unionPay.mid"));
        // 订单号
        String orderId = unionBankConfiguration.getSystemNum()+"-" + DateUtil.formatDate(new Date(), "yyyyMMddHHmmssSSS") + RandomStringUtils.randomNumeric(7);
        // 子订单号
        String subOrderId = unionBankConfiguration.getSystemNum() + "-SUB-" + DateUtil.formatDate(new Date(), "yyyyMMddHHmmssSSS") + RandomStringUtils.randomNumeric(7);
        // 总金额
        float totalAmt = studentIdArray.length * amt;
        // 获取商户的mid和tid
        School school = schoolService.getById(superviseBatchSubmitVo.getSchoolId());
        JSONObject attachedData = new JSONObject();
        attachedData.put("studentIds", studentIdArray);
        attachedData.put("schoolId", superviseBatchSubmitVo.getSchoolId());
        //attachedData.put("branchId", superviseBatchSubmitVo.getBranchId());
        // 测试1分钱
        log.warn("当前为支付测试环境下");
        String unionPayUrl = unionBankService.getUnionPayUrl(orderId,
                subOrderId,
                BigDecimal.valueOf(1),
                school.getMid(),
                school.getTid(),
                attachedData,
                0,
                BigDecimal.valueOf(0),
                BigDecimal.valueOf(0),
                superviseMid.getConfigValue());
        log.info("产生测试的银联支付接口 ==> {}", unionPayUrl);
        JSONObject extendData = new JSONObject();
        extendData.put("url", unionPayUrl);
        extendData.put("totalAmt", totalAmt);
        extendData.put("orderId", orderId);
        extendData.put("totalAmount", 1L);
        extendData.put("mid", school.getMid());
        extendData.put("tid", school.getTid());
        extendData.put("attachedData", attachedData);

        int submitRow = unionPayAfter(studentIdArray,
                orderId, amt, totalAmt, superviseBatchSubmitVo.getSchoolId(),
                superviseBatchSubmitVo.getBranchId(),
                new BankTransactionRecord().setExtendData(JSONObject.toJSONString(extendData)),
                subOrderId, BigDecimal.valueOf(0));
        log.info("superviseBatchSubmit row:{}", submitRow);
        // 模拟成功回调
        UnionNotifyVo unionNotifyVo = new UnionNotifyVo().setBankCardNo("XXXXXXXXXX")
                .setBankInfo("中国xx银行股份有限公司")
                .setBuyerUsername("BuyerUsername科技有限公司")
                .setConnectSys("UNIONPAY_B")
                .setCreateTime(DateUtil.getDate())
                .setInstMid("XXXXXInstMid")
                .setMchntUuid(UUID.randomUUID().toString().replace("-", ""))
                .setMerOrderId(orderId)
                .setMid(school.getMid())
                .setNotifyId(UUID.randomUUID().toString())
                .setOrderDesc("OrderDesc科技有限公司")
                .setPayTime(DateUtil.getDate())
                .setSeqId("XXXXXXXXSeqId")
                .setSettleDate(DateUtil.getDate("yyyy-MM-dd"))
                .setSign("XXXXXXXXXXXXXXXXXXXXXXXXXXXXX")
                .setStatus("TRADE_SUCCESS")
                .setSubInst("XXXXXX")
                .setTargetOrderId("XXXXX35HTXXXXX")
                .setTargetSys("UPG")
                .setTid(school.getTid())
                .setTotalAmount((long) totalAmt * 100);

        return unionPayNotify(unionNotifyVo);
    }*/
    
    public String specialSchoolUnionBatchSubmit(SuperviseBatchSubmitVo superviseBatchSubmitVo,SpecialSchool specialSchool,School school) {
    	
        if(StringUtil.isEmpty(specialSchool.getEnterpriseMid())){
        	throw new BusinessException(school.getName()+"公对公主商户号未设置");
        }
        String b2bmid = specialSchool.getEnterpriseMid();

        if(StringUtils.isEmpty(specialSchool.getEnterpriseTid())){
        	throw new BusinessException(school.getName()+"公对公终端号未设置");
        }
        String b2btid = specialSchool.getEnterpriseTid();
        
        if(StringUtils.isEmpty(specialSchool.getPersonMid())){
        	throw new BusinessException(school.getName()+"公对私主商户号未设置");
        }
        String b2cmid = specialSchool.getPersonMid();
                
        if(StringUtils.isEmpty(specialSchool.getPersonTid())){
        	throw new BusinessException(school.getName()+"公对私终端号未设置");
        }
        
        String b2ctid = specialSchool.getPersonTid();
        
        if(StringUtils.isEmpty(specialSchool.getQrcodeMid())){
        	throw new BusinessException(school.getName()+"扫码主商户号未设置");
        }
        String scancodemid = specialSchool.getQrcodeMid();
        
        if(StringUtils.isEmpty(specialSchool.getQrcodeTid())){
        	throw new BusinessException(school.getName()+"扫码私终端号未设置");
        }
        String scancodetid = specialSchool.getQrcodeTid();

        SysConfig sysConfig = sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "student.supervise.amt").last("limit 1"));
        if(sysConfig == null || StringUtils.isEmpty(sysConfig.getConfigValue())){
        	throw new BusinessException("未设置每位学员应交监管资金");
        }
        String studentSuperviseAmt = sysConfig.getConfigValue();
        
        BigDecimal studentAmt = BigDecimal.ZERO;
        try {
        	studentAmt = new BigDecimal(studentSuperviseAmt);
        } catch (Exception ex) {
            throw new BusinessException("设置的监管资金必须是数字");
        }
        if (studentAmt.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("监管资金金额必须大于0");
        }
        
        
        //手续费
        BigDecimal commissionRate = BigDecimal.ZERO;
        sysConfig = sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "supervise.import.commission"));
        if(sysConfig != null && StringUtils.isNotEmpty(sysConfig.getConfigValue())){
        	commissionRate = new BigDecimal(sysConfig.getConfigValue());
        }
                
        String[] studentIdArray = Convert.toStrArray(superviseBatchSubmitVo.getStudentIds());
        // 订单号
        String orderId = specialSchool.getAccessSource()+DateUtil.formatDate(new Date(), "yyyyMMddHHmmssSSS") + RandomStringUtils.randomNumeric(7);
        // 子订单号
        String subOrderId = specialSchool.getAccessSource() + "SUB" + DateUtil.formatDate(new Date(), "yyyyMMddHHmmssSSS") + RandomStringUtils.randomNumeric(7);
        // 总监管金额
        BigDecimal entryAmt = studentAmt.multiply(new BigDecimal(studentIdArray.length)).setScale(2, BigDecimal.ROUND_HALF_UP);
        
        //应收手续费：监管金额 * 手续费率
        BigDecimal commission = entryAmt.multiply(commissionRate).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal studentCommission = studentAmt.multiply(commissionRate).setScale(2, BigDecimal.ROUND_HALF_UP);
        
        
        SysConfig commissionConfig =  sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key","supervise.commission.type"));
        //0-支付整额方式，1-入账整额方式
        if(commissionConfig != null && commissionConfig.getConfigValue().equals("1")) {
        	entryAmt = entryAmt.subtract(commission).setScale(2, BigDecimal.ROUND_HALF_UP);
        	studentAmt = studentAmt.subtract(studentCommission).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        
        if(commission.compareTo(new BigDecimal(20))<=0 && superviseBatchSubmitVo.getTransType()==1) {
        	throw new BusinessException("手续费小于等于20元请选择个人网银交费");
        }
        
        //合计应收金额：监管金额 + 手续费
        BigDecimal totalAmt = entryAmt.add(commission).setScale(2, BigDecimal.ROUND_HALF_UP);

        // 获取商户的mid和tid
        String unionPayUrl = null;
        //个人网银
        if(superviseBatchSubmitVo.getTransType()==0){
        	unionPayUrl = unionBankService.getSpecialSchoolUnionPayUrl(orderId,
                    subOrderId,
                    totalAmt.multiply(BigDecimal.valueOf(100)).longValue(),
                    b2cmid,
                    b2ctid,
                    orderId,
                    superviseBatchSubmitVo.getTransType(),
                    entryAmt.multiply(BigDecimal.valueOf(100)).longValue(),
                    commission.multiply(BigDecimal.valueOf(100)).longValue(),
                    superviseBatchSubmitVo.getChnlNo(),specialSchool);
        }
        //企业网银
        if(superviseBatchSubmitVo.getTransType()==1){
        	unionPayUrl = unionBankService.getSpecialSchoolUnionPayUrl(orderId,
                    subOrderId,
                    totalAmt.multiply(BigDecimal.valueOf(100)).longValue(),
                    b2bmid,
                    b2btid,
                    orderId,
                    superviseBatchSubmitVo.getTransType(),
                    entryAmt.multiply(BigDecimal.valueOf(100)).longValue(),
                    commission.multiply(BigDecimal.valueOf(100)).longValue(),
                    superviseBatchSubmitVo.getChnlNo(),specialSchool);
        }
        //扫码
        if(superviseBatchSubmitVo.getTransType()==2) {        	
        	unionPayUrl = unionBankService.getSpecialSchoolQrcodeUrl(orderId, subOrderId,
        			totalAmt.multiply(BigDecimal.valueOf(100)).longValue(), 
        			scancodemid, 
        			scancodetid, 
        			orderId, 
        			entryAmt.multiply(BigDecimal.valueOf(100)).longValue(),
        			commission.multiply(BigDecimal.valueOf(100)).longValue(),
        			specialSchool);
        	
        }
        log.info("产生银联支付接口 ==> {}", unionPayUrl);
        JSONObject extendData = new JSONObject();
        extendData.put("url", unionPayUrl);
        extendData.put("totalAmt", totalAmt);
        extendData.put("orderId", orderId);
        
        BankTransactionRecord tranRecord = new BankTransactionRecord();
        tranRecord.setFromJson(JSONObject.toJSONString(extendData))
        .setB2bmid(b2bmid)
        .setB2btid(b2btid)
        .setB2cmid(b2cmid)
        .setB2ctid(b2ctid)
        .setScancodemid(scancodemid)
        .setScancodetid(scancodetid)
        .setSubOrderId(subOrderId)
        .setOrderId(orderId)
        .setTransType(superviseBatchSubmitVo.getTransType());
        
        unionPayAfter(studentIdArray, studentAmt,studentCommission,entryAmt, totalAmt, superviseBatchSubmitVo.getSchoolId(),
                superviseBatchSubmitVo.getBranchId(),
                tranRecord);
        
        return unionPayUrl;
    }
 
    /**
     * 银联支付批量提交
     *
     * @param superviseBatchSubmitVo
     * @return
     */
    @Override
    @Transactional
    public String insertUnionBatchSubmit(SuperviseBatchSubmitVo superviseBatchSubmitVo) {
    	
        School school = schoolService.getById(superviseBatchSubmitVo.getSchoolId());
        
        if(school == null){
    		throw new BusinessException("未获取到驾校");
    	}

        if (StringUtils.isEmpty(school.getBankAcctName())) {
            throw new BusinessException("驾校释放账户名未设置");
        }
        if (StringUtils.isEmpty(school.getPayAcctNo())) {
            throw new BusinessException("驾校释放账户号未设置");
        }

        SpecialSchool specialSchool = specialSchoolService.getById(school.getId());
        if(specialSchool != null) {
        	return specialSchoolUnionBatchSubmit(superviseBatchSubmitVo,specialSchool,school);
        }else{
            String expireDate = sysConfigService.selectConfigByKey("import.pay.expire.date");
            if(StringUtils.isEmpty(expireDate)){
                expireDate = "2024-03-15 00:00:00";
            }
            Date expire = DateUtil.parseDate(expireDate,"yyyy-MM-dd HH:mm:ss");
            String[] studentIdArray = Convert.toStrArray(superviseBatchSubmitVo.getStudentIds());
            for(String studentId : studentIdArray){
                SchoolStudent schoolStudent = schoolStudentService.getById(studentId);
                if(schoolStudent.getPrepareRegisteDate().after(expire)){
                    throw new BusinessException("学员"+schoolStudent.getName()+"在"+expireDate+"之后报名，请通过手机小程序交费");
                }
            }
        }

        SysConfig sysConfig = sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "supervise.unionPay.b2bmid").last("limit 1"));
        if(sysConfig == null || StringUtils.isEmpty(sysConfig.getConfigValue())){
        	throw new BusinessException("驾协银联公对公主商户号未设置");
        }
        String b2bmid = sysConfig.getConfigValue();

        sysConfig = sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "supervise.unionPay.b2btid").last("limit 1"));
        if(sysConfig == null || StringUtils.isEmpty(sysConfig.getConfigValue())){
        	throw new BusinessException("驾协银联公对公终端号未设置");
        }
        String b2btid = sysConfig.getConfigValue();
        
        sysConfig = sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "supervise.unionPay.b2cmid").last("limit 1"));
        if(sysConfig == null || StringUtils.isEmpty(sysConfig.getConfigValue())){
        	throw new BusinessException("驾协银联公对私主商户号未设置");
        }
        String b2cmid = sysConfig.getConfigValue();
                
        sysConfig = sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "supervise.unionPay.b2ctid").last("limit 1"));
        if(sysConfig == null || StringUtils.isEmpty(sysConfig.getConfigValue())){
        	throw new BusinessException("驾协银联公对私终端号未设置");
        }
        String b2ctid = sysConfig.getConfigValue();
        
        sysConfig = sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "supervise.unionPay.scancodemid").last("limit 1"));
        if(sysConfig == null || StringUtils.isEmpty(sysConfig.getConfigValue())){
        	throw new BusinessException("驾协银联扫码主商户号未设置");
        }
        String scancodemid = sysConfig.getConfigValue();
        

        
        sysConfig = sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "supervise.unionPay.scancodetid").last("limit 1"));
        if(sysConfig == null || StringUtils.isEmpty(sysConfig.getConfigValue())){
        	throw new BusinessException("驾协银联扫码私终端号未设置");
        }
        String scancodetid = sysConfig.getConfigValue();
        
        
        String subb2bmid = null;
        String subb2cmid = null;
        String subscancodemid = null;
        
        SysConfig commissionConfig =  sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key","supervise.commission.type"));
        if(commissionConfig == null || commissionConfig.getConfigValue().equals("0")) {
		      sysConfig = sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "supervise.unionPay.sub.b2bmid").last("limit 1"));
		      if(sysConfig == null || StringUtils.isEmpty(sysConfig.getConfigValue())){
		      	throw new BusinessException("驾协银联公对公监管商户号未设置");
		      }
		      subb2bmid = sysConfig.getConfigValue(); 
		    	
		      sysConfig = sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "supervise.unionPay.sub.b2cmid").last("limit 1"));
		      if(sysConfig == null || StringUtils.isEmpty(sysConfig.getConfigValue())){
		      	throw new BusinessException("驾协银联公对私监管商户号未设置");
		      }
		      subb2cmid = sysConfig.getConfigValue();
		    	
		      sysConfig = sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "supervise.unionPay.sub.scancodemid").last("limit 1"));
		      if(sysConfig == null || StringUtils.isEmpty(sysConfig.getConfigValue())){
		      	throw new BusinessException("驾协银联扫码监管商户号未设置");
		      }
		     subscancodemid = sysConfig.getConfigValue();
        }
               
       
        sysConfig = sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "student.supervise.amt").last("limit 1"));
        if(sysConfig == null || StringUtils.isEmpty(sysConfig.getConfigValue())){
        	throw new BusinessException("未设置每位学员应交监管资金");
        }
        String studentSuperviseAmt = sysConfig.getConfigValue();
        
        BigDecimal studentAmt = BigDecimal.ZERO;
        try {
        	studentAmt = new BigDecimal(studentSuperviseAmt);
        } catch (Exception ex) {
            throw new BusinessException("设置的监管资金必须是数字");
        }
        if (studentAmt.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("监管资金金额必须大于0");
        }
        
        
        //手续费
        BigDecimal commissionRate = BigDecimal.ZERO;
        sysConfig = sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "supervise.import.commission"));
        if(sysConfig != null && StringUtils.isNotEmpty(sysConfig.getConfigValue())){
        	commissionRate = new BigDecimal(sysConfig.getConfigValue());
        }
                
        String[] studentIdArray = Convert.toStrArray(superviseBatchSubmitVo.getStudentIds());
        // 订单号
        String orderId = unionBankConfiguration.getSystemNum() + DateUtil.formatDate(new Date(), "yyyyMMddHHmmssSSS") + RandomStringUtils.randomNumeric(7);
        // 子订单号
        String subOrderId = unionBankConfiguration.getSystemNum() + "SUB" + DateUtil.formatDate(new Date(), "yyyyMMddHHmmssSSS") + RandomStringUtils.randomNumeric(7);
        // 总监管金额
        BigDecimal entryAmt = studentAmt.multiply(new BigDecimal(studentIdArray.length)).setScale(2, BigDecimal.ROUND_HALF_UP);
        
        //应收手续费：监管金额 * 手续费率
        BigDecimal commission = entryAmt.multiply(commissionRate).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal studentCommission = studentAmt.multiply(commissionRate).setScale(2, BigDecimal.ROUND_HALF_UP);
        
        //0-支付整额方式，1-入账整额方式
        if(commissionConfig != null && commissionConfig.getConfigValue().equals("1")) {
        	entryAmt = entryAmt.subtract(commission).setScale(2, BigDecimal.ROUND_HALF_UP);
        	studentAmt = studentAmt.subtract(studentCommission).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        
        if(commission.compareTo(new BigDecimal(20))<=0 && superviseBatchSubmitVo.getTransType()==1) {
        	throw new BusinessException("手续费小于等于20元请选择个人网银交费");
        }
        
        //合计应收金额：监管金额 + 手续费
        BigDecimal totalAmt = entryAmt.add(commission).setScale(2, BigDecimal.ROUND_HALF_UP);

        // 获取商户的mid和tid
        String unionPayUrl = null;
        //个人网银
        if(superviseBatchSubmitVo.getTransType()==0){
        	unionPayUrl = unionBankService.getUnionPayUrl(orderId,
                    subOrderId,
                    totalAmt.multiply(BigDecimal.valueOf(100)).longValue(),
                    b2cmid,
                    b2ctid,
                    orderId,
                    superviseBatchSubmitVo.getTransType(),
                    entryAmt.multiply(BigDecimal.valueOf(100)).longValue(),
                    commission.multiply(BigDecimal.valueOf(100)).longValue(),
                    subb2cmid,
                    superviseBatchSubmitVo.getChnlNo());
        }
        //企业网银
        if(superviseBatchSubmitVo.getTransType()==1){
        	unionPayUrl = unionBankService.getUnionPayUrl(orderId,
                    subOrderId,
                    totalAmt.multiply(BigDecimal.valueOf(100)).longValue(),
                    b2bmid,
                    b2btid,
                    orderId,
                    superviseBatchSubmitVo.getTransType(),
                    entryAmt.multiply(BigDecimal.valueOf(100)).longValue(),
                    commission.multiply(BigDecimal.valueOf(100)).longValue(),
                    subb2bmid,superviseBatchSubmitVo.getChnlNo());
        }
        //扫码
        if(superviseBatchSubmitVo.getTransType()==2) {        	
        	unionPayUrl = unionBankService.getQrcodeUrl(orderId, subOrderId,
        			totalAmt.multiply(BigDecimal.valueOf(100)).longValue(), 
        			scancodemid, 
        			scancodetid, 
        			orderId, 
        			entryAmt.multiply(BigDecimal.valueOf(100)).longValue(),
        			commission.multiply(BigDecimal.valueOf(100)).longValue(),
        			subscancodemid);
        	
        }
        log.info("产生银联支付接口 ==> {}", unionPayUrl);
        JSONObject extendData = new JSONObject();
        extendData.put("url", unionPayUrl);
        extendData.put("totalAmt", totalAmt);
        extendData.put("orderId", orderId);
        
        BankTransactionRecord tranRecord = new BankTransactionRecord();
        tranRecord.setFromJson(JSONObject.toJSONString(extendData))
        .setB2bmid(b2bmid)
        .setB2btid(b2btid)
        .setB2cmid(b2cmid)
        .setB2ctid(b2ctid)
        .setSubb2bmid(subb2bmid)
        .setSubb2cmid(subb2cmid)
        .setScancodemid(scancodemid)
        .setScancodetid(scancodetid)
        .setSubscancodemid(subscancodemid)
        .setSubOrderId(subOrderId)
        .setOrderId(orderId)
        .setTransType(superviseBatchSubmitVo.getTransType());
        
        unionPayAfter(studentIdArray, studentAmt,studentCommission,entryAmt, totalAmt, superviseBatchSubmitVo.getSchoolId(),
                superviseBatchSubmitVo.getBranchId(),
                tranRecord);
        
        return unionPayUrl;
    }

    /**
     * 生成支付链接之后要做的操作(更改状态之类)
     *
     * @param studentIdArray
     * @param totalAmt
     * @param schoolId
     * @param branchId
     * @return
     */
    public void unionPayAfter(String[] studentIdArray,BigDecimal studentAmt,BigDecimal studentCommission,BigDecimal superviseAmt, BigDecimal totalAmt,
                             String schoolId,
                             String branchId, BankTransactionRecord transRecord) {
        //新增批量提交学员明细
        List<SuperviseBatchSubmitDetail> superviseBatchSubmitDetailList = new ArrayList<SuperviseBatchSubmitDetail>();
        for(String studentId : studentIdArray){
        	SchoolStudent student = schoolStudentMapper.selectById(studentId);
        	if(student != null){
        		transRecord
                .setStudentId(student.getId())
                .setSchoolId(student.getSchoolId())
                .setRegistrationId(student.getRegistrationId())
                .setBranchId(student.getBranchId())
                .setAmount(studentAmt)
                .setType(BankTransactionRecordType.PAY)
                .setCreateTime(new Date())
                .setStatus(false)
        		.setBankStatus("1");
        		bankTransactionRecordService.save(transRecord);
        		transRecord.setId(null);
        		
        		SuperviseBatchSubmitDetail superviseBatchSubmitDetail = new SuperviseBatchSubmitDetail();
                superviseBatchSubmitDetail.setOrderId(transRecord.getOrderId());
                superviseBatchSubmitDetail.setStudentId(student.getId());
                superviseBatchSubmitDetail.setCommission(studentCommission);
                superviseBatchSubmitDetail.setSuperviseFee(studentAmt);
                superviseBatchSubmitDetail.setTotalFee(studentAmt.add(studentCommission));
                superviseBatchSubmitDetail.setCreatedTime(new Date());
                superviseBatchSubmitDetail.setUpdatedTime(new Date());
                superviseBatchSubmitDetailList.add(superviseBatchSubmitDetail);
        	}
        }
        
        superviseBatchSubmitDetailService.saveBatch(superviseBatchSubmitDetailList);
        //新增批量提交
        SuperviseBatchSubmit superviseBatchSubmit = new SuperviseBatchSubmit();
        superviseBatchSubmit.setOrderId(transRecord.getOrderId());
        superviseBatchSubmit.setStudentCount(superviseBatchSubmitDetailList.size());
        superviseBatchSubmit.setTotalFee(totalAmt);
        superviseBatchSubmit.setSuperviseFee(superviseAmt);
        superviseBatchSubmit.setIsPay(0);
        superviseBatchSubmit.setCreatedTime(new Date());
        superviseBatchSubmit.setUpdatedTime(new Date());
        superviseBatchSubmit.setCommission(totalAmt.subtract(superviseAmt));

        if (StringUtils.isNotEmpty(schoolId)) {
            superviseBatchSubmit.setSchoolId(schoolId);
        }
        if (StringUtils.isNotEmpty(branchId)) {
            superviseBatchSubmit.setBranchId(branchId);
        }
        
        this.save(superviseBatchSubmit);
    }

    /**
     * 银联支付结果通知
     *
     * @param unionNotifyVo
     * @return
     */
    @Transactional(rollbackFor = {Exception.class},isolation = Isolation.READ_COMMITTED)
    @Override
    public boolean unionPayNotify(UnionNotifyVo unionNotifyVo) throws Exception{
        try {
        	String orderId = unionNotifyVo.getMerOrderId();
        	SuperviseBatchSubmit superviseBatchSubmit = this.getById(orderId);

            if(superviseBatchSubmit == null){
                throw new BusinessException("订单号不存在");
            }

            BigDecimal totalAmount = new BigDecimal(unionNotifyVo.getTotalAmount());
            if(totalAmount.compareTo(superviseBatchSubmit.getTotalFee().multiply(new BigDecimal(100))) != 0){
                throw new BusinessException("订单金额不相符");
            }

        	if(superviseBatchSubmit.getIsPay()==1){
        		return true;
        	}
        	
        	if(unionNotifyVo.getStatus().equals("TRADE_SUCCESS")){
        		paySuccess(superviseBatchSubmit,unionNotifyVo.getBankInfo(),unionNotifyVo.getBankCardNo(),unionNotifyVo.getTargetOrderId(),unionNotifyVo.getSeqId());
                return true;
        	}else{
                payFail(superviseBatchSubmit,unionNotifyVo.getErrMsg(),unionNotifyVo.getBankInfo(),
                        unionNotifyVo.getBankCardNo(),unionNotifyVo.getTargetOrderId(),unionNotifyVo.getSeqId());
                return false;
            }
        }catch (Exception e) {
            log.error("unionPayNotify error",e);
            throw e;
        }
    }

    /**
     * 新增批量提交
     *
     * @param superviseBatchSubmitVo 批量提交
     * @return 结果
     */
    @Override
    @Transactional
    public int insertSuperviseBatchSubmit(SuperviseBatchSubmitVo superviseBatchSubmitVo) {
        int row = 0;
        /*//生成批次号
        String orderId = getOrderId();
        String[] array = Convert.toStrArray(superviseBatchSubmitVo.getStudentIds());
        List<SchoolStudent> studentList = new ArrayList<SchoolStudent>();
        for (String studentId : array) {
            SchoolStudent schoolStudent = schoolStudentMapper.selectSchoolStudentById(studentId);
            studentList.add(schoolStudent);
        }
        try {
            long amt = unionBankService.checkParams(superviseBatchSubmitVo.getMid(),
                    superviseBatchSubmitVo.getTid(),
                    superviseBatchSubmitVo.getBankAcctName(),
                    superviseBatchSubmitVo.getPayAcctNo(),
                    superviseBatchSubmitVo.getBankName());
            // 总金额
            double totalAmt = studentList.size() * amt;
            // 获取支付地址
            BankAccount account = new BankAccount();
            account.setCustomerNo(superviseBatchSubmitVo.getCustomerNo());
            account.setSuperviseAccount(superviseBatchSubmitVo.getSuperviseAccount());

            //批量提交学员到银行，产生付款的虚假账号
            JSONObject jsonObject = bankService.batchSubmitStudentPay(orderId, studentList, account);
            VirturalAccountVo virturalAccountVo = jsonObject.toJavaObject(VirturalAccountVo.class);
            if (virturalAccountVo.getVirtual_account_array().size() > 0) {
                for (VirturalAccountVo.VirtualAccountArrayBean bean : virturalAccountVo.getVirtual_account_array()) {
                    SchoolStudent QueryStudent = new SchoolStudent();
                    QueryStudent.setName(bean.getStudents_name());
                    QueryStudent.setIdentity(bean.getStudents_id());
                    SchoolStudent student = schoolStudentMapper.checkUnique(QueryStudent);
                    if (student != null) {
                        student.setVirtualAccountName(bean.getVirtual_account_name());
                        student.setVirtualAccountNo(bean.getVirtual_account_no());
                        schoolStudentMapper.updateSchoolStudent(student);
                        //新增批量提交学员明细
                        SuperviseBatchSubmitDetail superviseBatchSubmitDetail = new SuperviseBatchSubmitDetail();
                        superviseBatchSubmitDetail.setId(IdUtils.fastSimpleUUID());
                        superviseBatchSubmitDetail.setOrderId(orderId);
                        superviseBatchSubmitDetail.setStudentId(student.getId());
                        superviseBatchSubmitDetail.setSuperviseFee(new BigDecimal(amt));
                        superviseBatchSubmitDetail.setVirtualAccountName(bean.getVirtual_account_name());
                        superviseBatchSubmitDetail.setVirtualAccountNo(bean.getVirtual_account_no());
                        superviseBatchSubmitDetail.setCreatedTime(new Date());
                        superviseBatchSubmitDetail.setUpdatedTime(new Date());
                        superviseBatchSubmitDetailMapper.insertSuperviseBatchSubmitDetail(superviseBatchSubmitDetail);
                    }
                }
            }

            //新增批量提交
            SuperviseBatchSubmit superviseBatchSubmit = new SuperviseBatchSubmit();
            superviseBatchSubmit.setOrderId(orderId);
            superviseBatchSubmit.setVirtualAccountName(virturalAccountVo.getJx_supervise_account());
            superviseBatchSubmit.setVirtualAccountNo(virturalAccountVo.getVirtual_pay_account_no());
            superviseBatchSubmit.setIsPay(0);
            superviseBatchSubmit.setStudentCount(studentList.size());
            superviseBatchSubmit.setCreatedTime(new Date());
            superviseBatchSubmit.setUpdatedTime(new Date());
            if (StringUtils.isNotEmpty(superviseBatchSubmitVo.getBranchId())) {
                superviseBatchSubmit.setBranchId(superviseBatchSubmitVo.getBranchId());
            }

            if (StringUtils.isNotEmpty(superviseBatchSubmitVo.getSchoolId())) {
                superviseBatchSubmit.setSchoolId(superviseBatchSubmitVo.getSchoolId());
            }

            superviseBatchSubmit.setTotalFee(new Double(totalAmt).floatValue());
            row = superviseBatchSubmitMapper.insertSuperviseBatchSubmit(superviseBatchSubmit);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException(e.getMessage());
        }*/
        return row;
    }


    /**
     * 修改批量提交
     *
     * @param superviseBatchSubmit 批量提交
     * @return 结果
     */
    @Override
    public int updateSuperviseBatchSubmit(SuperviseBatchSubmit superviseBatchSubmit) {
        return superviseBatchSubmitMapper.updateSuperviseBatchSubmit(superviseBatchSubmit);
    }

    /**
     * 批量删除批量提交
     *
     * @param orderIds 需要删除的批量提交主键
     * @return 结果
     */
    @Override
    public int deleteSuperviseBatchSubmitByOrderIds(String orderIds) {
        return superviseBatchSubmitMapper.deleteSuperviseBatchSubmitByOrderIds(Convert.toStrArray(orderIds));
    }

    /**
     * 删除批量提交信息
     *
     * @param orderId 批量提交主键
     * @return 结果
     */
    @Override
    public int deleteSuperviseBatchSubmitByOrderId(String orderId) {
        return superviseBatchSubmitMapper.deleteSuperviseBatchSubmitByOrderId(orderId);
    }


    /**
     * 获取批次号
     *
     * @param
     * @Return: void
     */
    public synchronized String getOrderId() {
        try {
            String maxNo = superviseBatchSubmitMapper.getTodayMaxNo();
            String number;
            // 截取今日最大编号的序号
            if (StringUtils.isNotEmpty(maxNo))
                number = String.valueOf(Integer.parseInt(maxNo.substring(10)) + 1);
            else
                number = "1";
            // 两位数内，做补零
            int length = number.length();
            if (length <= 2) {
                for (int i = 1; i <= 3 - length; i++) {
                    number = "0" + number;
                }
            }
            return new StringBuilder("").append(DateUtil.getDateNumber()).append(number).toString();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("获取批次号失败");
        }
    }


    /**
     * 展示监管资金交费明细
     */
    @Override
    public ShowPayInfoVo showPayInfo(SuperviseBatchSubmitVo superviseBatchSubmitVo) {
        SysConfig amtConfig = sysConfigService.getOne(new QueryWrapper<SysConfig>()
                .eq("config_key", "student.supervise.amt").last("limit 1"));
        if (amtConfig == null || StringUtils.isEmpty(amtConfig.getConfigValue())) {
            throw new BusinessException("未设置每位学员应交监管资金");
        }
        // 每位学员应交的监管资金
        BigDecimal amt = BigDecimal.valueOf(Double.parseDouble(amtConfig.getConfigValue()));
        String[] studentIdArray = Convert.toStrArray(superviseBatchSubmitVo.getStudentIds());

        //手续费率
        SysConfig sysConfig =
                sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "supervise.import.commission"));
        if(sysConfig==null || StringUtils.isEmpty(sysConfig.getConfigValue())){
        	throw new BusinessException("未设置平台手续费");
        }
        BigDecimal commissionRate = new BigDecimal(sysConfig.getConfigValue());

        //入账金额：学员数 * 监管金额
        BigDecimal entryAmt = amt.multiply(BigDecimal.valueOf(studentIdArray.length)).setScale(2, BigDecimal.ROUND_HALF_UP);

        //应收手续费：监管金额 * 手续费率
        BigDecimal commission = entryAmt.multiply(commissionRate).setScale(2, BigDecimal.ROUND_HALF_UP);
        sysConfig =  sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key","supervise.commission.type"));
        //0-支付整额方式，1-入账整额方式
        if(sysConfig != null && sysConfig.getConfigValue().equals("1")) {
        	//commission = entryAmt.multiply(commissionRate).setScale(2, BigDecimal.ROUND_HALF_UP);
        	entryAmt = entryAmt.subtract(commission);
        }

        //合计应收金额：监管金额 + 手续费
        BigDecimal totalAmt = entryAmt.add(commission).setScale(2, BigDecimal.ROUND_HALF_UP);

        ShowPayInfoVo showPayInfoVo = new ShowPayInfoVo();
        showPayInfoVo.setEntryAmtDcl(entryAmt);
        showPayInfoVo.setCommission(commission);
        showPayInfoVo.setTotalAmt(totalAmt);
        SpecialSchool specialSchool = specialSchoolService.getById(superviseBatchSubmitVo.getSchoolId());
        showPayInfoVo.setSpecialSchool(specialSchool);
        return showPayInfoVo;
    }


	@Override
	@Transactional(rollbackFor = {Exception.class},isolation = Isolation.READ_COMMITTED)
	public boolean unionScanCodePayNotify(UnionScanCodeNotifyVo unionScanCodeNotifyVo) throws Exception {
        try {
            SuperviseBatchSubmit superviseBatchSubmit = this.getById(unionScanCodeNotifyVo.getBillNo());

            if (superviseBatchSubmit == null) {
                return true;
            }

            if (superviseBatchSubmit.getIsPay() == 1) {
                return true;
            }

            BigDecimal totalAmount = new BigDecimal(unionScanCodeNotifyVo.getTotalAmount());
            if (totalAmount.compareTo(superviseBatchSubmit.getTotalFee().multiply(new BigDecimal(100))) != 0) {
                throw new BusinessException("订单金额不相符");
            }

            if (unionScanCodeNotifyVo.getBillStatus().equals("PAID")) {
                paySuccess(superviseBatchSubmit, unionScanCodeNotifyVo.getBankInfo(),
                        unionScanCodeNotifyVo.getBankCardNo(), unionScanCodeNotifyVo.getSeqId(), unionScanCodeNotifyVo.getSeqId());
                return true;
            } else {
                payFail(superviseBatchSubmit,unionScanCodeNotifyVo.getBillStatus(), unionScanCodeNotifyVo.getBankInfo(),
                        unionScanCodeNotifyVo.getBankCardNo(), unionScanCodeNotifyVo.getSeqId(), unionScanCodeNotifyVo.getSeqId());
                return false;
            }
        }catch(Exception ex){
            log.error("unionScanCodePayNotify error",ex);
            throw ex;
        }
    }

    private void payFail(SuperviseBatchSubmit superviseBatchSubmit,String errMsg, String bankInfo, String bankCardNo, String targetBankOrderId, String bankOrderId) {
        bankTransactionRecordService.update(new UpdateWrapper<BankTransactionRecord>()
                .eq("order_id", superviseBatchSubmit.getOrderId())
                .set("status", false)
                .set("bank_status", "1")
                .set("err_msg",errMsg)
                .set("bank_info", bankInfo)
                .set("bank_card_no", bankCardNo)
                .set("target_order_id", targetBankOrderId)
                .set("bank_order_id", bankOrderId));
	}

    private void paySuccess(SuperviseBatchSubmit superviseBatchSubmit, String bankInfo, String bankCardNo, String targetBankOrderId, String bankOrderId){

        superviseBatchSubmit.setIsPay(1);
        this.updateById(superviseBatchSubmit);

        // 增加驾校总监管金额并更新
        School school = schoolService.getById(superviseBatchSubmit.getSchoolId());
        BigDecimal superviseAmt = BigDecimal.ZERO;
        if(school.getSuperviseAmt()!=null){
            superviseAmt = school.getSuperviseAmt();
        }
        BigDecimal newSuperviseAmt = superviseAmt.add(superviseBatchSubmit.getSuperviseFee()).setScale(2, BigDecimal.ROUND_HALF_UP);
        school.setSuperviseAmt(newSuperviseAmt);
        boolean success = schoolService.updateById(school);
        if(!success){
            throw new BusinessException("更新学校余额失败");
        }

        // 保存驾校交易流水
        SchoolDealFlow schoolDealFlowLastRecord = schoolDealFlowService.getOne(new QueryWrapper<SchoolDealFlow>().eq("school_id", school.getId())
                .orderByDesc("id").last("limit 1"));
        SchoolDealFlow schoolDealFlow = new SchoolDealFlow();
        schoolDealFlow.setSchoolId(school.getId());
        schoolDealFlow.setAction(1);
        schoolDealFlow.setLastBalance(schoolDealFlowLastRecord==null ? BigDecimal.ZERO : schoolDealFlowLastRecord.getTheBalance());
        schoolDealFlow.setTheAmt(superviseBatchSubmit.getSuperviseFee());
        schoolDealFlow.setTheBalance(schoolDealFlow.getLastBalance().add(schoolDealFlow.getTheAmt()));
        schoolDealFlow.setBankInfo(bankInfo);
        schoolDealFlow.setBankCardNo(bankCardNo);
        schoolDealFlow.setTargetOrderId(targetBankOrderId);
        schoolDealFlow.setBankOrderId(bankOrderId);
        schoolDealFlowService.save(schoolDealFlow);
        success = schoolDealFlowService.updateById(schoolDealFlowLastRecord);
        if(!success){
            throw new BusinessException("更新学校流水失败");
        }

        SuperviseFlow superviseFlowLastRecord = superviseFlowService.getOne(new QueryWrapper<SuperviseFlow>().orderByDesc("id").last("limit 1"));

        SuperviseFlow superviseFlow = new SuperviseFlow();
        superviseFlow.setSchoolId(school.getId());
        superviseFlow.setAction(1);
        superviseFlow.setLastBalance(superviseFlowLastRecord == null ? BigDecimal.ZERO : superviseFlowLastRecord.getTheBalance());
        superviseFlow.setTheAmt(superviseBatchSubmit.getSuperviseFee());
        superviseFlow.setTheBalance(superviseFlow.getLastBalance().add(superviseFlow.getTheAmt()));
        superviseFlowService.save(superviseFlow);
        success = superviseFlowService.updateById(superviseFlowLastRecord);
        if(!success){
            throw new BusinessException("更新监管流水失败");
        }

        bankTransactionRecordService.update(new UpdateWrapper<BankTransactionRecord>()
                .eq("order_id", superviseBatchSubmit.getOrderId())
                .set("status", true)
                .set("bank_status", 0)
                .set("bank_info", bankInfo)
                .set("bank_card_no", bankCardNo)
                .set("target_order_id", targetBankOrderId)
                .set("bank_order_id", bankOrderId));

        List<SuperviseBatchSubmitDetail> superviseBatchSubmitDetailList = superviseBatchSubmitDetailService.list(new QueryWrapper<SuperviseBatchSubmitDetail>()
                .eq("order_id", superviseBatchSubmit.getOrderId()));
        for(SuperviseBatchSubmitDetail detail : superviseBatchSubmitDetailList){
            SchoolStudent student = schoolStudentService.getById(detail.getStudentId());
            if(student != null){
                student.setSuperviseDate(new Date());
                student.setSuperviseFee(student.getSuperviseFee()==null ? detail.getSuperviseFee() :
                        student.getSuperviseFee().add(detail.getSuperviseFee()));
                student.setIsSupervise(1);
                student.setSuperviseFeeIsOk(1);
                student.setReleaseVersion(0);
                student.setResidueSuperviseAmt(student.getResidueSuperviseAmt()  == null ? detail.getSuperviseFee() :
                        student.getResidueSuperviseAmt().add(detail.getSuperviseFee()));
                success = schoolStudentService.updateById(student);
                if(!success){
                    throw new BusinessException("更新学校余额失败");
                }
            }

            SupervisePay supervisePay = supervisePayService.getById(student.getId());
            if (supervisePay == null) {
                supervisePay = new SupervisePay();
                supervisePay.setBranchId(student.getBranchId());
                supervisePay.setRegistrationId(student.getRegistrationId());
                supervisePay.setRestStudyFee(new BigDecimal(0));
                supervisePay.setRestSuperviseFee(supervisePay.getRestSuperviseFee()==null ?  detail.getSuperviseFee() :
                        supervisePay.getRestSuperviseFee().add(detail.getSuperviseFee()));
                supervisePay.setSchoolId(student.getSchoolId());
                supervisePay.setStudentId(student.getId());
                supervisePay.setStudyFee(new BigDecimal(0));
                supervisePay.setCommissionFee(supervisePay.getCommissionFee()==null ? detail.getCommission() :
                        supervisePay.getCommissionFee().add(detail.getCommission()));
                supervisePay.setSuperviseFee(supervisePay.getSuperviseFee() == null ? detail.getTotalFee() :
                        supervisePay.getSuperviseFee().add(detail.getTotalFee()));
                supervisePay.setTotalRealFee(new BigDecimal(0));
                supervisePay.setTotalReleaseFee(new BigDecimal(0));
                supervisePayService.save(supervisePay);
            }else{
                supervisePay.setSuperviseFee(supervisePay.getSuperviseFee() == null ? detail.getTotalFee() :
                        supervisePay.getSuperviseFee().add(detail.getTotalFee()));
                supervisePay.setRestSuperviseFee(supervisePay.getRestSuperviseFee()==null ?  detail.getSuperviseFee() :
                        supervisePay.getRestSuperviseFee().add(detail.getSuperviseFee()));
                supervisePay.setCommissionFee(supervisePay.getCommissionFee()==null ? detail.getCommission() :
                        supervisePay.getCommissionFee().add(detail.getCommission()));
                supervisePayService.updateById(supervisePay);
            }
        }
    }
    @Override
    public void payRollback(SuperviseBatchSubmit superviseBatchSubmit) throws Exception{

        BankTransactionRecord bankTransactionRecord = bankTransactionRecordService.getOne(new LambdaQueryWrapper<BankTransactionRecord>()
                .eq(BankTransactionRecord::getOrderId, superviseBatchSubmit.getOrderId()).last("limit 1"));

        if(bankTransactionRecord==null) {
            return;
        }

        String appId = unionBankConfiguration.getAppId();
        String appKey = unionBankConfiguration.getAppKey();
        SpecialSchool specialSchool  = specialSchoolService.getById(bankTransactionRecord.getSchoolId());
        if(specialSchool != null){
            appId = specialSchool.getAppId();
            appKey = specialSchool.getAppKey();
        }
        if(bankTransactionRecord.getTransType()==0){
            String instMid = "WGDEFAULT";
            JSONObject responseJson = unionBankService.queryPayOrder(instMid,bankTransactionRecord.getB2cmid(),
                    bankTransactionRecord.getB2ctid(),superviseBatchSubmit.getOrderId(),appId,appKey);
            UnionNotifyVo unionNotifyVo = responseJson.toJavaObject(UnionNotifyVo.class);
            unionPayNotify(unionNotifyVo);
            superviseBatchSubmit.setIsSynBank(1);
            this.updateById(superviseBatchSubmit);
        }else if(bankTransactionRecord.getTransType()==1){
            String instMid = "WGDEFAULT";
            JSONObject responseJson = unionBankService.queryPayOrder(instMid,bankTransactionRecord.getB2bmid(),
                    bankTransactionRecord.getB2btid(),superviseBatchSubmit.getOrderId(),appId,appKey);
            UnionNotifyVo unionNotifyVo = responseJson.toJavaObject(UnionNotifyVo.class);
            unionPayNotify(unionNotifyVo);
        }else{
            String instMid = "QRPAYDEFAULT";
            JSONObject responseJson = unionBankService.queryScanCodeOrder(instMid,bankTransactionRecord.getScancodemid(),
                    bankTransactionRecord.getScancodetid(),superviseBatchSubmit.getOrderId(),superviseBatchSubmit.getCreatedTime(),
                    appId,appKey);
            UnionScanCodeNotifyVo unionScanCodeNotifyVo = responseJson.toJavaObject(UnionScanCodeNotifyVo.class);
            unionScanCodePayNotify(unionScanCodeNotifyVo);
        }
        superviseBatchSubmit.setIsSynBank(1);
        this.updateById(superviseBatchSubmit);
    }

    @Override
    public void syncPayOrderByDay(Integer day) throws Exception{
        Calendar cal = Calendar.getInstance();
        if(day == null) {
            cal.add(Calendar.DATE, -1);
        }else{
            cal.add(Calendar.DATE, -day);
        }
        List<SuperviseBatchSubmit> batchSubmitList = this.list(new LambdaQueryWrapper<SuperviseBatchSubmit>()
                .between(SuperviseBatchSubmit::getCreatedTime, cal.getTime() ,new Date())
                .eq(SuperviseBatchSubmit::getIsSynBank,0)
                .orderByDesc(SuperviseBatchSubmit::getCreatedTime));

        for(SuperviseBatchSubmit sbs : batchSubmitList) {
            try {
                payRollback(sbs);
                ThreadUtil.sleep(1000);
            }catch(Exception ex) {
                log.error("订单号："+sbs.getOrderId()+"回滚失败",ex);
                throw ex;
            }
        }
    }

    @Override
    public void syncPayOrder(String orderNo, String date) throws Exception{
        LambdaQueryWrapper<SuperviseBatchSubmit> query = new LambdaQueryWrapper<SuperviseBatchSubmit>()
                .eq(SuperviseBatchSubmit::getIsSynBank,0)
                .orderByDesc(SuperviseBatchSubmit::getCreatedTime);
        if(com.asiainfo.util.StringUtil.isNotEmpty(orderNo)){
            query.eq(SuperviseBatchSubmit::getOrderId,orderNo);
        }
        if(com.asiainfo.util.StringUtil.isNotEmpty(date)){
            query.likeRight(SuperviseBatchSubmit::getCreateTime,date);
        }
        List<SuperviseBatchSubmit> batchSubmitList = this.list(query);
        for(SuperviseBatchSubmit sbs : batchSubmitList) {
            try {
                payRollback(sbs);
                ThreadUtil.sleep(1000);
            }catch(Exception ex) {
                log.error("订单号："+sbs.getOrderId()+"回滚失败",ex);
                throw ex;
            }
        }
    }

}
