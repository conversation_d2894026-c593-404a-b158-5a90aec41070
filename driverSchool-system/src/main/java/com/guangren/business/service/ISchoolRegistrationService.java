package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolCar;
import com.guangren.business.domain.SchoolRegistration;
import com.guangren.business.vo.importTempete.SchoolRegistrationTemplete;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 报名点Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-02
 */
public interface ISchoolRegistrationService extends IService<SchoolRegistration>
{
    /**
     * 查询报名点
     * 
     * @param id 报名点主键
     * @return 报名点
     */
    public SchoolRegistration selectSchoolRegistrationById(String id);

    /**
     * 查询报名点列表
     * 
     * @param schoolRegistration 报名点
     * @return 报名点集合
     */
    public List<SchoolRegistration> selectSchoolRegistrationList(SchoolRegistration schoolRegistration);

    /**
     * 新增报名点
     * 
     * @param schoolRegistration 报名点
     * @return 结果
     */
    public int insertSchoolRegistration(SchoolRegistration schoolRegistration);

    /**
     * 修改报名点
     * 
     * @param schoolRegistration 报名点
     * @return 结果
     */
    public int updateSchoolRegistration(SchoolRegistration schoolRegistration);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SchoolRegistration schoolRegistration);

    /**
     * 批量删除报名点
     * 
     * @param ids 需要删除的报名点主键集合
     * @return 结果
     */
    public int deleteSchoolRegistrationByIds(String ids);

    /**
     * 删除报名点信息
     * 
     * @param id 报名点主键
     * @return 结果
     */
    public int deleteSchoolRegistrationById(String id);

    /**
     * 导出报名点列表
     */
    public List<SchoolRegistration> exportSchoolRegistrationList(SchoolRegistration schoolRegistration);

    public SchoolRegistrationTemplete importData(MultipartFile file) throws Exception;

    /**
     * 批量提交二维码审核申请
     * @param ids
     * @return
     */
    int submitAudit(String ids);
    /**
     * 批量审核二维码申请
     * @param ids 主表ID
     * @param auditStatus 设置的审核状态
     * @return
     */
    int batchAudit(String ids, Integer auditStatus);
    /**
     * 下载二维码图片
     * @param response
     * @param ids
     */
    void downloadQrcode(HttpServletResponse response, String ids);
    /**
     * 查看是否存在过期或未审核的二维码信息
     * @param id 业务ID
     * @return
     */
    SchoolRegistration existnotPassQrcode(String id);
}
