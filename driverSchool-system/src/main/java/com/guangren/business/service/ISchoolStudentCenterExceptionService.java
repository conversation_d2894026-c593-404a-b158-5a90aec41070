package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolStudentCenterException;

import java.util.List;

/**
 * 驾校管理Service接口
 * 
 * <AUTHOR>
 * @date 2023-02-15
 */
public interface ISchoolStudentCenterExceptionService extends IService<SchoolStudentCenterException>
{

    /**
     * 查询上报学时中心异常数据列表
     * <AUTHOR>
     * @date 2023/9/27 10:44
     * @param schoolStudentCenterException *
     * @return java.util.List<com.guangren.business.domain.SchoolStudentCenterException>
     */
    List<SchoolStudentCenterException> selectSchoolStudentCenterExceptionList(SchoolStudentCenterException schoolStudentCenterException);

    /**
     * 获取学时中心名称列表
     * <AUTHOR>
     * @date 2023/12/7 15:43
     * @return java.util.List<java.lang.String> *
     */
    List<String> getCenterNameList();
}
