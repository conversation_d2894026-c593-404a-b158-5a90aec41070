package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.RetainedAmount;

import java.util.List;

/**
 * 每日留存金额Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-06
 */
public interface IRetainedAmountService extends IService<RetainedAmount>
{
    /**
     * 查询每日留存金额
     * 
     * @param id 每日留存金额主键
     * @return 每日留存金额
     */
    public RetainedAmount selectRetainedAmountById(String id);

    /**
     * 查询每日留存金额列表
     * 
     * @param retainedAmount 每日留存金额
     * @return 每日留存金额集合
     */
    public List<RetainedAmount> selectRetainedAmountList(RetainedAmount retainedAmount);

    /**
     * 新增每日留存金额
     * 
     * @param retainedAmount 每日留存金额
     * @return 结果
     */
    public int insertRetainedAmount(RetainedAmount retainedAmount);

    /**
     * 修改每日留存金额
     * 
     * @param retainedAmount 每日留存金额
     * @return 结果
     */
    public int updateRetainedAmount(RetainedAmount retainedAmount);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(RetainedAmount retainedAmount);

    /**
     * 批量删除每日留存金额
     * 
     * @param ids 需要删除的每日留存金额主键集合
     * @return 结果
     */
    public int deleteRetainedAmountByIds(String ids);

    /**
     * 删除每日留存金额信息
     * 
     * @param id 每日留存金额主键
     * @return 结果
     */
    public int deleteRetainedAmountById(String id);
}
