package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.RetainedNumber;

import java.util.List;

/**
 * 每日留存人数Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-15
 */
public interface IRetainedNumberService extends IService<RetainedNumber>
{
    /**
     * 查询每日留存人数
     * 
     * @param id 每日留存人数主键
     * @return 每日留存人数
     */
    public RetainedNumber selectRetainedNumberById(String id);

    /**
     * 查询每日留存人数列表
     * 
     * @param retainedNumber 每日留存人数
     * @return 每日留存人数集合
     */
    public List<RetainedNumber> selectRetainedNumberList(RetainedNumber retainedNumber);

    /**
     * 新增每日留存人数
     * 
     * @param retainedNumber 每日留存人数
     * @return 结果
     */
    public int insertRetainedNumber(RetainedNumber retainedNumber);

    /**
     * 修改每日留存人数
     * 
     * @param retainedNumber 每日留存人数
     * @return 结果
     */
    public int updateRetainedNumber(RetainedNumber retainedNumber);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(RetainedNumber retainedNumber);

    /**
     * 批量删除每日留存人数
     * 
     * @param ids 需要删除的每日留存人数主键集合
     * @return 结果
     */
    public int deleteRetainedNumberByIds(String ids);

    /**
     * 删除每日留存人数信息
     * 
     * @param id 每日留存人数主键
     * @return 结果
     */
    public int deleteRetainedNumberById(String id);
}
