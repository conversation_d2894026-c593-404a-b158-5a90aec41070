package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.DriverTrainSsoSyncInfo;

import java.util.List;

public interface IDriverTrainSsoSyncInfoService extends IService<DriverTrainSsoSyncInfo> {

    List<DriverTrainSsoSyncInfo> selectDriverTrainSsoSyncInfoList(DriverTrainSsoSyncInfo driverTrainSsoSyncInfo);

    DriverTrainSsoSyncInfo selectDriverTrainSsoSyncInfoById(String id);

    int insertDriverTrainSsoSyncInfo(DriverTrainSsoSyncInfo driverTrainSsoSyncInfo);

    int updateDriverTrainSsoSyncInfo(DriverTrainSsoSyncInfo driverTrainSsoSyncInfo);

    int deleteDriverTrainSsoSyncInfoById(String id);

    int deleteDriverTrainSsoSyncInfoByIds(String[] ids);

    //同步驾培用户信息至单点登录平台
    void syncDriverSchoolUserInfo(String timeBegin, String timeEnd,String userId)throws Exception;
}
