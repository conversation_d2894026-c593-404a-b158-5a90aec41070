package com.guangren.business.service;

import java.util.List;
import java.util.Set;

import com.guangren.business.domain.OrganRole;

/**
 * 机构角色信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-07
 */
public interface IOrganRoleService 
{
    /**
     * 查询机构角色信息
     * 
     * @param roleId 机构角色信息主键
     * @return 机构角色信息
     */
    public OrganRole selectOrganRoleByRoleId(Long roleId);

    /**
     * 查询机构角色信息列表
     * 
     * @param organRole 机构角色信息
     * @return 机构角色信息集合
     */
    public List<OrganRole> selectOrganRoleList(OrganRole organRole);

    /**
     * 新增机构角色信息
     * 
     * @param organRole 机构角色信息
     * @return 结果
     */
    public int insertOrganRole(OrganRole organRole);

    /**
     * 修改机构角色信息
     * 
     * @param organRole 机构角色信息
     * @return 结果
     */
    public int updateOrganRole(OrganRole organRole);


    /**
     * 批量删除机构角色信息
     * 
     * @param roleIds 需要删除的机构角色信息主键集合
     * @return 结果
     */
    public int deleteOrganRoleByRoleIds(String roleIds);

    /**
     * 删除机构角色信息信息
     * 
     * @param roleId 机构角色信息主键
     * @return 结果
     */
    public int deleteOrganRoleByRoleId(Long roleId);

    public Set<String> selectRoleKeys(Long id);

    public int setOrganRoleSave(OrganRole organRole);

}
