package com.guangren.business.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.StaDate;

/**
 * 按日期统计所有指标Service接口
 * 
 * <AUTHOR>
 * @date 2023-06-12
 */
public interface IStaDateService extends IService<StaDate>
{
    /**
     * 查询按日期统计所有指标
     * 
     * @param id 按日期统计所有指标主键
     * @return 按日期统计所有指标
     */
    public StaDate selectStaDateById(Long id);

    /**
     * 查询按日期统计所有指标列表
     * 
     * @param staDate 按日期统计所有指标
     * @return 按日期统计所有指标集合
     */
    public List<StaDate> selectStaDateList(StaDate staDate);

    /**
     * 新增按日期统计所有指标
     * 
     * @param staDate 按日期统计所有指标
     * @return 结果
     */
    public int insertStaDate(StaDate staDate);

    /**
     * 修改按日期统计所有指标
     * 
     * @param staDate 按日期统计所有指标
     * @return 结果
     */
    public int updateStaDate(StaDate staDate);



    /**
     * 批量删除按日期统计所有指标
     * 
     * @param ids 需要删除的按日期统计所有指标主键集合
     * @return 结果
     */
    public int deleteStaDateByIds(String ids);

    /**
     * 删除按日期统计所有指标信息
     * 
     * @param id 按日期统计所有指标主键
     * @return 结果
     */
    public int deleteStaDateById(Long id);


    public StaDate getTodayStaDate();
}
