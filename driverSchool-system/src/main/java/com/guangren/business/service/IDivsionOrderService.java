package com.guangren.business.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.DivsionOrder;
import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.domain.SpecialSchool;
import com.guangren.business.vo.UnionNotifyVo;
import com.guangren.business.vo.UnionScanCodeNotifyVo;

import java.util.List;

/**
 * 分账总订单Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface IDivsionOrderService extends IService<DivsionOrder>
{
    /**
     * 查询分账总订单
     * 
     * @param id 分账总订单主键
     * @return 分账总订单
     */
    public DivsionOrder selectDivsionOrderById(Long id);

    /**
     * 查询分账总订单列表
     * 
     * @param divsionOrder 分账总订单
     * @return 分账总订单集合
     */
    public List<DivsionOrder> selectDivsionOrderList(DivsionOrder divsionOrder);

    /**
     * 新增分账总订单
     * 
     * @param divsionOrder 分账总订单
     * @return 结果
     */
    public int insertDivsionOrder(DivsionOrder divsionOrder);

    /**
     * 修改分账总订单
     * 
     * @param divsionOrder 分账总订单
     * @return 结果
     */
    public int updateDivsionOrder(DivsionOrder divsionOrder);

    /**
     * 批量删除分账总订单
     * 
     * @param ids 需要删除的分账总订单主键集合
     * @return 结果
     */
    public int deleteDivsionOrderByIds(String ids);

    /**
     * 删除分账总订单信息
     * 
     * @param id 分账总订单主键
     * @return 结果
     */
    public int deleteDivsionOrderById(Long id);

    boolean unionPayNotify(UnionNotifyVo unionNotifyVo) throws Exception;

    boolean unionScanCodePayNotify(UnionScanCodeNotifyVo unionScanCodeNotifyVo) throws Exception;

    JSONObject createMiniappQrcode(SchoolStudent student) throws Exception;


    JSONObject createMiniappPay(SchoolStudent student,String jsCode) throws Exception;

    JSONObject createSpecialSchoolQrcode(SchoolStudent student, SpecialSchool specialSchool);

    void refund(DivsionOrder order);
}
