package com.guangren.business.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.ProvinceStudyTime;
import com.guangren.business.domain.School;
import com.guangren.business.domain.SchoolContract;
import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.domain.SuperviseReleaseRecord;
import com.guangren.business.vo.StudentCountVo;
import com.guangren.business.vo.VariousNumberVo;
import com.guangren.common.exception.BusinessException;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

public interface ISchoolStudentService extends IService<SchoolStudent> {

    public boolean customerSave(SchoolStudent student, JSONObject json) throws BusinessException, Exception;
	
	public String getOpenId(String jsCode);
    /**
     * 查询学员
     *
     * @param id 学员主键
     * @return 学员
     */
    public SchoolStudent selectSchoolStudentById(String id);

    /**
     * 查询学员列表
     *
     * @param schoolStudent 学员
     * @return 学员集合
     */
    public List<SchoolStudent> selectSchoolStudentList(SchoolStudent schoolStudent);

    /**
     * 新增学员
     *
     * @param schoolStudent 学员
     * @return 结果
     */
    public int insertSchoolStudent(SchoolStudent schoolStudent);

    /**
     * 修改学员
     *
     * @param schoolStudent 学员
     * @return 结果
     */
    public int updateSchoolStudent(SchoolStudent schoolStudent);

    /**
     * 审核学员
     */
    public int checkStudent(SchoolStudent schoolStudent);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SchoolStudent schoolStudent);

    /**
     * 批量删除学员
     *
     * @param ids 需要删除的学员主键集合
     * @return 结果
     */
    public int deleteSchoolStudentByIds(String ids, String delReason);

    /**
     * 删除学员信息
     *
     * @param id 学员主键
     * @return 结果
     */
    public int deleteSchoolStudentById(String id);

    /**
     * 学员退学保存
     */
    public boolean dropOutSave(SchoolStudent schoolStudent);


    /**
     * 同步学员学时
     * @param students
     * @return
     */
    public void submitStudentAddDataToStudyCenter(List<SchoolStudent> students)throws Exception;

    /**
     * 学员退学
     * @return
     */
    public void submitStudentQuitDataToStudyCenter(List<SchoolStudent> students)throws Exception;

    /**
     * 从计时平台获取学员的学号
     * @param paramType 参数类型：0=身份证，1=学号
     * @param param 参数
     * @return com.alibaba.fastjson.JSONArray *
     */
    public JSONArray getStudentInfoFromStudyTimeCenter(Integer paramType, String param) throws Exception;


    VariousNumberVo getVariousNumber();
    
	public List<SchoolStudent> customSelectSchoolStudentList(SchoolStudent schoolStudent);

    /**
     * 学员统计数据
     * @return 结果
     */
    StudentCountVo getSchoolStudentCount(SchoolStudent schoolStudent);

    /**
     * 查询结业学员列表
     */
    List<SchoolStudent> selectSchoolGraduateStudentList(SchoolStudent schoolStudent);

    /**
     * 导出结业学员列表
     */
    List<SchoolStudent> exportGraduateSchoolStudentList(SchoolStudent schoolStudent);

    /**
     * 修改学员（对外访问接口）
     */
    int studentEventStrategyUpdate(SchoolStudent schoolStudent);

    void releaseMoney(SchoolStudent student, BigDecimal releaseMoney);

    void notifySchoolStudyCenterQuit(SchoolStudent student);

    public String quit(SchoolStudent student) throws Exception;

    public String releaseMoney(SchoolStudent student,int trainphase) throws Exception;


    /**
     * 修改学号保存
     * <AUTHOR>
     * @date 2023/11/3 13:55
     * @param schoolStudent  *
     */
    int editStunumSave(SchoolStudent schoolStudent);

    /**
     * 查询学员在计时平台的数据
     * <AUTHOR>
     * @date 2023/11/3 15:31
     * @param stunum 学号
     * @return java.util.List<com.guangren.business.domain.ProvinceStudyTime> *
     */
    List<ProvinceStudyTime> getStudentInfoStudyTime(String stunum);


    void handle(SchoolStudent student, School school, SuperviseReleaseRecord record, JSONObject responseJson, BigDecimal withdrawAmount);

//    void dikou(SchoolStudent student, School school, TempDikouData tdd);


    List<SchoolStudent> getQuitReleaseStudentList();

    void createContract(SchoolStudent schoolStudent, SchoolContract schoolContract) throws Exception;

    /**
     * 导入广仁特殊学员数据
     * <AUTHOR>
     * @date 2024/3/20 16:16
     * @param file *
     * @return int *
     */
    int grStudentImport(MultipartFile file);

    /** 审核学员保存 */
    void checkStudentSave(SchoolStudent student, boolean isCheckType);

    /**
     * 学员点击查看合同按钮，更新合同查看日期
     */
    int updateStudentContract(String studentId);

    /**
     * 判断学员的合同是否更新过
     * @param studentId 学员ID
     * @return
     */
    int checkStudentContractHasUpdate(String studentId);
}
