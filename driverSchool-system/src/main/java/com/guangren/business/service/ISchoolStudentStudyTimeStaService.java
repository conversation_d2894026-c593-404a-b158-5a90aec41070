package com.guangren.business.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolStudentStudyTimeSta;

/**
 * 学时统计，定时任务统计，有达标时通知银行释放资金Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface ISchoolStudentStudyTimeStaService extends IService<SchoolStudentStudyTimeSta>
{
    /**
     * 查询学时统计，定时任务统计，有达标时通知银行释放资金
     * 
     * @param studentId 学时统计，定时任务统计，有达标时通知银行释放资金主键
     * @return 学时统计，定时任务统计，有达标时通知银行释放资金
     */
    public SchoolStudentStudyTimeSta selectSchoolStudentStudyTimeStaByStudentId(String studentId);

    /**
     * 查询学时统计，定时任务统计，有达标时通知银行释放资金列表
     * 
     * @param schoolStudentStudyTimeSta 学时统计，定时任务统计，有达标时通知银行释放资金
     * @return 学时统计，定时任务统计，有达标时通知银行释放资金集合
     */
    public List<SchoolStudentStudyTimeSta> selectSchoolStudentStudyTimeStaList(SchoolStudentStudyTimeSta schoolStudentStudyTimeSta);

    /**
     * 新增学时统计，定时任务统计，有达标时通知银行释放资金
     * 
     * @param schoolStudentStudyTimeSta 学时统计，定时任务统计，有达标时通知银行释放资金
     * @return 结果
     */
    public int insertSchoolStudentStudyTimeSta(SchoolStudentStudyTimeSta schoolStudentStudyTimeSta);

    /**
     * 修改学时统计，定时任务统计，有达标时通知银行释放资金
     * 
     * @param schoolStudentStudyTimeSta 学时统计，定时任务统计，有达标时通知银行释放资金
     * @return 结果
     */
    public int updateSchoolStudentStudyTimeSta(SchoolStudentStudyTimeSta schoolStudentStudyTimeSta);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SchoolStudentStudyTimeSta schoolStudentStudyTimeSta);

    /**
     * 批量删除学时统计，定时任务统计，有达标时通知银行释放资金
     * 
     * @param studentIds 需要删除的学时统计，定时任务统计，有达标时通知银行释放资金主键集合
     * @return 结果
     */
    public int deleteSchoolStudentStudyTimeStaByStudentIds(String studentIds);

    /**
     * 删除学时统计，定时任务统计，有达标时通知银行释放资金信息
     * 
     * @param studentId 学时统计，定时任务统计，有达标时通知银行释放资金主键
     * @return 结果
     */
    public int deleteSchoolStudentStudyTimeStaByStudentId(String studentId);
}
