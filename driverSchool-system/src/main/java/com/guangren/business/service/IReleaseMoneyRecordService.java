package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.ReleaseMoneyRecord;

import java.util.List;

/**
 * 手动释放资金记录Service接口
 * 
 * <AUTHOR>
 * @date 2023-10-10
 */
public interface IReleaseMoneyRecordService extends IService<ReleaseMoneyRecord>
{
    /**
     * 查询手动释放资金记录
     * 
     * @param id 手动释放资金记录主键
     * @return 手动释放资金记录
     */
    public ReleaseMoneyRecord selectReleaseMoneyRecordById(Long id);

    /**
     * 查询手动释放资金记录列表
     * 
     * @param releaseMoneyRecord 手动释放资金记录
     * @return 手动释放资金记录集合
     */
    public List<ReleaseMoneyRecord> selectReleaseMoneyRecordList(ReleaseMoneyRecord releaseMoneyRecord);

    /**
     * 新增手动释放资金记录
     * 
     * @param releaseMoneyRecord 手动释放资金记录
     * @return 结果
     */
    public int insertReleaseMoneyRecord(ReleaseMoneyRecord releaseMoneyRecord);

    /**
     * 修改手动释放资金记录
     * 
     * @param releaseMoneyRecord 手动释放资金记录
     * @return 结果
     */
    public int updateReleaseMoneyRecord(ReleaseMoneyRecord releaseMoneyRecord);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(ReleaseMoneyRecord releaseMoneyRecord);

    /**
     * 批量删除手动释放资金记录
     * 
     * @param ids 需要删除的手动释放资金记录主键集合
     * @return 结果
     */
    public int deleteReleaseMoneyRecordByIds(String ids);

    /**
     * 删除手动释放资金记录信息
     * 
     * @param id 手动释放资金记录主键
     * @return 结果
     */
    public int deleteReleaseMoneyRecordById(Long id);

    /**
     * 审核释放申请保存（初审）
     * <AUTHOR>
     * @date 2023/12/12 10:02
     * @param releaseMoneyRecord *
     * @return int *
     */
    int checkReleaseApplySave(ReleaseMoneyRecord releaseMoneyRecord);

    /**
     * 释放资金
     * <AUTHOR>
     * @date 2023/12/12 10:36
     * @param id *
     */
    boolean release(String id) throws Exception;

    /**
     * 审核释放申请保存（终审）
     * <AUTHOR>
     * @date 2024/1/15 16:34
     * @param releaseMoneyRecord *
     * @return int *
     */
    int finalReviewSave(ReleaseMoneyRecord releaseMoneyRecord);
}
