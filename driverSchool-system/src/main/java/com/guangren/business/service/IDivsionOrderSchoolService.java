package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.DivsionOrderSchool;

import java.util.List;

/**
 * 分账学校订单Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface IDivsionOrderSchoolService extends IService<DivsionOrderSchool>
{
    /**
     * 查询分账学校订单
     * 
     * @param id 分账学校订单主键
     * @return 分账学校订单
     */
    public DivsionOrderSchool selectDivsionOrderSchoolById(Long id);

    /**
     * 查询分账学校订单列表
     * 
     * @param divsionOrderSchool 分账学校订单
     * @return 分账学校订单集合
     */
    public List<DivsionOrderSchool> selectDivsionOrderSchoolList(DivsionOrderSchool divsionOrderSchool);

    /**
     * 新增分账学校订单
     * 
     * @param divsionOrderSchool 分账学校订单
     * @return 结果
     */
    public int insertDivsionOrderSchool(DivsionOrderSchool divsionOrderSchool);

    /**
     * 修改分账学校订单
     * 
     * @param divsionOrderSchool 分账学校订单
     * @return 结果
     */
    public int updateDivsionOrderSchool(DivsionOrderSchool divsionOrderSchool);


    /**
     * 验证参数唯一性
     */
    //public String checkUnique(DivsionOrderSchool divsionOrderSchool);

    /**
     * 批量删除分账学校订单
     * 
     * @param ids 需要删除的分账学校订单主键集合
     * @return 结果
     */
    public int deleteDivsionOrderSchoolByIds(String ids);

    /**
     * 删除分账学校订单信息
     * 
     * @param id 分账学校订单主键
     * @return 结果
     */
    public int deleteDivsionOrderSchoolById(Long id);


    public void release(List<DivsionOrderSchool> orderSchoolList);

    public void checkReleaseOrder(List<DivsionOrderSchool> orderSchoolList);


    void refundOrder(List<DivsionOrderSchool> orderSchoolList);
}
