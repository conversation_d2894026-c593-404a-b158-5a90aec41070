package com.guangren.business.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.*;
import com.guangren.business.vo.SuperviseCountVo;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 学员交费总Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface ISupervisePayService extends IService<SupervisePay>
{
    /**
     * 查询学员交费总
     * 
     * @param studentId 学员交费总主键
     * @return 学员交费总
     */
    public SupervisePay selectSupervisePayByStudentId(String studentId);

    /**
     * 查询学员交费总列表
     * 
     * @param supervisePay 学员交费总
     * @return 学员交费总集合
     */
    public List<SupervisePay> selectSupervisePayList(SupervisePay supervisePay);

    /**
     * 新增学员交费总
     * 
     * @param supervisePay 学员交费总
     * @return 结果
     */
    public int insertSupervisePay(SupervisePay supervisePay);

    /**
     * 修改学员交费总
     * 
     * @param supervisePay 学员交费总
     * @return 结果
     */
    public int updateSupervisePay(SupervisePay supervisePay);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SupervisePay supervisePay);

    /**
     * 批量删除学员交费总
     * 
     * @param studentIds 需要删除的学员交费总主键集合
     * @return 结果
     */
    public int deleteSupervisePayByStudentIds(String studentIds);

    /**
     * 删除学员交费总信息
     * 
     * @param studentId 学员交费总主键
     * @return 结果
     */
    public int deleteSupervisePayByStudentId(String studentId);

    public Map<String, Object> importStudentPayData(MultipartFile file,SupervisePay supervisePay);

    /**
     * 获取监管统计数据
     */
    SuperviseCountVo getSuperviseCount();

    /**
     * 导出释放记录列表
     */
    List<SupervisePay> exportSupervisePayList(SupervisePay supervisePay);

    /**
     * 导出学员交费总列表
     * <AUTHOR>
     * @date 2023/10/18 11:35
     * @param supervisePay *
     * @return java.util.List<com.guangren.business.domain.SupervisePay> *
     */
    List<SupervisePay> selectExportSupervisePayList(SupervisePay supervisePay);


    public void withdrawSuccess(String studentId,String releaseAccountName,String releaseAccountNo,
    		Integer trainphase,BigDecimal withdrawAmount,
    		Integer releaseType,String releaseOrderNo,String bankOrderNo) throws Exception;



    public void withdrawFail(String studentId,String releaseAccountName,String releaseAccountNo,
                                Integer trainphase,BigDecimal withdrawAmount,
                                Integer releaseType,String releaseOrderNo,String bankOrderNo,String errMsg) throws Exception;

    /**
     * 释放资金申请保存
     * <AUTHOR>
     * @date 2023/12/8 16:49
     * @param supervisePay  *
     */
    void releaseMoneyApplySave(SupervisePay supervisePay);
}
