
package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolCar;
import com.guangren.business.vo.SchoolCarImportVo;
import com.guangren.business.vo.importTempete.SchoolCarOfficalTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 车辆管理Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface ISchoolCarService extends IService<SchoolCar>
{
    /**
     * 查询车辆管理
     * 
     * @param id 车辆管理主键
     * @return 车辆管理
     */
    public SchoolCar selectSchoolCarById(String id);

    /**
     * 查询车辆管理列表
     * 
     * @param schoolCar 车辆管理
     * @return 车辆管理集合
     */
    public List<SchoolCar> selectSchoolCarList(SchoolCar schoolCar);

    /**
     * 新增车辆管理
     * 
     * @param schoolCar 车辆管理
     * @return 结果
     */
    public int insertSchoolCar(SchoolCar schoolCar);

    /**
     * 修改车辆管理
     * 
     * @param schoolCar 车辆管理
     * @return 结果
     */
    public int updateSchoolCar(SchoolCar schoolCar);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SchoolCar schoolCar);

    /**
     * 批量删除车辆管理
     * 
     * @param ids 需要删除的车辆管理主键集合
     * @return 结果
     */
    public int deleteSchoolCarByIds(String ids);

    /**
     * 删除车辆管理信息
     * 
     * @param id 车辆管理主键
     * @return 结果
     */
    public int deleteSchoolCarById(String id);

    public SchoolCarOfficalTemplate importCar(MultipartFile file)throws Exception;

    /**
     * 导出车辆管理列表
     */
    List<SchoolCar> exportSchoolCarList(SchoolCar schoolCar);
    /**
     * 批量提交二维码审核申请
     * @param ids
     * @return
     */
    int submitAudit(String ids);
    /**
     * 批量审核二维码申请
     * @param ids 主表ID
     * @param auditStatus 设置的审核状态
     * @return
     */
    int batchAudit(String ids, Integer auditStatus);
    /**
     * 下载二维码图片
     * @param response
     * @param ids
     */
    void downloadQrcode(HttpServletResponse response, String ids);

    /**
     * 查看是否存在过期或未审核的二维码信息
     * @param id 业务ID
     * @return
     */
    SchoolCar existnotPassQrcode(String id);
}
