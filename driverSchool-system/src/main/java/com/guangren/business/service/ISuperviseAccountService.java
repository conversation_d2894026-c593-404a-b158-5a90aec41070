package com.guangren.business.service;

import java.util.List;

import com.guangren.business.domain.SuperviseAccount;

/**
 * 收款账户Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface ISuperviseAccountService
{
    /**
     * 查询收款账户
     * 
     * @param id 收款账户主键
     * @return 收款账户
     */
    public SuperviseAccount selectSuperviseAccountById(String id);

    /**
     * 查询收款账户列表
     * 
     * @param superviseAccount 收款账户
     * @return 收款账户集合
     */
    public List<SuperviseAccount> selectSuperviseAccountList(SuperviseAccount superviseAccount);

    /**
     * 新增收款账户
     * 
     * @param superviseAccount 收款账户
     * @return 结果
     */
    public int insertSuperviseAccount(SuperviseAccount superviseAccount);

    /**
     * 修改收款账户
     * 
     * @param superviseAccount 收款账户
     * @return 结果
     */
    public int updateSuperviseAccount(SuperviseAccount superviseAccount);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SuperviseAccount superviseAccount);

    /**
     * 批量删除收款账户
     * 
     * @param ids 需要删除的收款账户主键集合
     * @return 结果
     */
    public int deleteSuperviseAccountByIds(String ids);

    /**
     * 删除收款账户信息
     * 
     * @param id 收款账户主键
     * @return 结果
     */
    public int deleteSuperviseAccountById(String id);
}
