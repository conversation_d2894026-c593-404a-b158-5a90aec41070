package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SuperviseReleaseValidList;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface ISuperviseReleaseValidListService extends IService<SuperviseReleaseValidList> {

    @Transactional
    void release(List<Integer> ids);

    void reCreate();

    /**
     * 查询释放明单
     *
     * @param id 释放明单主键
     * @return 释放明单
     */
    public SuperviseReleaseValidList selectSuperviseReleaseValidListById(Long id);

    /**
     * 查询释放明单列表
     *
     * @param superviseReleaseValidList 释放明单
     * @return 释放明单集合
     */
    public List<SuperviseReleaseValidList> selectSuperviseReleaseValidListList(SuperviseReleaseValidList superviseReleaseValidList);

    /**
     * 新增释放明单
     *
     * @param superviseReleaseValidList 释放明单
     * @return 结果
     */
    public int insertSuperviseReleaseValidList(SuperviseReleaseValidList superviseReleaseValidList);

    /**
     * 修改释放明单
     *
     * @param superviseReleaseValidList 释放明单
     * @return 结果
     */
    public int updateSuperviseReleaseValidList(SuperviseReleaseValidList superviseReleaseValidList);

    /**
     * 批量删除释放明单
     *
     * @param ids 需要删除的释放明单主键集合
     * @return 结果
     */
    public int deleteSuperviseReleaseValidListByIds(String ids);

    /**
     * 删除释放明单信息
     *
     * @param id 释放明单主键
     * @return 结果
     */
    public int deleteSuperviseReleaseValidListById(Long id);

    void checkbankTrans(List<Integer> ids) throws Exception;

    void bankReturnMoneyOperate(List<Integer> ids);
}
