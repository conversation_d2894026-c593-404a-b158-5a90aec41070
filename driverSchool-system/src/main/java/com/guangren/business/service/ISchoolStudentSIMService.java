package com.guangren.business.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolStudentSIM;
import com.guangren.business.vo.BatchSendSimResponseVo;
import com.guangren.business.vo.SchoolStudentSimQueryVo;
import com.guangren.business.vo.StudentSimCountVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ISchoolStudentSIMService extends IService<SchoolStudentSIM> {
	
	
	public boolean sendMsg(String [] mobile,String smsTemplateId,String [] params);
	
	/**
	 * 开新卡
	 * @return JSONObject
	 * {
	 *   "respcode": "0", 
	 *   "respdesc": "成功", 
	 *   "resptype": "0"
	 * }
	 * */
	public JSONObject addMobileNumber(SchoolStudentSIM sim);
	
	/**
	 * 查询可选手机号列表
	 * [{
	 * 	mobileno 手机号
	 *  commid 商品号
	 * }]
	 * */
	public JSONArray QueryMobileNumberList(int page);
	/**
	 * 查询号码开卡详情
	 * @return JSONObject {
	 * 	orderId 订单ID
	 *  orderStatus  订单状态
	 *  createTime 下单时间 
	 *  finishTime 订单完成时间
	 *  servnumber 服务号码
	 *  expressno 物流配送单号
	 * }
	 * */
	public JSONObject QuerySimMobileDetail(String simMobile);
	/**
	 * 查询物流信息
	 * @return JSONArray
	 * [{
	 * 	traceinfo 路由节点
	 *  traceinfo 路由时间
	 * }]
	 * */
	public JSONObject QueryExpressInfo(String simMobile);
	
	/**
	 * 检查地址是否可以京东配送
	 *  @param cityCode 为以下值
	 *  广州 020
	 *	汕尾 660
	 *	阳江 662
	 *	揭阳 663
	 *	茂名 668
	 *	江门 750
	 *	韶关 751
	 *	惠州 752
	 *	梅州 753
	 *	汕头 754
	 *	深圳 755
	 *	珠海 756
	 *	佛山 757
	 *	肇庆 758
	 *	湛江 759
	 *	中山 760
	 *	河源 762
	 *	清远 763
	 *	云浮 766
	 *	潮州 768
	 *	东莞 769
	 * */
	public boolean checkAddressAvailable(String cityCode,String address);
	/**
	 * 发送验证码
	 * @param mobileno 手机号
	 * @param validateCode 发送的验证码
	 * */
	public boolean sendValidateCode(String mobileno,String validateCode);
	
	
	public boolean xuanwuSendValidateCode(String mobileno,String validateCode,String type);


	/**
	 * 查询运营数据
	 *
	 * @param studentId 运营数据主键
	 * @return 运营数据
	 */
	public SchoolStudentSIM selectSchoolStudentSimByStudentId(String studentId);

	/**
	 * 查询运营数据列表
	 *
	 * @param schoolStudentSim 运营数据
	 * @return 运营数据集合
	 */
	public List<SchoolStudentSIM> selectSchoolStudentSimList(SchoolStudentSIM schoolStudentSim);

	public Long customCount(QueryWrapper<SchoolStudentSIM> eq);

	/**
	 * 批量删除运营开卡数据
	 * @param ids 学员移动开卡数据主键
	 * @return 结果
	 */
    int deleteSchoolByIds(String ids);

	/**
	 * 开卡统计数据
	 */
	StudentSimCountVo getSchoolStudentSimCount(SchoolStudentSimQueryVo schoolStudentSimQueryVo);

	/**
	 * 查询补贴数据
	 */
	List<SchoolStudentSIM> selectSchoolStudentSubsidyList(SchoolStudentSIM schoolStudentSim);

	/**
	 * 查询待处理数据列表
	 */
	List<SchoolStudentSIM> selectPendingStudentSimList(SchoolStudentSIM schoolStudentSim);

	/**
	 * 导出运营数据列表
	 */
	List<SchoolStudentSIM> exportSchoolStudentSimList(SchoolStudentSimQueryVo schoolStudentSimQueryVo);

	/**
	 * 导出补贴数据列表
	 */
//	List<SchoolStudentSIM> exportSchoolStudentSubsidyList(SchoolStudentSimQueryVo schoolStudentSimQueryVo);

	/**
	 * 导出待处理数据列表
	 */
//	List<SchoolStudentSIM> exportPendingStudentSimList(SchoolStudentSimQueryVo schoolStudentSimQueryVo);

	/**
	 * 导入补贴数据
	 */
//    int importStudentSubsidyData(MultipartFile file);

	/**
	 * 导入待处理数据
	 */
//	int importPendingStudentSimData(MultipartFile file);

	/**
	 * 不开卡操作
	 */
	boolean notOpenCard(String id);

	/**
	 * 导入批量开卡数据
	 */
	BatchSendSimResponseVo importBatchOpenSimData(MultipartFile excel);

	/**
	 * 导入批量不开卡数据
	 */
	int importBatchNoOpenSim(MultipartFile excel);

	/**
	 * 导入运营数据
	 */
    int importStudentSimData(MultipartFile excel);

	boolean xuanwuSendMsg(String mobileno, String msg);

	/**
     * 导入批量未发起数据
     * <AUTHOR>
     * @date 2023/12/16 9:26
     * @param file *
     * @return int *
     */
    int importNoInitSim(MultipartFile file);
}
