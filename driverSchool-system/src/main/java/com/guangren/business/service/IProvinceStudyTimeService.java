package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.ProvinceStudyTime;

import java.util.List;
import java.util.Map;

public interface IProvinceStudyTimeService extends IService<ProvinceStudyTime> {

    /**
     * 查询省厅数据
     * <AUTHOR>
     * @date 2023/9/27 9:40
     * @param provinceStudyTime *
     * @return java.util.List<com.guangren.business.domain.SchoolStudentSIM>
     */
    List<ProvinceStudyTime> selectProvinceStudyTimeList(ProvinceStudyTime provinceStudyTime);

    List<Map<String, Object>> getReleaseStudentList();
}
