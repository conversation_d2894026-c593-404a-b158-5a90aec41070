package com.guangren.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guangren.business.domain.SchoolRank;
import com.guangren.business.mapper.SchoolRankMapper;
import com.guangren.business.service.ISchoolRankService;
import com.guangren.common.core.text.Convert;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 驾校排名Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class SchoolRankServiceImpl extends ServiceImpl<SchoolRankMapper, SchoolRank> implements ISchoolRankService {

    @Resource
    private SchoolRankMapper schoolRankMapper;

    /**
     * 查询驾校排名
     *
     * @param id 驾校排名主键
     * @return 驾校排名
     */
    @Override
    public SchoolRank selectSchoolRankById(String id) {
        return schoolRankMapper.selectSchoolRankById(id);
    }

    /**
     * 查询驾校排名列表
     *
     * @param schoolRank 驾校排名
     * @return 驾校排名
     */
    @Override
    public List<SchoolRank> selectSchoolRankList(SchoolRank schoolRank) {
        return schoolRankMapper.selectSchoolRankList(schoolRank);
    }

    /**
     * 新增驾校排名
     *
     * @param schoolRank 驾校排名
     * @return 结果
     */
    @Override
    public int insertSchoolRank(SchoolRank schoolRank) {
        return schoolRankMapper.insertSchoolRank(schoolRank);
    }

    /**
     * 修改驾校排名
     *
     * @param schoolRank 驾校排名
     * @return 结果
     */
    @Override
    public int updateSchoolRank(SchoolRank schoolRank) {
        return schoolRankMapper.updateSchoolRank(schoolRank);
    }

    /**
     * 批量删除驾校排名
     *
     * @param ids 需要删除的驾校排名主键
     * @return 结果
     */
    @Override
    public int deleteSchoolRankByIds(String ids) {
        return schoolRankMapper.deleteSchoolRankByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除驾校排名信息
     *
     * @param id 驾校排名主键
     * @return 结果
     */
    @Override
    public int deleteSchoolRankById(String id) {
        return schoolRankMapper.deleteSchoolRankById(id);
    }
}
