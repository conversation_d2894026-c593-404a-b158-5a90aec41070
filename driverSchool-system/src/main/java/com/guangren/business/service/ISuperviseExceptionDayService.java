package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.domain.SuperviseExceptionDay;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 资金监管异常记录Service接口
 * 
 * <AUTHOR>
 * @date 2023-04-12
 */
public interface ISuperviseExceptionDayService extends IService<SuperviseExceptionDay>
{
    /**
     * 查询资金监管异常记录
     * 
     * @param id 资金监管异常记录主键
     * @return 资金监管异常记录
     */
    public SuperviseExceptionDay selectSuperviseExceptionDayById(String id);

    /**
     * 查询资金监管异常记录列表
     * 
     * @param superviseExceptionDay 资金监管异常记录
     * @return 资金监管异常记录集合
     */
    public List<SuperviseExceptionDay> selectSuperviseExceptionDayList(SuperviseExceptionDay superviseExceptionDay);

    /**
     * 新增资金监管异常记录
     * 
     * @param superviseExceptionDay 资金监管异常记录
     * @return 结果
     */
    public int insertSuperviseExceptionDay(SuperviseExceptionDay superviseExceptionDay);

    /**
     * 修改资金监管异常记录
     * 
     * @param superviseExceptionDay 资金监管异常记录
     * @return 结果
     */
    public int updateSuperviseExceptionDay(SuperviseExceptionDay superviseExceptionDay);


    /**
     * 批量删除资金监管异常记录
     * 
     * @param ids 需要删除的资金监管异常记录主键集合
     * @return 结果
     */
    public int deleteSuperviseExceptionDayByIds(String ids);

    /**
     * 删除资金监管异常记录信息
     * 
     * @param id 资金监管异常记录主键
     * @return 结果
     */
    public int deleteSuperviseExceptionDayById(String id);
    
    
    public void addReleaseFail(BigDecimal releaseFee, Date releaseDate, SchoolStudent student, String errMsg);

    public void addReleaseFail(BigDecimal releaseFee, Date releaseDate, String errMsg,String schoolId);
}
