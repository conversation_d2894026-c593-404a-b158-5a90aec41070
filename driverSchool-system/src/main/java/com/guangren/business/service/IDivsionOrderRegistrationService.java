package com.guangren.business.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.DivsionOrderRegistration;

/**
 * 分账门店订单Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface IDivsionOrderRegistrationService extends IService<DivsionOrderRegistration>
{
    /**
     * 查询分账门店订单
     * 
     * @param id 分账门店订单主键
     * @return 分账门店订单
     */
    public DivsionOrderRegistration selectDivsionOrderRegistrationById(Long id);

    /**
     * 查询分账门店订单列表
     * 
     * @param divsionOrderRegistration 分账门店订单
     * @return 分账门店订单集合
     */
    public List<DivsionOrderRegistration> selectDivsionOrderRegistrationList(DivsionOrderRegistration divsionOrderRegistration);

    /**
     * 新增分账门店订单
     * 
     * @param divsionOrderRegistration 分账门店订单
     * @return 结果
     */
    public int insertDivsionOrderRegistration(DivsionOrderRegistration divsionOrderRegistration);

    /**
     * 修改分账门店订单
     * 
     * @param divsionOrderRegistration 分账门店订单
     * @return 结果
     */
    public int updateDivsionOrderRegistration(DivsionOrderRegistration divsionOrderRegistration);


    /**
     * 验证参数唯一性
     */
    //public String checkUnique(DivsionOrderRegistration divsionOrderRegistration);

    /**
     * 批量删除分账门店订单
     * 
     * @param ids 需要删除的分账门店订单主键集合
     * @return 结果
     */
    public int deleteDivsionOrderRegistrationByIds(String ids);

    /**
     * 删除分账门店订单信息
     * 
     * @param id 分账门店订单主键
     * @return 结果
     */
    public int deleteDivsionOrderRegistrationById(Long id);


    public void release(List<DivsionOrderRegistration> orderRegistrationList);

    public void checkReleaseOrder(List<DivsionOrderRegistration> orderRegistrationList);
}
