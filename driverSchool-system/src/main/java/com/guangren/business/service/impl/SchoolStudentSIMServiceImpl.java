package com.guangren.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.asiainfo.bean.AsiainfoHeader;
import com.asiainfo.httpsrest.RestHttpclient;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guangren.business.config.GrowinGioConfiguration;
import com.guangren.business.domain.SchoolStudentSIM;
import com.guangren.business.mapper.SchoolStudentSIMMapper;
import com.guangren.business.service.GroGioService;
import com.guangren.business.service.ISchoolStudentSIMService;
import com.guangren.business.vo.BatchSendSimImportVo;
import com.guangren.business.vo.BatchSendSimResponseVo;
import com.guangren.business.vo.SchoolStudentSimQueryVo;
import com.guangren.business.vo.StudentSimCountVo;
import com.guangren.business.vo.StudentSimImportVo;
import com.guangren.common.constant.Constants;
import com.guangren.common.core.text.Convert;
import com.guangren.common.exception.BusinessException;
import com.guangren.common.utils.DateUtil;
import com.guangren.common.utils.StringUtils;
import com.guangren.common.utils.bean.BeanUtils;
import com.guangren.common.utils.poi.ExcelUtil;
import com.mascloud.sdkclient.Client;
import com.xuanwu.mos.MessageData;
import com.xuanwu.mos.PostMsg;
import com.xuanwu.mos.PostMsgBuilder;
import com.xuanwu.mos.common.entity.Account;
import com.xuanwu.mos.common.entity.GsmsResponse;
import com.xuanwu.mos.common.entity.MTPack;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.locks.ReentrantLock;

@Service
public class SchoolStudentSIMServiceImpl extends ServiceImpl<SchoolStudentSIMMapper, SchoolStudentSIM> implements ISchoolStudentSIMService{

	private static final Logger log = LoggerFactory.getLogger(SchoolStudentSIMServiceImpl.class);
	
	@Value("${ruoyi.yidong.appId}")
	private String yidongAppId;
	@Value("${ruoyi.yidong.prefix-url}")
	private String yidongPrefixUrl;
	@Value("${ruoyi.yidong.wayId}")
	private String yidongWayId;
	@Value("${ruoyi.yidong.operatedId}")
	private String yidongOperatedId;
	
	@Value("${ruoyi.yidong.privateKey}")
	private String yidongPrivateKey;
	
	@Resource
	private SchoolStudentSIMMapper schoolStudentSimMapper;
	
	@Value("${ruoyi.yidong.productId}")
	private String yidongProductId;
	
	@Value("${ruoyi.yidong.recommendAreaId}")
	private String yidongRecommendAreaId;
	
	@Value("${ruoyi.yidong.sms.url}")
	private String smsUrl;
	
	@Value("${ruoyi.yidong.sms.username}")
	private String smsUsername;
	
	@Value("${ruoyi.yidong.sms.password}")
	private String smsPassword;
	
	@Value("${ruoyi.yidong.sms.companyName}")
	private String smsCompanyName;
	
	@Value("${ruoyi.yidong.sms.templateId}")
	private String smsTemplateId;
	
	@Value("${ruoyi.yidong.sms.sign}")
	private String smsSign;
	
	@Value("${ruoyi.yidong.wayContactcode}")
	private String wayContactcode;
	
	@Value("${ruoyi.yidong.waysubContactcode}")
	private String waysubContactcode;
	
	@Value("${ruoyi.xuanwu.username}")
	private String smsXuanwuUsername;
	@Value("${ruoyi.xuanwu.password}")
	private String smsXuanwuPassword;
	@Value("${ruoyi.xuanwu.sign}")
	private String smsXuanwuSign;

	@Resource
	private GroGioService groGioService;
	@Resource
	private GrowinGioConfiguration growinGioConfiguration;
	
	
	private AsiainfoHeader getAsiainfoHeader(String serialId){
		AsiainfoHeader header=new AsiainfoHeader();        
		header.setAppId(yidongAppId);  //根据实际情况填写
		header.setBusiSerial(serialId);  //32位
		header.setNonce(RandomStringUtils.randomAlphanumeric(32).toUpperCase()); //32位
		header.setTimestamp(DateUtil.formatDate(new Date(), "yyyyMMddHHmmssSSS"));   //当前时间     
	    return header;
	}
	
	/**
	 * 开新卡
	 * {
	 *   "respcode": "0", 
	 *   "respdesc": "成功", 
	 *   "resptype": "0"
	 * }
	 * */
	public JSONObject addMobileNumber(SchoolStudentSIM sim) {
		
		 AsiainfoHeader header = getAsiainfoHeader(sim.getId());
		 
		 log.info("header:"+header.toString());
	     String url = yidongPrefixUrl+"/OrderCenter/resource/AirpickinstallnewOrder/v1.1.1";
	     
	     JSONObject newOrderParams = new JSONObject();
	     newOrderParams.put("wayid", yidongWayId);
	     newOrderParams.put("operatorId", yidongOperatedId);
	     newOrderParams.put("areaCode", "769");
	     newOrderParams.put("areaName", "东莞");
	     newOrderParams.put("servnumber", sim.getSimMobile());
	     newOrderParams.put("userName", sim.getName());
	     newOrderParams.put("acceptType", "2");
	     newOrderParams.put("receiveType", "4");
	     newOrderParams.put("payWay", "1");
	     newOrderParams.put("telno", sim.getMobile());
	     newOrderParams.put("cerNo", sim.getIdentity());
	     newOrderParams.put("cerType", "01");
	     newOrderParams.put("province", sim.getDeliverProvince());
	     newOrderParams.put("addressCity", sim.getDeliverCity());
	     newOrderParams.put("addressArea", sim.getDeliverTown());
	     newOrderParams.put("address", sim.getDeliverAddress());
	     newOrderParams.put("mainprodid", sim.getSimProductCode());
	     newOrderParams.put("offlineCard", "3");
	     newOrderParams.put("recoid", yidongRecommendAreaId);
	     newOrderParams.put("wayContactcode", wayContactcode);
	     newOrderParams.put("waysubContactcode", waysubContactcode);
	     JSONObject msgbody = new JSONObject();
	     msgbody.put("newOrderParams", newOrderParams);
	     String body = msgbody.toJSONString();
	     log.info("addMobileNumber send body:"+body);
	     try{
	    	 String response = RestHttpclient.post(header, msgbody.toJSONString(),url,yidongPrivateKey);
	    	 log.error("addMobileNumber response:"+response);
	    	 JSONObject respJson = JSONObject.parseObject(response);
	    	 if(respJson != null && respJson.getIntValue("respcode")==0) {
	    		 ThreadUtil.execute(new Runnable() {
					public void run() {
						Map<String, Object> sendMessageParams = new HashMap<>();
			    		packGrowinGioData(sendMessageParams, sim);
						groGioService.sendMessage(sendMessageParams, sim);
					}
				});
	    	 }
	    	 return respJson;
	     }catch(Exception ex){
	    	 log.error("add mobile error",ex);
	     }
	     return null;
	}

	
	/**
	 * 封装外部渠道插码数据
	 * <AUTHOR>
	 * @date 2023/9/28 10:42
	 * @param map *
	 * @param sim *
	 */
	private void packGrowinGioData(Map<String, Object> map, SchoolStudentSIM sim) {
		map.put("apiUrl_var", "https://www.guanjiaxie.com:8089/business/studentSim"); // 调⽤接⼝的⻚⾯完整链接
		map.put("businessType_var", "放号"); // 属于放号业务则写“放号”
		map.put("goodsId_var", "1089970149115600896"); // 业务办理时所属的商品ID
		map.put("goodsName_var", "东莞99元全家享"); //业务办理时商品名称。
		map.put("skuId_var", ""); // 业务办理时所属的skuID。
		map.put("skuName_var", ""); //业务办理时sku名称。
		map.put("goodsType_var", "号卡"); //业务办理时商品类型。
		map.put("apiId_var", "AirpickinstallnewOrder"); //调⽤接⼝id
		map.put("apiName_var", "线上放号订单下单接口"); //调⽤接⼝名称。
		map.put("merchantsId_var", "GDOPEN_2067413"); //接⼝调⽤商⼾id。
		map.put("merchants_var", "东莞诚大通讯科技有限公司"); //接⼝调⽤商⼾名称。
		map.put("contactNumber_var", aesEncode(sim.getMobile()));//联系电话,传AES加密后的⼿机号
		map.put("processType_var", "提交订单成功"); // 业务办理时的每个办理步骤。（提交订单成功、提交订单失败、办理成功、办理失败、⽀付成功、⽀付失败）
		map.put("errorMessage_var", ""); // 业务办理失败时的失败原因明细。（⽹络问题、业务互斥……）
		map.put("payType_var", ""); // 业务办理⽀付时的⽀付⽅式名称。（⽀付宝、微信……）
		map.put("orderNumber_var", ""); // 业务办理⽀付完成时所属订单号。
		map.put("ordertype_var", ""); // 业务办理⽀付完成时所属订单类型名称。
		map.put("iop_tacticsId_var ", ""); // ⻚⾯访问时当前⻚⾯对应的IOP策略ID号。
		map.put("referFlowId_var ", ""); // ⻚⾯访问时当前⻚⾯运营位位置ID。
	}

	/**
	 * AES加密 ECB模式
	 * <AUTHOR>
	 * @date 2023/9/27 15:30
	 * @param content 需要加密的内容
	 * @return java.lang.String
	 */
	private String aesEncode(String content) {
		String key = growinGioConfiguration.getAesKey();
		byte[] encryptedBytes;
		try {
			SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "AES");
			Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
			cipher.init(Cipher.ENCRYPT_MODE, secretKey);
			encryptedBytes = cipher.doFinal(content.getBytes());
		}catch (Exception e) {
			log.error("AES encode error:" + e);
			throw new BusinessException("AES加密发生错误");
		}
		return Base64.getEncoder().encodeToString(encryptedBytes);
	}

	/**
	 * 查询可选手机号列表
	 * [{
	 * 	mobileno 手机号
	 *  commid 商品ID,
	 * }]
	 * */
	public JSONArray QueryMobileNumberList(int page){
		 AsiainfoHeader header = getAsiainfoHeader(UUID.randomUUID().toString().replace("-", ""));
		 log.info("header:"+header.toString());
	     String url = yidongPrefixUrl+"/BSS/commodity/querychoosenumberlist/v1.1.1";
	     JSONObject msgbody = new JSONObject();
	     msgbody.put("region", 769);
	     msgbody.put("tag", "0");
	     msgbody.put("page", page);
	     msgbody.put("packagecode", yidongProductId);
	     msgbody.put("recoid",yidongRecommendAreaId);
	     String body = msgbody.toJSONString();
	     log.info("QueryMobileNumberList send body:"+body);
	     try{
	    	 String response = RestHttpclient.post(header, msgbody.toJSONString(),url,yidongPrivateKey);
	    	 log.info("QueryMobileNumberList response:"+response);
	    	 JSONObject respJson = JSONObject.parseObject(response);
	    	 int code = respJson.getIntValue("respcode");
	    	 if(code == 0){
	    		 return respJson.getJSONObject("result").getJSONArray("infos");
	    	 }
	     }catch(Exception ex){
	    	 log.error("get mobile number list error",ex);
	     }
	     return null;
	}
	
	public static void main(String [] args) {
		SchoolStudentSIMServiceImpl serviceImpl  = new SchoolStudentSIMServiceImpl();
		JSONArray array = serviceImpl.QueryMobileNumberList(1);
		System.out.println(array.toJSONString());
		
		//SchoolStudentSIMServiceImpl test = new SchoolStudentSIMServiceImpl();
		//boolean flag = test.sendValidateCode("19928349133", "123456");
		//String encode = test.aesEncode("13921442403");
		//System.out.println("flag = " + flag);
	}
	
	/**
	 * 查询号码开卡详情
	 * {
	 * 	orderId: 342241426551841   订单ID
	 *  orderStatus: '待办理'  订单状态
	 *  createTime 下单时间 
	 *  finishTime 订单完成时间,如果为空，则未完成
	 *  servnumber 服务号码
	 *  expressno 物流配送单号
	 * }
	 * */
	public JSONObject QuerySimMobileDetail(String simMobile){
		AsiainfoHeader header = getAsiainfoHeader(UUID.randomUUID().toString().replace("-", ""));
	    String url = yidongPrefixUrl+"/OrderCenter/resource/dsairpickinstallqueryorder/v1.1.1";
	    JSONObject msgbody = new JSONObject();
	    msgbody.put("servnumber", simMobile);
	    try{
	    	 String response = RestHttpclient.post(header, msgbody.toJSONString(),url,yidongPrivateKey);
	    	 log.info("QuerySimMobileDetail:"+response);
	    	 JSONObject respJson = JSONObject.parseObject(response);
	    	 int code = respJson.getIntValue("respcode");
	    	 if(code == 0){
	    		 return respJson.getJSONObject("result").getJSONArray("order").getJSONObject(0);
	    	 }
	     }catch(Exception ex){
	    	 log.error("get mobile number list error",ex);
	     }
	     return null;
	}

	
	/**
	 * 查询物流信息
	 * [{
	 * 	content 路由节点
	 *  operationtime 路由时间
	 * }]
	 * */
	public JSONObject QueryExpressInfo(String simMobile){
		AsiainfoHeader header = getAsiainfoHeader(UUID.randomUUID().toString().replace("-", ""));
	    String url = yidongPrefixUrl+"/JD/service/qryexpresstrace/v1.1.1";
	    JSONObject msgbody = new JSONObject();
	    msgbody.put("servnumber", simMobile);
	    try{
	    	 String response = RestHttpclient.post(header, msgbody.toJSONString(),url,yidongPrivateKey);
	    	 JSONObject respJson = JSONObject.parseObject(response);
	    	 int code = respJson.getIntValue("respcode");
	    	 if(code == 0){
	    		 log.info("expressInfo:"+respJson.getJSONObject("result").getJSONArray("data").getJSONObject(0).toJSONString());	    		 
	    		 return respJson.getJSONObject("result").getJSONArray("data").getJSONObject(0);
	    	 }
	     }catch(Exception ex){
	    	 log.error("get mobile number list error",ex);
	     }
	     return null;
	}
	
	/**
	 * 检查地址是否可以京东配送
	 *  cityCode为以下值
	 *  广州 020
	 *	汕尾 660
	 *	阳江 662
	 *	揭阳 663
	 *	茂名 668
	 *	江门 750
	 *	韶关 751
	 *	惠州 752
	 *	梅州 753
	 *	汕头 754
	 *	深圳 755
	 *	珠海 756
	 *	佛山 757
	 *	肇庆 758
	 *	湛江 759
	 *	中山 760
	 *	河源 762
	 *	清远 763
	 *	云浮 766
	 *	潮州 768
	 *	东莞 769
	 * */
	public boolean checkAddressAvailable(String cityCode,String address){
		AsiainfoHeader header = getAsiainfoHeader(UUID.randomUUID().toString().replace("-", ""));
	    String url = yidongPrefixUrl+"/JD/service/jdcheckaddress/v1.1.1";
	    JSONObject msgbody = new JSONObject();
	    msgbody.put("provinceCode", "200");
	    msgbody.put("eparchyCode", cityCode);
	    msgbody.put("address", address);
	    try{
	    	 String response = RestHttpclient.post(header, msgbody.toJSONString(),url,yidongPrivateKey);
	    	 JSONObject respJson = JSONObject.parseObject(response);
	    	 int code = respJson.getIntValue("respcode");
	    	 if(code == 0){
	    		 return true;
	    	 }
	     }catch(Exception ex){
	    	 log.error("get mobile number list error",ex);
	     }
	     return false;
	}
	
	@Override
	public boolean sendValidateCode(String mobileno,String validateCode){
		Client client = Client.getInstance();
		boolean  isSuccess = client.login(smsUrl, smsUsername, smsPassword, smsCompanyName);
		if(isSuccess){
			int rtm = client.sendTSMS( new String[]{ mobileno }, smsTemplateId, new String[]{validateCode}, "", 1, smsSign, null);
			return rtm==1;
		}
	     return false;
	}
	@Override
	public boolean sendMsg(String [] mobile,String smsTemplateId,String [] params){
		Client client = Client.getInstance();
		boolean  isSuccess = client.login(smsUrl, smsUsername, smsPassword, smsCompanyName);
		if(isSuccess){
			int rtm = client.sendTSMS( mobile, smsTemplateId, params, "", 1, smsSign, null);
			return rtm==1;
		}
	    return false;
	}
	
	

	/**
	 * 查询运营数据
	 *
	 * @param studentId 运营数据主键
	 * @return 运营数据
	 */
	@Override
	public SchoolStudentSIM selectSchoolStudentSimByStudentId(String studentId)
	{
		return schoolStudentSimMapper.selectSchoolStudentSimByStudentId(studentId);
	}

	/**
	 * 查询运营数据列表
	 *
	 * @param schoolStudentSim 运营数据
	 * @return 运营数据
	 */
	@Override
	public List<SchoolStudentSIM> selectSchoolStudentSimList(SchoolStudentSIM schoolStudentSim)
	{
		return schoolStudentSimMapper.selectSchoolStudentSimList(schoolStudentSim);
	}

	@Override
	public Long customCount(QueryWrapper<SchoolStudentSIM> eq) {
		return schoolStudentSimMapper.customCount(eq);
	}

	/**
	 * 批量删除运营开卡数据
	 * @param ids 学员移动开卡数据主键
	 * @return 结果
	 */
	@Override
	public int deleteSchoolByIds(String ids) {
		String[] idsArr = Convert.toStrArray(ids);
		List< String> idsList = new ArrayList<>(idsArr.length);
		Collections.addAll(idsList, idsArr);
		return schoolStudentSimMapper.deleteBatchIds(idsList);
	}

	/**
	 * 开卡统计数据
	 */
	@Override
	public StudentSimCountVo getSchoolStudentSimCount(SchoolStudentSimQueryVo schoolStudentSimQueryVo) {
		return schoolStudentSimMapper.getSchoolStudentSimCount(schoolStudentSimQueryVo);
	}

	/**
	 * 查询补贴数据
	 */
	@Override
	public List<SchoolStudentSIM> selectSchoolStudentSubsidyList(SchoolStudentSIM schoolStudentSim) {
		return schoolStudentSimMapper.selectSchoolStudentSubsidyList(schoolStudentSim);
	}

	/**
	 * 查询待处理数据列表
	 */
	@Override
	public List<SchoolStudentSIM> selectPendingStudentSimList(SchoolStudentSIM schoolStudentSim) {
		return schoolStudentSimMapper.selectPendingStudentSimList(schoolStudentSim);
	}

	/**
	 * 导出运营数据列表
	 */
	@Override
	public List<SchoolStudentSIM> exportSchoolStudentSimList(SchoolStudentSimQueryVo schoolStudentSimQueryVo) {
		return schoolStudentSimMapper.exportSchoolStudentSimList(schoolStudentSimQueryVo);
	}

	/**
	 * 导出补贴数据列表
	 */
//	@Override
//	public List<SchoolStudentSIM> exportSchoolStudentSubsidyList(SchoolStudentSimQueryVo schoolStudentSimQueryVo) {
//		return schoolStudentSimMapper.exportSchoolStudentSubsidyList(schoolStudentSimQueryVo);
//	}

	/**
	 * 导出补贴数据列表
	 */
//	@Override
//	public List<SchoolStudentSIM> exportPendingStudentSimList(SchoolStudentSimQueryVo schoolStudentSimQueryVo) {
//		return schoolStudentSimMapper.exportPendingStudentSimList(schoolStudentSimQueryVo);
//	}

	/**
	 * 导入补贴数据
	 */
//	@Override
//	public int importStudentSubsidyData(MultipartFile excel) {
//		int count = 0;
//		try {
//			// 获取列表
//			ExcelUtil<StudentSubsidyImportVo> util = new ExcelUtil<>(StudentSubsidyImportVo.class);
//			List<StudentSubsidyImportVo> subsidyImportVoList = util.importExcel(excel.getInputStream());
//			if (StringUtils.isNull(subsidyImportVoList) || subsidyImportVoList.size() == 0) {
//				throw new BusinessException("导入数据不能为空！");
//			}
//
//			List<SchoolStudentSIM> schoolStudentSIMS = new ArrayList<>();
//
//			for (int i = 0; i < subsidyImportVoList.size(); i++) {
//				int row = i + 1;
//				String info = "第" + row + "条数据，";
//				StudentSubsidyImportVo studentSubsidyImportVo = subsidyImportVoList.get(i);
//				if (studentSubsidyImportVo == null) {
//					throw new BusinessException("导入数据不能为空！");
//				}
//
//				if (StringUtils.isEmpty(studentSubsidyImportVo.getName())) {
//					throw new BusinessException(info + "学员名称不能为空！");
//				}
//
//				if (StringUtils.isEmpty(studentSubsidyImportVo.getIdentity())) {
//					throw new BusinessException(info + "身份证号不能为空！");
//				}
//
//				if (StringUtils.isEmpty(studentSubsidyImportVo.getSimMobile())) {
//					throw new BusinessException(info + "学员卡号不能为空！");
//				}
//
//				SchoolStudentSIM sim = schoolStudentSimMapper.selectOne(new LambdaQueryWrapper<SchoolStudentSIM>()
//						.eq(SchoolStudentSIM::getName, studentSubsidyImportVo.getName())
//						.eq(SchoolStudentSIM::getIdentity, studentSubsidyImportVo.getIdentity())
//						.eq(SchoolStudentSIM::getSimMobile, studentSubsidyImportVo.getSimMobile()));
//
//				if (ObjectUtil.isNull(sim)) {
//					throw new BusinessException(info + "数据库中不存在该学员数据，请确认学员名称、身份证号和学员卡号是否正确");
//				}
//
//				//格式化数据
//				switch (studentSubsidyImportVo.getIsAddWechat()) {
//					case "否" : sim.setIsAddWechat(0); break;
//					case "是" : sim.setIsAddWechat(1); break;
//				}
//
//				switch (studentSubsidyImportVo.getIsReturnCash()) {
//					case "否" : sim.setIsReturnCash(0); break;
//					case "是" : sim.setIsReturnCash(1); break;
//				}
//
//				switch (studentSubsidyImportVo.getReturnCashFailReason()) {
//					case "学员未充值" : sim.setReturnCashFailReason(1); break;
//					case "学员分开充值" : sim.setReturnCashFailReason(2); break;
//					case "学员不加企微）" : sim.setReturnCashFailReason(3); break;
//				}
//
//				switch (studentSubsidyImportVo.getReturnCashIsCallOut()) {
//					case "无" : sim.setReturnCashIsCallOut(0); break;
//					case "有" : sim.setReturnCashIsCallOut(1); break;
//				}
//
//				switch (studentSubsidyImportVo.getIsDynamic()) {
//					case "否" : sim.setIsDynamic(0); break;
//					case "是" : sim.setIsDynamic(1); break;
//				}
//
//				switch (studentSubsidyImportVo.getSimCardStatus()) {
//					case "未返现未活跃" : sim.setSimCardStatus(1); break;
//					case "未返现已活跃" : sim.setSimCardStatus(2); break;
//					case "已返现未活跃" : sim.setSimCardStatus(3); break;
//					case "已返现已活跃" : sim.setSimCardStatus(4); break;
//					case "已活跃营销失败" : sim.setSimCardStatus(5); break;
//					case "已活跃营销成功" : sim.setSimCardStatus(6); break;
//				}
//
//				switch (studentSubsidyImportVo.getIsAddCallOutSale()) {
//					case "否" : sim.setIsAddCallOutSale(0); break;
//					case "是" : sim.setIsAddCallOutSale(1); break;
//				}
//				schoolStudentSIMS.add(sim);
//			}
//
//			for (SchoolStudentSIM schoolStudentSIM : schoolStudentSIMS) {
//				int update = schoolStudentSimMapper.updateById(schoolStudentSIM);
//				count += update;
//			}
//
//			return count;
//
//			} catch(Exception e){
//				throw new BusinessException(e.getMessage());
//			}
//	}

	/**
	 * 导入待处理数据
	 */
//	@Override
//	public int importPendingStudentSimData(MultipartFile excel) {
//		int count = 0;
//		try {
//			// 获取列表
//			ExcelUtil<PendingSchoolStudentSimImportVo> util = new ExcelUtil<>(PendingSchoolStudentSimImportVo.class);
//			List<PendingSchoolStudentSimImportVo> pendingSchoolStudentSimImportVos = util.importExcel(excel.getInputStream());
//			if (StringUtils.isNull(pendingSchoolStudentSimImportVos) || pendingSchoolStudentSimImportVos.size() == 0) {
//				throw new BusinessException("导入数据不能为空！");
//			}
//			List<SchoolStudentSIM> schoolStudentSIMS = new ArrayList<>();
//			for (int i = 0; i < pendingSchoolStudentSimImportVos.size(); i++) {
//				int row = i + 1;
//				String info = "第" + row + "条数据，";
//				PendingSchoolStudentSimImportVo pendingSchoolStudentSimImportVo = pendingSchoolStudentSimImportVos.get(i);
//				if (pendingSchoolStudentSimImportVo == null) {
//					throw new BusinessException("导入数据不能为空！");
//				}
//
//				if (StringUtils.isEmpty(pendingSchoolStudentSimImportVo.getName())) {
//					throw new BusinessException(info + "学员名称不能为空！");
//				}
//
//				if (StringUtils.isEmpty(pendingSchoolStudentSimImportVo.getIdentity())) {
//					throw new BusinessException(info + "身份证号不能为空！");
//				}
//
//				if (StringUtils.isEmpty(pendingSchoolStudentSimImportVo.getSimMobile())) {
//					throw new BusinessException(info + "学员卡号不能为空！");
//				}
//
//				SchoolStudentSIM sim = schoolStudentSimMapper.selectOne(new LambdaQueryWrapper<SchoolStudentSIM>()
//						.eq(SchoolStudentSIM::getName, pendingSchoolStudentSimImportVo.getName())
//						.eq(SchoolStudentSIM::getIdentity, pendingSchoolStudentSimImportVo.getIdentity())
//						.eq(SchoolStudentSIM::getSimMobile, pendingSchoolStudentSimImportVo.getSimMobile()));
//
//				if (ObjectUtil.isNull(sim)) {
//					throw new BusinessException(info + "数据库中不存在该学员数据，请确认学员名称、身份证号和学员卡号是否正确");
//				}
//
//				switch (pendingSchoolStudentSimImportVo.getIsAddWechat()) {
//					case "否" : sim.setIsAddWechat(0); break;
//					case "是" : sim.setIsAddWechat(1); break;
//				}
//
//				switch (pendingSchoolStudentSimImportVo.getIsCallOut()) {
//					case "无" : sim.setIsCallOut(0); break;
//					case "有" : sim.setIsCallOut(1); break;
//				}
//
//				switch (pendingSchoolStudentSimImportVo.getCallOutInfo()) {
//					case "学员拒接" : sim.setCallOutInfo(1); break;
//					case "学员拒绝开卡" : sim.setCallOutInfo(2); break;
//					case "学员不知情" : sim.setCallOutInfo(3); break;
//					case "学员同意" : sim.setCallOutInfo(4); break;
//					case "满五户" : sim.setCallOutInfo(5); break;
//					case "黑户" : sim.setCallOutInfo(6); break;
//				}
//
//				switch (pendingSchoolStudentSimImportVo.getFailReason()) {
//					case "学员原因" : sim.setFailReason(1); break;
//					case "系统原因" : sim.setFailReason(2); break;
//					case "快递原因" : sim.setFailReason(3); break;
//					case "门店原因" : sim.setFailReason(4); break;
//				}
//
//				schoolStudentSIMS.add(sim);
//			}
//
//			for (SchoolStudentSIM schoolStudentSIM : schoolStudentSIMS) {
//				int update = schoolStudentSimMapper.updateById(schoolStudentSIM);
//				count += update;
//			}
//
//			return count;
//
//		} catch(Exception e){
//			throw new BusinessException(e.getMessage());
//		}
//	}

	/**
	 * 不开卡操作
	 */
	@Override
	public boolean notOpenCard(String id) {
		SchoolStudentSIM schoolStudentSIM = new SchoolStudentSIM();
		schoolStudentSIM.setId(id);
		schoolStudentSIM.setIsNotOpenCard(2);
		return schoolStudentSimMapper.updateById(schoolStudentSIM) > 0;
	}
	
	private ReentrantLock lock = new ReentrantLock();

	/**
	 * 导入批量开卡数据
	 */
	@Override
	public BatchSendSimResponseVo importBatchOpenSimData(MultipartFile excel) {
		try {
			// 获取表格数据
			lock.lock();
			ExcelUtil<BatchSendSimImportVo> util = new ExcelUtil<>(BatchSendSimImportVo.class);
			List<BatchSendSimImportVo> batchSendSimImportVoList = util.importExcel(excel.getInputStream());

			if (batchSendSimImportVoList.size() > 50) {
				throw new BusinessException("批量开卡数据导入每次最多导入50条数据");
			}

			//开卡成功的数据
			ArrayList<SchoolStudentSIM> successList = new ArrayList<>(16);

			//开卡失败的数据
			ArrayList<SchoolStudentSIM> failList = new ArrayList<>(16);


			List<SchoolStudentSIM> schoolStudentSIMS = new ArrayList<>();
			for (int i = 0; i < batchSendSimImportVoList.size(); i++) {
				int row = i + 1;
				String info = "第" + row + "条数据，";
				BatchSendSimImportVo batchSendSimImportVo = batchSendSimImportVoList.get(i);
				SchoolStudentSIM sim = schoolStudentSimMapper.selectOne(new LambdaQueryWrapper<SchoolStudentSIM>()
						.eq(SchoolStudentSIM::getName, batchSendSimImportVo.getName())
						.likeLeft(SchoolStudentSIM::getIdentity, batchSendSimImportVo.getIdentity()));

				if (ObjectUtil.isNull(sim)) {
					throw new BusinessException(info + "数据库中不存在该学员数据，请确认学员名称和身份证号是否正确");
				}
				if (sim.getIsNotOpenCard() == 2) {
					throw new BusinessException(info + "该学员已被设为不开卡，不允许进行开卡操作");
				}
				schoolStudentSIMS.add(sim);
			}

			//开卡操作
			for (SchoolStudentSIM sim : schoolStudentSIMS) {
				if (StringUtils.isEmpty(sim.getSimMobile()) || sim.getIsSyn() == 1) {
					failList.add(sim);
					continue;
				}
				JSONObject response = addMobileNumber(sim);
				if (response != null) {
					if (response.getIntValue("respcode") == 0) {
						sim.setIsSyn(1);
						sim.setSynDate(new Date());
						sim.setIsNotOpenCard(1);
						sim.setOpenSimTime(new Date());
						schoolStudentSimMapper.updateById(sim);
						successList.add(sim);
					} else {
						sim.setSynFailMsg(response.getString("respdesc"));
						schoolStudentSimMapper.updateById(sim);
						failList.add(sim);
					}
				} else {
					failList.add(sim);
				}
			}

			BatchSendSimResponseVo responseVo = new BatchSendSimResponseVo();
			responseVo.setSuccessList(successList);
			responseVo.setFailList(failList);

			StringBuilder msg = new StringBuilder();

			msg.append("导入成功，开卡成功的数据：")
					.append(successList.size())
					.append("条，")
					.append("开卡失败的数据：")
					.append(failList.size())
					.append("条。<br />")
					.append("开卡成功的数据名单：<br />");

			successList.forEach(schoolStudentSIM -> msg.append(schoolStudentSIM.getName())
					.append(" ")
					.append(schoolStudentSIM.getIdentity())
					.append("<br />"));

			if (CollectionUtil.isNotEmpty(failList)) {
				msg.append("<br />开卡失败的数据名单：<br />");

				failList.forEach(schoolStudentSIM -> msg.append(schoolStudentSIM.getName())
						.append(" ")
						.append(schoolStudentSIM.getIdentity())
						.append("<br />"));
			}

			responseVo.setMsg(msg.toString());

			return responseVo;

		} catch (Exception e) {
			log.error("BatchSendSimToYiDong error", e);
			throw new BusinessException(e.getMessage());
		}finally{
			lock.unlock();
		}
	}

	/**
	 * 导入批量不开卡数据
	 */
	@Override
	public int importBatchNoOpenSim(MultipartFile excel) {
		int count = 0;
		try {
			// 获取表格数据
			ExcelUtil<BatchSendSimImportVo> util = new ExcelUtil<>(BatchSendSimImportVo.class);
			List<BatchSendSimImportVo> batchSendSimImportVoList = util.importExcel(excel.getInputStream());
			for (int i = 0; i < batchSendSimImportVoList.size(); i++) {
				int row = i + 1;
				String info = "第" + row + "条数据，";
				BatchSendSimImportVo batchSendSimImportVo = batchSendSimImportVoList.get(i);
				SchoolStudentSIM sim = schoolStudentSimMapper.selectOne(new LambdaQueryWrapper<SchoolStudentSIM>()
						.eq(SchoolStudentSIM::getName, batchSendSimImportVo.getName())
						.eq(SchoolStudentSIM::getIdentity, batchSendSimImportVo.getIdentity()));
				if (ObjectUtil.isNull(sim)) {
					throw new BusinessException(info + "数据库中不存在该学员数据，请确认学员名称和身份证号是否正确");
				}
				sim.setIsNotOpenCard(2);
				int update = schoolStudentSimMapper.updateById(sim);
				count += update;
			}
		}catch (Exception e) {
			log.error("BatchNoOpenSim error", e);
			throw new BusinessException(e.getMessage());
		}
		return count;
	}

	/**
	 * 导入运营数据
	 */
	@Override
	public int importStudentSimData(MultipartFile excel) {
		int count = 0;
		try {
			// 获取表格数据
			ExcelUtil<StudentSimImportVo> util = new ExcelUtil<>(StudentSimImportVo.class);
			List<StudentSimImportVo> studentSimImportVos = util.importExcel(excel.getInputStream());

			List<SchoolStudentSIM> sims = new ArrayList<>();
			for (int i = 0; i < studentSimImportVos.size(); i++) {
				int row = i + 1;
				String info = "第" + row + "条数据，";
				StudentSimImportVo studentSimImportVo = studentSimImportVos.get(i);
				SchoolStudentSIM sim = schoolStudentSimMapper.selectOne(new LambdaQueryWrapper<SchoolStudentSIM>()
						.eq(SchoolStudentSIM::getName, studentSimImportVo.getName())
						.eq(SchoolStudentSIM::getSimMobile, studentSimImportVo.getSimMobile())
						.likeLeft(SchoolStudentSIM::getIdentity, studentSimImportVo.getIdentity()));

				if (ObjectUtil.isNull(sim)) {
					throw new BusinessException(info + "数据库中不存在该学员数据，请确认学员名称、身份证号后六位和学员卡号是否正确");
				}

				BeanUtils.copyBeanProp(sim, studentSimImportVo);

				//格式化数据
				switch (studentSimImportVo.getIsAddWechat()) {
					case "否" : sim.setIsAddWechat(0); break;
					case "是" : sim.setIsAddWechat(1); break;
				}

				switch (studentSimImportVo.getIsCallOut()) {
					case "无" : sim.setIsCallOut(0); break;
					case "有" : sim.setIsCallOut(1); break;
				}

				switch (studentSimImportVo.getIsResetSim()) {
					case "否" : sim.setIsResetSim("0"); break;
					case "是" : sim.setIsResetSim("1"); break;
				}

				switch (studentSimImportVo.getIsReturnCash()) {
					case "否" : sim.setIsReturnCash(0); break;
					case "是" : sim.setIsReturnCash(1); break;
				}

				switch (studentSimImportVo.getIsDynamic()) {
					case "否" : sim.setIsDynamic(0); break;
					case "是" : sim.setIsDynamic(1); break;
				}

				switch (studentSimImportVo.getIsAddCallOutSale()) {
					case "否" : sim.setIsAddCallOutSale(0); break;
					case "是" : sim.setIsAddCallOutSale(1); break;
				}

				sim.setRechargeTime(DateUtil.parseDate(studentSimImportVo.getRechargeTime()));
				sim.setRecallDate(DateUtil.parseDateCustom(studentSimImportVo.getRecallDate(), DateUtil.DATE));
				sims.add(sim);
			}
			for (SchoolStudentSIM sim : sims) {
				int update = schoolStudentSimMapper.updateById(sim);
				count += update;
			}
		}catch (Exception e) {
			log.error("importStudentSimData error", e);
			throw new BusinessException(e.getMessage());
		}
		return count;
	}

	@Override
	public boolean xuanwuSendValidateCode(String mobileno, String validateCode,String type) {
		final PostMsg pm = PostMsgBuilder.getInstance() 
				.setShortConnMode(false) 
				.setSoTimeout(300000) 
				.setMaxConnForMT(2) 
				.setMaxConnForMO(2) 
				.setEnableSsl(false)
				.build();
		
		pm.getGwHost().setHost("ns.mosapi.cn", 9080); 
		pm.getWsHost().setHost("ns.mosapi.cn", 9070); 
		
		pm.getGwHost().setRetryTimes(1); // 重试次数
		pm.getGwHost().setRetryInterval(500); // 重试间隔: 毫秒
		
		Account account = new Account(smsXuanwuUsername, smsXuanwuPassword); // 单账号
		MTPack pack = new MTPack();
		pack.setBatchID(UUID.randomUUID());
		pack.setMsgType(MTPack.MsgType.SMS);
		pack.setDistinctFlag(true); 
		ArrayList<MessageData> msgs = new ArrayList<MessageData>();
		
		/** 单发, 一号码一内容 */
		pack.setSendType(MTPack.SendType.GROUP);
		String msgContent=""; //短信内容
		switch (type){
			case Constants.STUDENT_ACTION_DROP_CONFIRM:
				msgContent= "您正在确认退学，验证码为"+validateCode+"，5分钟之内有效，请勿泄露【"+smsXuanwuSign+"】";
				break;
			case Constants.STUDENT_ACTION_REGISTER:
				msgContent = "您正在进行学车报名，请本人签署电子合同，确保学车权益，验证码为"+validateCode+"，5分钟之内有效，请勿泄露";
				break;
			default:
				msgContent= "你的验证码为"+validateCode+",5分钟之内有效【"+smsXuanwuSign+"】";
		}
		msgs.add(new MessageData(mobileno,msgContent));
		pack.setMsgs(msgs);
		try {
			GsmsResponse resp = pm.post(account, pack);
			log.info("xuanwu send sms resp:"+resp.toString());
			if(resp.getResult()==0) {
				return true;
			}
		} catch (Exception e) {
			log.error("send xuanwu sms error",e);
		}
		return false;
	}
	@Override
	public boolean xuanwuSendMsg(String mobileno, String msg) {
		final PostMsg pm = PostMsgBuilder.getInstance()
				.setShortConnMode(false)
				.setSoTimeout(300000)
				.setMaxConnForMT(2)
				.setMaxConnForMO(2)
				.setEnableSsl(false)
				.build();

		pm.getGwHost().setHost("ns.mosapi.cn", 9080);
		pm.getWsHost().setHost("ns.mosapi.cn", 9070);

		pm.getGwHost().setRetryTimes(1); // 重试次数
		pm.getGwHost().setRetryInterval(500); // 重试间隔: 毫秒

		Account account = new Account(smsXuanwuUsername, smsXuanwuPassword); // 单账号
		MTPack pack = new MTPack();
		pack.setBatchID(UUID.randomUUID());
		pack.setMsgType(MTPack.MsgType.SMS);
		pack.setDistinctFlag(true);
		ArrayList<MessageData> msgs = new ArrayList<MessageData>();

		/** 单发, 一号码一内容 */
		pack.setSendType(MTPack.SendType.GROUP);
		msgs.add(new MessageData(mobileno, msg+"【"+smsXuanwuSign+"】"));
		pack.setMsgs(msgs);
		try {
			GsmsResponse resp = pm.post(account, pack);
			log.info("xuanwu send sms resp:"+resp.toString());
			if(resp.getResult()==0) {
				return true;
			}
		} catch (Exception e) {
			log.error("send xuanwu sms error",e);
		}
		return false;
	}

	/**
	 * 导入批量未发起数据
	 * <AUTHOR>
	 * @date 2023/12/16 9:26
	 * @param file *
	 * @return int *
	 */
	@Override
	public int importNoInitSim(MultipartFile file) {
		try {
			// 获取表格数据
			ExcelUtil<BatchSendSimImportVo> util = new ExcelUtil<>(BatchSendSimImportVo.class);
			List<BatchSendSimImportVo> studentSimNoInitVos = util.importExcel(file.getInputStream());
			List<SchoolStudentSIM> schoolStudentSIMS = new ArrayList<>();
			for (int i = 0; i < studentSimNoInitVos.size(); i++) {
				System.out.println("in");
				int row = i + 1;
				String info = "第" + row + "条数据，";
				BatchSendSimImportVo batchSendSimImportVo = studentSimNoInitVos.get(i);
				SchoolStudentSIM sim = schoolStudentSimMapper.selectOne(new LambdaQueryWrapper<SchoolStudentSIM>()
						.eq(SchoolStudentSIM::getName, batchSendSimImportVo.getName())
						.eq(SchoolStudentSIM::getIdentity, batchSendSimImportVo.getIdentity()));

				if (ObjectUtil.isNull(sim)) {
					throw new BusinessException(info + "数据库中不存在该学员数据，请确认学员姓名和身份证号是否正确");
				}
				sim.setIsNotOpenCard(0);
				schoolStudentSIMS.add(sim);
			}
			updateBatchById(schoolStudentSIMS, 2000);
			return schoolStudentSIMS.size();
		}catch (Exception e) {
			log.error("importNoInitSim error", e);
			throw new BusinessException(e.getMessage());
		}
	}

	
}
