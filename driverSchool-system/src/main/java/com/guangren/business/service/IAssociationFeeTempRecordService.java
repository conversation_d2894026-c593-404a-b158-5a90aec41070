package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.AssociationFeeTempRecord;
import com.guangren.business.domain.School;

import java.util.List;
import java.util.Set;

public interface IAssociationFeeTempRecordService extends IService<AssociationFeeTempRecord> {

    public int insertAssociationFeeTempRecord(AssociationFeeTempRecord associationFeeTempRecord);

    public int updateAssociationFeeTempRecord(AssociationFeeTempRecord associationFeeTempRecord);

    public List<AssociationFeeTempRecord> selectSuccessPayAssociationFeeTempRecords(AssociationFeeTempRecord associationFeeTempRecord);

    public List<AssociationFeeTempRecord> selectUnReleaseAssociationFeeTempRecords(AssociationFeeTempRecord associationFeeTempRecord);

    //存储未释放 5元监管手续费的 Set
    public Set<String> getNeedAddAssociationFeeSet(AssociationFeeTempRecord associationFeeTempRecord);

    public void withDrawAssociationFeeToSchool(String beginDateStr, String endDateStr, School school)throws Exception;

    public void syncAssociationFeeByDay(String beginDateStr, String endDateStr)throws Exception;
}

