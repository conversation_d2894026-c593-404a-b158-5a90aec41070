package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.BankTransactionRecord;

import java.util.List;

public interface IBankTransactionRecordService extends IService<BankTransactionRecord> {

    /**
     * 查询银行交易流水
     *
     * @param studentId 银行交易流水主键
     * @return 银行交易流水
     */
    public BankTransactionRecord selectTBankTransactionRecordByStudentId(String studentId);

    /**
     * 查询银行交易流水列表
     *
     * @param BankTransactionRecord 银行交易流水
     * @return 银行交易流水集合
     */
    public List<BankTransactionRecord> selectTBankTransactionRecordList(BankTransactionRecord BankTransactionRecord);

    /*
        @Override
        public int deleteTBankTransactionRecordByStudentId(String studentId) {
            return bankTransactionRecordMapper.deleteTBankTransactionRecordByStudentId(studentId);
        }*/


    void syncWithdrawByDay(Integer day) throws Exception;

    void syncWithdraw(String orderNo, String date) throws Exception;

    void bankReturnWithdrawRollback(BankTransactionRecord bankTransactionRecord) throws Exception;

    void bankBankErrorWithdrawRollback(String orderNo);

    void syncLixiByDay(Integer day) throws Exception;

    void lixiRollback(BankTransactionRecord bankTransactionRecord) throws Exception;
    /*
    *//**
     * 新增银行交易流水
     *
     * @param BankTransactionRecord 银行交易流水
     * @return 结果
     *//*
    public int insertTBankTransactionRecord(BankTransactionRecord BankTransactionRecord);

    *//**
     * 修改银行交易流水
     *
     * @param BankTransactionRecord 银行交易流水
     * @return 结果
     *//*
    public int updateTBankTransactionRecord(BankTransactionRecord BankTransactionRecord);


    *//**
     * 验证参数唯一性
     *//*
    public String checkUnique(BankTransactionRecord BankTransactionRecord);

    *//**
     * 批量删除银行交易流水
     *
     * @param studentIds 需要删除的银行交易流水主键集合
     * @return 结果
     *//*
    public int deleteTBankTransactionRecordByStudentIds(String studentIds);

    *//**
     * 删除银行交易流水信息
     *
     * @param studentId 银行交易流水主键
     * @return 结果
     *//*
    public int deleteTBankTransactionRecordByStudentId(String studentId);*/

}
