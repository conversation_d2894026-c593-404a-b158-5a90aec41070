package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.DivsionOrderAssociation;

import java.util.List;

/**
 * 监管服务费订单Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface IDivsionOrderAssociationService extends IService<DivsionOrderAssociation>
{
    /**
     * 查询监管服务费订单
     * 
     * @param id 监管服务费订单主键
     * @return 监管服务费订单
     */
    public DivsionOrderAssociation selectDivsionOrderAssociationById(Long id);

    /**
     * 查询监管服务费订单列表
     * 
     * @param divsionOrderAssociation 监管服务费订单
     * @return 监管服务费订单集合
     */
    public List<DivsionOrderAssociation> selectDivsionOrderAssociationList(DivsionOrderAssociation divsionOrderAssociation);

    /**
     * 新增监管服务费订单
     * 
     * @param divsionOrderAssociation 监管服务费订单
     * @return 结果
     */
    public int insertDivsionOrderAssociation(DivsionOrderAssociation divsionOrderAssociation);

    /**
     * 修改监管服务费订单
     * 
     * @param divsionOrderAssociation 监管服务费订单
     * @return 结果
     */
    public int updateDivsionOrderAssociation(DivsionOrderAssociation divsionOrderAssociation);

    /**
     * 批量删除监管服务费订单
     * 
     * @param ids 需要删除的监管服务费订单主键集合
     * @return 结果
     */
    public int deleteDivsionOrderAssociationByIds(String ids);

    /**
     * 删除监管服务费订单信息
     * 
     * @param id 监管服务费订单
     * @return 结果
     */
    public int deleteDivsionOrderAssociationById(Long id);
}
