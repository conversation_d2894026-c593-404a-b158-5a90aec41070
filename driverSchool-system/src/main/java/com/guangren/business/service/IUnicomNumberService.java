package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.UnicomNumber;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 联通号码Service接口
 * 
 * <AUTHOR>
 * @date 2023-10-24
 */
public interface IUnicomNumberService extends IService<UnicomNumber>
{
    /**
     * 查询联通号码
     * 
     * @param id 联通号码主键
     * @return 联通号码
     */
    public UnicomNumber selectUnicomNumberById(String id);

    /**
     * 查询联通号码列表
     * 
     * @param unicomNumber 联通号码
     * @return 联通号码集合
     */
    public List<UnicomNumber> selectUnicomNumberList(UnicomNumber unicomNumber);

    /**
     * 新增联通号码
     * 
     * @param unicomNumber 联通号码
     * @return 结果
     */
    public int insertUnicomNumber(UnicomNumber unicomNumber);

    /**
     * 修改联通号码
     * 
     * @param unicomNumber 联通号码
     * @return 结果
     */
    public int updateUnicomNumber(UnicomNumber unicomNumber);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(UnicomNumber unicomNumber);

    /**
     * 批量删除联通号码
     * 
     * @param ids 需要删除的联通号码主键集合
     * @return 结果
     */
    public int deleteUnicomNumberByIds(String ids);

    /**
     * 删除联通号码信息
     * 
     * @param id 联通号码主键
     * @return 结果
     */
    public int deleteUnicomNumberById(String id);

    /**
     * 导入号码数据
     * <AUTHOR>
     * @date 2023/10/26 9:33
     * @param file *
     * @return java.lang.String *
     */
    int importNumberData(MultipartFile file);
}
