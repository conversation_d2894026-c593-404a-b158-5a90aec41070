package com.guangren.business.service.impl;

import java.util.List;
import com.guangren.common.constant.UserConstants;
import com.guangren.common.utils.uuid.IdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.guangren.business.mapper.OrganUserRoleMapper;
import com.guangren.business.domain.OrganUserRole;
import com.guangren.business.service.IOrganUserRoleService;
import com.guangren.common.core.text.Convert;

/**
 * 机构用户和角色关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-08
 */
@Service
public class OrganUserRoleServiceImpl implements IOrganUserRoleService 
{
    @Autowired
    private OrganUserRoleMapper organUserRoleMapper;

    /**
     * 查询机构用户和角色关联
     * 
     * @param userId 机构用户和角色关联主键
     * @return 机构用户和角色关联
     */
    @Override
    public OrganUserRole selectOrganUserRoleByUserId(Long userId)
    {
        return organUserRoleMapper.selectOrganUserRoleByUserId(userId);
    }

    /**
     * 查询机构用户和角色关联列表
     * 
     * @param organUserRole 机构用户和角色关联
     * @return 机构用户和角色关联
     */
    @Override
    public List<OrganUserRole> selectOrganUserRoleList(OrganUserRole organUserRole)
    {
        return organUserRoleMapper.selectOrganUserRoleList(organUserRole);
    }

    /**
     * 新增机构用户和角色关联
     * 
     * @param organUserRole 机构用户和角色关联
     * @return 结果
     */
    @Override
    public int insertOrganUserRole(OrganUserRole organUserRole)
    {
        return organUserRoleMapper.insertOrganUserRole(organUserRole);
    }

    /**
     * 修改机构用户和角色关联
     * 
     * @param organUserRole 机构用户和角色关联
     * @return 结果
     */
    @Override
    public int updateOrganUserRole(OrganUserRole organUserRole)
    {
        return organUserRoleMapper.updateOrganUserRole(organUserRole);
    }





    /**
     * 批量删除机构用户和角色关联
     * 
     * @param userIds 需要删除的机构用户和角色关联主键
     * @return 结果
     */
    @Override
    public int deleteOrganUserRoleByUserIds(String userIds)
    {
        return organUserRoleMapper.deleteOrganUserRoleByUserIds(Convert.toStrArray(userIds));
    }

    /**
     * 删除机构用户和角色关联信息
     * 
     * @param userId 机构用户和角色关联主键
     * @return 结果
     */
    @Override
    public int deleteOrganUserRoleByUserId(Long userId)
    {
        return organUserRoleMapper.deleteOrganUserRoleByUserId(userId);
    }
}
