package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.RegistrationStudyCenterCode;

import java.util.List;

/**
 * 门店与学时平台签约代码Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
public interface IRegistrationStudyCenterCodeService extends IService<RegistrationStudyCenterCode>
{
    /**
     * 查询门店与学时平台签约代码
     * 
     * @param id 门店与学时平台签约代码主键
     * @return 门店与学时平台签约代码
     */
    public RegistrationStudyCenterCode selectRegistrationStudyCenterCodeById(Long id);

    /**
     * 查询门店与学时平台签约代码列表
     * 
     * @param registrationStudyCenterCode 门店与学时平台签约代码
     * @return 门店与学时平台签约代码集合
     */
    public List<RegistrationStudyCenterCode> selectRegistrationStudyCenterCodeList(RegistrationStudyCenterCode registrationStudyCenterCode);

    /**
     * 新增门店与学时平台签约代码
     * 
     * @param registrationStudyCenterCode 门店与学时平台签约代码
     * @return 结果
     */
    public int insertRegistrationStudyCenterCode(RegistrationStudyCenterCode registrationStudyCenterCode);

    /**
     * 修改门店与学时平台签约代码
     * 
     * @param registrationStudyCenterCode 门店与学时平台签约代码
     * @return 结果
     */
    public int updateRegistrationStudyCenterCode(RegistrationStudyCenterCode registrationStudyCenterCode);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(RegistrationStudyCenterCode registrationStudyCenterCode);

    /**
     * 批量删除门店与学时平台签约代码
     * 
     * @param ids 需要删除的门店与学时平台签约代码主键集合
     * @return 结果
     */
    public int deleteRegistrationStudyCenterCodeByIds(String ids);

    /**
     * 删除门店与学时平台签约代码信息
     * 
     * @param id 门店与学时平台签约代码主键
     * @return 结果
     */
    public int deleteRegistrationStudyCenterCodeById(Long id);
}
