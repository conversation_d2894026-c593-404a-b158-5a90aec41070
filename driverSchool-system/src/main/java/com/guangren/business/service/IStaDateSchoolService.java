package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.StaDateSchool;

import java.util.List;

/**
 * 按学校日期统计所有指标Service接口
 * 
 * <AUTHOR>
 * @date 2023-06-12
 */
public interface IStaDateSchoolService extends IService<StaDateSchool>
{
    /**
     * 查询按学校日期统计所有指标
     * 
     * @param id 按学校日期统计所有指标主键
     * @return 按学校日期统计所有指标
     */
    public StaDateSchool selectStaDateSchoolById(Long id);

    /**
     * 查询按学校日期统计所有指标列表
     * 
     * @param staDateSchool 按学校日期统计所有指标
     * @return 按学校日期统计所有指标集合
     */
    public List<StaDateSchool> selectStaDateSchoolList(StaDateSchool staDateSchool);

    /**
     * 新增按学校日期统计所有指标
     * 
     * @param staDateSchool 按学校日期统计所有指标
     * @return 结果
     */
    public int insertStaDateSchool(StaDateSchool staDateSchool);

    /**
     * 修改按学校日期统计所有指标
     * 
     * @param staDateSchool 按学校日期统计所有指标
     * @return 结果
     */
    public int updateStaDateSchool(StaDateSchool staDateSchool);

    /**
     * 批量删除按学校日期统计所有指标
     * 
     * @param ids 需要删除的按学校日期统计所有指标主键集合
     * @return 结果
     */
    public int deleteStaDateSchoolByIds(String ids);

    /**
     * 删除按学校日期统计所有指标信息
     * 
     * @param id 按学校日期统计所有指标主键
     * @return 结果
     */
    public int deleteStaDateSchoolById(Long id);

    public StaDateSchool selectSchoolData(String schoolId,String staDate);

    /**
     * 查询驾校监管数据
     * <AUTHOR>
     * @date 2023/10/16 16:35
     * @return com.guangren.common.core.domain.AjaxResult *
     */
    List<StaDateSchool> selectSchoolSuperviseData(StaDateSchool staDateSchool);
}
