package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolContract;
import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.domain.StudentResignContract;
import com.guangren.business.vo.importTempete.StudentResignContractTemplate;
import com.guangren.common.exception.BusinessException;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IStudentResignContractService extends IService<StudentResignContract> {

    /**
     * 通过学生id查询学生重签合同信息
     * @param studentId
     * @return
     */
    public StudentResignContract selectStudentResignContractByStudentId(String studentId);


    /**
     * 查询学生重签合同列表
     * @param studentResignContract
     * @return
     */
    public List<StudentResignContract> selectStudentResignContractList(StudentResignContract studentResignContract);

    /**
     * 新增学生重签合同
     * @param studentResignContract
     * @return
     */
    public int insertStudentResignContract(StudentResignContract studentResignContract);

    /**
     * 修改学生重签合同
     * @param studentResignContract
     * @return
     */
    public int updateStudentResignContract(StudentResignContract studentResignContract);

    /**
     * 删除学生重签合同信息
     * @param studentId
     * @return
     */
    public int deletStudentResignContractByStudentId(String studentId);

    /**
     * 批量删除学生重签合同信息
     * @param studentIds
     * @return
     */
    public int deletStudentResignContractByStudentIds(String[] studentIds);

    /**
     * 导入数据
     * @param file
     * @return
     * @throws Exception
     */
    StudentResignContractTemplate importData(MultipartFile file) throws Exception;

    /**
     * 重签合同定时任务(重新替换学员的原合同)
     * @param schoolId
     * @throws BusinessException
     */
    void createNeedResignContract(String schoolId)throws BusinessException;

    /**
     * 生成重签合同（返回合同URL）
     * @param schoolStudent
     * @param schoolContract
     * @return
     * @throws Exception
     */
    String createNeedResignContract(SchoolStudent schoolStudent, SchoolContract schoolContract) throws Exception;
}
