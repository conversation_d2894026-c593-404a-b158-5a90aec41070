package com.guangren.business.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolStudentStudyTime;

import java.util.List;

public interface ISchoolStudentStudyTimeService extends IService<SchoolStudentStudyTime> {
    /**
     * 查询学时数据
     *
     * @param id 学时数据主键
     * @return 学时数据
     */
    public SchoolStudentStudyTime selectSchoolStudentStudyTimeById(String id);

    /**
     * 查询学时数据列表
     *
     * @param schoolStudentStudyTime 学时数据
     * @return 学时数据集合
     */
    public List<SchoolStudentStudyTime> selectSchoolStudentStudyTimeList(SchoolStudentStudyTime schoolStudentStudyTime);

    /**
     * 新增学时数据
     *
     * @param schoolStudentStudyTime 学时数据
     * @return 结果
     */
    public int insertSchoolStudentStudyTime(SchoolStudentStudyTime schoolStudentStudyTime);

    /**
     * 修改学时数据
     *
     * @param schoolStudentStudyTime 学时数据
     * @return 结果
     */
    public int updateSchoolStudentStudyTime(SchoolStudentStudyTime schoolStudentStudyTime);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SchoolStudentStudyTime schoolStudentStudyTime);

    /**
     * 批量删除学时数据
     *
     * @param ids 需要删除的学时数据主键集合
     * @return 结果
     */
    public int deleteSchoolStudentStudyTimeByIds(String ids);

    /**
     * 删除学时数据信息
     *
     * @param id 学时数据主键
     * @return 结果
     */
    public int deleteSchoolStudentStudyTimeById(String id);
    
    
    public void studyTimeCenterAddData(JSONObject item,String organId) throws Exception;
	
}
