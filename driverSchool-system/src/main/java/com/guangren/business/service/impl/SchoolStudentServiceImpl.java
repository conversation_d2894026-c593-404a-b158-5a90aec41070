package com.guangren.business.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guangren.business.domain.*;
import com.guangren.business.domain.BankAccount;
import com.guangren.business.domain.BankTransactionRecord;
import com.guangren.business.domain.DivisionAccount;
import com.guangren.business.domain.OrganUser;
import com.guangren.business.domain.ProvinceStudyTime;
import com.guangren.business.domain.PublicFile;
import com.guangren.business.domain.RegistrationStudyCenterCode;
import com.guangren.business.domain.School;
import com.guangren.business.domain.SchoolBranch;
import com.guangren.business.domain.SchoolContact;
import com.guangren.business.domain.SchoolContract;
import com.guangren.business.domain.SchoolDealFlow;
import com.guangren.business.domain.SchoolRegistration;
import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.domain.SchoolStudentCenterException;
import com.guangren.business.domain.SchoolStudentContract;
import com.guangren.business.domain.SchoolStudentDel;
import com.guangren.business.domain.SchoolStudentDropOut;
import com.guangren.business.domain.SchoolStudentSIM;
import com.guangren.business.domain.SchoolStudyCenter;
import com.guangren.business.domain.SuperviseFlow;
import com.guangren.business.domain.SupervisePay;
import com.guangren.business.domain.SuperviseReleaseRecord;
import com.guangren.business.domain.SynInterface;
import com.guangren.business.domain.UnicomNumber;
import com.guangren.business.domain.UseUnicomNumber;
import com.guangren.business.enumration.BankTransactionRecordType;
import com.guangren.business.mapper.SchoolStudentMapper;
import com.guangren.business.service.*;
import com.guangren.business.service.IBankService;
import com.guangren.business.service.IBankTransactionRecordService;
import com.guangren.business.service.IDivisionAccountService;
import com.guangren.business.service.IOrganUserService;
import com.guangren.business.service.IProvinceStudentService;
import com.guangren.business.service.IProvinceStudyTimeService;
import com.guangren.business.service.IPublicFileService;
import com.guangren.business.service.IRegistrationStudyCenterCodeService;
import com.guangren.business.service.ISchoolBranchService;
import com.guangren.business.service.ISchoolContactService;
import com.guangren.business.service.ISchoolContractService;
import com.guangren.business.service.ISchoolDealFlowService;
import com.guangren.business.service.ISchoolRegistrationService;
import com.guangren.business.service.ISchoolService;
import com.guangren.business.service.ISchoolStudentCenterExceptionService;
import com.guangren.business.service.ISchoolStudentContractService;
import com.guangren.business.service.ISchoolStudentDelService;
import com.guangren.business.service.ISchoolStudentDropOutService;
import com.guangren.business.service.ISchoolStudentSIMService;
import com.guangren.business.service.ISchoolStudentService;
import com.guangren.business.service.ISchoolStudyCenterService;
import com.guangren.business.service.ISuperviseExceptionDayService;
import com.guangren.business.service.ISuperviseFlowService;
import com.guangren.business.service.ISupervisePayService;
import com.guangren.business.service.ISuperviseReleaseRecordService;
import com.guangren.business.service.ISynInterfaceService;
import com.guangren.business.service.IUnicomNumberService;
import com.guangren.business.service.IUseUnicomNumberService;
import com.guangren.business.vo.ContractVo;
import com.guangren.business.vo.GrStudentTemplateVo;
import com.guangren.business.vo.StudentCountVo;
import com.guangren.business.vo.VariousNumberVo;
import com.guangren.common.config.RuoYiConfig;
import com.guangren.common.constant.Constants;
import com.guangren.common.constant.DictConst;
import com.guangren.common.constant.UserConstants;
import com.guangren.common.core.domain.entity.SysOrganUser;
import com.guangren.common.core.text.Convert;
import com.guangren.common.enums.Action;
import com.guangren.common.enums.DataType;
import com.guangren.common.exception.BusinessException;
import com.guangren.common.utils.DateUtil;
import com.guangren.common.utils.DateUtils;
import com.guangren.common.utils.ImageUtil;
import com.guangren.common.utils.MD5Util;
import com.guangren.common.utils.MyEncrypt;
import com.guangren.common.utils.OkRequestCompent;
import com.guangren.common.utils.ShiroUtils;
import com.guangren.common.utils.StringUtils;
import com.guangren.common.utils.ZipUtils;
import com.guangren.common.utils.bean.BeanUtils;
import com.guangren.common.utils.poi.ExcelUtil;
import com.guangren.common.utils.uuid.IdUtils;
import com.guangren.system.service.ISysConfigService;
import com.guangren.system.service.ISysDictDataService;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Service
public class SchoolStudentServiceImpl extends ServiceImpl<SchoolStudentMapper, SchoolStudent> implements ISchoolStudentService{
    @Resource
    private SchoolStudentMapper schoolStudentMapper;
    @Autowired
    private ISchoolStudentService studentService;
    @Autowired
    private ISchoolStudyCenterService schoolStudyCenterService;
    @Autowired
    private ISynInterfaceService synInterfaceService;
    @Autowired
    private OkRequestCompent request;
    @Autowired
    private IBankService bankService;
    @Autowired
    private ISchoolBranchService schoolBranchService;
    @Autowired
    private IOrganUserService organUserService;

    @Autowired
    private ISupervisePayService supervisePayService;

    @Autowired
    private ISuperviseReleaseRecordService superviseReleaseRecordService;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private ISchoolStudentCenterExceptionService schoolStudentCenterExceptionService;
    @Resource
    private ISchoolStudentDelService schoolStudentDelService;
    @Autowired
    private ISchoolService schoolService;
    @Autowired
    private IPublicFileService publicFileService;
    @Resource
    private ISchoolStudentDropOutService schoolStudentDropOutService;

    @Autowired
    private ISuperviseExceptionDayService superviseExceptionDayService;
    @Autowired
    private IBankTransactionRecordService bankTransactionRecordService;
    @Autowired
    private UnionBankService unionBankService;
    @Resource
    private IProvinceStudyTimeService provinceStudyTimeService;
    @Resource
    private IProvinceStudentService provinceStudentService;
    @Resource
    private ISchoolRegistrationService schoolRegistrationService;

    @Autowired
    private ISchoolStudentContractService schoolStudentContractService;

    @Autowired
    private ISchoolContactService schoolContactService;

    @Resource
    private IRegistrationStudyCenterCodeService registrationStudyCenterCodeService;

    @Resource
    private ISysConfigService configService;

    @Resource
    private IDivisionAccountService divisionAccountService;

    @Resource
    private ISchoolContractService schoolContractService;

    @Resource
    private IStudentSkipContractService studentSkipContractService;

    private ReentrantLock lock = new ReentrantLock();


    @Value("${ruoyi.miniApp.appId}")
    private String appId;
    @Value("${ruoyi.miniApp.appSecret}")
    private String appSecret;
    @Value("${ruoyi.profile}")
    private String imagePath;

    @Resource
    private IUnicomNumberService unicomNumberService;
    @Resource
    private IUseUnicomNumberService useUnicomNumberService;

    @Autowired
    private ISchoolStudentSIMService studentSIMService;

    @Autowired
    private ISysDictDataService sysDictDataService;
    @Autowired
    private YiDongShuiEQianService yiDongShuiEQianService;

    @Autowired
    private IDivsionOrderService divsionOrderService;



    private static final Logger log = LoggerFactory.getLogger(SchoolStudentServiceImpl.class);

    @Override
    @Transactional(rollbackFor = Exception.class,isolation = Isolation.READ_COMMITTED)
    public boolean customerSave(SchoolStudent student, JSONObject json) throws BusinessException,  Exception {
        SchoolStudent temp = this.getOne(new QueryWrapper<SchoolStudent>().eq("identity", student.getIdentity()).eq("is_quit", 0).last("limit 1"));
        if(temp != null){
            throw new BusinessException("该证件号已被使用");
        }

        List<SchoolStudent> tempList = this.list(new QueryWrapper<SchoolStudent>().eq("identity", student.getIdentity()).eq("is_quit", 1));
        for(SchoolStudent tmp : tempList) {
            long count = schoolStudentDropOutService.count(new LambdaQueryWrapper<>(SchoolStudentDropOut.class)
                    .eq(SchoolStudentDropOut::getStudentId,tmp.getId())
                    .eq(SchoolStudentDropOut::getIsDone,0));
            if(count > 0){
                throw new BusinessException("该证件正在退学中，请完成退学流程才能报名");
            }
        }

        temp = this.getOne(new QueryWrapper<SchoolStudent>().eq("mobile", student.getMobile()).eq("is_quit", 0).last("limit 1"));
        if(temp != null){
            throw new BusinessException("该手机号已被使用");
        }

        tempList = this.list(new QueryWrapper<SchoolStudent>().eq("mobile", student.getMobile()).eq("is_quit", 1));
        for(SchoolStudent tmp : tempList) {
            SchoolStudentDropOut dropOutStudent = schoolStudentDropOutService.getOne(new QueryWrapper<SchoolStudentDropOut>().eq("student_id", tmp.getId()).last("limit 1"));
            if(dropOutStudent != null && dropOutStudent.getIsDone()==0) {
                throw new BusinessException("该手机号正在退学中，请完成退学流程才能报名");
            }
        }

        if(save(student)){
            addNumber(json,student);
            SchoolRegistration registration = schoolRegistrationService.getById(student.getRegistrationId());
            int reviewStudentType = registration.getReviewStudentType() != null ? registration.getReviewStudentType() : 0;
            if (reviewStudentType == 2) {
                //自动审核，直接生成驾校盖章的合同
                checkStudentSave(student, false);
            }
            return true;
        }
        return false;
    }

    private void addNumber(JSONObject json,SchoolStudent student){
        String deliverProvince = json.getString("deliverProvince");
        String deliverCity = json.getString("deliverCity");
        String deliverTown = json.getString("deliverTown");
        String deliverAddress = json.getString("deliverAddress");
        JSONObject simObject = json.getJSONObject("sim");
        if(simObject==null){
            return;
        }

        Integer mobileType = simObject.getInteger("mobileType");
        String simMobile =  simObject.getString("simMobile");

        if (mobileType != null && mobileType  == 0) {
            UnicomNumber unicomNumber = unicomNumberService.getOne(new LambdaQueryWrapper<UnicomNumber>()
                    .eq(UnicomNumber::getNumber, simMobile));
            if (unicomNumber != null && unicomNumber.getStatus() == 0) {
                unicomNumber.setStatus(1);
                unicomNumberService.updateById(unicomNumber);
                UseUnicomNumber useUnicomNumber = new UseUnicomNumber();
                useUnicomNumber.setId(IdUtils.fastSimpleUUID());
                useUnicomNumber.setUnicomNumberId(unicomNumber.getId());
                useUnicomNumber.setStudentId(student.getId());
                useUnicomNumber.setUseStatus(2);
                useUnicomNumber.setDeliverProvince(deliverProvince);
                useUnicomNumber.setDeliverCity(deliverCity);
                useUnicomNumber.setDeliverTown(deliverTown);
                useUnicomNumber.setDeliverAddress(deliverAddress);
                useUnicomNumber.setUseTime(new Date());
                useUnicomNumber.setRecipientMobile(simObject.getString("recipientMobile"));
                useUnicomNumber.setCreatedTime(new Date());
                useUnicomNumber.setUpdatedTime(new Date());
                useUnicomNumberService.save(useUnicomNumber);
            }
        }else{
            SchoolStudentSIM sim = new SchoolStudentSIM();
            sim.setStudentId(student.getId());
            sim.setSimMobile(simMobile);
            sim.setSimProductCode(simObject.getString("simProductCode"));
            sim.setDeliverProvince(deliverProvince);
            sim.setDeliverCity(deliverCity);
            sim.setDeliverTown(deliverTown);
            sim.setDeliverAddress(deliverAddress);
            sim.setLoginCount(0);
            sim.setIsSyn(0);
            sim.setName(student.getName());
            sim.setMobile(student.getMobile());
            sim.setIdentityType(student.getIdentityType());
            sim.setIdentity(student.getIdentity());
            sim.setIsSignal(0);
            sim.setOpenId(student.getOpenId());
            sim.setRecipientMobile(simObject.getString("recipientMobile"));
            studentSIMService.save(sim);
        }
    }

    @Override
    public String getOpenId(String jsCode) {
        try {
            String grant_type = "authorization_code";
            String res = HttpRequest.post("https://api.weixin.qq.com/sns/jscode2session")
                    .form("appid",appId)
                    .form("secret",appSecret)
                    .form("js_code",jsCode)
                    .form("grant_type",grant_type)
                    .execute().body();
            log.debug("get openId: "+res);
            JSONObject json = JSONObject.parseObject(res);
            return json.getString("openid");
        } catch (Exception e) {
            log.error("get openId error",e);
        }
        return null;
    }


    /**
     * 查询学员
     *
     * @param id 学员主键
     * @return 学员
     */
    @Override
    public SchoolStudent selectSchoolStudentById(String id)
    {
        SchoolStudent schoolStudent = schoolStudentMapper.selectSchoolStudentById(id);
        List<PublicFile> publicFiles = publicFileService.list(new LambdaQueryWrapper<PublicFile>()
                .eq(PublicFile::getRefrence, schoolStudent.getId())
                .eq(PublicFile::getRefrenceType, DictConst.FILE_TYPE_DROPOUT_CERT_IMG.getDict()));
        schoolStudent.setDropOutImgList(publicFiles);
        return schoolStudent;
    }

    /**
     * 查询学员列表
     *
     * @param schoolStudent 学员
     * @return 学员
     */
    @Override
    public List<SchoolStudent> selectSchoolStudentList(SchoolStudent schoolStudent)
    {
        return schoolStudentMapper.selectSchoolStudentList(schoolStudent);
    }

    /**
     * 新增学员
     *
     * @param schoolStudent 学员
     * @return 结果
     */
    @Override
    public int insertSchoolStudent(SchoolStudent schoolStudent)
    {
        if (schoolStudent.getBusinessType().equals("增驾")) {
            if (ObjectUtil.isNull(schoolStudent.getOldLicenseDate())) {
                throw new BusinessException("业务类型为增驾时，原领驾照日期不能为空");
            }
            MultipartFile[] image = schoolStudent.getImages();
            if (image == null) {
                throw new BusinessException("业务类型为增驾时，原驾驶证图片不能为空");
            }
            for (MultipartFile img : image) {
                PublicFile publicFile = publicFileService.saveFile(img, "0", DictConst.FILE_TYPE_OLD_LICENSE_IMG.getDict());
                schoolStudent.setOldLicenseImage(publicFile.getWebPath());
            }
        }
        long count = this.count(new LambdaQueryWrapper<SchoolStudent>()
                .eq(SchoolStudent::getMobile,schoolStudent.getMobile())
                .eq(SchoolStudent::getIsQuit,0));
        if(count>0){
            throw new BusinessException("已经存在的手机号");
        }
        schoolStudent.setId(IdUtils.fastSimpleUUID());
        schoolStudent.setPrepareRegisteDate(new Date());
        schoolStudent.setCreatedTime(new Date());
        return schoolStudentMapper.insert(schoolStudent);
    }

    /**
     * 修改学员
     * @param schoolStudent 学员
     * @return 结果
     */
    @Override
    public int updateSchoolStudent(SchoolStudent schoolStudent)
    {
        if (StringUtils.isNotEmpty(schoolStudent.getBusinessType())) {
            if (schoolStudent.getBusinessType().equals("增驾")) {
                MultipartFile[] image = schoolStudent.getImages();
                if (image == null) {
                    throw new BusinessException("业务类型为增驾时，原驾驶证图片不能为空");
                }
                if (ObjectUtil.isNull(schoolStudent.getOldLicenseDate())) {
                    throw new BusinessException("业务类型为增驾时，原领驾照日期不能为空");
                }
                for (MultipartFile img : image) {
                    PublicFile publicFile = publicFileService.saveFile(img, "0", DictConst.FILE_TYPE_OLD_LICENSE_IMG.getDict());
                    schoolStudent.setOldLicenseImage(publicFile.getWebPath());
                }
            }
        }
        long count = this.count(new LambdaQueryWrapper<SchoolStudent>()
                .eq(SchoolStudent::getMobile,schoolStudent.getMobile())
                .eq(SchoolStudent::getIsQuit,0)
                .ne(SchoolStudent::getId,schoolStudent.getId()));
        if(count>0){
            throw new BusinessException("手机号已经存在");
        }

        schoolStudent.setUpdatedTime(new Date());
        schoolStudent.setIsCheck(0);
        schoolStudent.setRealProvince(schoolStudent.getHujiProvince());
        schoolStudent.setRealCity(schoolStudent.getHujiCity());
        schoolStudent.setRealTown(schoolStudent.getHujiTown());
        schoolStudent.setRealAddress(schoolStudent.getHujiAddress());
        return schoolStudentMapper.updateById(schoolStudent);
    }

    /**
     * 审核学员
     */
    @Override
    public int checkStudent(SchoolStudent schoolStudent) {
        schoolStudent.setUpdatedTime(new Date());
        return schoolStudentMapper.updateById(schoolStudent);
    }

    /**
     * 验证参数唯一性
     * @param schoolStudent 学员
     * @return 学员
     */
    @Override
    public String checkUnique(SchoolStudent schoolStudent)
    {
        String id = schoolStudent.getId() == null ? "-1"  : schoolStudent.getId();
        SchoolStudent info=schoolStudentMapper.checkUnique(schoolStudent);
        if(StringUtils.isNotNull(info) && !info.getId().equals(id)){
            return UserConstants.COMMOM_NOT_UNIQUE;
        }
        return UserConstants.COMMOM_UNIQUE;
    }


    /**
     * 批量删除学员
     * @param ids 需要删除的学员主键
     * @return 结果
     */
    @Override
    public int deleteSchoolStudentByIds(String ids, String delReason)
    {
        if (StrUtil.isBlank(delReason)) {
            throw new BusinessException("请输入删除原因");
        }
        List<SchoolStudent> students = schoolStudentMapper.selectBatchIds(Arrays.asList(ids.split(",")));
        int rows = 0;
        for(SchoolStudent schoolStudent : students){
            SchoolStudentDel schoolStudentDel = new SchoolStudentDel();
            schoolStudentDel.setDelReason(delReason);
            BeanUtils.copyBeanProp(schoolStudentDel, schoolStudent);
            schoolStudentDel.setId("");
            schoolStudentDel.setCreatedTime(new Date());
            schoolStudentDel.setUpdatedTime(new Date());
            schoolStudentDel.setStudentId(schoolStudent.getId());
            schoolStudentDelService.save(schoolStudentDel);
            schoolStudentMapper.deleteById(schoolStudent.getId());
            try {
                SchoolStudentContract ssc = schoolStudentContractService.getById(schoolStudent.getId());
                if (ssc != null && StringUtils.isNotEmpty(ssc.getUserId())) {
                    yiDongShuiEQianService.deleteUser(ssc.getUserId());
                }
            }catch(Exception ex){
                log.error("delete contract user error",ex);
            }
            try{
                JSONObject jsonObject = yiDongShuiEQianService.findUserByApplyNo(schoolStudent.getMobile());
                if(jsonObject != null){
                    String userId = jsonObject.getString("userId");
                    if(StringUtils.isNotEmpty(userId)){
                        yiDongShuiEQianService.deleteUser(userId);
                    }
                }
            }catch(Exception ex){
                log.error("delete contract user error",ex);
            }
            rows++;
        };
        return rows;
    }

    /**
     * 删除学员信息
     * @param id 学员主键
     * @return 结果
     */
    @Override
    public int deleteSchoolStudentById(String id)
    {
        return schoolStudentMapper.deleteSchoolStudentById(id);
    }

    /**
     * 学员退学保存
     */
    @Override
    @Transactional(rollbackFor = {Exception.class},isolation = Isolation.READ_COMMITTED)
    public boolean dropOutSave(SchoolStudent schoolStudent) {
        //参数开启期间为避免纠纷，禁止退学
          Integer skipContractStatus = studentSkipContractService.querySkipContractStatus();
            boolean isSkipContract = skipContractStatus.equals(Constants.SKIP_CONTRACT_SWITCH_ON);
        if(isSkipContract){
            throw new BusinessException("当前管理员已开启跳过合同签署步骤，暂时无法进行退学。");
        }
        SchoolStudent student = schoolStudentMapper.selectById(schoolStudent.getId());

        if (student.getIsQuit() == 1){
            throw new BusinessException("学员已退学状态");
        }
        student.setIsQuit(1);
        schoolStudentMapper.updateById(student);

        SchoolStudentContract ssc = schoolStudentContractService.getById(student.getId());
        if(ssc != null && StringUtils.isNotEmpty(ssc.getUserId())){
            try {
                yiDongShuiEQianService.deleteUser(ssc.getUserId());
            }catch(Exception ex){
                //不做处理
                log.error("删除用户出错",ex);
            }
        }

        SchoolStudentDropOut schoolStudentDropOut = new SchoolStudentDropOut();
        // 保存退学凭证
        if (schoolStudent.getImages() != null && schoolStudent.getImages().length > 0) {
            MultipartFile[] images = schoolStudent.getImages();
            List<String> imageList= new ArrayList<>(images.length);
            List<PublicFile> publicFiles = publicFileService.saveFiles(images,
                    schoolStudent.getId(),
                    DictConst.FILE_TYPE_DROPOUT_CERT_IMG.getDict());
            for (PublicFile fileItem : publicFiles) {
                imageList.add(fileItem.getFileName());
            }
            String imgJson = JSONArray.toJSONString(imageList);
            schoolStudentDropOut.setDropOutImages(imgJson);
        }
        BeanUtils.copyBeanProp(schoolStudentDropOut, student);
        schoolStudentDropOut.setId(null);
        schoolStudentDropOut.setCreatedTime(new Date());
        schoolStudentDropOut.setUpdatedTime(new Date());
        schoolStudentDropOut.setStudentName(student.getName());
        School school = schoolService.getById(student.getSchoolId());
        SchoolBranch schoolBranch = schoolBranchService.getById(student.getBranchId());
        SchoolRegistration registration = schoolRegistrationService.getById(student.getRegistrationId());
        schoolStudentDropOut.setSchoolName(school.getName());
        schoolStudentDropOut.setBranchName(schoolBranch==null?"":schoolBranch.getName());
        schoolStudentDropOut.setRegistrationName(registration.getName());
        schoolStudentDropOut.setStudentStatus(student.getStatus());
        schoolStudentDropOut.setStudentId(schoolStudent.getId());
        schoolStudentDropOut.setStatus(0);
        schoolStudentDropOut.setIsDone(0);
        schoolStudentDropOut.setIsMember(school.getIsMember());
        schoolStudentDropOut.setIsReissue(false);
        schoolStudentDropOut.setIsFirstTrial(Constants.FIRST_TRIAL_STATUS_PEND);
        schoolStudentDropOut.setDropOutReason(schoolStudent.getDropOutReason());
        if(student.getIsCheck() == 0) {
            if (student.getSuperviseFeeIsOk() == 0) {
                schoolStudentDropOut.setStatus(1);
                schoolStudentDropOut.setIsDone(1);
                schoolStudentDropOut.setIsFirstTrial(Constants.FINAL_TRIAL_STATUS_DOWN);
            }else if (student.getSuperviseFeeIsOk() == 1){
                throw new BusinessException( "学员监管资金已到账，请审核后再退学");
            }
        }
        schoolStudentDropOut.setCreateTime(new Date());
        schoolStudentDropOut.setUpdateTime(new Date());

       /* if (student.getIsSupervise() ==0){
            throw new BusinessException( "未设置是否资金监管");
        }
        if (student.getSuperviseFeeIsOk() ==0){
            throw new BusinessException( "监管资金没有到账");
        }
        if (student.getQuitIsSyn()==1){
            throw new BusinessException("退学数据已同步");
        }*/

        //对接退学接口
//        try {
//            List<SchoolStudent> studentList= new ArrayList<SchoolStudent>();
//            studentList.add(student);
//            schoolStudentService.submitStudentQuitDataToStudyCenter(studentList);
//        }catch (Exception ex){
//            logger.error(ex.getMessage());
//            return AjaxResult.error("退学失败 "+ex.getMessage());
//        }

        return schoolStudentDropOutService.save(schoolStudentDropOut);
    }

    public static void main(String [] args) throws Exception {
        File file = new File("C:\\Users\\<USER>\\Desktop\\submit-json-李海琴.txt");
        String str = FileUtils.readFileToString(file, "utf-8");
        JSONObject object = JSONObject.parseObject(str);
        System.out.println(MyEncrypt.getInstance().encrypt(object.toJSONString()));;
    }

    /**
     * 同步学员学时
     * @param students
     * @return
     */
    @Override
    public void submitStudentAddDataToStudyCenter(List<SchoolStudent> students)throws Exception{

        for(SchoolStudent stu : students){
            SchoolStudent student = this.getById(stu.getId());
            SchoolStudyCenter schoolStudentCenter = schoolStudyCenterService.getOne(new QueryWrapper<SchoolStudyCenter>()
                    .eq("school_id", student.getSchoolId()).eq("organ_id", student.getStudyCenterId()));
            JSONArray array = new JSONArray();
            if(schoolStudentCenter != null){
                JSONObject object = new JSONObject();
                object.put("orgCode", schoolStudentCenter.getSchoolCode().trim());
                object.put("studentName", student.getName());
                object.put("nationality", student.getNation());
                object.put("idCardType", student.getIdentityType());
                object.put("idCard", student.getIdentity());
                object.put("gender", student.getGender()==1?"男":"女");
                object.put("birthday", student.getBirthday());
                object.put("education", student.getEducation());
                object.put("mobile", student.getMobile());
                object.put("ancestralPlace",student.getRealProvince()+student.getRealCity()+student.getRealTown()+student.getRealAddress());
                object.put("postcode", "");
                object.put("address", student.getRealProvince()+student.getRealCity()+student.getRealTown()+student.getRealAddress());
                if(org.apache.commons.lang.StringUtils.isNotEmpty(student.getHeadImage())){
                    String physicalPath = student.getHeadImage().replace("/profile", imagePath);
                    File file = new File(physicalPath);
                    if(file.exists()){
                        String base64 = ImageUtil.imageToBase64(file);
                        object.put("image",base64);
                    }
                }
                object.put("residenceNo", "");
                object.put("residenceAddress", "");
                if(student.getSuperviseDate() != null){
                    object.put("applyDate", DateUtil.formatDate(student.getSuperviseDate(),"yyyy-MM-dd"));
                }else{
                    object.put("applyDate", DateUtil.formatDate(student.getRegisteDate(),"yyyy-MM-dd"));
                }
                object.put("licenseType",student.getLicenseType());
                object.put("businessType", student.getBusinessType());
                object.put("from", "");
                object.put("recommendName", "");
                object.put("designateTeacherName", "");
                object.put("oldDriveCarType", student.getOldLicenseType());
                object.put("oldLicenseNo", student.getOldLicenseNo());
                object.put("oldLicenseUseDate", DateUtil.formatDate(student.getOldLicenseDate(), "yyyy-MM-dd"));
                object.put("applyAddress", student.getRealProvince()+student.getRealCity()+student.getRealTown()+student.getRealAddress());
                object.put("isOtherTo", "");
                object.put("remark", student.getRemark());
                if (student.getLicenseType().equals("增驾")) {
                    if (org.apache.commons.lang.StringUtils.isNotEmpty(student.getOldLicenseImage())) {

                        String oldPhysicalPath = student.getOldLicenseImage().replace("/profile", imagePath);
                        File file = new File(oldPhysicalPath);
                        if(file.exists()){
                            String base64 = ImageUtil.imageToBase64(file);
                            object.put("oldLicenseImage",base64);
                        }
                    }
                }
                // 门店签约代码
                RegistrationStudyCenterCode registrationStudyCenterCode = registrationStudyCenterCodeService.getOne(new LambdaQueryWrapper<RegistrationStudyCenterCode>()
                        .eq(RegistrationStudyCenterCode::getRegistrationId, student.getRegistrationId())
                        .eq(RegistrationStudyCenterCode::getOrganId, student.getStudyCenterId()));
                if (registrationStudyCenterCode != null && StringUtils.isNotEmpty(registrationStudyCenterCode.getRegistrationCode())) {
                    object.put("registrationCode", registrationStudyCenterCode.getRegistrationCode());
                }else {
                    object.put("registrationCode", "");
                }

                array.add(object);
            }
            SynInterface synInterface = synInterfaceService.getOne(new QueryWrapper<SynInterface>().eq("organ_id", student.getStudyCenterId()).eq("organ_type", 6));
            if(synInterface != null && array.size()>0){

                JSONObject params = new JSONObject();
                params.put("username", synInterface.getUsername());
                params.put("password", MD5Util.md5(synInterface.getPassword()));
                params.put("timestamp", new Date().getTime());

                JSONObject p = new JSONObject();
                p.put("dataType", DataType.student);
                p.put("action", Action.add);
                p.put("data", array);

                params.put("params", MyEncrypt.getInstance().encrypt(p.toJSONString()));

                String signStr = params.getString("username")+params.getString("password")+params.getString("timestamp")+params.getString("params");
                String sign = MD5Util.md5(signStr);
                params.put("sign", sign);

                SchoolStudentCenterException ce = new SchoolStudentCenterException();
                ce.setBranchId(student.getBranchId());
                ce.setCenterName("");
                ce.setSchoolId(student.getSchoolId());
                ce.setRegistrationId(student.getRegistrationId());
                ce.setStudentId(student.getId());
                ce.setStudentIdentity(student.getIdentity());
                ce.setStudentName(student.getName());
                ce.setSubmitParams(p.toJSONString());
                ce.setSynDate(new Date());
                ce.setRequestUrl(synInterface.getUrl());

                OrganUser organUser = organUserService.getById(student.getStudyCenterId());
                if(organUser != null && StringUtils.isNotEmpty(organUser.getOrganName())){
                    ce.setCenterName(organUser.getOrganName());
                }
                //请求学时平台接口提交学员信息
                try{
                    String response = request.runPost(synInterface.getUrl(), params.toJSONString());
                    JSONObject returnJson = JSONObject.parseObject(response);
                    Integer code = returnJson.getInteger("code");
                    if(code == null){
                        code = returnJson.getInteger("Code");
                    }
                    if(code != null && code == 200){
                        ce.setIsSuccess(1);
                        ce.setSuccessReason(returnJson.toJSONString());
                        schoolStudentCenterExceptionService.save(ce);
                        student.setStudyCenterIsSyn(1);
                        student.setStudyCenterSynDate(new Date());
                        //student.setRegisteDate(new Date());
                        updateById(student);
                    }else{
                        String message = returnJson.getString("message");
                        if(StringUtils.isEmpty(message)){
                            message = returnJson.getString("Message");
                        }
                        ce.setIsSuccess(0);
                        ce.setFailReason(returnJson.toJSONString());
                        schoolStudentCenterExceptionService.save(ce);
                        throw new Exception("同步数据未成功，学时平台返回："+message);
                    }
                }catch(IOException ex){
                    log.error("send url error:"+synInterface.getUrl(),ex);
                    throw ex;
                }catch(JSONException | ClassCastException ex){
                    log.error("json parse error:"+synInterface.getUrl(),ex);
                    throw ex;
                }catch(Exception ex){
                    log.error("transfor data error:"+synInterface.getUrl(),ex);
                    throw ex;
                }
            }else{
                throw new BusinessException("数据同步接口未设置");
            }
        }
    }

    public static String getIgnoreCaseKey(JSONObject jsonObject, String targetKey)throws Exception {
        if (jsonObject == null || targetKey == null) {
            return null;
        }
        for (String key : jsonObject.keySet()) {
            if (key.equalsIgnoreCase(targetKey)) {
                return jsonObject.get(key).toString();
            }
        }
        return null;
    }
    public static JSONArray getIgnoreCaseJsonArray(JSONObject jsonObject, String targetKey)throws Exception {
        if (jsonObject == null || targetKey == null) {
            return null;
        }
        for (String key : jsonObject.keySet()) {
            if (key.equalsIgnoreCase(targetKey)) {
                return jsonObject.getJSONArray(key);
            }
        }
        return null;
    }


    /**
     * 学员退学
     * @param students
     * @return
     */
    @Override
    @Transactional
    public void submitStudentQuitDataToStudyCenter(List<SchoolStudent> students) throws Exception {
        for(SchoolStudent student : students){

            SchoolStudyCenter schoolStudentCenter = schoolStudyCenterService.getOne(new QueryWrapper<SchoolStudyCenter>()
                    .eq("school_id", student.getSchoolId()).eq("organ_id", student.getStudyCenterId()));
            JSONArray array = new JSONArray();
            if(schoolStudentCenter != null){
                JSONObject object = new JSONObject();
                object.put("orgCode", schoolStudentCenter.getSchoolCode());
                object.put("studentName", student.getName());
                object.put("idCard", student.getIdentity());
                object.put("remark", student.getRemark());
                array.add(object);
            }
            SynInterface synInterface = synInterfaceService.getOne(new QueryWrapper<SynInterface>().eq("organ_id", student.getStudyCenterId()).eq("organ_type", 6));
            if(synInterface == null || array.size()==0){
                throw new BusinessException("学时平台数据同步接口未设置");
            }

            JSONObject params = new JSONObject();
            params.put("username", synInterface.getUsername());
            params.put("password", MD5Util.md5(synInterface.getPassword()));
            params.put("timestamp", new Date().getTime());

            JSONObject p = new JSONObject();
            p.put("dataType", DataType.student);
            p.put("action", Action.quit);
            p.put("data", array);
            params.put("params", MyEncrypt.getInstance().encrypt(p.toJSONString()));

            String signStr = params.getString("username")+params.getString("password")+params.getString("timestamp")+params.getString("params");
            String sign = MD5Util.md5(signStr);
            params.put("sign", sign);
            try{
                String response = request.runPost(synInterface.getUrl(), params.toJSONString());
                JSONObject returnJson = JSONObject.parseObject(response);
                Integer code = returnJson.getInteger("code");
                if(code == null){
                    code = returnJson.getInteger("Code");
                }
                if(code == null || code != 200){
                    String message = returnJson.getString("message");
                    if(StringUtils.isEmpty(message)){
                        message = returnJson.getString("Message");
                    }
                    throw new Exception("学时平台退学失败："+message);
                }
                student.setIsQuit(1);
                student.setQuitIsSyn(1);
                student.setStatus(99);
                student.setQuitDate(new Date());
                updateById(student);

                if(student.getIsSupervise() !=null && student.getIsSupervise()==1 && student.getSuperviseFeeIsOk() != null && student.getSuperviseFeeIsOk()==1){
                    BankAccount account = new BankAccount();
                    if(StringUtils.isNotEmpty(student.getBranchId())){
                        SchoolBranch branch = schoolBranchService.getById(student.getBranchId());
                        if(branch != null && branch.getIsControledByschool() != null && branch.getIsControledByschool()==0){
                            account.setCommonAccount(branch.getBankCommonAccountNo());
                            account.setCommonAccountName(branch.getBankCommonAccountName());
                            account.setCustomerNo(branch.getBankCustomerNo());
                            account.setSuperviseAccountName(branch.getBankSuperviseAccountName());
                            account.setSuperviseAccount(branch.getBankSuperviseAccountNo());
                        }
                    }
                    if(StringUtils.isEmpty(account.getCommonAccount())){
                        School school = schoolService.getById(student.getSchoolId());
                        if(school != null){
                            account.setCommonAccount(school.getBankCommonAccountNo());
                            account.setCommonAccountName(school.getBankCommonAccountName());
                            account.setCustomerNo(school.getBankCustomerNo());
                            account.setSuperviseAccountName(school.getBankSuperviseAccountName());
                            account.setSuperviseAccount(school.getBankSuperviseAccountNo());
                        }
                    }
                    if(StringUtils.isEmpty(account.getCommonAccount())){
                        log.warn("common account don't set,schhoolId is "+student.getSchoolId()+",branchId is "+student.getBranchId());
                        continue;
                    }
                    boolean success = false;
                    try{
                        success = bankService.quitReleaseSuperviseMoney(student, account);
                    }catch(Exception ex){
                        log.error(ex.getMessage(),ex);
                        throw ex;
                    }

                    String subjectName = "退学";
                    if(success){
                        SuperviseReleaseRecord record = superviseReleaseRecordService.getOne(new QueryWrapper<SuperviseReleaseRecord>().eq("student_id", student.getId()).eq("subject_name", subjectName).last("limit 1"));
                        if(record == null){
                            //增加释放记录
                            record = new SuperviseReleaseRecord();
                            record.setAccountName(account.getCommonAccountName());
                            record.setAccountNo(account.getCommonAccount());
                            record.setBankName(sysConfigService.selectConfigByKey("cooperation.bank.name"));
                            record.setBranchId(student.getBranchId());
                            record.setCheckValidStudyTime(0L);
                            record.setExamineStudyTime(0L);
                            record.setFailCount(0);
                            record.setIsPass(0);
                            record.setIsSuccess(1);
                            record.setRegistrationId(student.getRegistrationId());
                            record.setReleaseDate(new Date());
                            record.setReleaseFee(student.getBalance());
                            record.setSchoolId(student.getSchoolId());
                            record.setStudentId(student.getId());
                            record.setSubjectName(subjectName);
                            record.setSuperviseFee(student.getSuperviseFee());
                            superviseReleaseRecordService.save(record);
                        }else{
                            record.setAccountName(account.getCommonAccountName());
                            record.setAccountNo(account.getCommonAccount());
                            record.setBankName(sysConfigService.selectConfigByKey("cooperation.bank.name"));
                            record.setBranchId(student.getBranchId());
                            record.setCheckValidStudyTime(0L);
                            record.setExamineStudyTime(0L);
                            record.setFailCount(0);
                            record.setIsPass(0);
                            record.setIsSuccess(1);
                            record.setRegistrationId(student.getRegistrationId());
                            record.setReleaseDate(new Date());
                            record.setReleaseFee(student.getBalance());
                            record.setSchoolId(student.getSchoolId());
                            record.setStudentId(student.getId());
                            record.setSubjectName(subjectName);
                            record.setSuperviseFee(student.getSuperviseFee());
                            superviseReleaseRecordService.updateById(record);
                        }

                        SupervisePay pay = supervisePayService.getById(student.getId());
                        if(pay != null){
                            pay.setReleaseAccountName(account.getCommonAccountName());
                            pay.setReleaseAccountNo(account.getCommonAccount());
                            pay.setReleaseBankName(record.getBankName());
                            pay.setRestSuperviseFee(new BigDecimal(pay.getRestSuperviseFee().floatValue()-record.getReleaseFee().floatValue()));
                            if(pay.getRestSuperviseFee().compareTo(new BigDecimal(0))==-1){
                                pay.setRestSuperviseFee(new BigDecimal(0));
                            }
                            pay.setTotalReleaseFee(new BigDecimal(pay.getTotalReleaseFee().floatValue()+record.getReleaseFee().floatValue()));
                            if(pay.getTotalReleaseFee().compareTo(pay.getSuperviseFee())==1){
                                pay.setTotalReleaseFee(pay.getSuperviseFee());
                            }
                            supervisePayService.updateById(pay);

                        }else{
                            log.warn("There is not supervise pay record,studentId: "+student.getId());
                        }
                    }else{
                        SuperviseReleaseRecord record = superviseReleaseRecordService.getOne(new QueryWrapper<SuperviseReleaseRecord>().eq("student_id", student.getId()).eq("subject_name", subjectName).last("limit 1"));
                        if(record == null){
                            //增加释放记录
                            record = new SuperviseReleaseRecord();
                            record.setAccountName(account.getCommonAccountName());
                            record.setAccountNo(account.getCommonAccount());
                            record.setBankName(sysConfigService.selectConfigByKey("cooperation.bank.name"));
                            record.setBranchId(student.getBranchId());
                            record.setCheckValidStudyTime(0L);
                            record.setExamineStudyTime(0L);
                            record.setFailCount(1);
                            record.setIsPass(0);
                            record.setIsSuccess(0);
                            record.setRegistrationId(student.getRegistrationId());
                            record.setReleaseDate(new Date());
                            record.setReleaseFee(student.getBalance());
                            record.setSchoolId(student.getSchoolId());
                            record.setStudentId(student.getId());
                            record.setSubjectName(subjectName);
                            record.setSuperviseFee(student.getSuperviseFee());
                            superviseReleaseRecordService.save(record);
                        }else{
                            record.setAccountName(account.getCommonAccountName());
                            record.setAccountNo(account.getCommonAccount());
                            record.setBankName(sysConfigService.selectConfigByKey("cooperation.bank.name"));
                            record.setBranchId(student.getBranchId());
                            record.setCheckValidStudyTime(0L);
                            record.setExamineStudyTime(0L);
                            record.setFailCount(record.getFailCount()+1);
                            record.setIsPass(0);
                            record.setIsSuccess(0);
                            record.setRegistrationId(student.getRegistrationId());
                            record.setReleaseDate(new Date());
                            record.setReleaseFee(new BigDecimal(sysConfigService.selectConfigByKey("subject2.release.amt")));
                            record.setSchoolId(student.getSchoolId());
                            record.setStudentId(student.getId());
                            record.setSubjectName(subjectName);
                            record.setSuperviseFee(student.getSuperviseFee());
                            superviseReleaseRecordService.updateById(record);
                        }
                    }
                }
            }catch(IOException ex){
                log.error("send url error:"+synInterface.getUrl());
                throw ex;
            }catch(JSONException | ClassCastException ex){
                log.error("json parse error:"+synInterface.getUrl(),ex);
                throw ex;
            }catch(Exception ex){
                log.error("transfor data error:"+synInterface.getUrl(),ex);
                throw ex;
            }
        }
    }

    /**
     * 从计时平台获取学员的学号
     * @param paramType 参数类型：0=身份证，1=学号
     * @param queryParam 参数
     * @return com.alibaba.fastjson.JSONArray *
     */
    public JSONArray getStudentInfoFromStudyTimeCenter(Integer paramType, String queryParam) throws Exception{

        LambdaQueryWrapper<SchoolStudent> wrapper = new LambdaQueryWrapper<>();
        if (paramType == 0) {
            wrapper.eq(SchoolStudent::getIdentity, queryParam).eq(SchoolStudent::getIsQuit, 0);
        }else if (paramType == 1) {
            wrapper.eq(SchoolStudent::getStunum, queryParam).eq(SchoolStudent::getIsQuit, 0);
        }

        SchoolStudent schoolStudent = schoolStudentMapper.selectOne(wrapper);

        if (schoolStudent != null) {
            SynInterface synInterface = synInterfaceService.getOne(new LambdaQueryWrapper<SynInterface>()
                    .eq(SynInterface::getOrganId, schoolStudent.getStudyCenterId())
                    .eq(SynInterface::getOrganType, 6).last("limit 1"));
            if (synInterface != null) {
                if(StringUtils.isNotEmpty(synInterface.getUrl())){
                    JSONArray array = new JSONArray();
                    if (paramType == 0) {
                        JSONObject object = new JSONObject();
                        object.put("idCard", queryParam);
                        array.add(object);
                    }else if (paramType == 1) {
                        JSONObject object = new JSONObject();
                        object.put("stunum", queryParam);
                        array.add(object);
                    }
                    JSONObject params = new JSONObject();
                    params.put("username", synInterface.getUsername());
                    params.put("password", MD5Util.md5(synInterface.getPassword()));
                    params.put("timestamp", new Date().getTime());

                    JSONObject p = new JSONObject();
                    p.put("dataType", DataType.student);
                    p.put("action", Action.query);
                    p.put("data", array);
                    log.info("查询参参数：==="+p.toJSONString());
                    params.put("params", MyEncrypt.getInstance().encrypt(p.toJSONString()));

                    String signStr = params.getString("username")+params.getString("password")+params.getString("timestamp")+params.getString("params");
                    String sign = MD5Util.md5(signStr);
                    params.put("sign", sign);
                    try{
                        log.info("getStudentInfoFromStudyTimeCenter:"+synInterface.getUrl());
                        log.info("getStudentInfoFromStudyTimeCenter json:"+ params.toJSONString());
                        String response = request.runPost(synInterface.getUrl(), params.toJSONString());
                        JSONObject returnJson = JSONObject.parseObject(response.toLowerCase());
                        log.info("getStudentInfoFromStudyTimeCenter response:"+ returnJson.toJSONString());
                        Integer code = returnJson.getInteger("code");
                        if(code == 200){
                            JSONArray returnArray =  returnJson.getJSONArray("data");
                            if(returnArray.size()>0){
                                return returnArray;
                            }else {
                                throw new BusinessException("未查询到数据");
                            }
                        }else if (code == 500) {
                            throw new BusinessException(returnJson.getString("message"));
                        }
                    }catch(Exception ex){
                        log.error("getStudentInfoFromStudyTimeCenter error",ex);
                    }
                }
            }else {
                throw new BusinessException("查询失败");
            }
        }
        return null;
    }

    @Override
    public VariousNumberVo getVariousNumber() {
        List<SchoolStudent> schoolStudents = schoolStudentMapper.selectList(null);
        VariousNumberVo variousNumberVo = new VariousNumberVo();

        long enrollTotal =
                schoolStudents.stream()
                        .filter(schoolStudent -> schoolStudent != null && schoolStudent.getStatus() == 2).count();

        long unauditedTotal =
                schoolStudents.stream()
                        .filter(schoolStudent -> schoolStudent != null && schoolStudent.getIsCheck() == 0).count();

        long studyCenterIsSynTotal =
                schoolStudents.stream()
                        .filter(schoolStudent -> schoolStudent != null && schoolStudent.getStudyCenterIsSyn() == 0).count();

        long unregulatedTotal =
                schoolStudents.stream()
                        .filter(schoolStudent -> schoolStudent != null && schoolStudent.getIsSupervise() == 0).count();

        long dropOutTotal =
                schoolStudents.stream()
                        .filter(schoolStudent -> schoolStudent != null && schoolStudent.getIsQuit() == 1).count();

//        List<SchoolStudent> graduations =
//                schoolStudents.stream()
//                        .filter(schoolStudent -> schoolStudent != null && schoolStudent.getStatus() == 99)
//                        .collect(Collectors.toList());
        long graduationTotal = 0;
//        if (CollectionUtil.isNotEmpty(graduations)) {
//            graduationTotal = graduations.size();
//        }

        variousNumberVo.setEnrollTotal(enrollTotal);
        variousNumberVo.setUnauditedTotal(unauditedTotal);
        variousNumberVo.setStudyCenterIsSynTotal(studyCenterIsSynTotal);
        variousNumberVo.setUnregulatedTotal(unregulatedTotal);
        variousNumberVo.setDropOutTotal(dropOutTotal);
        variousNumberVo.setGraduationTotal(graduationTotal);
        System.out.println(schoolStudents.size());
        System.out.println(unauditedTotal);
        System.out.println(studyCenterIsSynTotal);
        System.out.println(unregulatedTotal);
        System.out.println(dropOutTotal);
        System.out.println(graduationTotal);
        return variousNumberVo;
    }


    @Override
    public List<SchoolStudent> customSelectSchoolStudentList(SchoolStudent schoolStudent) {
        //如果为驾协用户，则需要选择驾校、填写学员名字、身份证、手机号码、学号才允许查询
        SysOrganUser sysOrganUser = ShiroUtils.getSysOrganUser();
        if(sysOrganUser!=null && sysOrganUser.getUsername().equalsIgnoreCase("jiaxie")){
            List<SchoolStudent>schoolStudents = new ArrayList<>();
            if(StringUtils.isBlank(schoolStudent.getSchoolId()) && StringUtils.isBlank(schoolStudent.getName()) && StringUtils.isBlank(schoolStudent.getStunum())
                    && StringUtils.isBlank(schoolStudent.getIdentity()) && StringUtils.isBlank(schoolStudent.getMobile())&&schoolStudent.getParams()==null){
                return schoolStudents;
            }else if(schoolStudent.getParams().get("beginTime")!=null || schoolStudent.getParams().get("endTime")!=null) {
                if(StringUtils.isBlank(schoolStudent.getParams().get("beginTime").toString()) && StringUtils.isBlank(schoolStudent.getParams().get("endTime").toString())){
                    return schoolStudents;
                }
            }
            else{
                //协会视角下的身份证、手机号、学号、学员名称必须精确搜索，因此区别开下方的模糊搜索方法
                Map<String, Object> params = schoolStudent.getParams();
                params.put("isJiaxie",1);
            }
        }


        if (StringUtils.isNotBlank(schoolStudent.getIdentity())) {
            List<String> identityList = Arrays.asList(schoolStudent.getIdentity().split(" "));
            schoolStudent.setIdentityList(identityList);
        }
        return schoolStudentMapper.customSelectSchoolStudentList(schoolStudent);
    }


    /**
     * 学员统计数据
     * @return 结果
     */
    @Override
    public StudentCountVo getSchoolStudentCount(SchoolStudent schoolStudent) {
        return schoolStudentMapper.getSchoolStudentCount(schoolStudent);
    }

    /**
     * 查询结业学员列表
     */
    @Override
    public List<SchoolStudent> selectSchoolGraduateStudentList(SchoolStudent schoolStudent) {
        return schoolStudentMapper.selectSchoolGraduateStudentList(schoolStudent);
    }

    /**
     * 导出结业学员列表
     */
    @Override
    public List<SchoolStudent> exportGraduateSchoolStudentList(SchoolStudent schoolStudent) {
        return schoolStudentMapper.exportSchoolGraduateStudentList(schoolStudent);
    }

    /**
     * 修改学员（对外访问接口）
     */
    @Override
    public int studentEventStrategyUpdate(SchoolStudent schoolStudent) {
        schoolStudent.setUpdatedTime(new Date());
        //schoolStudent.setIsCheck(0);
        schoolStudent.setRealProvince(schoolStudent.getHujiProvince());
        schoolStudent.setRealCity(schoolStudent.getHujiCity());
        schoolStudent.setRealTown(schoolStudent.getHujiTown());
        schoolStudent.setRealAddress(schoolStudent.getHujiAddress());
        return schoolStudentMapper.updateSchoolStudent(schoolStudent);
    }

    @Override
    public String releaseMoney(SchoolStudent student,int trainphase) throws Exception {
        //通知银行释放
        if (student.getIsSupervise() != null && student.getIsSupervise() == 1 && student.getSuperviseFeeIsOk() != null && student.getSuperviseFeeIsOk() == 1) {
            try {
                //由于晚上有银行对账时间，以防订单状态不一致，释放时间和监管时间需相差一天
                Date now = new Date();
                Calendar cal = Calendar.getInstance();
                cal.setTime(now);
                cal.add(Calendar.DATE, -1);
                if(student.getSuperviseDate().compareTo(cal.getTime()) > 0) {
                    superviseExceptionDayService.addReleaseFail(student.getResidueSuperviseAmt(), now, student, "释放监管金额时间和受监管时间相差太短，需相差一天");
                    return null;
                }

                // 获取驾校得到银行卡
                School school = schoolService.getById(student.getSchoolId());

                JSONObject paramsObject = provinceStudentService.getReleaseParams(student.getId());
                String clientNo = paramsObject.getString("clientNo");
                String payAcctNo = paramsObject.getString("payAcctNo");;
                String signKey = paramsObject.getString("signKey");;
                String payAcctName = paramsObject.getString("payAcctName");;
                String sysNum = paramsObject.getString("sysNum");

                // 如果没有设置释放账号就增加释放异常
                if (StringUtils.isEmpty(payAcctNo) ||
                        StringUtils.isEmpty(clientNo) || StringUtils.isEmpty(signKey)){
                    superviseExceptionDayService.addReleaseFail(student.getResidueSuperviseAmt(), now, student, school.getName()+"未设置释放银行卡，暂停释放");
                    return null;
                }

                BigDecimal withdrawalAmount = student.getResidueSuperviseAmt();
                // 判断驾校总监管金额是否足够本次提现
                if (school.getSuperviseAmt().compareTo(withdrawalAmount) < 0) {
                    superviseExceptionDayService.addReleaseFail(withdrawalAmount, now, student, school.getName()+"驾校剩余可释放监管金额不足，暂停释放");
                    return null;
                }

                BankTransactionRecord btr = new BankTransactionRecord()
                        .setStudentId(student.getId())
                        .setSchoolId(student.getSchoolId())
                        .setRegistrationId(student.getRegistrationId())
                        .setBranchId(student.getBranchId())
                        .setAmount(withdrawalAmount)
                        .setType(BankTransactionRecordType.WITHDRAW)
                        .setCreateTime(now);

                String subjectName = sysDictDataService.selectDictLabel("study_stage",String.valueOf(trainphase));
                JSONObject extendData = new JSONObject();
                extendData.put("subjectName", subjectName);
                extendData.put("amount", withdrawalAmount);
                extendData.put("trainPhase", trainphase);

                btr.setExtendData(extendData.toJSONString());

                JSONObject responseJson = new JSONObject();
                boolean success = false;
                try {
                    success = unionBankService.withdrawal(withdrawalAmount,sysNum,clientNo,payAcctNo
                            ,signKey,student.getName()+"/"+student.getIdentity()+"/"+subjectName,responseJson);
                } catch (Exception ex) {
                    log.error("提现失败", ex);
                }

                String releaseOrderNo = responseJson.getString("withdrawalNo");
                String transactionNo = responseJson.getString("transactionNo");

                SuperviseReleaseRecord record = new SuperviseReleaseRecord()
                        .setAccountName(payAcctName)
                        .setAccountNo(school.getPayAcctNo()).setBranchId(student.getBranchId())
                        .setIsSuccess(success ? 1 : 0).setRegistrationId(student.getRegistrationId())
                        .setReleaseDate(now).setReleaseFee(withdrawalAmount)
                        .setSchoolId(student.getSchoolId()).setStudentId(student.getId())
                        .setSubjectName(subjectName).setSuperviseFee(student.getSuperviseFee())
                        .setBankAcctName(payAcctName).setPayAcctNo(payAcctNo)
                        .setPushTime(now).setOrderNo(releaseOrderNo);
                superviseReleaseRecordService.save(record);

                btr.setBankOrderId(transactionNo);
                btr.setOrderId(releaseOrderNo);

                if (success) {
                    log.info(student.getName()+"("+student.getId()+")退学银行资金释放成功...........");
                    try {
                        supervisePayService.withdrawSuccess(student.getId(),payAcctName,payAcctNo,trainphase,withdrawalAmount,2,releaseOrderNo,transactionNo);
                        btr.setStatus(true);
                        btr.setBankStatus("0");
                        bankTransactionRecordService.save(btr);
                    }catch(Exception ex) {
                        btr.setStatus(false);
                        btr.setBankStatus("2");
                        bankTransactionRecordService.save(btr);
                        record.setIsSuccess(0);
                        superviseReleaseRecordService.updateById(record);
                        log.error("正在为"+student.getName()+"("+student.getId()+")退学业务操作失败",ex);
                    }

                }else {
                    log.info(student.getName()+"("+student.getId()+")退学银行资金资金失败...........");
                    btr.setErrMsg(responseJson.getString("errMsg"));
                    btr.setStatus(false);
                    btr.setBankStatus("2");
                    bankTransactionRecordService.save(btr);
                    superviseExceptionDayService.addReleaseFail(student.getResidueSuperviseAmt(), now, student, responseJson.getString("errMsg"));
                }
                return btr.getOrderId();

            }catch(Exception ex) {
                log.error("releaseMoney error",ex);
                throw ex;
            }
        }
        return null;
    }

    /**
     * 不真正释放资金，适合线下退款的情况
     * */
    @Override
    public void releaseMoney(SchoolStudent student, BigDecimal releaseMoney) {
        //通知银行释放
        if (student.getIsSupervise() != null && student.getIsSupervise() == 1 && student.getSuperviseFeeIsOk() != null && student.getSuperviseFeeIsOk() == 1) {
            try {
                Date now = new Date();
                // 获取驾校得到银行卡
                School school = schoolService.getById(student.getSchoolId());

                JSONObject paramsObject = provinceStudentService.getReleaseParams(student.getId());
                String clientNo = paramsObject.getString("clientNo");
                String payAcctNo = paramsObject.getString("payAcctNo");;
                String signKey = paramsObject.getString("signKey");;
                String payAcctName = paramsObject.getString("payAcctName");;
                String sysNum = paramsObject.getString("sysNum");

                // 如果没有设置释放账号就增加释放异常
                if (StringUtils.isEmpty(payAcctNo) ||
                        StringUtils.isEmpty(clientNo) || StringUtils.isEmpty(signKey)){
                    superviseExceptionDayService.addReleaseFail(student.getResidueSuperviseAmt(), now, student, school.getName()+"未设置释放银行卡，暂停释放");
                }

                BigDecimal withdrawalAmount = releaseMoney;
                // 判断驾校总监管金额是否足够本次提现
                if (school.getSuperviseAmt().compareTo(withdrawalAmount) < 0) {
                    superviseExceptionDayService.addReleaseFail(withdrawalAmount, now, student, school.getName()+"驾校剩余可释放监管金额不足，暂停释放");
                }

                BankTransactionRecord btr = new BankTransactionRecord()
                        .setStudentId(student.getId())
                        .setSchoolId(student.getSchoolId())
                        .setRegistrationId(student.getRegistrationId())
                        .setBranchId(student.getBranchId())
                        .setAmount(withdrawalAmount)
                        .setType(BankTransactionRecordType.WITHDRAW)
                        .setCreateTime(now);

                String subjectName = sysDictDataService.selectDictLabel("study_stage",String.valueOf(0));
                JSONObject extendData = new JSONObject();
                extendData.put("subjectName", subjectName);
                extendData.put("amount", withdrawalAmount);
                extendData.put("trainPhase", 0);

                btr.setExtendData(extendData.toJSONString());

                String releaseOrderNo = RandomUtil.randomString(10);
                String transactionNo = RandomUtil.randomString(10);

                //线下退款不走线上释放，因此不需要在释放表中展示
//                SuperviseReleaseRecord record = new SuperviseReleaseRecord()
//                        .setAccountName(payAcctName)
//                        .setAccountNo(school.getPayAcctNo()).setBranchId(student.getBranchId())
//                        .setIsSuccess(1).setRegistrationId(student.getRegistrationId())
//                        .setReleaseDate(now).setReleaseFee(withdrawalAmount)
//                        .setSchoolId(student.getSchoolId()).setStudentId(student.getId())
//                        .setSubjectName(subjectName).setSuperviseFee(student.getSuperviseFee())
//                        .setBankAcctName(payAcctName).setPayAcctNo(payAcctNo)
//                        .setPushTime(now).setOrderNo(releaseOrderNo);
//                superviseReleaseRecordService.save(record);

                btr.setBankOrderId(transactionNo);
                btr.setOrderId(releaseOrderNo);

                try {
                    supervisePayService.withdrawSuccess(student.getId(),payAcctName,payAcctNo,0,withdrawalAmount,2,releaseOrderNo,transactionNo);
                    btr.setStatus(true);
                    btr.setBankStatus("0");
                    bankTransactionRecordService.save(btr);
                    DivsionOrder divsionOrder = divsionOrderService.getOne(new LambdaQueryWrapper<>(DivsionOrder.class)
                            .eq(DivsionOrder::getStudentId,student.getId()).eq(DivsionOrder::getIsPay,1)
                            .orderByDesc(DivsionOrder::getId).last("limit 1"));
                    if(divsionOrder != null) {
                        divsionOrderService.refund(divsionOrder);
                    }
                }catch(Exception ex) {
                    btr.setStatus(false);
                    btr.setBankStatus("2");
                    bankTransactionRecordService.save(btr);
//                    record.setIsSuccess(0);
//                    superviseReleaseRecordService.updateById(record);
                    log.error("正在为"+student.getName()+"("+student.getId()+")退学业务操作失败",ex);
                }

            }catch(Exception ex) {
                log.error("releaseMoney error",ex);
                throw ex;
            }
        }
    }

    @Override
    public void notifySchoolStudyCenterQuit(SchoolStudent student) {
        SchoolStudyCenter schoolStudentCenter = schoolStudyCenterService.getOne(new QueryWrapper<SchoolStudyCenter>()
                .eq("school_id", student.getSchoolId()).eq("organ_id", student.getStudyCenterId()));
        JSONArray array = new JSONArray();
        if (schoolStudentCenter != null) {
            JSONObject object = new JSONObject();
            object.put("orgCode", schoolStudentCenter.getSchoolCode());
            object.put("studentName", student.getName());
            object.put("idCard", student.getIdentity());
            object.put("remark", student.getRemark());
            array.add(object);
        }
        SynInterface synInterface = synInterfaceService.getOne(new QueryWrapper<SynInterface>().eq("organ_id", student.getStudyCenterId()).eq("organ_type", 6));
        if (synInterface != null && array.size() > 0) {
            JSONObject params = new JSONObject();
            params.put("username", synInterface.getUsername());
            params.put("password", MD5Util.md5(synInterface.getPassword()));
            params.put("timestamp", new Date().getTime());

            JSONObject p = new JSONObject();
            p.put("dataType", DataType.student);
            p.put("action", Action.quit);
            p.put("data", array);
            log.info("退学发起url："+synInterface.getUrl());
            log.info("退学发起："+p.toJSONString());
            params.put("params", MyEncrypt.getInstance().encrypt(p.toJSONString()));

            String signStr = params.getString("username") + params.getString("password") + params.getString("timestamp") + params.getString("params");
            String sign = MD5Util.md5(signStr);
            params.put("sign", sign);
            try {
                String response = request.runPost(synInterface.getUrl(), params.toJSONString());
                log.info("退学返回："+response);
                JSONObject returnJson = JSONObject.parseObject(response);
                Integer code = returnJson.getInteger("code");
                if (code == null) {
                    code = returnJson.getInteger("Code");
                }
                if(code == null || code != 200) {
                    log.info("正在为"+student.getName()+"("+student.getId()+")退学同步到计时中心失败");
                }else {
                    log.info("正在为"+student.getName()+"("+student.getId()+")退学同步到计时中心成功");
                }
            }catch(Exception ex) {
                log.error("send request error",ex);
            }
        }
    }



    @Override
    public String quit(SchoolStudent student) throws Exception {
        try {
            lock.lock();

            SchoolStudentDropOut dropOut = schoolStudentDropOutService.getOne(new QueryWrapper<SchoolStudentDropOut>()
                    .eq("student_id", student.getId())
                    .eq("is_done",1)
                    .in("status",4,7)
                    .last("limit 1"));
            if(dropOut == null) {
                throw new BusinessException(student.getName()+"无退学申请，或者退学申请流程未完结");
            }
            log.info("正在为"+student.getName()+"("+student.getId()+")退学释放资金...........");
            String orderId = releaseMoney(student,5);
            final SchoolStudent stu = student;
            ThreadUtil.execute(new Runnable() {
                @Override
                public void run() {
                    notifySchoolStudyCenterQuit(stu);
                }
            });
            return orderId;
        }catch(Exception ex) {
            log.error("student quit error",ex);
            throw ex;
        }finally {
            lock.unlock();
        }
    }

    /**
     * 修改学号保存
     * <AUTHOR>
     * @date 2023/11/3 13:56
     * @param schoolStudent  *
     */
    @Override
    public int editStunumSave(SchoolStudent schoolStudent) {
        return schoolStudentMapper.updateById(schoolStudent);
    }

    /**
     * 查询学员在计时平台的数据
     * <AUTHOR>
     * @date 2023/11/3 15:31
     * @param stunum 学号
     * @return java.util.List<com.guangren.business.domain.ProvinceStudyTime> *
     */
    @Override
    public List<ProvinceStudyTime> getStudentInfoStudyTime(String stunum) {
        List<ProvinceStudyTime> list = provinceStudyTimeService.list(new LambdaQueryWrapper<ProvinceStudyTime>()
                .eq(ProvinceStudyTime::getStunum, stunum));
        List<ProvinceStudyTime> collect = list.stream()
                .filter(provinceStudyTime -> "2".equals(provinceStudyTime.getTrainphase()))
                .collect(Collectors.toList());

        // 数据库的数据包含科目2则直接返回
        if (collect.size() > 0) {
            return list;
        }else {
            // 不包含科目2的数据则从计时平台获取（前14天内的数据）
            List<String> fourteenDayStr = getFourteenDayStr();
            fourteenDayStr.forEach(date -> {
                try {
                    provinceStudentService.getStudyTime(stunum, date);
                    ThreadUtil.sleep(2000);
                } catch (Exception e) {
                    log.error("从计时平台获取数据发生异常:"+e.getMessage());
                }
            });

            return provinceStudyTimeService.list(new LambdaQueryWrapper<ProvinceStudyTime>()
                    .eq(ProvinceStudyTime::getStunum, stunum));
        }
    }

    /**
     * 获取前14天的日期字符串
     * <AUTHOR>
     * @date 2023/11/3 16:25   *
     */
    private List<String> getFourteenDayStr() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<String> dateStrings = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            // 格式化当前日期为字符串
            String formattedDate = currentDate.format(formatter);
            dateStrings.add(formattedDate);
            // 减去一天，获取前一天的日期
            currentDate = currentDate.minusDays(1);
        }
        return dateStrings;
    }

    @Autowired
    private ISchoolDealFlowService schoolDealFlowService;
    @Autowired
    private ISuperviseFlowService superviseFlowService;

    @Transactional
    public void handle(SchoolStudent student,School school,SuperviseReleaseRecord record,JSONObject responseJson,BigDecimal withdrawAmount){
        log.info(student.getName()+"释放剩余金额:"+student.getResidueSuperviseAmt()+"成功");
        BankTransactionRecord btr = bankTransactionRecordService.getOne(new QueryWrapper<BankTransactionRecord>()
                .eq("order_id",record.getOrderNo()));

        if(btr != null) {
            //增加冲抵记录
//            BigDecimal btrbAmount = record.getReleaseFee().add(withdrawAmount);
//            BankTransactionRecordBackup btrb = new BankTransactionRecordBackup();
//            BeanUtil.copyProperties(btr, btrb);
//            btrb.setAmount(btrbAmount);
//            btrb.setIsDikou(0);
//            btrb.setOrderId(btr.getOrderId()+","+responseJson.getString("withdrawalNo"));
//            btrb.setBankOrderId(btr.getBankOrderId()+","+responseJson.getString("transactionNo"));
//            bankTransactionRecordBackupService.save(btrb);
            log.info("BankTransactionRecordBackup add success:"+student.getName());
            //修改银行交易记录
            btr.setStatus(false);
            btr.setBankStatus("1");
            bankTransactionRecordService.updateById(btr);
            log.info("bankTransactionRecord update success:"+student.getName());

            //修改释放记录
            record.setIsSuccess(0);
            superviseReleaseRecordService.updateById(record);
            log.info("superviseReleaseRecord update success:"+student.getName());
            //修改学员余额
            student.setResidueSuperviseAmt(student.getResidueSuperviseAmt().add(record.getReleaseFee()));
            this.updateById(student);
            log.info("student update success:"+student.getName());

            //修改监管信息
            SupervisePay supervisePay =  supervisePayService.getById(student.getId());
            if(supervisePay != null){
                supervisePay.setRestSuperviseFee(supervisePay.getRestSuperviseFee().add(record.getReleaseFee()));
                supervisePay.setTotalReleaseFee(supervisePay.getTotalReleaseFee().subtract(record.getReleaseFee()));
                supervisePay.setUpdatedTime(new Date());
                supervisePayService.updateById(supervisePay);
                log.info("supervisePay update success:"+student.getName());
            }
            //修改学校余额
            BigDecimal superviseAmt = school.getSuperviseAmt();
            BigDecimal newSuperviseAmt = superviseAmt.add(record.getReleaseFee());
            if(newSuperviseAmt.compareTo(new BigDecimal(0))<0) {
                newSuperviseAmt = new BigDecimal(0);
            }
            school.setSuperviseAmt(newSuperviseAmt);
            boolean flag = schoolService.updateById(school);
            if(!flag) {
                throw new BusinessException("school更新失败");
            }
            log.info("school update success:"+student.getName()+"("+school.getName()+")");

            JSONObject extendData = JSONObject.parseObject(btr.getExtendData());
            String trainPhase = extendData.getString("trainPhase");
            String subjectName = extendData.getString("subjectName");
            provinceStudyTimeService.update(new LambdaUpdateWrapper<ProvinceStudyTime>()
                    .set(ProvinceStudyTime::getIsRelease, 0)
                    .set(ProvinceStudyTime::getReleaseType,0)
                    .eq(ProvinceStudyTime::getName, student.getName())
                    .eq(ProvinceStudyTime::getIdcard, student.getIdentity())
                    .eq(ProvinceStudyTime::getTraintype, student.getLicenseType())
                    .eq(ProvinceStudyTime::getTrainphase, trainPhase));

            //修改交易记录
            BigDecimal releaseAmt = record.getReleaseFee();
            SchoolDealFlow lastRecord = schoolDealFlowService.getOne(new QueryWrapper<SchoolDealFlow>().eq("school_id", student.getSchoolId()).orderByDesc("id").last("limit 1"));
            if(lastRecord == null){
                SchoolDealFlow schoolDealFlow = new SchoolDealFlow();
                schoolDealFlow.setSchoolId(student.getSchoolId());
                schoolDealFlow.setAction(2);
                schoolDealFlow.setLastBalance(BigDecimal.ZERO);
                schoolDealFlow.setTheAmt(releaseAmt);
                schoolDealFlow.setTheBalance(releaseAmt);
                schoolDealFlow.setBankOrderId(responseJson.getString("transactionNo"));
                schoolDealFlow.setStudentId(student.getId());
                switch (Integer.parseInt(trainPhase)) {
                    case 1: schoolDealFlow.setRelationSubject("科目1"); break;
                    case 2: schoolDealFlow.setRelationSubject("科目2"); break;
                    case 3: schoolDealFlow.setRelationSubject("科目3"); break;
                    case 4: schoolDealFlow.setRelationSubject("科目4"); break;
                    case 0: schoolDealFlow.setRelationSubject(subjectName); break;
                }
                schoolDealFlowService.save(schoolDealFlow);
            }else{
                SchoolDealFlow schoolDealFlow = new SchoolDealFlow();
                schoolDealFlow.setSchoolId(student.getSchoolId());
                schoolDealFlow.setAction(2);
                schoolDealFlow.setLastBalance(lastRecord.getTheBalance());
                schoolDealFlow.setTheAmt(releaseAmt);
                schoolDealFlow.setTheBalance(schoolDealFlow.getLastBalance().add(schoolDealFlow.getTheAmt()));
                schoolDealFlow.setStudentId(student.getId());
                switch (Integer.parseInt(trainPhase)) {
                    case 1: schoolDealFlow.setRelationSubject("科目1"); break;
                    case 2: schoolDealFlow.setRelationSubject("科目2"); break;
                    case 3: schoolDealFlow.setRelationSubject("科目3"); break;
                    case 4: schoolDealFlow.setRelationSubject("科目4"); break;
                    case 0: schoolDealFlow.setRelationSubject(subjectName); break;
                }
                schoolDealFlowService.save(schoolDealFlow);

                lastRecord.setUpdatedTime(lastRecord.getUpdatedTime());
                flag = schoolDealFlowService.updateById(lastRecord);
                if(!flag) {
                    throw new BusinessException("schoolDealFlow更新失败");
                }
            }

            SuperviseFlow lastRecord2 = superviseFlowService.getOne(new QueryWrapper<SuperviseFlow>().orderByDesc("id").last("limit 1"));
            if(lastRecord2 == null){
                // 保存监管账户流水
                SuperviseFlow superviseFlow = new SuperviseFlow();
                superviseFlow.setSchoolId(student.getSchoolId());
                superviseFlow.setAction(2);
                superviseFlow.setLastBalance(BigDecimal.ZERO);
                superviseFlow.setTheAmt(releaseAmt);
                superviseFlow.setTheBalance(releaseAmt);
                superviseFlow.setStudentId(student.getId());
                switch (Integer.parseInt(trainPhase)) {
                    case 1: superviseFlow.setRelationSubject("科目1"); break;
                    case 2: superviseFlow.setRelationSubject("科目2"); break;
                    case 3: superviseFlow.setRelationSubject("科目3"); break;
                    case 4: superviseFlow.setRelationSubject("科目4"); break;
                    case 0: superviseFlow.setRelationSubject(subjectName); break;
                }
                superviseFlowService.save(superviseFlow);
            }else{
                SuperviseFlow superviseFlow = new SuperviseFlow();
                superviseFlow.setSchoolId(student.getSchoolId());
                superviseFlow.setAction(2);
                superviseFlow.setLastBalance(lastRecord2.getTheBalance());
                superviseFlow.setTheAmt(releaseAmt);
                superviseFlow.setTheBalance(superviseFlow.getLastBalance().add(superviseFlow.getTheAmt()));
                superviseFlow.setStudentId(student.getId());
                switch (Integer.parseInt(trainPhase)) {
                    case 1: superviseFlow.setRelationSubject("科目1"); break;
                    case 2: superviseFlow.setRelationSubject("科目2"); break;
                    case 3: superviseFlow.setRelationSubject("科目3"); break;
                    case 4: superviseFlow.setRelationSubject("科目4"); break;
                    case 0: superviseFlow.setRelationSubject(subjectName); break;
                }
                superviseFlowService.save(superviseFlow);

                lastRecord2.setUpdatedTime(lastRecord2.getUpdatedTime());
                flag = superviseFlowService.updateById(lastRecord2);
                if(!flag) {
                    throw new BusinessException("superviseFlow更新失败");
                }
            }
        }
    }

//    @Override
//    @Transactional
//    public void dikou(SchoolStudent student, School school, TempDikouData tdd){
//
//        String orderNo = "CD" + DateUtil.formatDate(new Date(), "yyyyMMddHHmmssSSS") + RandomStringUtils.randomNumeric(7);
//
//        SupervisePay pay = supervisePayService.getById(student.getId());
//        if (pay != null) {
//            pay.setReleaseAccountName(school.getBankAcctName());
//            pay.setReleaseAccountNo(school.getPayAcctNo());
//            pay.setReleaseBankName(school.getBankName());
//            pay.setRestSuperviseFee(pay.getRestSuperviseFee().subtract(tdd.getAmount()));
//            if (pay.getRestSuperviseFee().compareTo(new BigDecimal(0)) < 0) {
//                pay.setRestSuperviseFee(new BigDecimal(0));
//            }
//            pay.setTotalReleaseFee(pay.getTotalReleaseFee().add(tdd.getAmount()));
//            if (pay.getTotalReleaseFee().compareTo(pay.getSuperviseFee()) > 0) {
//                pay.setTotalReleaseFee(pay.getSuperviseFee());
//            }
//            supervisePayService.updateById(pay);
//        }
//
//        ProvinceStudyTime pst = provinceStudyTimeService.getOne(new LambdaUpdateWrapper<ProvinceStudyTime>()
//                .eq(ProvinceStudyTime::getName,student.getName())
//                .eq(ProvinceStudyTime::getIdcard,student.getIdentity())
//                .eq(ProvinceStudyTime::getTraintype,student.getLicenseType())
//                .eq(ProvinceStudyTime::getTrainphase,tdd.getTrainphase())
//                .eq(ProvinceStudyTime::getIsRelease,0));
//        if(pst != null){
//            pst.setIsRelease(1);
//            pst.setReleaseType(2);
//            provinceStudyTimeService.updateById(pst);
//        }
//
//        // 减去学生剩余待释放监管金额并更新
//        student.setResidueSuperviseAmt(student.getResidueSuperviseAmt().subtract(tdd.getAmount()));
//        this.updateById(student);
//
//        // 减去驾校总监管金额并更新
//        school.setSuperviseAmt(school.getSuperviseAmt().subtract(tdd.getAmount()));
//        schoolService.updateById(school);
//
//
//        String subjectName = null;
//        switch (tdd.getTrainphase()) {
//            case 1: subjectName = "科目1"; break;
//            case 2: subjectName = "科目2"; break;
//            case 3: subjectName = "科目3"; break;
//            case 4: subjectName = "科目4"; break;
//            default: subjectName = "无";
//        }
//        // 保存驾校交易流水
//        SchoolDealFlow lastRecord = schoolDealFlowService.getOne(new QueryWrapper<SchoolDealFlow>().eq("school_id", school.getId()).orderByDesc("id").last("limit 1"));
//        if(lastRecord != null) {
//            SchoolDealFlow schoolDealFlow = new SchoolDealFlow();
//            schoolDealFlow.setSchoolId(school.getId());
//            schoolDealFlow.setAction(0);
//            schoolDealFlow.setLastBalance(lastRecord.getTheBalance());
//            schoolDealFlow.setTheAmt(BigDecimal.ZERO.subtract(tdd.getAmount()));
//            schoolDealFlow.setTheBalance(schoolDealFlow.getLastBalance().add(schoolDealFlow.getTheAmt()));
//            schoolDealFlow.setStudentId(student.getId());
//            schoolDealFlow.setBankOrderId(orderNo);
//            schoolDealFlow.setRelationSubject(subjectName);
//            schoolDealFlowService.save(schoolDealFlow);
//        }
//
//        SuperviseFlow lastRecord2 = superviseFlowService.getOne(new QueryWrapper<SuperviseFlow>().orderByDesc("id").last("limit 1"));
//        // 保存监管账户流水
//        if(lastRecord2 != null) {
//            SuperviseFlow superviseFlow = new SuperviseFlow();
//            superviseFlow.setSchoolId(school.getId());
//            superviseFlow.setAction(0);
//            superviseFlow.setLastBalance(lastRecord2.getTheBalance());
//            superviseFlow.setTheAmt(BigDecimal.ZERO.subtract(tdd.getAmount()));
//            superviseFlow.setTheBalance(superviseFlow.getLastBalance().add(superviseFlow.getTheAmt()));
//            superviseFlow.setStudentId(student.getId());
//            superviseFlow.setRelationSubject(subjectName);
//            superviseFlowService.save(superviseFlow);
//        }
//
//        Date now = new Date();
//        tdd.setIsDikou(1).setDikouAmount(tdd.getAmount())
//                .setDikouStudentId(student.getId())
//                .setDikouDate(now)
//                .setDikouOrderNo(orderNo)
//                .setDikouOperator(ShiroUtils.getSysOrganUser().getUsername())
//                .setDikouPushtime(pst.getPushtime())
//                .setDikouFailReason("")
//                .setDikouStudentIdentity(student.getIdentity())
//                .setDikouStudentName(student.getName())
//                .setDikouTrainphase(Integer.parseInt(pst.getTrainphase()));
//        tempDikouDataService.updateById(tdd);
//
//
//        superviseReleaseRecordService.save(student, subjectName,tdd.getAmount(), true,now,now,orderNo);
//
//        JSONObject extendData = new JSONObject();
//        extendData.put("subjectName", subjectName);
//        extendData.put("trainPhase", tdd.getTrainphase());
//        extendData.put("amount", tdd.getAmount());
//        BankTransactionRecord bankTransactionRecord = new BankTransactionRecord()
//                .setExtendData(extendData.toJSONString())
//                .setStudentId(student.getId())
//                .setSchoolId(student.getSchoolId())
//                .setRegistrationId(student.getRegistrationId())
//                .setBranchId(student.getBranchId())
//                .setAmount(tdd.getAmount())
//                .setType(BankTransactionRecordType.WITHDRAW)
//                .setCreateTime(now)
//                .setBankOrderId(orderNo)
//                .setOrderId(orderNo)
//                .setBankStatus("0")
//                .setStatus(true)
//                .setErrMsg("冲抵");
//        bankTransactionRecordService.save(bankTransactionRecord);
//
//    }

    @Override
    public List<SchoolStudent> getQuitReleaseStudentList() {
        return schoolStudentMapper.getQuitReleaseStudentList();
    }


    @Override
    public void createContract(SchoolStudent schoolStudent, SchoolContract schoolContract) throws Exception{
        try {
            School school = schoolService.selectSchoolById(schoolStudent.getSchoolId());
            Map<String, Object> variables = new HashMap<String, Object>();
            variables.put("schoolName",school.getName());
            variables.put("enterpriseNo", schoolContract.getEnterpriseNo());
            variables.put("schoolAddress", school.getProvince()+school.getCity()+school.getTown()+school.getAddress());

            variables.put("schoolTel",school.getSchoolContactList().isEmpty() ? "" : school.getSchoolContactList().get(0).getTel());

            variables.put("registrationName", schoolStudent.getRegistration().getName());

            SchoolContact contact = schoolContactService.getOne(new LambdaQueryWrapper<SchoolContact>()
                    .eq(SchoolContact::getRegistrationId, schoolStudent.getRegistrationId()).last("limit 1"));
            variables.put("registrationTel", contact != null ? contact.getTel() : "");
            variables.put("registrationContact", contact != null ? contact.getName() : "");
            variables.put("stuName", schoolStudent.getName());
            variables.put("stuSex", schoolStudent.getGender()==0?"女":"男");
            variables.put("stuIdcard", schoolStudent.getIdentity());
            variables.put("stuMobile", schoolStudent.getMobile());
            variables.put("stuAddress", schoolStudent.getProvince() + schoolStudent.getCity() + schoolStudent.getTown() + schoolStudent.getAddress());
            //驾驶证类型打勾
            if(StringUtils.isNotEmpty(schoolStudent.getLicenseType())) {
                if (schoolStudent.getLicenseType().equalsIgnoreCase("C1")) {
                    //C1驾照类型
                    variables.put("stuLicenseTypeCheckC1", "√");
                } else if (schoolStudent.getLicenseType().equalsIgnoreCase("C2")) {
                    //C2驾照类型
                    variables.put("stuLicenseTypeCheckC2", "√");
                } else {
                    //其他驾照类型
                    variables.put("stuLicenseTypeCheckOther", schoolStudent.getLicenseType());
                }
            }
            variables.put("payFee", schoolStudent.getPayFee());
            variables.put("date1", DateUtil.formatDate(new Date(), "yyyy年MM月dd日"));
            variables.put("date2", DateUtil.formatDate(new Date(), "yyyy年MM月dd日"));
            //增加初领类型打勾
            if(StringUtils.isNotEmpty(schoolStudent.getBusinessType())) {
                if (schoolStudent.getBusinessType().equalsIgnoreCase("初领")) {
                    //初领
                    variables.put("businessTypeChuxue", "√");
                }else if (schoolStudent.getBusinessType().equalsIgnoreCase("增驾")){
                     //增驾
                    variables.put("businessTypeZengjia", "√");
                }else  {
                    //重修
                    variables.put("businessTypeChongxiu", "√");
                }
            }
            //variables.put("businessType", schoolStudent.getBusinessType());
            String customNumber = getContractNumber();
            variables.put("contractNumber", customNumber);
            variables.put("platformFee",schoolStudent.getPayFee());
            variables.put("attachedItem", schoolStudent.getAttachedItem());

            //生成合同中的理论培训费、科二三实操费用、服务费
            String trainFee = sysConfigService.selectConfigByKey("student.pay.train.fee"); // 设置的监管资金，目前主要是1500，基本不用4300
            BigDecimal textTrainFee = BigDecimal.ZERO;
            BigDecimal serviceFee = BigDecimal.ZERO;
            BigDecimal operateFee = BigDecimal.ZERO;
            BigDecimal operateFee2 = BigDecimal.ZERO;

            ContractVo contractVo = JSONObject.parseObject(schoolContract.getVars(), ContractVo.class);

            // 判断是否使用驾校合同模板中的金额
            boolean useContractTemplate = schoolStudent.getTextTrainFee() == null
                    || schoolStudent.getServiceFee() == null
                    || schoolStudent.getOperateFee() == null
                    || schoolStudent.getOperateFee2() == null
                    || schoolStudent.getTextTrainFee().add(schoolStudent.getServiceFee())
                    .add(schoolStudent.getOperateFee())
                    .add(schoolStudent.getOperateFee2())
                    .compareTo(new BigDecimal(trainFee)) < 0;

            if (useContractTemplate) {
                if(null==contractVo||contractVo.getTextTrainFee()==null
                        ||contractVo.getServiceFee()==null
                        ||contractVo.getOperateFee()==null
                        ||contractVo.getOperateFee2()==null){
                    throw new BusinessException("该学员所在驾校未正确设置合同模板金额数，请通知驾校检查");
                }
                //如果驾校审核的时候或学员报名时填写的理论费、服务费、科二科三实操费不正确或低于1500的则取模板的金额
                textTrainFee = new BigDecimal(contractVo.getTextTrainFee());
                serviceFee = new BigDecimal(contractVo.getServiceFee());
                operateFee = new BigDecimal(contractVo.getOperateFee());
                operateFee2 = new BigDecimal(contractVo.getOperateFee2());

            } else {
                textTrainFee = schoolStudent.getTextTrainFee(); // 理论培训费
                serviceFee = schoolStudent.getServiceFee(); // 综合服务费
                operateFee = schoolStudent.getOperateFee(); // 科二实际操作费
                operateFee2 = schoolStudent.getOperateFee2(); // 科三实际操作费
            }

            BigDecimal totalFee = textTrainFee.add(serviceFee).add(operateFee).add(operateFee2);

            if (totalFee.compareTo(new BigDecimal(trainFee)) < 0) {
                throw new BusinessException("生成合同失败，该学员报名时的理论培训费+综合服务费+科二实际操作费+科三实际操作费小于监管资金:" + trainFee);
            }
            //设置合同的总金额
            variables.put("contractFee", totalFee);
            variables.put("studyTimeFee", "");
            variables.put("studyTimeFee2", "");
            variables.put("phase2Fee1", "");
            variables.put("phase2Fee2", "");
            variables.put("phase3Fee1", "");
            variables.put("phase3Fee2", "");
            variables.put("adviseContact", "");
            variables.put("adviseEmail", "");
            variables.put("adviseAddress", "");
            variables.put("adviseTel1", "");
            variables.put("adviseTel2", "");
            variables.put("jiesongFee","");
            if (StringUtils.isNotEmpty(schoolContract.getVars())) {
                variables.putAll(JSONObject.parseObject(schoolContract.getVars()));
            }
            //这四项放schoolContract.getVars()下面是避免被覆盖重新赋值为空
            variables.put("textTrainFee", textTrainFee);
            variables.put("serviceFee", serviceFee);
            variables.put("operateFee", operateFee);
            variables.put("operateFee2", operateFee2);
            variables.put("attachedItem",schoolStudent.getAttachedItem());
            //设置非必填项目
            if(schoolStudent.getPhase2Fee1()!=null){
                variables.put("phase2Fee1",schoolStudent.getPhase2Fee1()); //科目二补训费
            }
            if(schoolStudent.getPhase3Fee1()!=null){
                variables.put("phase3Fee1",schoolStudent.getPhase3Fee1()); //科目三补训费
            }
            if(schoolStudent.getStudyTimeFee()!=null){
                variables.put("studyTimeFee",schoolStudent.getStudyTimeFee()); //科目二学时费
            }
            if(schoolStudent.getStudyTimeFee2()!=null){
                variables.put("studyTimeFee2",schoolStudent.getStudyTimeFee2()); //科目三学时费
            }
            if(schoolStudent.getJiesongFee()!=null){
                variables.put("jiesongFee",schoolStudent.getJiesongFee()); //接送费
            }

            if(StringUtils.isNotEmpty(variables.get("jiesongFee").toString())){

                if(new BigDecimal(variables.get("jiesongFee").toString()).compareTo(BigDecimal.ZERO) > 0){
                    variables.put("jiesong2check","√");
                }else{
                    //为0则免费
                    variables.put("jiesong1check","√");
                }
            }else{
                variables.put("jiesong1check","√");
            }

            //调用templateDetail大概会用到4-5秒，注意数据时效性
            JSONArray componentArray = yiDongShuiEQianService.templateDetail(schoolContract.getUserId(), schoolContract.getTemplateNo());
            List<YiDongShuiEQianService.TemplateComponent> componentList = new ArrayList<>();
            for (int i = 0; i < componentArray.size(); i++) {
                JSONObject item = componentArray.getJSONObject(i);
                YiDongShuiEQianService.TemplateComponent component = new YiDongShuiEQianService.TemplateComponent();
                component.setComponentNo(item.getString("componentNo"));
                String componentName = item.getJSONObject("context").getString("componentName");
                String value = variables.get(componentName)==null?"":variables.get(componentName).toString();
                component.setComponentValue(value);
                componentList.add(component);
            }
            JSONObject templateJson = yiDongShuiEQianService.createContractByTemplate(schoolContract.getUserId(), schoolStudent.getName() + schoolStudent.getIdentity(), schoolContract.getTemplateNo(), componentList);
            String iotNumber = templateJson.getString("lotNumber");
            String contractNumber = null;
            int count = 0;
            while (true) {
                JSONObject contractStatusJson = yiDongShuiEQianService.contractStatus(schoolContract.getUserId(), iotNumber);
                int contractState = contractStatusJson.getIntValue("contractState");
                if (contractState == 1) {
                    contractNumber = contractStatusJson.getString("serialNumber");
                    break;
                } else {
                    count++;
                    ThreadUtil.sleep(100);
                }
                if (count == 3) {
                    break;
                }
            }
            if (StringUtils.isEmpty(contractNumber)) {
                throw new BusinessException("未产生合同号");
            }

            SchoolStudentContract ssc = schoolStudentContractService.getById(schoolStudent.getId());
            if (ssc == null || StringUtils.isEmpty(ssc.getUserId())) {
                int cardType = 0;
                if (!schoolStudent.getIdentityType().equals("身份证")) {
                    cardType = 10;
                }
                JSONObject jsonObject = null;
                try {
                    jsonObject = yiDongShuiEQianService.addPersonUser(schoolStudent.getName(), cardType, schoolStudent.getIdentity(), schoolStudent.getMobile());
                } catch (Exception ex) {
                    log.info("ex.message:=============="+ex.getMessage());
                    if (ex.getMessage().equals("手机号已存在")) {
                        List<SchoolStudentContract> list = schoolStudentContractService.list(new LambdaQueryWrapper<>(SchoolStudentContract.class)
                                .eq(SchoolStudentContract::getMobile, schoolStudent.getMobile()));
                        for (SchoolStudentContract contract : list) {
                            if (StringUtils.isNotEmpty(contract.getUserId())) {
                                yiDongShuiEQianService.deleteUser(contract.getUserId());
                            }
                        }
                        try {
                            jsonObject = yiDongShuiEQianService.addPersonUser(schoolStudent.getName(), cardType, schoolStudent.getIdentity(), schoolStudent.getMobile());
                        }catch(Exception exception){
                            if(exception.getMessage().equals("applyNo已存在")){
                                jsonObject = yiDongShuiEQianService.findUserByApplyNo(schoolStudent.getMobile());
                            }else {
                                throw exception;
                            }
                        }
                    } else if(ex.getMessage().equals("applyNo已存在")){
                        jsonObject = yiDongShuiEQianService.findUserByApplyNo(schoolStudent.getMobile());
                    } else {
                        throw ex;
                    }
                }
                if (jsonObject != null) {
                    ssc = new SchoolStudentContract()
                            .setMobile(schoolStudent.getMobile())
                            .setStudentId(schoolStudent.getId())
                            .setHasView(0)
                            .setUpdateContractTime(DateUtils.getNowDate())//新增也要设置更新合同时间
                            .setUserId(jsonObject.getString("userId"));
                    schoolStudentContractService.save(ssc);
                }
            }
            yiDongShuiEQianService.signContract(schoolContract.getUserId(), contractNumber, 0, 2, schoolContract.getSealId(), "甲方（盖章）：", 15, -15);
            JSONObject jsonObject = yiDongShuiEQianService.downloadContract(contractNumber);
            String downloadPath = RuoYiConfig.getDownloadPath() + "contract";
            File file = new File(downloadPath);
            if (!file.exists()) {
                file.mkdirs();
            }
            String filepath = downloadPath + "/" + contractNumber + ".pdf";
            File downContractFile = new File(filepath);
            if (!downContractFile.exists()) {
                downContractFile.createNewFile();
            }
            Base64.decodeToFile(jsonObject.getString("file"), downContractFile);
            String webPath = Constants.RESOURCE_PREFIX + "/download/contract/" + contractNumber + ".pdf";
            ssc.setContractUrl(webPath);
            ssc.setContractNumber(contractNumber);
            ssc.setHasView(0);
            ssc.setCustomNumber(customNumber);
            ssc.setIsSign(0);
            ssc.setUpdateContractTime(DateUtils.getNowDate());//设置更新合同时间
            schoolStudentContractService.updateById(ssc);

            SchoolStudent student = new SchoolStudent();
            student.setId(schoolStudent.getId());
            student.setIsCheck(1);
            student.setRegisteDate(new Date());
            student.setStatus(2);
            student.setUpdatedTime(new Date());
            this.updateById(student);

            try {
                //查询是否在跳过合同签署记录表中，如果在需要更新记录
                StudentSkipContract studentSkipContractDb = studentSkipContractService.getById(schoolStudent.getId());
                if(studentSkipContractDb!=null){
                    studentSkipContractDb.setNewContractPath(webPath);
                    studentSkipContractDb.setUpdateBy(ShiroUtils.getLoginName());
                    studentSkipContractDb.setUpdateTime(new Date());
                    studentSkipContractService.updateById(studentSkipContractDb);
                }
            }catch (Exception ex){
                ex.printStackTrace();
                log.error("更新跳过合同签署记录表失败",ex);
            }

        }catch(Exception ex){
            log.error("产生合同失败",ex);
            throw ex;
        }
    }

    /**
     * 学员查看合同后更新查看合同时间
     * @param studentId
     * @return
     */
    @Override
    public int updateStudentContract(String studentId) {
        int count =0;
        SchoolStudentContract ssc = schoolStudentContractService.getById(studentId);
        if(ssc == null) {
            throw new BusinessException("当前学员不存在合同文件，请驾校重新审核该学员生成合同文件");
        }else{
           ssc.setViewContractTime(DateUtils.getNowDate()); //更新合同查看时间
           schoolStudentContractService.updateById(ssc);
        }
        return count;
    }

    /**
     * 判断该学员的合同是否更新过
     * @param studentId 学员ID
     * @return
     */
    @Override
    public int checkStudentContractHasUpdate(String studentId) {
        int noViewContract = Constants.CONTRACT_VIEWED;
        SchoolStudent schoolStudent = studentService.selectSchoolStudentById(studentId);
        if(schoolStudent != null && schoolStudent.getIsCheck().equals(Constants.AUDIT_STATUS_UNAUDITED)){
            throw new BusinessException("当前学员尚未审核，请联系驾校审核后，重新进入小程序进行签署");
        }
        SchoolStudentContract ssc = schoolStudentContractService.getById(studentId);
        if(ssc == null) {
            throw new BusinessException("当前学员不存在合同文件，请驾校重新审核该学员生成合同文件");
        }else{
            if(ssc.getViewContractTime()==null){
                //证明没查看合同
                noViewContract = Constants.CONTRACT_NOT_VIEWED;
                return noViewContract;
            }

            if(ssc.getUpdateContractTime()!=null && ssc.getViewContractTime()!=null){
                //学员在查看合同在更新合同之前
                if(ssc.getViewContractTime().before(ssc.getUpdateContractTime())){
                    //合同发生了更新
                    noViewContract = Constants.CONTRACT_UPDATE_NOT_VIEWED;
                }
            }
        }
        return noViewContract;
    }

    private String getContractNumber(){
        String prefix = sysConfigService.selectConfigByKey("contract.prefix");
        if(StringUtils.isEmpty(prefix)){
            prefix = "DGJP";
        }
        String contractPrefix = prefix  + DateUtil.formatDate(new Date(),"yyyyMMdd");
        SchoolStudentContract schoolStudentContract = schoolStudentContractService.getOne(new LambdaQueryWrapper<SchoolStudentContract>()
                .likeRight(SchoolStudentContract::getCustomNumber,contractPrefix)
                .orderByDesc(SchoolStudentContract::getCustomNumber)
                .last("limit 1"));
        if(schoolStudentContract == null || StringUtils.isEmpty(schoolStudentContract.getCustomNumber())){
            contractPrefix += "0001";
            return contractPrefix;
        }
        Long tempNumber = Long.parseLong(schoolStudentContract.getCustomNumber().substring(4));
        return prefix + (tempNumber + 1);
    }

    /**
     * 导入广仁特殊学员数据
     * <AUTHOR>
     * @date 2024/3/20 16:17
     * @param file *
     * @return int *
     */
    @Transactional(rollbackFor = {Exception.class})
    @Override
    public int grStudentImport(MultipartFile file) {
        int row = 0;
        try {
            String tempPath = RuoYiConfig.getDownloadPath() + "/temp";
            String zipTempPath = RuoYiConfig.getDownloadPath() + "/temp/学员模板";
            String templateFilePath = zipTempPath + "/学员模板.xlsx";
            File excel = new File(templateFilePath);
            ZipUtils.unzip(file, tempPath, Charset.forName("GBK"));

            FileInputStream is = new FileInputStream(excel);
            ExcelUtil<GrStudentTemplateVo> util = new ExcelUtil<>(GrStudentTemplateVo.class);
            List<GrStudentTemplateVo> grStudentTemplateVos = util.importExcel(is);
            Map<String, SchoolRegistration> registrationMap = new HashMap<>(grStudentTemplateVos.size());

            // 校验数据
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            for (int i = 0; i < grStudentTemplateVos.size(); i++) {
                row = i + 1;
                String info = "第" + row + "条数据，";

                GrStudentTemplateVo grStudentTemplateVo = grStudentTemplateVos.get(i);

                Set<ConstraintViolation<GrStudentTemplateVo>> violations = validator.validate(grStudentTemplateVo);

                if (!violations.isEmpty()) {
                    for (ConstraintViolation<GrStudentTemplateVo> violation : violations) {
                        throw new BusinessException(info + violation.getMessage());
                    }
                }

                SchoolStudent studentByDb = this.getOne(new LambdaQueryWrapper<SchoolStudent>()
                        .eq(SchoolStudent::getName, grStudentTemplateVo.getName())
                        .eq(SchoolStudent::getIdentity, grStudentTemplateVo.getIdentity())
                        .eq(SchoolStudent::getIsQuit, 0)
                        .last(" LIMIT 1"));

                if (studentByDb != null) {
                    throw new BusinessException(info + "该证件号已被使用");
                }

                List<SchoolStudent> tempList = this.list(new QueryWrapper<SchoolStudent>()
                        .eq("identity", grStudentTemplateVo.getIdentity())
                        .eq("is_quit", 1));
                for(SchoolStudent tmp : tempList) {
                    SchoolStudentDropOut dropOutStudent = schoolStudentDropOutService.getOne(new QueryWrapper<SchoolStudentDropOut>()
                            .eq("student_id", tmp.getId())
                            .last("limit 1"));
                    if(dropOutStudent != null && dropOutStudent.getIsDone()==0) {
                        throw new BusinessException("该证件正在退学中，请完成退学流程才能报名");
                    }
                }

                studentByDb = this.getOne(new QueryWrapper<SchoolStudent>()
                        .eq("mobile", grStudentTemplateVo.getMobile())
                        .eq("is_quit", 0)
                        .last("limit 1"));
                if(studentByDb != null){
                    throw new BusinessException("该手机号已被使用");
                }

                tempList = this.list(new QueryWrapper<SchoolStudent>()
                        .eq("mobile", grStudentTemplateVo.getMobile())
                        .eq("is_quit", 1));
                for(SchoolStudent tmp : tempList) {
                    SchoolStudentDropOut dropOutStudent = schoolStudentDropOutService.getOne(new QueryWrapper<SchoolStudentDropOut>()
                            .eq("student_id", tmp.getId()).last("limit 1"));
                    if(dropOutStudent != null && dropOutStudent.getIsDone() == 0) {
                        throw new BusinessException("该手机号正在退学中，请完成退学流程才能报名");
                    }
                }

                SchoolRegistration registration = schoolRegistrationService.getOne(new LambdaQueryWrapper<SchoolRegistration>()
                        .eq(SchoolRegistration::getName, grStudentTemplateVo.getRegistrationName()));
                if (registration != null) {
                    registrationMap.put(grStudentTemplateVo.getIdentity(), registration);
                }else {
                    throw new BusinessException(info + "报名点不存在。");
                }
            }

            List<SchoolStudent> schoolStudentByInsert = new ArrayList<>(grStudentTemplateVos.size());
            for (GrStudentTemplateVo grStudentTemplateVo : grStudentTemplateVos) {
                String headImagePath = zipTempPath + "/" + grStudentTemplateVo.getHeadImageName();
                String oldLicenseImagePath = zipTempPath + "/" + grStudentTemplateVo.getOldLicenseImageName();
                SchoolStudent student = new SchoolStudent();
                BeanUtils.copyBeanProp(student, grStudentTemplateVo);
                File headImg = new File(headImagePath);
                File oldLicenseImg = new File(oldLicenseImagePath);
                student.setHeadImg(headImg);
                student.setOldLicenseImg(oldLicenseImg);
                try {
                    handleGrStudent(grStudentTemplateVo, student, registrationMap);
                    schoolStudentByInsert.add(student);
                }catch (Exception e) {
                    log.error("handleGrStudent error:", e);
                    schoolStudentByInsert.clear();
                    throw new BusinessException(e.getMessage());
                }
            }

            this.saveBatch(schoolStudentByInsert);
        }catch (Exception e) {
            log.error("grStudentImport error:", e);
            throw new BusinessException(e.getMessage());
        }
        return row;
    }

    /**
     * 处理广仁特殊学员的数据
     * <AUTHOR>
     * @date 2024/3/21 10:37
     * @param schoolStudent *
     */
    private void handleGrStudent(GrStudentTemplateVo grStudentTemplateVo,
                                 SchoolStudent schoolStudent,
                                 Map<String, SchoolRegistration> registrationMap) {
        schoolStudent.setId(IdUtils.fastSimpleUUID());
        SchoolRegistration registration = registrationMap.get(grStudentTemplateVo.getIdentity());
        schoolStudent.setRegistrationId(registration.getId());
        schoolStudent.setBranchId(registration.getBranchId());
        schoolStudent.setSchoolId(registration.getSchoolId());
        if (schoolStudent.getBusinessType().equals("增驾")) {
            if (schoolStudent.getOldLicenseDate() == null) {
                throw new BusinessException("业务类型为增驾时，请填写原驾驶证领取日期");
            }
            if (StringUtils.isEmpty(grStudentTemplateVo.getOldLicenseImageName()) || !schoolStudent.getOldLicenseImg().exists()) {
                throw new BusinessException("业务类型为增驾时，请附上原驾驶证图片");
            }
            try {
                MultipartFile multipartFile = com.guangren.common.utils.file.FileUtils.convertMultipartFile(schoolStudent.getOldLicenseImg());
                PublicFile publicFile = publicFileService.saveFile(multipartFile, "0", DictConst.FILE_TYPE_OLD_LICENSE_IMG.getDict());
                schoolStudent.setOldLicenseImage(publicFile.getWebPath());
            }catch (Exception e) {
                log.error("convertMultipartFile error:", e);
                throw new BusinessException("convertMultipartFile error");
            }
        }

        if (!schoolStudent.getHeadImg().exists()) {
            throw new BusinessException(schoolStudent.getName() + "照片不存在");
        }
        try {
            String filepath = RuoYiConfig.getAvatarPath()+File.separator+ DateUtils.datePath();
            String filename = ImageUtil.saveImage(filepath, ImageUtil.imageToBase64(schoolStudent.getHeadImg()));
            String webFilename = Constants.RESOURCE_PREFIX+"/avatar/"+DateUtils.datePath()+"/"+filename;
            schoolStudent.setHeadImage(webFilename);
        }catch (Exception e) {
            log.error("convertMultipartFile error:", e);
            throw new BusinessException("convertMultipartFile error");
        }
        schoolStudent.setGender(1);
        schoolStudent.setBirthday("19700101");
        schoolStudent.setStatus(1);
        schoolStudent.setIsPay(0);
        schoolStudent.setNation("汉族");

        List<SchoolStudyCenter> schoolStudentCenterList = schoolStudyCenterService.list(new QueryWrapper<SchoolStudyCenter>()
                .eq("school_id", schoolStudent.getSchoolId()));
        if(schoolStudentCenterList.size() == 0){
            schoolStudent.setStudyCenterId("0");
        }else{
            boolean isSetDefault = false;
            for(SchoolStudyCenter sc : schoolStudentCenterList){
                if(sc.getIsDefault() != null && sc.getIsDefault()==1){
                    schoolStudent.setStudyCenterId(sc.getOrganId());
                    isSetDefault = true;
                    break;
                }
            }
            if(!isSetDefault){
                schoolStudent.setStudyCenterId(schoolStudentCenterList.get(0).getOrganId());
            }
        }
        schoolStudent.setStudyCenterIsSyn(0);
        schoolStudent.setIsSupervise(1); // 从9月1日开始默认为需要监管
        schoolStudent.setSuperviseFeeIsOk(0);
        schoolStudent.setIsQuit(0);
        schoolStudent.setQuitIsSyn(0);
        schoolStudent.setPrepareRegisteDate(cn.hutool.core.date.DateUtil.parse("2024-03-14", "yyyy-MM-dd"));
        schoolStudent.setOriginData(1);
        schoolStudent.setVersion(1);
    }

    /**
     * 学员审核保存
     * @param student *
     * @param isCheckType 是否检查审核类型 false为自动审核
     */
    @Override
    public void checkStudentSave(SchoolStudent student, boolean isCheckType) {
        SchoolStudent schoolStudent = selectSchoolStudentById(student.getId());
        SchoolRegistration registration = schoolRegistrationService.getById(schoolStudent.getRegistrationId());

        if(student.getAttachedItem() != null  && student.getAttachedItem().length()>300){
            throw new BusinessException("补充协议字数超过限制");
        }
        BigDecimal contractFee = student.getContractFee();
        BigDecimal trainFee = new BigDecimal(configService.selectConfigByKey("student.pay.train.fee"));
        BigDecimal payFee = student.getPayFee();


        if(student.getTextTrainFee()!=null&&student.getServiceFee()!=null&&student.getOperateFee()!=null&&student.getOperateFee()!=null){
           //传递合同金额
            schoolStudent.setTextTrainFee(student.getTextTrainFee()==null?new BigDecimal(0):student.getTextTrainFee());
            schoolStudent.setServiceFee(student.getServiceFee()==null?new BigDecimal(0):student.getServiceFee());
            schoolStudent.setOperateFee(student.getOperateFee()==null?new BigDecimal(0):student.getOperateFee());
            schoolStudent.setOperateFee2(student.getOperateFee2()==null?new BigDecimal(0):student.getOperateFee2());
            schoolStudent.setStudyTimeFee(student.getStudyTimeFee()==null?new BigDecimal(0):student.getStudyTimeFee());
            schoolStudent.setStudyTimeFee2(student.getStudyTimeFee2()==null?new BigDecimal(0):student.getStudyTimeFee2());
            schoolStudent.setPhase2Fee1(student.getPhase2Fee1()==null?new BigDecimal(0):student.getPhase2Fee1());
            schoolStudent.setPhase3Fee1(student.getPhase3Fee1()==null?new BigDecimal(0):student.getPhase3Fee1());
            schoolStudent.setJiesongFee(student.getJiesongFee()==null?new BigDecimal(0):student.getJiesongFee());
        }



        if(StringUtils.isEmpty(schoolStudent.getStudyCenterId()) || schoolStudent.getStudyCenterId().equals("0")){
            throw new BusinessException("学员" + schoolStudent.getName() + "未选择学时平台。");
        }

        if(schoolStudent.getIsCheck() == 1 ){
            throw new BusinessException("学员" + schoolStudent.getName() + "已经是已审核状态。");
        }

        if(!isCheckType){
            //自动审核，设置学员为已审核
            schoolStudent.setIsCheck(Constants.AUDIT_STATUS_AUDITED);
        }

        if(payFee.compareTo(new BigDecimal(4300)) >= 0 || payFee.compareTo(new BigDecimal(1500)) == 0){

        }else{
            throw new BusinessException("学员支付金额设置不正确");
        }


        if (contractFee.compareTo(payFee) < 0) {
            throw new BusinessException("培训费合计的设置需大于或等于平台支付金额");
        }
        if (payFee.compareTo(trainFee) < 0) {
            throw new BusinessException("学员支付金额设置不正确");
        }
        SchoolContract schoolContract = schoolContractService.getById(schoolStudent.getSchoolId());
        if(schoolContract == null || StringUtils.isEmpty(schoolContract.getSealId())){
            throw new BusinessException("请先设置合同模板并上传印章");
        }

        DivisionAccount divisionAccount = divisionAccountService.getOne(new LambdaQueryWrapper<DivisionAccount>()
                .eq(DivisionAccount::getRefId,schoolStudent.getRegistrationId())
                .eq(DivisionAccount::getType,3)
                .eq(DivisionAccount::getIsCheck,1)
                .eq(DivisionAccount::getAccountType,2));
        if(divisionAccount == null){
            throw new BusinessException("未设置收学费账户,请设置收学费账户并审核");
        }

        if (isCheckType) {
            SysOrganUser organUser = ShiroUtils.getSysOrganUser();
            if(!organUser.isAdmin()) {
                int reviewStudentType = registration.getReviewStudentType() != null ? registration.getReviewStudentType() : 0;
                switch (reviewStudentType) {
                    case 0:
                        if (!schoolStudent.getSchoolId().equals(organUser.getSchoolId())) {
                            throw new BusinessException("账号与门店设置的学员审核类型不符");
                        }
                        break;

                    case 1:
                        if (!schoolStudent.getRegistrationId().equals(organUser.getRegistrationId())) {
                            throw new BusinessException("账号与门店设置的学员审核类型不符");
                        }
                        break;
                }
            }
        }

        schoolStudent.setContractFee(contractFee);
        schoolStudent.setPayFee(payFee);
        // 后台审核直接赋值，小程序注册从驾校合同表获取补充协议
        if (isCheckType) {
            schoolStudent.setAttachedItem(student.getAttachedItem());
        }else {
            if(StringUtils.isNotEmpty(schoolContract.getVars())) {
                JSONObject varJson = JSON.parseObject(schoolContract.getVars());
                if(varJson != null) {
                    schoolStudent.setAttachedItem(varJson.getString("attachedItem"));
                }
            }
        }
        //判断是否开起了跳过合同签订参数
        Integer skipContractStatus = studentSkipContractService.querySkipContractStatus();
            boolean isSkipContract = skipContractStatus.equals(Constants.SKIP_CONTRACT_SWITCH_ON);
        if(isSkipContract){
            try {
                StudentSkipContract studentSkipContract = new StudentSkipContract();
                studentSkipContract.setStudentId(schoolStudent.getId());
                studentSkipContractService.handleSkipContract(studentSkipContract);
            }catch (Exception e){
                throw new BusinessException(e.getMessage());
            }
        }else {
            try {
                createContract(schoolStudent,schoolContract);
            }catch (Exception e) {
                throw new BusinessException("生成合同失败:"+e.getMessage());
            }
        }

    }

}
