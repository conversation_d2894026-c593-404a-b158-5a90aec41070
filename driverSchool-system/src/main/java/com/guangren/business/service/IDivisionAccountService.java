package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.DivisionAccount;
import com.guangren.business.vo.DivisionAccountVo;
import com.guangren.common.core.domain.AjaxResult;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分账账户Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface IDivisionAccountService extends IService<DivisionAccount>
{
    /**
     * 查询分账账户
     * 
     * @param id 分账账户主键
     * @return 分账账户
     */
    public DivisionAccount selectDivisionAccountById(Long id);

    /**
     * 查询分账账户列表
     * 
     * @param divisionAccount 分账账户
     * @return 分账账户集合
     */
    public List<DivisionAccount> selectDivisionAccountList(DivisionAccount divisionAccount);

    /**
     * 新增分账账户
     * 
     * @param divisionAccount 分账账户
     * @return 结果
     */
    public int insertDivisionAccount(DivisionAccount divisionAccount);

    /**
     * 修改分账账户
     * 
     * @param divisionAccount 分账账户
     * @return 结果
     */
    public int updateDivisionAccount(DivisionAccount divisionAccount);

    /**
     * 批量删除分账账户
     * 
     * @param ids 需要删除的分账账户主键集合
     * @return 结果
     */
    public int deleteDivisionAccountByIds(String ids);

    /**
     * 删除分账账户信息
     * 
     * @param id 分账账户主键
     * @return 结果
     */
    public int deleteDivisionAccountById(Long id);

    /**
     * 设置收学费账户保存
     * <AUTHOR>
     * @date 2024/2/27 15:18
     * @param divisionAccountVo  *
     */
    void setDivisionAccountSave(DivisionAccountVo divisionAccountVo);

    AjaxResult validateDivsionFee(BigDecimal divsionFee);
}
