package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolCalculateFlowAmount;

import java.util.List;

/**
 * 驾校结息流水（留存金额占比）Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-06
 */
public interface ISchoolCalculateFlowAmountService extends IService<SchoolCalculateFlowAmount>
{
    /**
     * 查询驾校结息流水（留存金额占比）
     * 
     * @param id 驾校结息流水（留存金额占比）主键
     * @return 驾校结息流水（留存金额占比）
     */
    public SchoolCalculateFlowAmount selectSchoolCalculateFlowAmountById(String id);

    /**
     * 查询驾校结息流水（留存金额占比）列表
     * 
     * @param schoolCalculateFlowAmount 驾校结息流水（留存金额占比）
     * @return 驾校结息流水（留存金额占比）集合
     */
    public List<SchoolCalculateFlowAmount> selectSchoolCalculateFlowAmountList(SchoolCalculateFlowAmount schoolCalculateFlowAmount);

    /**
     * 新增驾校结息流水（留存金额占比）
     * 
     * @param schoolCalculateFlowAmount 驾校结息流水（留存金额占比）
     * @return 结果
     */
    public int insertSchoolCalculateFlowAmount(SchoolCalculateFlowAmount schoolCalculateFlowAmount);

    /**
     * 修改驾校结息流水（留存金额占比）
     * 
     * @param schoolCalculateFlowAmount 驾校结息流水（留存金额占比）
     * @return 结果
     */
    public int updateSchoolCalculateFlowAmount(SchoolCalculateFlowAmount schoolCalculateFlowAmount);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SchoolCalculateFlowAmount schoolCalculateFlowAmount);

    /**
     * 批量删除驾校结息流水（留存金额占比）
     * 
     * @param ids 需要删除的驾校结息流水（留存金额占比）主键集合
     * @return 结果
     */
    public int deleteSchoolCalculateFlowAmountByIds(String ids);

    /**
     * 删除驾校结息流水（留存金额占比）信息
     * 
     * @param id 驾校结息流水（留存金额占比）主键
     * @return 结果
     */
    public int deleteSchoolCalculateFlowAmountById(String id);

    /**
     * 结息统计保存
     * <AUTHOR>
     * @date 2024/1/9 14:24
     * @param schoolCalculateFlowAmount  *
     */
    void interestCountSave(SchoolCalculateFlowAmount schoolCalculateFlowAmount);

    /**
     * 提交结算
     * <AUTHOR>
     * @date 2024/1/9 14:47
     * @param id  *
     */
    void submitCalculate(String id);
}
