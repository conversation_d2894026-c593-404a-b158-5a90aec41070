package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.FeedBackOpinion;

import java.util.List;

/**
 * 反馈意见Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface IFeedBackOpinionService extends IService<FeedBackOpinion>
{
    /**
     * 查询反馈意见
     * 
     * @param id 反馈意见主键
     * @return 反馈意见
     */
    public FeedBackOpinion selectFeedBackOpinionById(Long id);

    /**
     * 查询反馈意见列表
     * 
     * @param feedBackOpinion 反馈意见
     * @return 反馈意见集合
     */
    public List<FeedBackOpinion> selectFeedBackOpinionList(FeedBackOpinion feedBackOpinion);

    /**
     * 新增反馈意见
     * 
     * @param feedBackOpinion 反馈意见
     * @return 结果
     */
    public int insertFeedBackOpinion(FeedBackOpinion feedBackOpinion);

    /**
     * 修改反馈意见
     * 
     * @param feedBackOpinion 反馈意见
     * @return 结果
     */
    public int updateFeedBackOpinion(FeedBackOpinion feedBackOpinion);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(FeedBackOpinion feedBackOpinion);

    /**
     * 批量删除反馈意见
     * 
     * @param ids 需要删除的反馈意见主键集合
     * @return 结果
     */
    public int deleteFeedBackOpinionByIds(String ids);

    /**
     * 删除反馈意见信息
     * 
     * @param id 反馈意见主键
     * @return 结果
     */
    public int deleteFeedBackOpinionById(Long id);
}
