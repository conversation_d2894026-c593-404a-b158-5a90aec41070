package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SuperviseReleaseRecord;

import java.util.List;

/**
 * 资金监管释放记录Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface ISuperviseReleaseRecordService extends IService<SuperviseReleaseRecord>
{
    /**
     * 查询资金监管释放记录
     * 
     * @param id 资金监管释放记录主键
     * @return 资金监管释放记录
     */
    public SuperviseReleaseRecord selectSuperviseReleaseRecordById(String id);

    /**
     * 查询资金监管释放记录列表
     * 
     * @param superviseReleaseRecord 资金监管释放记录
     * @return 资金监管释放记录集合
     */
    public List<SuperviseReleaseRecord> selectSuperviseReleaseRecordList(SuperviseReleaseRecord superviseReleaseRecord);

    /**
     * 新增资金监管释放记录
     * 
     * @param superviseReleaseRecord 资金监管释放记录
     * @return 结果
     */
    public int insertSuperviseReleaseRecord(SuperviseReleaseRecord superviseReleaseRecord);

    /**
     * 修改资金监管释放记录
     * 
     * @param superviseReleaseRecord 资金监管释放记录
     * @return 结果
     */
    public int updateSuperviseReleaseRecord(SuperviseReleaseRecord superviseReleaseRecord);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SuperviseReleaseRecord superviseReleaseRecord);

    /**
     * 批量删除资金监管释放记录
     * 
     * @param ids 需要删除的资金监管释放记录主键集合
     * @return 结果
     */
    public int deleteSuperviseReleaseRecordByIds(String ids);

    /**
     * 删除资金监管释放记录信息
     * 
     * @param id 资金监管释放记录主键
     * @return 结果
     */
    public int deleteSuperviseReleaseRecordById(String id);
    
    
//    public void save(SchoolStudent student,String subjectName,BigDecimal releaseFee,boolean isSuccess,Date releaseDate,Date subjectPushtime,String releaseOrderNo);
//
    
//    public void save(School school,String subjectName,BigDecimal releaseFee,boolean isSuccess,Date releaseDate,String releaseOrderNo);
}
