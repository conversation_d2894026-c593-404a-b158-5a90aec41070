package com.guangren.business.service;

import com.guangren.business.vo.BcfirmParam;
import com.guangren.business.vo.ClientLessBean;

/**
 * <AUTHOR>
 * @description 兴业银行直联Service
 * @date 2024/3/13
 */
public interface BcfirmService {

    /**
     * 构建转账汇款指令所需的报文
     * <AUTHOR> *
     * @date 2024/3/13 8:56
     * @param bcfirmParam *
     * @return java.lang.String *
     */
    String createTransferAccountParams(BcfirmParam bcfirmParam);

    /**
     * 构建指令回单查询所需的报文
     * <AUTHOR>
     * @date 2024/3/13 8:56
     * @param bcfirmParam *
     * @return java.lang.String *
     */
    String createQueryParams(BcfirmParam bcfirmParam);

    /**
     * 获取回单号
     * <AUTHOR>
     * @date 2024/3/13 9:01
     * @param bcfirmParam *
     * @param clientLessBean *
     * @return java.lang.String *
     */
    String getReceiptNo(BcfirmParam bcfirmParam, ClientLessBean clientLessBean);

    boolean transfer(BcfirmParam bcfirmParam);
}
