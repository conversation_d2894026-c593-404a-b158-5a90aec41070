package com.guangren.business.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guangren.business.domain.PublicFile;
import com.guangren.business.domain.SchoolBranch;
import com.guangren.business.domain.SchoolBusinessLicense;
import com.guangren.business.domain.SchoolContact;
import com.guangren.business.mapper.SchoolBranchMapper;
import com.guangren.business.mapper.SchoolContactMapper;
import com.guangren.business.mapper.SchoolMapper;
import com.guangren.business.service.IPublicFileService;
import com.guangren.business.service.ISchoolBranchService;
import com.guangren.business.service.ISchoolBusinessLicenseService;
import com.guangren.business.service.ISchoolContactService;
import com.guangren.common.constant.DictConst;
import com.guangren.common.constant.UserConstants;
import com.guangren.common.core.text.Convert;
import com.guangren.common.exception.BusinessException;
import com.guangren.common.exception.ServiceException;
import com.guangren.common.utils.StringUtils;
import com.guangren.common.utils.uuid.IdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 分校Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-01
 */
@Service
public class SchoolBranchServiceImpl extends ServiceImpl<SchoolBranchMapper, SchoolBranch> implements ISchoolBranchService 
{
    @Autowired
    private SchoolBranchMapper schoolBranchMapper;
    @Autowired
    private SchoolMapper schoolMapper;
    @Autowired
    private SchoolContactMapper schoolContactMapper;
    @Autowired
    private IPublicFileService publicFileService;
    @Autowired
    private ISchoolBusinessLicenseService schoolBusinessLicenseService;
    @Autowired
    private ISchoolContactService schoolContactService;

    /**
     * 查询分校
     * 
     * @param id 分校主键
     * @return 分校
     */
    @Override
    public SchoolBranch selectSchoolBranchById(String id)
    {
        return schoolBranchMapper.selectSchoolBranchById(id);
    }

    /**
     * 查询分校列表
     * 
     * @param schoolBranch 分校
     * @return 分校
     */
    @Override
    public List<SchoolBranch> selectSchoolBranchList(SchoolBranch schoolBranch)
    {
        return schoolBranchMapper.selectSchoolBranchList(schoolBranch);
    }

    /**
     * 新增分校
     * 
     * @param schoolBranch 分校
     * @return 结果
     */
    @Transactional
    @Override
    public int insertSchoolBranch(SchoolBranch schoolBranch)
    {
        this.checkIsUnique(schoolBranch,"add");
        if (StringUtils.isEmpty(schoolBranch.getTown())) {
            throw new ServiceException("请选择所属县/镇/区");
        }
        schoolBranch.setId(IdUtils.fastSimpleUUID());
        schoolBranchFormat(schoolBranch);
        int rows=0;
        rows =schoolBranchMapper.insertSchoolBranch(schoolBranch);
        //保存学校相关执照
        insertBusinessLicense(schoolBranch);
        //保存联系人信息
        inserContact(schoolBranch);
        //保存图片
        MultipartFile[] images = schoolBranch.getSchoolImages();
        List<String> imageList= new ArrayList<String>();
        if (images != null && images.length > 0) {
            List<PublicFile> file=publicFileService.saveFiles(images,schoolBranch.getId(), DictConst.FILE_TYPE_SCHOOL_BRANCH_IMG.getDict());
            for (PublicFile fileItem:file) {
                imageList.add(fileItem.getFileName());
            }
            String imgJson= JSONArray.toJSONString(imageList);
            schoolBranch.setImages(imgJson);
            rows= schoolBranchMapper.updateSchoolBranch(schoolBranch);
        }
        return rows;
    }

    /**
     * 修改分校
     * 
     * @param schoolBranch 分校
     * @return 结果
     */
    @Transactional
    @Override
    public int updateSchoolBranch(SchoolBranch schoolBranch)
    {
        this.checkIsUnique(schoolBranch,"update");
        int rows=0;
        schoolMapper.deleteSchoolBusinessLicenseBySchoolBranchId(schoolBranch.getSchoolId(),schoolBranch.getId());
        schoolContactMapper.deleteSchoolContactBySchoolBranchId(schoolBranch.getSchoolId(),schoolBranch.getId());
        schoolBranchFormat(schoolBranch);
        //保存分校相关执照
        schoolBusinessLicenseService.remove(new LambdaQueryWrapper<SchoolBusinessLicense>()
        		.eq(SchoolBusinessLicense::getSchoolId, schoolBranch.getSchoolId())
        		.eq(SchoolBusinessLicense::getBranchId, schoolBranch.getId())
        		.isNull(SchoolBusinessLicense::getRegistrationId));
        insertBusinessLicense(schoolBranch);
        //保存联系人信息
        schoolContactService.remove(new LambdaQueryWrapper<SchoolContact>()
        		.eq(SchoolContact::getSchoolId, schoolBranch.getSchoolId())
        		.eq(SchoolContact::getBranchId, schoolBranch.getId())
        		.isNull(SchoolContact::getRegistrationId));
        inserContact(schoolBranch);

        String imageIds = schoolBranch.getImageIds();
        List<String> imageList= new ArrayList<String>();

        MultipartFile[] images = schoolBranch.getSchoolImages();
        // 修改原来的图片
        if (StringUtils.isNotEmpty(imageIds)) {
            publicFileService.clearRefrence(schoolBranch.getId(),imageIds, DictConst.FILE_TYPE_SCHOOL_BRANCH_IMG.getDict());
        }else{
            publicFileService.clearRefrence(schoolBranch.getId(), "0", DictConst.FILE_TYPE_SCHOOL_BRANCH_IMG.getDict());
        }
        // 保存新增的图片
        if (images != null && images.length > 0) {
            List<PublicFile> file=publicFileService.saveFiles(images,schoolBranch.getId(), DictConst.FILE_TYPE_SCHOOL_BRANCH_IMG.getDict());
            for (PublicFile fileItem:file) {
                imageList.add(fileItem.getFileName());
            }
            String imgJson= JSONArray.toJSONString(imageList);
            schoolBranch.setImages(imgJson);
        }
        rows=schoolBranchMapper.updateSchoolBranch(schoolBranch);
        return rows;
    }

    /**
     * 检查分校名称的唯一性
     *
     * @param schoolBranch 分校对象，包含分校的相关信息
     * @param type 操作类型，用于区分是添加还是更新分校
     *
     * 此方法主要用于在添加或更新分校时，检查分校名称是否唯一
     * 如果分校名称已经存在，则抛出异常，提示用户分校名称重复
     */
    public void checkIsUnique(SchoolBranch schoolBranch,String type){
        // 确保验证类型不为空，因为这是判断操作类型的依据
        Assert.notBlank(type,"校验类型不能为空");
        Assert.notBlank(schoolBranch.getName(),"分校名称不能为空");
        Assert.notBlank(schoolBranch.getSchoolId(),"驾校ID不能为空");

        // 根据操作类型执行不同的验证逻辑
        if(type.equals("update")){
            // 在更新操作中，确保分校ID不为空，因为更新操作依赖于ID
            Assert.notBlank(schoolBranch.getId(),"分校ID不能为空");

            // 校验名称不能重复
            // 通过查询数据库中除当前分校外是否有相同名称的分校，来判断名称是否唯一
            Long count = schoolBranchMapper.selectCount(new LambdaQueryWrapper<SchoolBranch>()
                    .eq(SchoolBranch::getName, schoolBranch.getName())
                    .eq(SchoolBranch::getSchoolId, schoolBranch.getSchoolId())
                    .ne(SchoolBranch::getId, schoolBranch.getId()));
            if(count>0){
                throw new BusinessException("当前分校名称:"+schoolBranch.getName()+"已存在于该驾校,请确认。");
            }
        }else if (type.equals("add")){
            // 在添加操作中，直接检查是否有相同名称的分校，因为是新增操作，不存在ID的问题
            Long count = schoolBranchMapper.selectCount(new LambdaQueryWrapper<SchoolBranch>()
                    .eq(SchoolBranch::getSchoolId, schoolBranch.getSchoolId())
                    .eq(SchoolBranch::getName, schoolBranch.getName()));
            if(count>0){
                throw new BusinessException("当前分校名称"+schoolBranch.getName()+"已存在,请确认。");
            }
        }
    }

    /**
     * 验证参数唯一性
     *
     * @param schoolBranch 分校
     * @return 分校
     */
    @Override
    public String checkUnique(SchoolBranch schoolBranch)
    {
        String id = schoolBranch.getId() == null ? "-1"  : schoolBranch.getId();
        SchoolBranch info=schoolBranchMapper.checkUnique(schoolBranch);
        if(StringUtils.isNotNull(info) && !info.getId().equals(id)){
            return UserConstants.COMMOM_NOT_UNIQUE;
        }
        return UserConstants.COMMOM_UNIQUE;
    }


    /**
     * 批量删除分校
     * 
     * @param ids 需要删除的分校主键
     * @return 结果
     */
    @Override
    public int deleteSchoolBranchByIds(String ids)
    {
        String[] array= Convert.toStrArray(ids);
        for (String item :array) {
            SchoolBranch branch= schoolBranchMapper.selectSchoolBranchById(item);
            schoolMapper.deleteSchoolBusinessLicenseBySchoolBranchId(branch.getSchoolId(),item);
            schoolContactMapper.deleteSchoolContactBySchoolBranchId(branch.getSchoolId(),item);
        }
        return schoolBranchMapper.deleteSchoolBranchByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除分校信息
     * 
     * @param id 分校主键
     * @return 结果
     */
    @Override
    public int deleteSchoolBranchById(String id)
    {
        return schoolBranchMapper.deleteSchoolBranchById(id);
    }

    @Override
    public int saveAccount(SchoolBranch schoolBranch) {
        return schoolBranchMapper.updateSchoolBranch(schoolBranch);
    }

    public void schoolBranchFormat(SchoolBranch schoolBranch){
        if (StringUtils.isNotEmpty(schoolBranch.getLicenseTypes())){
            List<String> licenseTypeList = new ArrayList<String>();
            String[] arry=Convert.toStrArray(schoolBranch.getLicenseTypes());
            for (String item:arry) {
                licenseTypeList.add(item);
            }
            String licenseTypeJson= JSONArray.toJSONString(licenseTypeList);
            schoolBranch.setLicenseTypes(licenseTypeJson);
        }
        if (StringUtils.isNotEmpty(schoolBranch.getChargeModes())){
            List<String> chargeModesList = new ArrayList<String>();
            String[] arry=Convert.toStrArray(schoolBranch.getChargeModes());
            for (String item:arry) {
                chargeModesList.add(item);
            }
            String chargeModesJson= JSONArray.toJSONString(chargeModesList);
            schoolBranch.setChargeModes(chargeModesJson);
        }
    }

    /**
     * 新增学校相关执照信息
     * @param schoolBranch 驾校管理对象
     */
    public void insertBusinessLicense(SchoolBranch schoolBranch)
    {
        List<SchoolBusinessLicense> schoolBusinessLicenseList = schoolBranch.getSchoolBusinessLicenseList();
        String id = schoolBranch.getId();
        if (schoolBusinessLicenseList.size()>0)
        {
            List<SchoolBusinessLicense> list = new ArrayList<SchoolBusinessLicense>();
            for (SchoolBusinessLicense schoolBusinessLicense : schoolBusinessLicenseList)
            {
                if (StringUtils.isNotEmpty(schoolBusinessLicense.getNo())){
                    schoolBusinessLicense.setBranchId(id);
                    schoolBusinessLicense.setSchoolId(schoolBranch.getSchoolId());
                    schoolBusinessLicense.setId(IdUtils.fastSimpleUUID());
                    schoolBusinessLicense.setType(1);
                    schoolBusinessLicense.setCreatedTime(new Date());
                    schoolBusinessLicense.setUpdatedTime(new Date());
                    if (schoolBusinessLicense.getIsExpiration() != null && schoolBusinessLicense.getIsExpiration() ==1){
                        schoolBusinessLicense.setExpiration(schoolBusinessLicense.getIsExpiration()+"");
                    }
                    list.add(schoolBusinessLicense);
                }
            }
            if (list.size() > 0)
            {
                schoolMapper.batchSchoolBusinessLicense(list);
            }
        }
    }

    public void inserContact(SchoolBranch schoolBranch){
        List<SchoolContact> schoolContactList= schoolBranch.getSchoolContactList();
        String id = schoolBranch.getId();
        if (schoolContactList.size()>0){
            for (SchoolContact item : schoolContactList){
                item.setBranchId(id);
                item.setSchoolId(schoolBranch.getSchoolId());
                item.setId(IdUtils.fastSimpleUUID());
                if(StringUtils.isNotEmpty(item.getName()) && StringUtils.isNotEmpty(item.getTel())){
                    schoolContactMapper.insertSchoolContact(item);
                }
            }
        }
    }

    /**
     * 导出分校列表
     */
    @Override
    public List<SchoolBranch> exportSchoolBranchList(SchoolBranch schoolBranch) {
        return schoolBranchMapper.exportSchoolBranchList(schoolBranch);
    }
}
