package com.guangren.business.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolContact;

/**
 * 联系人Service接口
 * 
 * <AUTHOR>
 * @date 2023-02-15
 */
public interface ISchoolContactService extends IService<SchoolContact>
{
    /**
     * 查询联系人
     * 
     * @param id 联系人主键
     * @return 联系人
     */
    public SchoolContact selectSchoolContactById(String id);

    /**
     * 查询联系人列表
     * 
     * @param schoolContact 联系人
     * @return 联系人集合
     */
    public List<SchoolContact> selectSchoolContactList(SchoolContact schoolContact);

    /**
     * 新增联系人
     * 
     * @param schoolContact 联系人
     * @return 结果
     */
    public int insertSchoolContact(SchoolContact schoolContact);

    /**
     * 修改联系人
     * 
     * @param schoolContact 联系人
     * @return 结果
     */
    public int updateSchoolContact(SchoolContact schoolContact);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SchoolContact schoolContact);

    /**
     * 批量删除联系人
     * 
     * @param ids 需要删除的联系人主键集合
     * @return 结果
     */
    public int deleteSchoolContactByIds(String ids);

    /**
     * 删除联系人信息
     * 
     * @param id 联系人主键
     * @return 结果
     */
    public int deleteSchoolContactById(String id);
}
