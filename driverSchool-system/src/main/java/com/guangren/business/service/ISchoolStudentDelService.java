package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolStudentDel;

import java.util.List;

/**
 * 学员删除Service接口
 * 
 * <AUTHOR>
 * @date 2023-07-20
 */
public interface ISchoolStudentDelService extends IService<SchoolStudentDel>
{
    /**
     * 查询学员删除
     * 
     * @param id 学员删除主键
     * @return 学员删除
     */
    public SchoolStudentDel selectSchoolStudentDelById(String id);

    /**
     * 查询学员删除列表
     * 
     * @param schoolStudentDel 学员删除
     * @return 学员删除集合
     */
    public List<SchoolStudentDel> selectSchoolStudentDelList(SchoolStudentDel schoolStudentDel);

    /**
     * 新增学员删除
     * 
     * @param schoolStudentDel 学员删除
     * @return 结果
     */
    public int insertSchoolStudentDel(SchoolStudentDel schoolStudentDel);

    /**
     * 修改学员删除
     * 
     * @param schoolStudentDel 学员删除
     * @return 结果
     */
    public int updateSchoolStudentDel(SchoolStudentDel schoolStudentDel);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SchoolStudentDel schoolStudentDel);

    /**
     * 批量删除学员删除
     * 
     * @param ids 需要删除的学员删除主键集合
     * @return 结果
     */
    public int deleteSchoolStudentDelByIds(String ids);

    /**
     * 删除学员删除信息
     * 
     * @param id 学员删除主键
     * @return 结果
     */
    public int deleteSchoolStudentDelById(String id);
}
