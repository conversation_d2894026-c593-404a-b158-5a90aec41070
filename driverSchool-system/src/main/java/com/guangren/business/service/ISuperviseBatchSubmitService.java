package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SuperviseBatchSubmit;
import com.guangren.business.vo.ShowPayInfoVo;
import com.guangren.business.vo.SuperviseBatchSubmitVo;
import com.guangren.business.vo.UnionNotifyVo;
import com.guangren.business.vo.UnionScanCodeNotifyVo;

import java.util.List;

/**
 * 收款账户Service接口
 *
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface ISuperviseBatchSubmitService extends IService<SuperviseBatchSubmit> {

    /**
     * 新增批量提交
     *
     * @param superviseBatchSubmit 批量提交
     * @return 结果
     */
    public int insertSuperviseBatchSubmit(SuperviseBatchSubmit superviseBatchSubmit);

    /**
     * 查询批量提交
     *
     * @param orderId 批量提交主键
     * @return 批量提交
     */
    public SuperviseBatchSubmit selectSuperviseBatchSubmitByOrderId(String orderId);

    /**
     * 查询批量提交列表
     *
     * @param superviseBatchSubmit 批量提交
     * @return 批量提交集合
     */
    public List<SuperviseBatchSubmit> selectSuperviseBatchSubmitList(SuperviseBatchSubmit superviseBatchSubmit);

    /**
     * 新增批量提交
     *
     * @param superviseBatchSubmitVo 批量提交
     * @return 结果
     */
    public int insertSuperviseBatchSubmit(SuperviseBatchSubmitVo superviseBatchSubmitVo);

    /**
     * 修改批量提交
     *
     * @param superviseBatchSubmit 批量提交
     * @return 结果
     */
    public int updateSuperviseBatchSubmit(SuperviseBatchSubmit superviseBatchSubmit);


    /**
     * 批量删除批量提交
     *
     * @param orderIds 需要删除的批量提交主键集合
     * @return 结果
     */
    public int deleteSuperviseBatchSubmitByOrderIds(String orderIds);

    /**
     * 删除批量提交信息
     *
     * @param orderId 批量提交主键
     * @return 结果
     */
    public int deleteSuperviseBatchSubmitByOrderId(String orderId);

    /**
     * 测试银联支付流程
     *
     * @param superviseBatchSubmitVo
     * @return
     */
    ///public boolean testUnionPay(SuperviseBatchSubmitVo superviseBatchSubmitVo);

    /**
     * 银联支付批量提交
     *
     * @param superviseBatchSubmitVo
     * @return
     */
    String insertUnionBatchSubmit(SuperviseBatchSubmitVo superviseBatchSubmitVo);

    /**
     * 银联支付结果通知
     *
     * @param unionNotifyVo
     * @return
     */
    boolean unionPayNotify(UnionNotifyVo unionNotifyVo)  throws Exception;

    /**
     * 展示监管资金交费明细
     */
    ShowPayInfoVo showPayInfo(SuperviseBatchSubmitVo superviseBatchSubmitVo);

	public boolean unionScanCodePayNotify(UnionScanCodeNotifyVo unionScanCodeNotifyVo) throws Exception;

    void payRollback(SuperviseBatchSubmit superviseBatchSubmit) throws Exception;

    void syncPayOrderByDay(Integer day) throws Exception;

    void syncPayOrder(String orderNo, String date) throws Exception;
}
