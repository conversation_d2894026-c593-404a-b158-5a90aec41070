package com.guangren.business.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.util.StringUtil;
import com.guangren.business.domain.*;
import com.guangren.business.enumration.BankTransactionRecordType;
import com.guangren.business.mapper.SuperviseReleaseValidListMapper;
import com.guangren.business.service.*;
import com.guangren.common.core.text.Convert;
import com.guangren.common.exception.BusinessException;
import com.guangren.common.utils.ShiroUtils;
import com.guangren.system.domain.SysConfig;
import com.guangren.system.service.ISysConfigService;
import com.guangren.system.service.ISysDictDataService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
public class SuperviseReleaseValidLilstServiceImpl extends ServiceImpl<SuperviseReleaseValidListMapper, SuperviseReleaseValidList> implements ISuperviseReleaseValidListService {

    @Resource
    private SuperviseReleaseValidListMapper superviseReleaseValidListMapper;

    @Autowired
    private  IBankTransactionRecordService bankTransactionRecordService;

    @Autowired
    private  ISchoolStudentService schoolStudentService;

    @Autowired
    private  ISchoolService schoolService;
    @Autowired
    private  IProvinceStudyTimeService provinceStudyTimeService;
    @Autowired
    private  ISpecialSchoolService specialSchoolService;

    @Autowired
    private  UnionBankService unionBankService;

    @Autowired
    private  ISuperviseReleaseRecordService superviseReleaseRecordService;

    @Autowired
    private  ISupervisePayService supervisePayService;

    @Autowired
    private  ISuperviseExceptionDayService superviseExceptionDayService;

    @Autowired
    private  ISchoolStudentDropOutService schoolStudentDropOutService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ISysDictDataService sysDictDataService;

    @Autowired
    private IDivisionAccountService divisionAccountService;

    @Autowired
    private IProvinceStudentService provinceStudentService;




    /**
     * 释放
     * */
    @Override
    public void release(List<Integer> ids){
        List<SuperviseReleaseValidList> list = this.list(new LambdaQueryWrapper<SuperviseReleaseValidList>()
                .in(SuperviseReleaseValidList::getId,ids));
        for(SuperviseReleaseValidList item : list){
            if(item.getReleaseStatus()==1){
               item.setFailReason("已经释放，不能再释放");
               this.updateById(item);
               continue;
            }

            String subjectName = sysDictDataService.selectDictLabel("study_stage",item.getSubjectName().toString());

            SuperviseReleaseRecord releaseRecord  = superviseReleaseRecordService.getOne(new LambdaQueryWrapper<SuperviseReleaseRecord>()
                    .eq(SuperviseReleaseRecord::getStudentId,item.getStudentId())
                    .eq(SuperviseReleaseRecord::getIsSuccess,1)
                    .eq(SuperviseReleaseRecord::getSubjectName,subjectName).last("limit 1"));
            if(releaseRecord != null){
                if(releaseRecord.getSubjectName().equals("其他")){
                    //当释放科目为其他的，需要排除掉 24年 12月份退回 5 元监管费的记录
                    if(releaseRecord.getReleaseFee().compareTo(item.getReleaseMoney())==0){
                        item.setFailReason("该学员已经有"+subjectName+"的释放记录,请确定是否重复释放");
                        this.updateById(item);
                        continue;
                    }
                }else {
                    item.setFailReason("该学员已经有"+subjectName+"的释放记录,请确定是否重复释放");
                    this.updateById(item);
                    continue;
                }
            }

            SchoolStudent student = schoolStudentService.getById(item.getStudentId());
            if(!validateStudent(student,item)){
                continue;
            }
            School school =schoolService.getById(item.getSchoolId());
            if(!validateSchool(school,item)){
                continue;
            }

            String orderNo = null;
            if(item.getSubjectName()==5){
                orderNo = startQuitRelease(student,school,item);
            }else if(item.getSubjectName()==0){
                orderNo = startOtherRelease(student,school,item);
            }else{
                orderNo = startSubjectRelease(student,school,item);
            }
        }
    }

    private String startOtherRelease(SchoolStudent student,School school,SuperviseReleaseValidList item){
        return releaseMoney(student,school,item,null);
    }

    private String startQuitRelease(SchoolStudent student,School school,SuperviseReleaseValidList item){
        if(student.getIsQuit()==0) {
            item.setFailReason("该学员目前不在退学状态，请先申请退学");
            this.updateById(item);
            return null;
        }

        SchoolStudentDropOut dropOut = schoolStudentDropOutService.getOne(new QueryWrapper<SchoolStudentDropOut>()
                .eq("student_id", student.getId())
                .eq("is_done",1)
                .in("status",4,7)
                .last("limit 1"));
        if(dropOut == null) {
            item.setFailReason("无退学申请，或者退学申请流程未完结");
            this.updateById(item);
            return null;
        }
        String orderNo =  releaseMoney(student,school,item,null);
        final SchoolStudent stu = student;
        ThreadUtil.execute(new Runnable() {
            @Override
            public void run() {
                schoolStudentService.notifySchoolStudyCenterQuit(stu);
            }
        });

        return orderNo;
    }



    private String releaseMoney(SchoolStudent student,School school,SuperviseReleaseValidList item,ProvinceStudyTime pst){

        String subjectName = sysDictDataService.selectDictLabel("study_stage",String.valueOf(item.getSubjectName()));

        Date now = new Date();
        //拓展数据
        JSONObject extendData = new JSONObject();
        extendData.put("subjectName", subjectName);
        extendData.put("trainPhase",item.getSubjectName());
        extendData.put("amount", item.getReleaseMoney());
        BankTransactionRecord bankTransactionRecord = new BankTransactionRecord()
                .setStudentId(student.getId())
                .setSchoolId(student.getSchoolId())
                .setRegistrationId(student.getRegistrationId())
                .setBranchId(student.getBranchId())
                .setAmount(item.getReleaseMoney())
                .setType(BankTransactionRecordType.WITHDRAW)
                .setExtendData(extendData.toJSONString())
                .setCreateTime(now);

        JSONObject paramsObject = provinceStudentService.getReleaseParams(student.getId());
        String clientNo = paramsObject.getString("clientNo");
        String payAcctNo = paramsObject.getString("payAcctNo");;
        String signKey = paramsObject.getString("signKey");;
        String payAcctName = paramsObject.getString("payAcctName");;
        String sysNum = paramsObject.getString("sysNum");

        if(StringUtils.isEmpty(clientNo) || StringUtils.isEmpty(payAcctName) || StringUtils.isEmpty(signKey)){
            superviseExceptionDayService.addReleaseFail(item.getReleaseMoney(), now, student, school.getName() + "未设置释放银行卡或未审核，暂停释放");
            throw new BusinessException(school.getName() + "未设置释放银行卡或未审核，暂停释放");
        }

        JSONObject responseJson = new JSONObject();
        boolean success = false;
        try {
            success = unionBankService.withdrawal(item.getReleaseMoney(),sysNum,clientNo,payAcctNo,
                    signKey,student.getName()+"/"+student.getIdentity()+"/"+subjectName,responseJson);
        }catch(Exception ex){
            log.error(student.getName()+"银行释放出错",ex);
            item.setFailReason(ex.getMessage());
            item.setReleaseStatus(2);
            this.updateById(item);
        }
        String orderNo = responseJson.getString("withdrawalNo");
        String bankOrderNo = responseJson.getString("transactionNo");

        SuperviseReleaseRecord record = new SuperviseReleaseRecord()
                .setAccountName(payAcctName)
                .setAccountNo(school.getPayAcctNo()).setBranchId(student.getBranchId())
                .setIsSuccess(success?1:0).setRegistrationId(student.getRegistrationId())
                .setReleaseDate(now).setReleaseFee(item.getReleaseMoney())
                .setSchoolId(student.getSchoolId()).setStudentId(student.getId())
                .setSubjectName(subjectName).setSuperviseFee(student.getSuperviseFee())
                .setBankAcctName(payAcctName).setPayAcctNo(payAcctNo)
                .setPushTime(now).setOrderNo(orderNo);
        superviseReleaseRecordService.save(record);
        // 银行订单号
        bankTransactionRecord.setBankOrderId(bankOrderNo);
        bankTransactionRecord.setOrderId(orderNo);
        bankTransactionRecord.setBankStatus("0");
        if (success) {
            try {
                supervisePayService.withdrawSuccess(student.getId(),payAcctName,payAcctNo,item.getSubjectName(),item.getReleaseMoney(),2,orderNo,bankOrderNo);
                bankTransactionRecord.setStatus(true);
            }catch(Exception ex) {
                log.error("银行支付成功，但逻辑表执行发生错误",ex);
                item.setReleaseStatus(2);
                item.setFailReason("银行支付成功，但逻辑表执行发生错误，请对账之后再释放");
                this.updateById(item);
                record.setIsSuccess(0);
                superviseReleaseRecordService.updateById(record);
                bankTransactionRecord.setStatus(false);
                bankTransactionRecord.setBankStatus("2"); //写成2方便回滚
            }
        } else {
            bankTransactionRecord.setErrMsg(responseJson.getString("errMsg"));
            bankTransactionRecord.setStatus(false);
            bankTransactionRecord.setBankStatus("2");//失败，则为处理中
            superviseExceptionDayService.addReleaseFail(item.getReleaseMoney(), now, student, responseJson.getString("errMsg"));
        }
        // 保存交易流水记录

        bankTransactionRecordService.save(bankTransactionRecord);
        return bankTransactionRecord.getOrderId();
    }


    private String startSubjectRelease(SchoolStudent student,School school,SuperviseReleaseValidList item){

        if(student.getIsQuit()==1){
            item.setFailReason("该学员已退学");
            this.updateById(item);
            return null;
        }
        ProvinceStudyTime pst = provinceStudyTimeService.getOne(new LambdaQueryWrapper<ProvinceStudyTime>()
                .eq(ProvinceStudyTime::getIsRelease, 0)
                .eq(ProvinceStudyTime::getStunum, item.getStudentStunum())
                .eq(ProvinceStudyTime::getTrainphase, item.getSubjectName())
                .eq(ProvinceStudyTime::getTraintype, student.getLicenseType())
                .last("limit 1"));
        if(pst == null){
            item.setFailReason("未找到省厅共享记录");
            this.updateById(item);
            return null;
        }
        return releaseMoney(student,school,item,pst);
    }

    private boolean validateSchool(School school, SuperviseReleaseValidList item) {
        if(school == null){
            item.setFailReason("驾校不存在");
            this.updateById(item);
            return false;
        }
        // 如果没有设置释放账号就增加释放异常
        if (StringUtils.isEmpty(school.getPayAcctNo()) || StringUtils.isEmpty(school.getBankCustomerNo()) || StringUtils.isEmpty(school.getSignKey())) {
            item.setFailReason("未设置释放银行卡，暂停释放");
            this.updateById(item);
            return false;
        }

        // 监管资金账户未审核则增加释放异常
        if (school.getSuperviseAccountIsCheck() == null || school.getSuperviseAccountIsCheck() == 0) {
            item.setFailReason("监管资金账户未审核，暂停释放");
            this.updateById(item);
            return false;
        }

        // 判断驾校总监管金额是否足够本次提现
        if (school.getSuperviseAmt().compareTo(item.getReleaseMoney()) < 0) {
            item.setFailReason("驾校剩余可释放监管金额不足");
            this.updateById(item);
            return false;
        }
        return true;
    }

    private boolean validateStudent(SchoolStudent student,SuperviseReleaseValidList item){
        if(item.getReleaseStatus()==2){
            BankTransactionRecord btr = bankTransactionRecordService.getOne(new LambdaQueryWrapper<BankTransactionRecord>()
                    .eq(BankTransactionRecord::getStudentId,item.getStudentId())
                    .eq(BankTransactionRecord::getType, BankTransactionRecordType.WITHDRAW)
                    .eq(BankTransactionRecord::getBankStatus,2));
            if(btr != null){
                item.setFailReason("该学员正在对账中");
                this.updateById(item);
                return false;
            }
        }
        if(student==null){
            item.setFailReason("该学员不存在");
            this.updateById(item);
            return false;
        }
        if(student.getIsCheck()!=1){
            item.setFailReason("该学员未审核");
            this.updateById(item);
            return false;
        }
        if(student.getIsSupervise()!=1){
            item.setFailReason("该学员未受监管");
            this.updateById(item);
            return false;
        }
        if(student.getSuperviseFeeIsOk()!=1){
            item.setFailReason("该学员资金未托管");
            this.updateById(item);
            return false;
        }

        if(student.getResidueSuperviseAmt().compareTo(item.getReleaseMoney())<0){
            item.setFailReason("该学员托管资金不足");
            this.updateById(item);
            return false;
        }

        Date now  = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(now);
        cal.add(Calendar.DATE, -1);
        if(student.getSuperviseDate().compareTo(cal.getTime()) > 0) {
            item.setFailReason("释放监管金额时间和受监管时间相差太短，需相差一天");
            this.updateById(item);
            return false;
        }
        return true;
    }

    /**
     * 重新生新名单
     * */
    @Override
    public void reCreate(){
        Date now = new Date();
        BigDecimal srcTrainphase2ReleaseAmt = BigDecimal.ZERO;
        SysConfig sysConfig2  = sysConfigService.getOne(new QueryWrapper<SysConfig>()
                .eq("config_key", "subject2.release.amt")
                .last("limit 1"));
        srcTrainphase2ReleaseAmt = new BigDecimal(sysConfig2.getConfigValue());

        BigDecimal srcTrainphase3ReleaseAmt = BigDecimal.ZERO;
        SysConfig sysConfig3  = sysConfigService.getOne(new QueryWrapper<SysConfig>()
                .eq("config_key", "subject3.release.amt")
                .last("limit 1"));
        srcTrainphase3ReleaseAmt = new BigDecimal(sysConfig3.getConfigValue());

        SysConfig sysConfigCommissionRate =
                sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key", "supervise.import.commission"));

        BigDecimal commissionRate = new BigDecimal(sysConfigCommissionRate.getConfigValue());
        SysConfig sysConfigCommission =  sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key","supervise.commission.type"));
        BigDecimal trainphase2ReleaseAmt = srcTrainphase2ReleaseAmt.multiply(new BigDecimal(1).subtract(commissionRate));
        BigDecimal trainphase3ReleaseAmt = srcTrainphase3ReleaseAmt.multiply(new BigDecimal(1).subtract(commissionRate));


        List<Map<String,Object>> releaseStudentList = provinceStudyTimeService.getReleaseStudentList();
        for(Map<String,Object> map : releaseStudentList){
            String studentId = map.get("id").toString();
            SchoolStudent student = schoolStudentService.getById(studentId);
            if(student==null){
                continue;
            }
            int trainphase = Integer.parseInt(map.get("trainphase").toString());
            Date pushtime = DateUtil.parse(map.get("pushtime").toString(),"yyyy-MM-dd HH:mm:ss");
            String branchName = map.get("branch_name")==null ? "" : map.get("branch_name").toString();
            String branchId = map.get("branch_id")==null ? "" : map.get("branch_id").toString();
            String schoolId = map.get("school_id").toString();
            String schoolName = map.get("school_name").toString();
            String registrationId = map.get("registration_id")==null ? "" : map.get("registration_id").toString();
            String registrationName = map.get("registration_name")==null ? "" : map.get("registration_name").toString();
            String studentName = map.get("name").toString();
            String identity = map.get("identity").toString();
            int gender = Integer.parseInt(map.get("gender").toString());
            String stunum = map.get("stunum")==null? "":map.get("stunum").toString();
            String licenseType = map.get("license_type").toString();
            String mobile = map.get("mobile").toString();
            Date superviseDate = DateUtil.parse(map.get("supervise_date").toString(),"yyyy-MM-dd HH:mm:ss");

            Date trainphase2Date = cn.hutool.core.date.DateUtil.parse("2024-01-01 00:00:00","yyyy-MM-dd HH:mm:ss");
            if(trainphase==3 && superviseDate.before(trainphase2Date)){
                continue;
            }
            BigDecimal releaseMoney = BigDecimal.ZERO;
            if(student.getReleaseVersion()==0){
                if (trainphase == 2) {
                    releaseMoney = trainphase2ReleaseAmt;
                } else if (trainphase == 3) {
                    releaseMoney = trainphase3ReleaseAmt;
                }
            }else if(student.getReleaseVersion()==1){
                if (trainphase == 2) {
                    releaseMoney = srcTrainphase2ReleaseAmt;
                } else if (trainphase == 3) {
                    releaseMoney = srcTrainphase3ReleaseAmt;
                }
            }else{
                if (trainphase == 2) {
                    releaseMoney = srcTrainphase2ReleaseAmt;
                } else if (trainphase == 3) {
                    BigDecimal withdrawAmount = student.getResidueSuperviseAmt();
                    if(student.getResidueSuperviseAmt().compareTo(srcTrainphase2ReleaseAmt)>0){
                        withdrawAmount = student.getResidueSuperviseAmt().subtract(srcTrainphase2ReleaseAmt);
                    }
                    releaseMoney = withdrawAmount;
                }
            }

            SuperviseReleaseValidList srvl = this.getOne(new LambdaQueryWrapper<SuperviseReleaseValidList>()
                    .eq(SuperviseReleaseValidList::getStudentId,studentId)
                    .eq(SuperviseReleaseValidList::getSubjectName,trainphase)
                    .last("limit 1"));
            if(srvl == null){
                srvl = new SuperviseReleaseValidList()
                        .setPushtime(pushtime)
                        .setBranchName(branchName)
                        .setBranchId(branchId)
                        .setRegistrationId(registrationId)
                        .setRegistrationName(registrationName)
                        .setSchoolId(schoolId)
                        .setSchoolName(schoolName)
                        .setStudentId(studentId)
                        .setStudentIdentity(identity)
                        .setStudentName(studentName)
                        .setStudentSex(gender)
                        .setStudentStunum(stunum)
                        .setStudentMobile(mobile)
                        .setSubjectName(trainphase)
                        .setCreatedTime(now)
                        .setReleaseMoney(releaseMoney)
                        .setStudentTraintype(licenseType);
                this.save(srvl);
            }
        }

        List<SchoolStudent> students = schoolStudentService.getQuitReleaseStudentList();
        for(SchoolStudent student : students){
            SuperviseReleaseValidList srvl = this.getOne(new LambdaQueryWrapper<SuperviseReleaseValidList>()
                    .eq(SuperviseReleaseValidList::getStudentId,student.getId())
                    .eq(SuperviseReleaseValidList::getSubjectName,5)
                    .last("limit 1"));
            if(srvl == null){
                srvl = new SuperviseReleaseValidList()
                        .setReleaseMoney(student.getResidueSuperviseAmt())
                        .setPushtime(now)
                        .setBranchName(student.getBranch().getName())
                        .setBranchId(student.getBranchId())
                        .setRegistrationId(student.getRegistrationId())
                        .setRegistrationName(student.getRegistration().getName())
                        .setSchoolId(student.getSchoolId())
                        .setSchoolName(student.getSchool().getName())
                        .setStudentId(student.getId())
                        .setStudentIdentity(student.getIdentity())
                        .setStudentName(student.getName())
                        .setStudentSex(student.getGender())
                        .setStudentStunum(student.getStunum())
                        .setStudentMobile(student.getMobile())
                        .setSubjectName(5)
                        .setCreatedTime(now)
                        .setStudentTraintype(student.getLicenseType());
                this.save(srvl);
            }
        }
    }

    /**
     * 查询释放明单
     *
     * @param id 释放明单主键
     * @return 释放明单
     */
    @Override
    public SuperviseReleaseValidList selectSuperviseReleaseValidListById(Long id)
    {
        return superviseReleaseValidListMapper.selectSuperviseReleaseValidListById(id);
    }

    /**
     * 查询释放明单列表
     *
     * @param superviseReleaseValidList 释放明单
     * @return 释放明单
     */
    @Override
    public List<SuperviseReleaseValidList> selectSuperviseReleaseValidListList(SuperviseReleaseValidList superviseReleaseValidList)
    {
         if (StringUtils.isNotBlank(superviseReleaseValidList.getStudentIdentity())) {
            List<String> identityList = Arrays.asList(superviseReleaseValidList.getStudentIdentity().split(" "));
            superviseReleaseValidList.setIdentityList(identityList);
        }
        return superviseReleaseValidListMapper.selectSuperviseReleaseValidListList(superviseReleaseValidList);
    }

    /**
     * 新增释放明单
     *
     * @param superviseReleaseValidList 释放明单
     * @return 结果
     */
    @Override
    public int insertSuperviseReleaseValidList(SuperviseReleaseValidList superviseReleaseValidList)
    {
        return superviseReleaseValidListMapper.insertSuperviseReleaseValidList(superviseReleaseValidList);
    }

    /**
     * 修改释放明单
     *
     * @param superviseReleaseValidList 释放明单
     * @return 结果
     */
    @Override
    public int updateSuperviseReleaseValidList(SuperviseReleaseValidList superviseReleaseValidList)
    {
        return superviseReleaseValidListMapper.updateSuperviseReleaseValidList(superviseReleaseValidList);
    }

    /**
     * 批量删除释放明单
     *
     * @param ids 需要删除的释放明单主键
     * @return 结果
     */
    @Override
    public int deleteSuperviseReleaseValidListByIds(String ids)
    {
        return superviseReleaseValidListMapper.deleteSuperviseReleaseValidListByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除释放明单信息
     *
     * @param id 释放明单主键
     * @return 结果
     */
    @Override
    public int deleteSuperviseReleaseValidListById(Long id)
    {
        return superviseReleaseValidListMapper.deleteSuperviseReleaseValidListById(id);
    }

    @Override
    @Transactional
    public void checkbankTrans(List<Integer> ids) throws Exception  {
        try {
            List<SuperviseReleaseValidList> list = this.list(new LambdaQueryWrapper<SuperviseReleaseValidList>()
                    .in(SuperviseReleaseValidList::getId, ids));
            for (SuperviseReleaseValidList item : list) {
                bankTransactionRecordService.syncWithdraw(item.getReleaseOrderNo(),null);
            }
        }catch(Exception ex){
            log.error("check bank transcation error",ex);
            throw ex;
        }
    }

    @Override
    @Transactional
    public void bankReturnMoneyOperate(List<Integer> ids) {
        try {
            List<SuperviseReleaseValidList> list = this.list(new LambdaQueryWrapper<SuperviseReleaseValidList>()
                    .in(SuperviseReleaseValidList::getId, ids));
            for (SuperviseReleaseValidList item : list) {
                bankTransactionRecordService.bankBankErrorWithdrawRollback(item.getReleaseOrderNo());
            }
        }catch(Exception ex){
            log.error("bankReturnMoneyOperate error",ex);
            throw ex;
        }
    }
}
