package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolRank;

import java.util.List;

/**
 * 驾校排名Service接口
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
public interface ISchoolRankService extends IService<SchoolRank>
{
    /**
     * 查询驾校排名
     *
     * @param id 驾校排名主键
     * @return 驾校排名
     */
    public SchoolRank selectSchoolRankById(String id);

    /**
     * 查询驾校排名列表
     *
     * @param schoolRank 驾校排名
     * @return 驾校排名集合
     */
    public List<SchoolRank> selectSchoolRankList(SchoolRank schoolRank);

    /**
     * 新增驾校排名
     *
     * @param schoolRank 驾校排名
     * @return 结果
     */
    public int insertSchoolRank(SchoolRank schoolRank);

    /**
     * 修改驾校排名
     *
     * @param schoolRank 驾校排名
     * @return 结果
     */
    public int updateSchoolRank(SchoolRank schoolRank);

    /**
     * 批量删除驾校排名
     *
     * @param ids 需要删除的驾校排名主键集合
     * @return 结果
     */
    public int deleteSchoolRankByIds(String ids);

    /**
     * 删除驾校排名信息
     *
     * @param id 驾校排名主键
     * @return 结果
     */
    public int deleteSchoolRankById(String id);
}
