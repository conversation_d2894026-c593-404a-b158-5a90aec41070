package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolBranch;

import java.util.List;

/**
 * 分校Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-01
 */
public interface ISchoolBranchService extends IService<SchoolBranch>
{
    /**
     * 查询分校
     * 
     * @param id 分校主键
     * @return 分校
     */
    public SchoolBranch selectSchoolBranchById(String id);

    /**
     * 查询分校列表
     * 
     * @param schoolBranch 分校
     * @return 分校集合
     */
    public List<SchoolBranch> selectSchoolBranchList(SchoolBranch schoolBranch);

    /**
     * 新增分校
     * 
     * @param schoolBranch 分校
     * @return 结果
     */
    public int insertSchoolBranch(SchoolBranch schoolBranch);

    /**
     * 修改分校
     * 
     * @param schoolBranch 分校
     * @return 结果
     */
    public int updateSchoolBranch(SchoolBranch schoolBranch);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SchoolBranch schoolBranch);

    /**
     * 批量删除分校
     * 
     * @param ids 需要删除的分校主键集合
     * @return 结果
     */
    public int deleteSchoolBranchByIds(String ids);

    /**
     * 删除分校信息
     * 
     * @param id 分校主键
     * @return 结果
     */
    public int deleteSchoolBranchById(String id);

    public int saveAccount(SchoolBranch schoolBranch);

    /**
     * 导出分校列表
     */
    List<SchoolBranch> exportSchoolBranchList(SchoolBranch schoolBranch);
}
