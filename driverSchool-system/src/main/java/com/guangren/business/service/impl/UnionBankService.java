package com.guangren.business.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpBase;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.guangren.business.config.UnionBankConfiguration;
import com.guangren.business.domain.DivsionOrder;
import com.guangren.business.domain.SpecialSchool;
import com.guangren.business.domain.UnionBankWithdrawal;
import com.guangren.business.service.IBankTransactionRecordService;
import com.guangren.business.service.IProvinceStudyTimeService;
import com.guangren.business.service.ISchoolCalculateFlowService;
import com.guangren.business.service.ISchoolDealFlowService;
import com.guangren.business.service.ISchoolService;
import com.guangren.business.service.ISpecialSchoolService;
import com.guangren.business.service.ISuperviseBatchSubmitDetailService;
import com.guangren.business.service.ISuperviseBatchSubmitService;
import com.guangren.business.service.ISuperviseFlowService;
import com.guangren.business.service.ISuperviseReleaseRecordService;
import com.guangren.business.service.ISuperviseReleaseValidListService;
import com.guangren.business.vo.DivsionOrderAssociationExcelVo;
import com.guangren.common.config.RuoYiConfig;
import com.guangren.common.utils.DateUtil;
import com.guangren.common.utils.DateUtils;
import com.guangren.common.utils.MD5Util;
import com.guangren.common.utils.StringUtils;
import com.guangren.common.utils.ftp.FTPUtil;
import com.guangren.common.utils.poi.ExcelUtil;
import com.guangren.system.domain.SysConfig;
import com.guangren.system.service.ISysConfigService;
import lombok.SneakyThrows;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class UnionBankService {

    @Value("${server.domain}")
    private String domain;
    @Autowired
    private IBankTransactionRecordService bankTransactionRecordService;
    @Autowired
    private UnionBankConfiguration unionBankConfiguration;

    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private SchoolStudentServiceImpl schoolStudentService;
    @Autowired
    private SchoolStudentStudyTimeStaServiceImpl schoolStudentStudyTimeStaService;

    @Autowired
    private SupervisePayServiceImpl supervisePayService;
    
    @Autowired
    private ISuperviseReleaseRecordService superviseReleaseRecordService;
    @Autowired
    private ISchoolDealFlowService schoolDealFlowService;
    @Autowired
    private ISuperviseFlowService superviseFlowService;
    @Autowired
    private ISuperviseBatchSubmitService superviseBatchSubmitService;
    @Autowired
    private ISchoolService schoolService;
    @Autowired
    private ISuperviseBatchSubmitDetailService superviseBatchSubmitDetailService;
    @Autowired
    private IProvinceStudyTimeService provinceStudyTimeService;
    @Autowired
    private ISpecialSchoolService specialSchoolService;
    @Autowired
    private ISchoolCalculateFlowService schoolCalculateFlowService;

    @Autowired
    private ISuperviseReleaseValidListService superviseReleaseValidListService;


    
    private static final Logger log = LoggerFactory.getLogger(UnionBankService.class);
    
    @SneakyThrows
    static String getSignature(String appId, String appKey, String timestamp, String nonce, String body) {
        byte[] data = body.getBytes(StandardCharsets.UTF_8);
        String bodyDigest = DigestUtils.sha256Hex(new ByteArrayInputStream(data));
        String s1 = appId + timestamp + nonce + bodyDigest;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(appKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] localSignature = mac.doFinal(s1.getBytes(StandardCharsets.UTF_8));
        return Base64.encodeBase64String(localSignature);
    }

    public static String getMerchantOrderId() {
        return DateUtils.parseDateToStr("yyyyMMddHHmmssSSS", new Date()) + RandomStringUtils.randomNumeric(7);
    }




    @SneakyThrows
    public String getSpecialSchoolQrcodeUrl(String orderId,
                                 String orderSubId,
                                 long totalAmount,
                                 String mid,
                                 String tid,
                                 Object attachedData,
                                 long superviseAmt,
                                 long commission, SpecialSchool specialSchool) {
        String timestamp = DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS);
        String nonce = UUID.randomUUID().toString();
        JSONObject json = new JSONObject();
        json.put("requestTimestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()));
        // 处理失败：无效订单号，订单号必须以35YJ开头
        json.put("billNo", orderId); // 商户订单号
        json.put("billDate", DateUtils.dateTimeNow("yyyy-MM-dd"));
        json.put("billDesc", "监管费");
        json.put("mid", mid); // 商户号
        json.put("tid", tid);    // 终端号
        json.put("instMid", "QRPAYDEFAULT");
        json.put("totalAmount", totalAmount);      // 支付总金额,单位分，即 totalAmount/100 元
        // 支付结果回调地址
        json.put("notifyUrl", domain + unionBankConfiguration.getNotifyScanCodeUrl());
        json.put("attachedData", attachedData);
        //分账金额：手续费
        json.put("platformAmount", commission);
        //分账标记：为true时goods和subOrders不能同时为空
        json.put("divisionFlag", false);
        
        
        log.info("下单报文：===="+json.toJSONString());
        
        String signature = getSignature(specialSchool.getAppId(), specialSchool.getAppKey(), timestamp, nonce, json.toJSONString());
        String authorization = "OPEN-BODY-SIG AppId=" + "\"" + specialSchool.getAppId() + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + signature + "\"";
        
        HttpRequest request = HttpUtil.createPost(unionBankConfiguration.getQrcodeUrl())
        		.header("Content-Type", "application/json")
        		.header("Authorization",authorization)
        		.body(json.toJSONString());
        
        HttpResponse response = request.execute();
        if(response.isOk()) {
        	JSONObject resJSON = JSONObject.parseObject(response.body());
        	String errCode = resJSON.getString("errCode");
        	String billNo = resJSON.getString("billNo");
        	if(errCode.equals("SUCCESS") && billNo.equals(orderId)) {
        		return resJSON.getString("billQRCode");
        	}
        }
        return null;
    }
    
    @SneakyThrows
    public String getSpecialSchoolUnionPayUrl(String orderId,
                                 String orderSubId,
                                 long totalAmount,
                                 String mid,
                                 String tid,
                                 Object attachedData,
                                 Integer transType,
                                 long superviseAmt,
                                 long commission, String chnlNo,SpecialSchool specialSchool) {
        String timestamp = DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS);
        String nonce = UUID.randomUUID().toString();
        JSONObject json = new JSONObject();
//        json.put("requestTimestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.addDays(new Date(), -1)));
        json.put("requestTimestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()));
        // 处理失败：无效订单号，订单号必须以35HT开头
        json.put("merOrderId", orderId); // 商户订单号
        json.put("mid", mid); // 商户号
        json.put("tid", tid);    // 终端号
        json.put("instMid", "WGDEFAULT");
        json.put("totalAmount", totalAmount);      // 支付总金额,单位分，即 totalAmount/100 元
        if (transType == 0) {
            json.put("transType", "UPG_PERSONAL");
        }else if (transType == 1) {
            json.put("transType", "UPG_BUSINESS");
        }
        json.put("bizType", "100003");
        //json.put("transType", "UPG_PERSONAL");
        //json.put("chnlNo", "***********");
//        json.put("termType", "PC");
        json.put("chnlType", "PC");
        // 支付结果回调地址
        json.put("notifyUrl", domain + unionBankConfiguration.getNotifyUrl());
//        json.put("returnUrl", domain + returnUrl);
        json.put("attachedData", attachedData);
        //分账金额：手续费
        json.put("platformAmount", commission);
        //分账标记：为true时goods和subOrders不能同时为空
        json.put("divisionFlag", false);
        
        json.put("chnlNo", chnlNo);

        log.info(json.toJSONString());
        String signature = getSignature(specialSchool.getAppId(), specialSchool.getAppKey(), timestamp, nonce, json.toJSONString());

        String param = "authorization=OPEN-FORM-PARAM"
                + "&appId=" + specialSchool.getAppId()
                + "&timestamp=" + timestamp
                + "&nonce=" + nonce
                + "&content=" + URLEncoder.encode(json.toString(), "UTF-8")
                + "&signature=" + URLEncoder.encode(signature, "UTF-8");

        return unionBankConfiguration.getNetPayUrl() + "?" + param;
    }
    
    
    @SneakyThrows
    public String getQrcodeUrl(String orderId,
                                 String orderSubId,
                                 long totalAmount,
                                 String mid,
                                 String tid,
                                 Object attachedData,
                                 long superviseAmt,
                                 long commission, String superviseMid) {
        String timestamp = DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS);
        String nonce = UUID.randomUUID().toString();
        JSONObject json = new JSONObject();
        json.put("requestTimestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()));
        // 处理失败：无效订单号，订单号必须以35YJ开头
        json.put("billNo", orderId); // 商户订单号
        json.put("billDate", DateUtils.dateTimeNow("yyyy-MM-dd"));
        json.put("billDesc", "监管费");
        json.put("mid", mid); // 商户号
        json.put("tid", tid);    // 终端号
        json.put("instMid", "QRPAYDEFAULT");
        json.put("totalAmount", totalAmount);      // 支付总金额,单位分，即 totalAmount/100 元
        // 支付结果回调地址
        json.put("notifyUrl", domain + unionBankConfiguration.getNotifyScanCodeUrl());
        json.put("attachedData", attachedData);
        //分账金额：手续费
        json.put("platformAmount", commission);
        //分账标记：为true时goods和subOrders不能同时为空
        json.put("divisionFlag", true);
        //子订单信息
        JSONObject subOrder = new JSONObject();
        subOrder.put("mid", superviseMid);
        subOrder.put("merOrderId", orderSubId);
        subOrder.put("totalAmount", superviseAmt);
        JSONArray subOrders = new JSONArray();
        subOrders.add(subOrder);
        SysConfig sysConfigCommission =  sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key","supervise.commission.type"));
        //0-支付整额方式，1-入账整额方式
        if(sysConfigCommission != null && sysConfigCommission.getConfigValue().equals("1")) {
        	json.put("divisionFlag", false);
        }else {
        	json.put("subOrders", subOrders);
        }
        
        
        log.info("下单报文：===="+json.toJSONString());
        
        String signature = getSignature(unionBankConfiguration.getAppId(), unionBankConfiguration.getAppKey(), timestamp, nonce, json.toJSONString());
        String authorization = "OPEN-BODY-SIG AppId=" + "\"" + unionBankConfiguration.getAppId() + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + signature + "\"";
        
        HttpRequest request = HttpUtil.createPost(unionBankConfiguration.getQrcodeUrl())
        		.header("Content-Type", "application/json")
        		.header("Authorization",authorization)
        		.body(json.toJSONString());
        
        HttpResponse response = request.execute();
        if(response.isOk()) {
        	JSONObject resJSON = JSONObject.parseObject(response.body());
        	String errCode = resJSON.getString("errCode");
        	String billNo = resJSON.getString("billNo");
        	if(errCode.equals("SUCCESS") && billNo.equals(orderId)) {
        		return resJSON.getString("billQRCode");
        	}
        }
        return null;
    }

    //二维码支付不分账
    public String getMiniAppQrcodeNoSplitUrl(String orderNo,JSONArray subOrders,long totalAmount,
                                             long platformAmount,String mid,String tid,
                                             String attachedData) {
        String timestamp = DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS);
        String nonce = UUID.randomUUID().toString();
        JSONObject json = new JSONObject();
        json.put("requestTimestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()));
        // 处理失败：无效订单号，订单号必须以35YJ开头
        json.put("billNo", orderNo); // 商户订单号
        json.put("billDate", DateUtils.dateTimeNow("yyyy-MM-dd"));
        json.put("billDesc", "监管费");
        json.put("mid", mid); // 商户号
        json.put("tid", tid);    // 终端号
        json.put("instMid", "QRPAYDEFAULT");
        json.put("totalAmount", totalAmount);      // 支付总金额,单位分，即 totalAmount/100 元
        // 支付结果回调地址
        json.put("notifyUrl", domain + unionBankConfiguration.getNotifyMiniappScanCodeUrl());
        json.put("attachedData", attachedData);

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.HOUR,2);
        json.put("expireTime",DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",cal.getTime()));
        //分账金额：手续费
        json.put("platformAmount", totalAmount);
        //分账标记：为true时goods和subOrders不能同时为空
        //2024-10-22 要求先不向子商户分帐10元
        json.put("divisionFlag", false);
        //子订单信息
        //json.put("subOrders", subOrders);
        String signature = getSignature(unionBankConfiguration.getAppId(), unionBankConfiguration.getAppKey(), timestamp, nonce, json.toJSONString());
        String authorization = "OPEN-BODY-SIG AppId=" + "\"" + unionBankConfiguration.getAppId() + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + signature + "\"";

        HttpRequest request = HttpUtil.createPost(unionBankConfiguration.getQrcodeUrl())
                .header("Content-Type", "application/json")
                .header("Authorization",authorization)
                .body(json.toJSONString());

        try (HttpResponse response = request.execute()) {
            log.info(request.toString());
            log.info(response.toString());
            JSONObject resJSON = JSONObject.parseObject(response.body());
            String errCode = resJSON.getString("errCode");
            String billNo = resJSON.getString("billNo");
            if(errCode.equals("SUCCESS") && billNo.equals(orderNo)) {
                return resJSON.getString("billQRCode");
            }
        }

        return null;
    }

    public String getMiniAppQrcodeUrl(String orderNo,JSONArray subOrders,long totalAmount,
                                      long platformAmount,String mid,String tid,
                                      String attachedData) {
        String timestamp = DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS);
        String nonce = UUID.randomUUID().toString();
        JSONObject json = new JSONObject();
        json.put("requestTimestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()));
        // 处理失败：无效订单号，订单号必须以35YJ开头
        json.put("billNo", orderNo); // 商户订单号
        json.put("billDate", DateUtils.dateTimeNow("yyyy-MM-dd"));
        json.put("billDesc", "监管费");
        json.put("mid", mid); // 商户号
        json.put("tid", tid);    // 终端号
        json.put("instMid", "QRPAYDEFAULT");
        json.put("totalAmount", totalAmount);      // 支付总金额,单位分，即 totalAmount/100 元
        // 支付结果回调地址
        json.put("notifyUrl", domain + unionBankConfiguration.getNotifyMiniappScanCodeUrl());
        json.put("attachedData", attachedData);

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.HOUR,2);
        json.put("expireTime",DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",cal.getTime()));
        //分账金额：手续费
        json.put("platformAmount", platformAmount);
        //分账标记：为true时goods和subOrders不能同时为空
        json.put("divisionFlag", true);
        //子订单信息
        json.put("subOrders", subOrders);
        String signature = getSignature(unionBankConfiguration.getAppId(), unionBankConfiguration.getAppKey(), timestamp, nonce, json.toJSONString());
        String authorization = "OPEN-BODY-SIG AppId=" + "\"" + unionBankConfiguration.getAppId() + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + signature + "\"";

        HttpRequest request = HttpUtil.createPost(unionBankConfiguration.getQrcodeUrl())
                .header("Content-Type", "application/json")
                .header("Authorization",authorization)
                .body(json.toJSONString());

        try (HttpResponse response = request.execute()) {
            log.info(request.toString());
            log.info(response.toString());
            JSONObject resJSON = JSONObject.parseObject(response.body());
            String errCode = resJSON.getString("errCode");
            String billNo = resJSON.getString("billNo");
            if(errCode.equals("SUCCESS") && billNo.equals(orderNo)) {
                return resJSON.getString("billQRCode");
            }
        }

        return null;
    }


    public JSONObject debugGetMiniappRequest(String orderNo,JSONArray subOrders,long totalAmount,long platformAmount,String mid,String tid,String attachedData){
        JSONObject object = new JSONObject();
        object.put("package","Sign=WXPay");
        object.put("appId","wxcf7c37f82cf75798");
        object.put("sign","8C4B63127BE65757C41369D21EB211B8");
        object.put("partnerId","102510208503");
        object.put("prepayId","wx20170425193304c92c9f33a10089330713");
        object.put("nonceStr","MquuMZwYnACubCaXSqrTrxKkpINYtRPt");
        object.put("timeStamp","20170425193304");
        object.put("qrCode","weixin://wxpay/bizpayurl?pr=GhOkBtP");
        return object;
    }

    /**
     * 小程序支付无需分账
     * @return
     */
    public JSONObject getMiniappNoSpiltRequest(String orderNo,JSONArray subOrders,long totalAmount,
                                        long platformAmount,String mid,String tid,
                                        String openId,String appId,String attachedData){

        String nonce = UUID.randomUUID().toString();
        JSONObject json = new JSONObject();
        json.put("msgId", RandomUtil.randomString(10));
        json.put("requestTimestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()));
        // 处理失败：无效订单号，订单号必须以35YJ开头
        json.put("merOrderId", orderNo); // 商户订单号
        json.put("orderDesc","学费监管费");
        json.put("mid", mid); // 商户号
        json.put("tid", tid);    // 终端号
        json.put("instMid", "MINIDEFAULT");
        json.put("totalAmount", totalAmount);      // 支付总金额,单位分，即 totalAmount/100 元
        // 支付结果回调地址
        json.put("notifyUrl", domain + unionBankConfiguration.getNotifyMiniappUrl());
        json.put("attachedData", attachedData);
        //分账金额：主商户得到的金额
        //       json.put("platformAmount", platformAmount);
        //分账标记：为true时goods和subOrders不能同时为空
//        json.put("divisionFlag", true);
//        json.put("subOrders", subOrders);
        //2024-10-22 要求先不向子商户分帐10元
        json.put("platformAmount", totalAmount);
        json.put("divisionFlag",false);
        json.put("subAppId",appId);
        json.put("subOpenId",openId);
        json.put("tradeType","MINI");

        String timestamp = DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS);
        String signature = getSignature(unionBankConfiguration.getAppId(), unionBankConfiguration.getAppKey(), timestamp, nonce, json.toJSONString());
        String authorization = "OPEN-BODY-SIG AppId=" + "\"" + unionBankConfiguration.getAppId() + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + signature + "\"";

        HttpRequest request = HttpUtil.createPost(unionBankConfiguration.getMiniappOrderUrl())
                .header("Content-Type", "application/json")
                .header("Authorization",authorization)
                .body(json.toJSONString());


        try (HttpResponse response = request.execute()) {
            log.info(request.toString());
            log.info(response.toString());
            JSONObject resJSON = JSONObject.parseObject(response.body());
            String errCode = resJSON.getString("errCode");
            if(errCode.equals("SUCCESS")) {
                return resJSON.getJSONObject("miniPayRequest");
            }
        }

        return null;
    }

    /**
     * 小程序支付，需分账
     * */
    public JSONObject getMiniappRequest(String orderNo,JSONArray subOrders,long totalAmount,
                                        long platformAmount,String mid,String tid,
                                        String openId,String appId,String attachedData){

        String nonce = UUID.randomUUID().toString();
        JSONObject json = new JSONObject();
        json.put("msgId", RandomUtil.randomString(10));
        json.put("requestTimestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()));
        // 处理失败：无效订单号，订单号必须以35YJ开头
        json.put("merOrderId", orderNo); // 商户订单号
        json.put("orderDesc","学费监管费");
        json.put("mid", mid); // 商户号
        json.put("tid", tid);    // 终端号
        json.put("instMid", "MINIDEFAULT");
        json.put("totalAmount", totalAmount);      // 支付总金额,单位分，即 totalAmount/100 元
        // 支付结果回调地址
        json.put("notifyUrl", domain + unionBankConfiguration.getNotifyMiniappUrl());
        json.put("attachedData", attachedData);
        //分账金额：手续费
        json.put("platformAmount", platformAmount);
        //分账标记：为true时goods和subOrders不能同时为空
        json.put("divisionFlag", true);
        json.put("subOrders", subOrders);
        json.put("subAppId",appId);
        json.put("subOpenId",openId);
        json.put("tradeType","MINI");

        String timestamp = DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS);
        String signature = getSignature(unionBankConfiguration.getAppId(), unionBankConfiguration.getAppKey(), timestamp, nonce, json.toJSONString());
        String authorization = "OPEN-BODY-SIG AppId=" + "\"" + unionBankConfiguration.getAppId() + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + signature + "\"";

        HttpRequest request = HttpUtil.createPost(unionBankConfiguration.getMiniappOrderUrl())
                .header("Content-Type", "application/json")
                .header("Authorization",authorization)
                .body(json.toJSONString());


        try (HttpResponse response = request.execute()) {
            log.info(request.toString());
            log.info(response.toString());
            JSONObject resJSON = JSONObject.parseObject(response.body());
            String errCode = resJSON.getString("errCode");
            if(errCode.equals("SUCCESS")) {
                return resJSON.getJSONObject("miniPayRequest");
            }
        }

        return null;
    }

    public JSONObject getMiniappRefundRequest(String orderNo,String mid,String tid,
                                        BigDecimal platformAmount,JSONArray subOrders,BigDecimal refundAmount){

        String nonce = UUID.randomUUID().toString();
        JSONObject json = new JSONObject();
        json.put("msgId", RandomUtil.randomString(10));
        json.put("requestTimestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()));
        // 处理失败：无效订单号，订单号必须以35YJ开头
        json.put("merOrderId", orderNo); // 商户订单号
        json.put("orderDesc","学费监管费退费");
        json.put("mid", mid); // 商户号
        json.put("tid", tid);    // 终端号
        json.put("instMid", "MINIDEFAULT");
        json.put("refundAmount", refundAmount);      // 支付总金额,单位分，即 totalAmount/100 元
        // 支付结果回调地址
        json.put("refundOrderId", orderNo);
        //分账金额：手续费
        json.put("platformAmount", platformAmount);
        //分账标记：为true时goods和subOrders不能同时为空
        json.put("subOrders", subOrders);

        String timestamp = DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS);
        String signature = getSignature(unionBankConfiguration.getAppId(), unionBankConfiguration.getAppKey(), timestamp, nonce, json.toJSONString());
        String authorization = "OPEN-BODY-SIG AppId=" + "\"" + unionBankConfiguration.getAppId() + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + signature + "\"";

        HttpRequest request = HttpUtil.createPost(unionBankConfiguration.getMiniappRefundOrderUrl())
                .header("Content-Type", "application/json")
                .header("Authorization",authorization)
                .body(json.toJSONString());


        try (HttpResponse response = request.execute()) {
            log.info(request.toString());
            log.info(response.toString());
            JSONObject resJSON = JSONObject.parseObject(response.body());
            String errCode = resJSON.getString("errCode");
            String refundStatus = resJSON.getString("refundStatus");
            if(errCode.equals("SUCCESS") && refundStatus.equals("SUCCESS")) {
                return resJSON;
            }
        }

        return null;
    }

    public JSONObject getMiniappScanCodeRefundRequest(String orderNo,String orderDate,String mid,String tid,
                                              BigDecimal platformAmount,JSONArray subOrders,BigDecimal refundAmount){

        String nonce = UUID.randomUUID().toString();
        JSONObject json = new JSONObject();
        json.put("msgId", RandomUtil.randomString(10));
        json.put("requestTimestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()));
        // 处理失败：无效订单号，订单号必须以35YJ开头
        json.put("billNo", orderNo); // 商户订单号
        json.put("billDate",orderDate);
        json.put("mid", mid); // 商户号
        json.put("tid", tid);    // 终端号
        json.put("instMid", "QRPAYDEFAULT");
        json.put("refundAmount", refundAmount);      // 支付总金额,单位分，即 totalAmount/100 元
        // 支付结果回调地址
        json.put("refundOrderId", orderNo);
        //分账金额：手续费
        json.put("platformAmount", platformAmount);
        //分账标记：为true时goods和subOrders不能同时为空
        json.put("subOrders", subOrders);
        String timestamp = DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS);
        String signature = getSignature(unionBankConfiguration.getAppId(), unionBankConfiguration.getAppKey(), timestamp, nonce, json.toJSONString());
        String authorization = "OPEN-BODY-SIG AppId=" + "\"" + unionBankConfiguration.getAppId() + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + signature + "\"";

        HttpRequest request = HttpUtil.createPost(unionBankConfiguration.getMiniappScancodeRefundOrderUrl())
                .header("Content-Type", "application/json")
                .header("Authorization",authorization)
                .body(json.toJSONString());


        try (HttpResponse response = request.execute()) {
            log.info(request.toString());
            log.info(response.toString());
            JSONObject resJSON = JSONObject.parseObject(response.body());
            String errCode = resJSON.getString("errCode");
            String refundStatus = resJSON.getString("refundStatus");
            if(errCode.equals("SUCCESS") && refundStatus.equals("SUCCESS")) {
                return resJSON;
            }
        }

        return null;
    }

    /**
     * 生成银联支付地址
     *
     * @param orderId      订单号
     * @param orderSubId   子订单号
     * @param totalAmount  总金额
     * @param mid          商户号
     * @param tid          终端号
     * @param attachedData 附加数据
     * @param transType 交易类型 0=个人网银，1=企业网银，2=扫码
     * @param superviseAmt 总监管金额
     * @param commission 手续费
     */
    @SneakyThrows
    public String getUnionPayUrl(String orderId,
                                 String orderSubId,
                                 long totalAmount,
                                 String mid,
                                 String tid,
                                 Object attachedData,
                                 Integer transType,
                                 long superviseAmt,
                                 long commission, String superviseMid,String chnlNo) {
        String timestamp = DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS);
        String nonce = UUID.randomUUID().toString();
        JSONObject json = new JSONObject();
//        json.put("requestTimestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.addDays(new Date(), -1)));
        json.put("requestTimestamp", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()));
        // 处理失败：无效订单号，订单号必须以35HT开头
        json.put("merOrderId", orderId); // 商户订单号
        json.put("mid", mid); // 商户号
        json.put("tid", tid);    // 终端号
        json.put("instMid", "WGDEFAULT");
        json.put("totalAmount", totalAmount);      // 支付总金额,单位分，即 totalAmount/100 元
        if (transType == 0) {
            json.put("transType", "UPG_PERSONAL");
        }else if (transType == 1) {
            json.put("transType", "UPG_BUSINESS");
        }
        json.put("bizType", "100003");
        //json.put("transType", "UPG_PERSONAL");
        //json.put("chnlNo", "***********");
//        json.put("termType", "PC");
        json.put("chnlType", "PC");
        // 支付结果回调地址
        json.put("notifyUrl", domain + unionBankConfiguration.getNotifyUrl());
//        json.put("returnUrl", domain + returnUrl);
        json.put("attachedData", attachedData);
        //分账金额：手续费
        json.put("platformAmount", commission);
        //分账标记：为true时goods和subOrders不能同时为空
        json.put("divisionFlag", true);
        
        json.put("chnlNo", chnlNo);
        //子订单信息
        JSONObject subOrder = new JSONObject();
        subOrder.put("mid", superviseMid);
        subOrder.put("merOrderId", orderSubId);
        subOrder.put("totalAmount", superviseAmt);
        JSONArray subOrders = new JSONArray();
        subOrders.add(subOrder);
        SysConfig sysConfigCommission =  sysConfigService.getOne(new QueryWrapper<SysConfig>().eq("config_key","supervise.commission.type"));
        //0-支付整额方式，1-入账整额方式
        if(sysConfigCommission != null && sysConfigCommission.getConfigValue().equals("1")) {
        	json.put("divisionFlag", false);
        }else {
        	json.put("subOrders", subOrders);
        }
        
        
        log.info(json.toJSONString());
        String signature = getSignature(unionBankConfiguration.getAppId(), unionBankConfiguration.getAppKey(), timestamp, nonce, json.toJSONString());

        String param = "authorization=OPEN-FORM-PARAM"
                + "&appId=" + unionBankConfiguration.getAppId()
                + "&timestamp=" + timestamp
                + "&nonce=" + nonce
                + "&content=" + URLEncoder.encode(json.toString(), "UTF-8")
                + "&signature=" + URLEncoder.encode(signature, "UTF-8");

        return unionBankConfiguration.getNetPayUrl() + "?" + param;
    }



    /**
     * 生成签名
     *
     * @param body
     * @return
     */
    public String sign(Map<String, Object> body,String signKey) {
        LinkedHashMap<String, Object> linkedHashMap = mapSortedByKey(body);
        // 签名
        StringBuilder params = new StringBuilder();
        for (String key : linkedHashMap.keySet()) {
            params.append(key).append("=").append(linkedHashMap.get(key)).append("&");
        }
        // 末尾的key
        params.append("key=").append(signKey);
        return MD5Util.md5(params.toString()).toUpperCase();
    }

    /**
     * map 排序
     *
     * @param map
     * @return
     */
    public static LinkedHashMap<String, Object> mapSortedByKey(Map<String, Object> map) {
        LinkedHashMap<String, Object> linkedHashMap = new LinkedHashMap<>();
        List<Map.Entry<String, Object>> list = new ArrayList<>(map.entrySet());
        // 使用Comparator对List进行排序
        list.sort(Map.Entry.comparingByKey());
        // 遍历排序后的List
        for (Map.Entry<String, Object> entry : list) {
            linkedHashMap.put(entry.getKey(), entry.getValue());
        }
        return linkedHashMap;
    }
    
   









    /**
     * 小程序订单查询
     * @return  JSONObject
     * {
     *     "errCode":"SUCCESS",
     *     "errMsg":"错误消息",
     *     "status":"TRADE_SUCCESS",
     *     "seqId":"平台流水号",
     *     "totalAmount":"支付总金额",
     *     "merOrderId":"商户订单号",
     *     "bankInfo":"银行信息",
     *     "bankCardNo":"银行卡号",
     *     "targetOrderId":"目标平台单号"
     * }
     * */
    public JSONObject queryMiniappPayOrder(DivsionOrder order){
        String instMid = "MINIDEFAULT";
        SpecialSchool specialSchool  = specialSchoolService.getById(order.getSchoolId());
        if(specialSchool != null){
            return queryPayOrder(instMid,order.getMid(),order.getTid(),order.getOrderNo(),
                    specialSchool.getAppId(),specialSchool.getAppKey());
        }else{
            return queryPayOrder(instMid,order.getMid(),order.getTid(),order.getOrderNo(),
                    unionBankConfiguration.getAppId(),unionBankConfiguration.getAppKey());
        }
    }

    public JSONObject queryPayOrder(String instMid,String mid,String tid,String orderNo,String appId,String appKey){
        String timestamp = DateUtils.parseDateToStr(DateUtils.YYYYMMDDHHMMSS, new Date());
        String nonce = UUID.randomUUID().toString().replace("-", "");
        JSONObject json = new JSONObject();
        json.put("instMid", instMid); // 业务类型
        json.put("mid", mid); // 商户号
        json.put("tid", tid);    // 终端号
        json.put("merOrderId", orderNo); // 商户订单号
        json.put("requestTimestamp", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date()));
        String signatrue = getSignature(appId, appKey, timestamp, nonce, json.toString());
        String authorization = "OPEN-BODY-SIG AppId=" + "\"" + appId + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + signatrue + "\"";

        String responseStr = bankRequest(unionBankConfiguration.getNetPayQueryUrl(), json.toJSONString(), authorization);
        log.info("queryMiniappOrder response:"+responseStr);
        return JSONObject.parseObject(responseStr);
    }

    public JSONObject queryScanCodeOrder(DivsionOrder order){
        String instMid = "QRPAYDEFAULT";
        SpecialSchool specialSchool  = specialSchoolService.getById(order.getSchoolId());
        if(specialSchool != null) {
            return queryScanCodeOrder(instMid,order.getMid(),order.getTid(),order.getOrderNo(),
                    order.getOrderTime(),specialSchool.getAppId(),specialSchool.getAppKey());
        }else{
            return queryScanCodeOrder(instMid,order.getMid(),order.getTid(),order.getOrderNo(),
                    order.getOrderTime(),unionBankConfiguration.getAppId(),unionBankConfiguration.getAppKey());
        }
    }

    public JSONObject queryScanCodeOrder(String instMid,String mid,String tid,String orderNo,Date orderTime,String appId,String appKey){
        String timestamp = DateUtils.parseDateToStr(DateUtils.YYYYMMDDHHMMSS, new Date());
        String nonce = UUID.randomUUID().toString().replace("-", "");
        JSONObject json = new JSONObject();
        json.put("instMid", instMid); // 业务类型
        json.put("mid", mid); // 商户号
        json.put("tid",tid);    // 终端号
        json.put("billNo", orderNo); // 商户订单号
        json.put("billDate", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, orderTime));
        json.put("requestTimestamp", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date()));
        String  signatrue = getSignature(appId, appKey, timestamp, nonce, json.toString());
        String  authorization = "OPEN-BODY-SIG AppId=" + "\"" + appId + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + signatrue + "\"";


        String responseStr = bankRequest(unionBankConfiguration.getQrCodeQueryUrl(), json.toJSONString(), authorization);
        log.info("银联扫码支付订单查询返回 ==>{}", responseStr);
        return JSONObject.parseObject(responseStr);
    }

    public JSONObject queryWithdrawOrder(String customerNo,String withdrawalNo,String signKey){
        JSONObject params = new JSONObject();
        params.put("clientNo", customerNo);
        params.put("withdrawalNo", withdrawalNo);
        params.put("sign", sign(params,signKey));
        String responseStr = bankRequest(unionBankConfiguration.getWithdrawFlowUrl(), params.toJSONString());
        log.info("银联提现订单查询返回 ==>{}", responseStr);
        return JSONObject.parseObject(responseStr);
    }

    @SneakyThrows
    public String bankRequest(String url, String body, String authorization) {
        // 发起请求
        HttpRequest req = new HttpRequest(url);
        req.header(Header.CONTENT_TYPE, ContentType.JSON.toString())
                .header(Header.ACCEPT_CHARSET, "UTF-8")
                .header("Authorization", authorization)
                .body(body, ContentType.JSON.toString())
                .httpVersion(HttpBase.HTTP_1_1)
                .setMethod(Method.POST);
        try {
            log.info("url ==>{}", url);
            log.info("body ==>{}", body);
            log.info("header ==>{}", JSONObject.toJSONString(req.headers()));
            String response = req.execute().body();
            response = response.trim().replace("\\", "");
            // 保存响应信息
//            fromJson.put("response", response);
            return response;
        } catch (Exception e) {
        	log.error("bank request error",e);
            throw e;
        }
    }

    @SneakyThrows
    public String bankRequest(String url, String body) {
        return bankRequest(url, body, null);
    }


    private String getBody(UnionBankWithdrawal unionBankWithdrawal) {

        // 这里顺序不能乱
        LinkedHashMap<String, Object> linkedHashMap = new LinkedHashMap<>();
        //银行标识
        linkedHashMap.put("bankType", unionBankWithdrawal.getBankType());
        // 客户号
        linkedHashMap.put("clientNo", unionBankWithdrawal.getClientNo());
        //付款账号
        linkedHashMap.put("payAcctNo", unionBankWithdrawal.getPayAcctNo());
        // 提现金额
        linkedHashMap.put("withdrawAmount", unionBankWithdrawal.getWithdrawAmount());
        //申请单号
        linkedHashMap.put("withdrawalNo", unionBankWithdrawal.getWithdrawalNo());

        linkedHashMap.put("remark",unionBankWithdrawal.getRemark());

        linkedHashMap.put("sign", sign(linkedHashMap,unionBankWithdrawal.getSign()));

        return JSONObject.toJSONString(linkedHashMap);
    }



    public boolean withdrawal(BigDecimal withdrawAmount,
                              String sysNum,
                              String clientNo,
                              String payAcctNo,
                              String signKey,
                              String remark,
                              JSONObject responseJson) throws Exception {
        if(RuoYiConfig.isDebug()){
            return debugWithdrawal(withdrawAmount,sysNum,clientNo,payAcctNo,signKey,remark,responseJson);
        }

        boolean success = false;
        try {
            if(responseJson == null){
                responseJson = new JSONObject();
            }
            String orderId = sysNum + DateUtil.formatDate(new Date(), "yyyyMMddHHmmssSSS") + RandomStringUtils.randomNumeric(7);
            responseJson.put("withdrawalNo", orderId);
            //这里只需要配置驾校的收款信息即可，因为银联那边已经根据商户号绑定了监管账户的信息
            UnionBankWithdrawal unionBankWithdrawal = new UnionBankWithdrawal()
                    .setWithdrawalNo(orderId).setWithdrawAmount(withdrawAmount.setScale(2, RoundingMode.HALF_UP))
                    .setBankType(unionBankConfiguration.getBankType()).setSign(signKey)
                    .setRemark(remark).setClientNo(clientNo).setPayAcctNo(payAcctNo);
            String applyWithdrawal = getBody(unionBankWithdrawal);

            log.info("applyWithdrawal params ==>{}", applyWithdrawal);
            // 请求
            String applyWithdrawalResponse = bankRequest(unionBankConfiguration.getApplyWithdrawalUrl(), applyWithdrawal);
            log.info("applyWithdrawal response ==>{}", applyWithdrawalResponse);
            JSONObject applyWithdrawJson = JSONObject.parseObject(applyWithdrawalResponse);
            responseJson.putAll(applyWithdrawJson);
            if (applyWithdrawJson.getString("status").equals("0") && applyWithdrawJson.getString("resultCode").equals("0")) {
                success = true;
            }
        }catch(Exception e) {
            responseJson.put("errMsg",e.getMessage());
            log.error("unionbank withdrawal error",e);
            throw e;
        }
        return success;
    }

    public boolean debugWithdrawal(BigDecimal withdrawAmount,
                              String sysNum,
                              String clientNo,
                              String payAcctNo,
                              String signKey,
                              String remark,
                              JSONObject responseJson) throws Exception {

        boolean success = false;
        try {
            if(responseJson == null){
                responseJson = new JSONObject();
            }
            String orderId = sysNum + DateUtil.formatDate(new Date(), "yyyyMMddHHmmssSSS") + RandomStringUtils.randomNumeric(7);
            responseJson.put("withdrawalNo", orderId);

            UnionBankWithdrawal unionBankWithdrawal = new UnionBankWithdrawal()
                    .setWithdrawalNo(orderId).setWithdrawAmount(withdrawAmount.setScale(2, BigDecimal.ROUND_HALF_UP))
                    .setBankType(unionBankConfiguration.getBankType()).setSign(signKey)
                    .setRemark(remark).setClientNo(clientNo).setPayAcctNo(payAcctNo);
            String applyWithdrawal = getBody(unionBankWithdrawal);

            log.info("applyWithdrawal params ==>{}", applyWithdrawal);

            JSONObject applyWithdrawJson = new JSONObject();
            applyWithdrawJson.put("status",0);
            applyWithdrawJson.put("resultCode",0);
            applyWithdrawJson.put("transactionNo",RandomUtil.randomString(10));
            applyWithdrawJson.put("withdrawalNo",orderId);
            applyWithdrawJson.put("errMsg","");

            log.info("applyWithdrawal response ==>{}", applyWithdrawJson.toJSONString());
            responseJson.putAll(applyWithdrawJson);

            if (applyWithdrawJson.getString("status").equals("0") && applyWithdrawJson.getString("resultCode").equals("0")) {
                success = true;
            }
        }catch(Exception e) {
            responseJson.put("errMsg",e.getMessage());
            log.error("unionbank withdrawal error",e);
            throw e;
        }
        return success;
    }

    /** 下载银行清算表单 */
    public List<DivsionOrderAssociationExcelVo>  downloadFile(String dateStr){
        List<DivsionOrderAssociationExcelVo> list = new ArrayList<DivsionOrderAssociationExcelVo>();
        try {
            // 通过FTP下载表格
            if (StringUtils.isEmpty(dateStr)) {
                LocalDate nowDate = LocalDate.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                dateStr = nowDate.format(formatter);
            }

            String remoteDirPath = "/build/";
            String fileName = "10050023940000_" + dateStr + "_9.xls";
            String remoteFilePath = remoteDirPath + fileName;
            String localFilePath = RuoYiConfig.getDownloadPath() + "/" + fileName;

            if (FTPUtil.remoteFileExists(remoteFilePath)) {
                FTPUtil.download(remoteFilePath, localFilePath);
                File file = new File(localFilePath);
                FileInputStream inputStream = new FileInputStream(file);
                ExcelUtil<DivsionOrderAssociationExcelVo> util = new ExcelUtil<>(DivsionOrderAssociationExcelVo.class);
                list = util.importExcel(inputStream, 2);
                log.info("已下载文件条目："+list.size());
            }else{
                log.info("不存在文件："+remoteFilePath);
            }
        }catch(Exception ex){
            log.error("downloadFile error:", ex);
        }
        return list;
    }
}
