package com.guangren.business.service;

import java.util.List;
import com.guangren.business.domain.SupervisePayDetail;

/**
 * 支付明细Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface ISupervisePayDetailService 
{
    /**
     * 查询支付明细
     * 
     * @param id 支付明细主键
     * @return 支付明细
     */
    public SupervisePayDetail selectSupervisePayDetailById(String id);

    /**
     * 查询支付明细列表
     * 
     * @param supervisePayDetail 支付明细
     * @return 支付明细集合
     */
    public List<SupervisePayDetail> selectSupervisePayDetailList(SupervisePayDetail supervisePayDetail);

    /**
     * 新增支付明细
     * 
     * @param supervisePayDetail 支付明细
     * @return 结果
     */
    public int insertSupervisePayDetail(SupervisePayDetail supervisePayDetail);

    /**
     * 修改支付明细
     * 
     * @param supervisePayDetail 支付明细
     * @return 结果
     */
    public int updateSupervisePayDetail(SupervisePayDetail supervisePayDetail);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SupervisePayDetail supervisePayDetail);

    /**
     * 批量删除支付明细
     * 
     * @param ids 需要删除的支付明细主键集合
     * @return 结果
     */
    public int deleteSupervisePayDetailByIds(String ids);

    /**
     * 删除支付明细信息
     * 
     * @param id 支付明细主键
     * @return 结果
     */
    public int deleteSupervisePayDetailById(String id);
}
