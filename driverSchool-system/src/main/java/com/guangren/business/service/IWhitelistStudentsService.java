package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.WhitelistStudents;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 白名单学员Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-04
 */
public interface IWhitelistStudentsService extends IService<WhitelistStudents>
{
    /**
     * 查询白名单学员
     * 
     * @param id 白名单学员主键
     * @return 白名单学员
     */
    public WhitelistStudents selectWhitelistStudentsById(String id);

    /**
     * 查询白名单学员列表
     * 
     * @param whitelistStudents 白名单学员
     * @return 白名单学员集合
     */
    public List<WhitelistStudents> selectWhitelistStudentsList(WhitelistStudents whitelistStudents);

    /**
     * 新增白名单学员
     * 
     * @param whitelistStudents 白名单学员
     * @return 结果
     */
    public int insertWhitelistStudents(WhitelistStudents whitelistStudents);

    /**
     * 修改白名单学员
     * 
     * @param whitelistStudents 白名单学员
     * @return 结果
     */
    public int updateWhitelistStudents(WhitelistStudents whitelistStudents);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(WhitelistStudents whitelistStudents);

    /**
     * 批量删除白名单学员
     * 
     * @param ids 需要删除的白名单学员主键集合
     * @return 结果
     */
    public int deleteWhitelistStudentsByIds(String ids);

    /**
     * 删除白名单学员信息
     * 
     * @param id 白名单学员主键
     * @return 结果
     */
    public int deleteWhitelistStudentsById(String id);

    /**
     * 导入白名单学员数据
     * <AUTHOR>
     * @date 2024/1/4 15:27
     * @param file *
     * @return int *
     */
    int importData(MultipartFile file);

    /**
     * 转学保存
     * <AUTHOR>
     * @date 2024/1/5 11:31
     * @param whitelistStudents  *
     */
    void transferSave(WhitelistStudents whitelistStudents);

    /**
     * 审核转学学员保存
     * <AUTHOR>
     * @date 2024/1/5 15:48
     * @param whitelistStudents  *
     */
    void reviewerSave(WhitelistStudents whitelistStudents);

    /**
     * 白名单学员退学保存
     * <AUTHOR>
     * @date 2024/1/11 16:13
     * @param whitelistStudents  *
     */
    void dropOutSave(WhitelistStudents whitelistStudents);
}
