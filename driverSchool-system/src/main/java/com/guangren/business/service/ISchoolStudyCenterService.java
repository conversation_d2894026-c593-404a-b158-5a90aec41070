package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolStudyCenter;

import java.util.List;

public interface ISchoolStudyCenterService extends IService<SchoolStudyCenter>{
    /**
     * 查询学时签约中心
     *
     * @param schoolId 学时签约中心主键
     * @return 学时签约中心
     */
    public SchoolStudyCenter selectSchoolStudyCenterBySchoolId(String schoolId);

    /**
     * 查询学时签约中心列表
     *
     * @param schoolStudyCenter 学时签约中心
     * @return 学时签约中心集合
     */
    public List<SchoolStudyCenter> selectSchoolStudyCenterList(SchoolStudyCenter schoolStudyCenter);

    /**
     * 新增学时签约中心
     *
     * @param schoolStudyCenter 学时签约中心
     * @return 结果
     */
    public int insertSchoolStudyCenter(SchoolStudyCenter schoolStudyCenter);

    /**
     * 修改学时签约中心
     *
     * @param schoolStudyCenter 学时签约中心
     * @return 结果
     */
    public int updateSchoolStudyCenter(SchoolStudyCenter schoolStudyCenter);



    /**
     * 批量删除学时签约中心
     *
     * @param schoolIds 需要删除的学时签约中心主键集合
     * @return 结果
     */
    public int deleteSchoolStudyCenterBySchoolIds(String schoolIds);

    /**
     * 删除学时签约中心信息
     *
     * @param schoolId 学时签约中心主键
     * @return 结果
     */
    public int deleteSchoolStudyCenterBySchoolId(String schoolId);

    /**
     * 设置与学时平台签约明细
     */
    public int saveStudyCenter(SchoolStudyCenter schoolStudyCenter);

    /**
     * 设置默认学时平台
     */
    int setDefaultStudyCenter(SchoolStudyCenter studyCenter);
}
