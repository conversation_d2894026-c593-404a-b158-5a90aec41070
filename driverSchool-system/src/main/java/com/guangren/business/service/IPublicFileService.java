package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.PublicFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

/**
 * 公共文件Service接口
 * 
 * <AUTHOR>
 * @date 2023-02-25
 */
public interface IPublicFileService extends IService<PublicFile>
{
    /**
     * 查询公共文件
     * 
     * @param id 公共文件主键
     * @return 公共文件
     */
    public PublicFile selectPublicFileById(Long id);

    public PublicFile saveFileToDb(File file,String refrenceId,long refrenceType);
    public PublicFile selectOneByRefrenceAndType(String refrence, Long refrenceType);
    /**
     * 查询公共文件列表
     * 
     * @param publicFile 公共文件
     * @return 公共文件集合
     */
    public List<PublicFile> selectPublicFileList(PublicFile publicFile);

    /**
     * 新增公共文件
     * 
     * @param publicFile 公共文件
     * @return 结果
     */
    public int insertPublicFile(PublicFile publicFile);

    /**
     * 修改公共文件
     * 
     * @param publicFile 公共文件
     * @return 结果
     */
    public int updatePublicFile(PublicFile publicFile);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(PublicFile publicFile);

    /**
     * 批量删除公共文件
     * 
     * @param ids 需要删除的公共文件主键集合
     * @return 结果
     */
    public int deletePublicFileByIds(String ids);

    /**
     * 删除公共文件信息
     * 
     * @param id 公共文件主键
     * @return 结果
     */
    public int deletePublicFileById(Long id);



    /**
     * 保存单个文件
     *
     * @param file：文件
     * @param refrenceId：业务表ID
     * @param refrenceType：业务文件类型
     * @Author: peng.xy
     * @Date: 2021/1/15/0015 上午 10:18
     */
    public PublicFile saveFile(MultipartFile file,String refrenceId,long refrenceType);

    /**
     * 保存单个文件
     *
     * @param file：文件
     * @param refrenceId：业务表ID
     * @param refrenceType：业务文件类型
     * @param baseDir：文件路径
     * @Author: peng.xy
     * @Date: 2021/1/15/0015 上午 10:18
     */
    public PublicFile saveFile(MultipartFile file, String refrenceId, long refrenceType, String baseDir);

    /**
     * 保存多个文件
     *
     * @param files：文件
     * @param refrenceId：业务表ID
     * @param refrenceType：业务文件类型
     * @Author: peng.xy
     * @Date: 2021/1/15/0015 上午 10:18
     */
    public List<PublicFile> saveFiles(MultipartFile[] files, String refrenceId, long refrenceType);

    /**
     * 清理文件外键
     *
     * @param refrence
     * @param fileIds
     * @param refrenceType
     * @Return: int
     * @Author: peng.xy
     * @Date: 2021/1/15/0015 下午 14:45
     */
    public int clearRefrence(String refrence, String fileIds, Long refrenceType);

    int batchClearRefrence(String refrence, String fileIds, Long refrenceType);

    /**  * 批量插入
     *
     * @param list
     * @Return: int
     * @Author: peng.xy
     * @Date: 2021/1/15/0015 下午 13:38
     */
    int batchInsert(List<PublicFile> list);

}
