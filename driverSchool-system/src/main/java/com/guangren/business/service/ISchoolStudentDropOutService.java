package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.domain.SchoolStudentDropOut;
import com.guangren.business.vo.CheckQuitStudentVo;

import java.util.List;

/**
 * 学员退学Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-09
 */
public interface ISchoolStudentDropOutService extends IService<SchoolStudentDropOut>
{
    /**
     * 查询学员退学
     * 
     * @param id 学员退学主键
     * @return 学员退学
     */
    public SchoolStudentDropOut selectSchoolStudentDropOutById(String id);

    /**
     * 查询学员退学列表
     * 
     * @param schoolStudentDropOut 学员退学
     * @return 学员退学集合
     */
    public List<SchoolStudentDropOut> selectSchoolStudentDropOutList(SchoolStudentDropOut schoolStudentDropOut);

    /**
     * 新增学员退学
     * 
     * @param schoolStudentDropOut 学员退学
     * @return 结果
     */
    public int insertSchoolStudentDropOut(SchoolStudentDropOut schoolStudentDropOut);

    /**
     * 修改学员退学
     * 
     * @param schoolStudentDropOut 学员退学
     * @return 结果
     */
    public int updateSchoolStudentDropOut(SchoolStudentDropOut schoolStudentDropOut);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SchoolStudentDropOut schoolStudentDropOut);

    /**
     * 批量删除学员退学
     * 
     * @param ids 需要删除的学员退学主键集合
     * @return 结果
     */
    public int deleteSchoolStudentDropOutByIds(String ids);

    /**
     * 删除学员退学信息
     * 
     * @param id 学员退学主键
     * @return 结果
     */
    public int deleteSchoolStudentDropOutById(String id);

    /**
     * 审核退学学员保存
     */
    boolean checkQuitStudentSave(CheckQuitStudentVo checkQuitStudentVo);

    /**
     * 重新发起学员退学保存
     */
    void reissueStudentQuitSave(String id, SchoolStudent schoolStudent);

    /**
     * 检查退学学员状态
     */
    void checkStudentStatus(String id);

    /**
     * 查询退学学员（7天退学学员）
     * <AUTHOR>
     * @date 2023/10/31 17:24
     * @param schoolStudentDropOut *
     * @return java.util.List<com.guangren.business.domain.SchoolStudentDropOut> *
     */
    List<SchoolStudentDropOut> selectSevenDayDropoutSchoolStudentDropOutList(SchoolStudentDropOut schoolStudentDropOut);

    /**
     * 撤销学员退学保存
     * <AUTHOR>
     * @date 2023/11/10 15:53
     * @param schoolStudentDropOut  *
     */
    int revokeDropoutSave(SchoolStudentDropOut schoolStudentDropOut);

    /**
     * 小程序发起退学
     * <AUTHOR>
     * @date 2023/11/16 17:11
     * @param student *
     * @param dropOutReason  *
     */
    void sendDropOut(SchoolStudent student, String dropOutReason);

    /**
     * 审核保存退学学员
     * <AUTHOR>
     * @date 2024/1/12 10:53
     * @param checkQuitStudentVo  *
     */
    void reviewerSave(CheckQuitStudentVo checkQuitStudentVo);

    /**
     * 退学学员初审
     * @param checkQuitStudentVo
     */
    void firstTrial(CheckQuitStudentVo checkQuitStudentVo);

    /**
     * 七天退学学员初审
     * @param checkQuitStudentVo
     */
    void firstTrialBySeven(CheckQuitStudentVo checkQuitStudentVo);
}
