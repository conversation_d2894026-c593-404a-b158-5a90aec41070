package com.guangren.business.service;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.drcbank.open.sdk.DefaultDrcbClient;
import com.drcbank.open.sdk.DrcbClient;
import com.drcbank.open.sdk.DrcbConfig;
import com.drcbank.open.sdk.DrcbException;
import com.guangren.business.domain.BankAccount;
import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.enumration.ExamSubject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

public interface IBankService {
    /**
     * 批量提交学员到银行，产生付款的虚假账号
     */
    public JSONObject batchSubmitStudentPay(String orderId, List<SchoolStudent> studentList, BankAccount account) throws Exception;

    /**
     * 释放监管资金
     */
    public boolean releaseSuperviseMoney(SchoolStudent student, BankAccount account, ExamSubject subject) throws Exception;

    /**
     * 退学时释放监管资金
     */
    public boolean quitReleaseSuperviseMoney(SchoolStudent student, BankAccount account) throws Exception;

    /**
     * 查询学员余额
     */
    public JSONObject queryStudentBalance(SchoolStudent student, BankAccount account) throws Exception;

    /**
     * 学员退学
     */
    public boolean studentQuit(SchoolStudent student, BankAccount account) throws Exception;

    /**
     * 账户销毁
     */
    public boolean destroyAccount(SchoolStudent student, BankAccount account) throws Exception;
}
