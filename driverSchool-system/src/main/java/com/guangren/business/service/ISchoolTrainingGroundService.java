package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.SchoolTrainingGround;
import com.guangren.business.vo.importTempete.SchoolTrainingGroundTemplete;
import com.guangren.business.vo.importTempete.TrainingGroundOfficialTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 训练场地Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface ISchoolTrainingGroundService extends IService<SchoolTrainingGround>
{
    /**
     * 查询训练场地
     * 
     * @param id 训练场地主键
     * @return 训练场地
     */
    public SchoolTrainingGround selectSchoolTrainingGroundById(String id);

    /**
     * 查询训练场地列表
     * 
     * @param schoolTrainingGround 训练场地
     * @return 训练场地集合
     */
    public List<SchoolTrainingGround> selectSchoolTrainingGroundList(SchoolTrainingGround schoolTrainingGround);

    /**
     * 新增训练场地
     * 
     * @param schoolTrainingGround 训练场地
     * @return 结果
     */
    public int insertSchoolTrainingGround(SchoolTrainingGround schoolTrainingGround);

    /**
     * 修改训练场地
     * 
     * @param schoolTrainingGround 训练场地
     * @return 结果
     */
    public int updateSchoolTrainingGround(SchoolTrainingGround schoolTrainingGround);


    /**
     * 验证参数唯一性
     */
    public String checkUnique(SchoolTrainingGround schoolTrainingGround);

    /**
     * 批量删除训练场地
     * 
     * @param ids 需要删除的训练场地主键集合
     * @return 结果
     */
    public int deleteSchoolTrainingGroundByIds(String ids);

    /**
     * 删除训练场地信息
     * 
     * @param id 训练场地主键
     * @return 结果
     */
    public int deleteSchoolTrainingGroundById(String id);

    /**
     * 导入训练场信息
     * @param file
     * @return
     */
    public SchoolTrainingGroundTemplete importData(MultipartFile file)throws Exception;

    /**
     * 导出训练场地列表
     */
    List<SchoolTrainingGround> exportSchoolTrainingGroundList(SchoolTrainingGround schoolTrainingGround);

    /**
     * 批量提交二维码审核申请
     * @param ids
     * @return
     */
    int submitAudit(String ids);

    /**
     * 批量审核二维码申请
     * @param ids 训练场的主表ID
     * @param auditStatus 设置的审核状态
     * @return
     */
    int batchAudit(String ids,Integer auditStatus);

    /**
     * 下载二维码图片
     * @param response
     * @param ids
     */
    void downloadQrcode(HttpServletResponse response, String ids);

    /**
     * 查看是否存在过期或未审核的二维码信息
     * @param id 业务ID
     * @return
     */
    SchoolTrainingGround existnotPassQrcode(String id);

    /**
     * 训练场信息官方数据导入
     * @param file
     * @return
     */
    TrainingGroundOfficialTemplate importOfficialData(MultipartFile file)throws Exception;
}
