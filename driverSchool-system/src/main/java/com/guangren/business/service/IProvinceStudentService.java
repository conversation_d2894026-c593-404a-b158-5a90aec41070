package com.guangren.business.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.ProvinceStudent;
import com.guangren.business.domain.ProvinceStudyTime;
import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.domain.SupervisePay;

import java.math.BigDecimal;

public interface IProvinceStudentService extends IService<ProvinceStudent>{
    /**
     * 省平台登录
     * <p>
     * ***********
     *
     * @return
     */
    public String login() throws Exception;

    /**
     * 学时共享公安信息
     * <p>
     * 004300700010
     */
    public void getStudyTime(String stunum,String date) throws Exception;

    /**
     * 提现
     */
    public void withdraw(String name,String idcard) throws Exception;

    void manualWithdraw();

    /**
     * 手动释放资金的提现流程
     * <AUTHOR>
     * @date 2023/10/10 16:40
     * @param supervisePay  *
     */
    public void withdraw(SupervisePay supervisePay) throws Exception;

    /**
     * 学员提现方法
     * @param provinceStudyTime *
     * @param student *
     * @param type 释放类型：1=自动释放，2=手动释放
     */
    public String studentWithdraw(ProvinceStudyTime provinceStudyTime,
                                SchoolStudent student,
                                Integer type) throws Exception;


    String manualStudentWithdraw(ProvinceStudyTime provinceStudyTime,
                                 SchoolStudent student,
                                 Integer type) throws Exception;

    BigDecimal validateRelease(SchoolStudent student, ProvinceStudyTime provinceStudyTime);

    JSONObject getReleaseParams(String studentId);

    JSONObject getReleaseLixiParams(String schoolId);
}
