package com.guangren.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guangren.business.domain.DivsionOrderPlatform;

import java.util.List;

/**
 * 分账平台订单Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface IDivsionOrderPlatformService extends IService<DivsionOrderPlatform>
{
    /**
     * 查询分账平台订单
     * 
     * @param id 分账平台订单主键
     * @return 分账平台订单
     */
    public DivsionOrderPlatform selectDivsionOrderPlatformById(Long id);

    /**
     * 查询分账平台订单列表
     * 
     * @param divsionOrderPlatform 分账平台订单
     * @return 分账平台订单集合
     */
    public List<DivsionOrderPlatform> selectDivsionOrderPlatformList(DivsionOrderPlatform divsionOrderPlatform);

    /**
     * 新增分账平台订单
     * 
     * @param divsionOrderPlatform 分账平台订单
     * @return 结果
     */
    public int insertDivsionOrderPlatform(DivsionOrderPlatform divsionOrderPlatform);

    /**
     * 修改分账平台订单
     * 
     * @param divsionOrderPlatform 分账平台订单
     * @return 结果
     */
    public int updateDivsionOrderPlatform(DivsionOrderPlatform divsionOrderPlatform);

    /**
     * 批量删除分账平台订单
     * 
     * @param ids 需要删除的分账平台订单主键集合
     * @return 结果
     */
    public int deleteDivsionOrderPlatformByIds(String ids);

    /**
     * 删除分账平台订单信息
     * 
     * @param id 分账平台订单主键
     * @return 结果
     */
    public int deleteDivsionOrderPlatformById(Long id);
}
