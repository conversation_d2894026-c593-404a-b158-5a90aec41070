package com.guangren.business.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@TableName("t_test_bank_virtual_account")
@Data
public class TestBankVirtualAccount {
	@TableId(type=IdType.INPUT)
	private String virtualAccountNo;
	
	private String transSerial;
	private String customerNo;
	private String jxOrderId;
	private Float superviseAmt;
	private Integer amtIsOk;
	private Integer notifyIsOk;
	private Date createdTime;
	private Date updatedTime;
	
}
