package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guangren.common.annotation.Excel;
import com.guangren.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 分账总订单对象 t_divsion_order_association
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
@Data
@TableName("t_divsion_order_association")
@Accessors(chain = true)
public class DivsionOrderAssociation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    @TableId(type= IdType.AUTO)
    private Long id;

    /**  */
    private Long orderId;

    /** 学校ID */
    private String schoolId;

    /** 学校名称 */
    @Excel(name = "驾校")
    private String schoolName;

    /** 分校ID */
    private String branchId;

    /** 分校名字 */
    @Excel(name = "分校")
    private String branchName;

    /** 门店ID */
    private String registrationId;

    /** 门店名称 */
    @Excel(name = "报名点")
    private String registrationName;

    /** 学生ID */
    private String studentId;

    /** 学生报名时姓名 */
    @Excel(name = "学员姓名")
    private String studentName;

    /** 学生报名时身份证号 */
    @Excel(name = "身份证号")
    private String studentIdcard;

    /** 学生报名时电话 */
    @Excel(name = "手机号")
    private String studentMobile;

    /** 银行单号 */
    @Excel(name = "银行单号")
    private String bankOrderNo;

    /** 订单号 */
    @Excel(name = "系统单号")
    private String orderNo;

    /** 子订单号 */
    private String subOrderNo;

    /** 平台费 */
    @Excel(name = "交易金额")
    private BigDecimal associationFee;

    /** 0-未清算，1-已清算，2=已到账 */
    @Excel(name = "交易状态", readConverterExp = "0=未清算,1=已清算,2=已到账")
    private Integer status;

    /** 订单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "交易时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /** 清算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkTime;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdTime;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updatedTime;

    @TableField(exist = false)
    private List<String> ids;

    @TableField(exist = false)
    private String beginOrderTime;

    @TableField(exist = false)
    private String endOrderTime;
}
