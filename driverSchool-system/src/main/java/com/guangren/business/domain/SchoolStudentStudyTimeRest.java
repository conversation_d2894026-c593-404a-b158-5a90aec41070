package com.guangren.business.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.guangren.business.vo.StudyTimeStaExportVo;
import com.guangren.common.annotation.Excel;
import com.guangren.common.annotation.Excels;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName(value="t_school_student_study_time_rest")
public class SchoolStudentStudyTimeRest {
	
	@TableId(type=IdType.ASSIGN_UUID)
	private String id;
	
	private String studentName;
	
	private String studentIdentity;
	
	//@NotEmpty(message="教练不能为空")
	@Length(max=20,message="教练长度不能超过20个字符")
	private String teacherName;
	
	//@NotEmpty(message="车牌不能为空")
	@Length(max=10,message="车牌长度不能超过20个字符")
	private String carNo;
	
	@NotEmpty(message="业务类型不能为空")
	@Excel(name = "业务类型",sort = 6,needMerge = true)
	private String businessType;
	
	@NotEmpty(message="驾照类型不能为空")
	@Excel(name = "驾驶证",sort = 7,needMerge = true)
	private String licenseType;

	@NotEmpty(message="培训课目不能为空")
	private String subjectName;
	
	@NotEmpty(message="课时类型不能为空")
	private String classType;
	
	@NotEmpty(message="课时子类不能为空")
	private String classSubType;
	
	@NotNull(message="开始时间不能为空或者格式不正确")
	private Date beginTime;
	
	@NotNull(message="结束时间不能为空或者格式不正确")
	private Date endTime;
	
	@NotNull(message="学习时长不能为空或者格式不正确")
	@Range(min=0,message="学习时长必须大于0")
	private Integer studyTime;
	
	@NotNull(message="审核状态不能为空")
	private Integer checkStatus;
	
	@NotNull(message="审核之后的有效时长不能为空")
	@Range(min=0,message="审核之后的有效时长必须大于0")
	private Integer checkValidStudyTime;
	
	private Integer checkInvalidStudyTime;
	private Date createdTime;
	private Date updatedTime;
	private String schoolId;
	private String branchId;
	private String registrationId;

	@NotNull(message="审核时间不能为空或者格式不正确")
	private Date checkTime;

	private BigDecimal legend;
	private String reason;


	@Excels({
			@Excel(name = "驾校", targetAttr = "name", type = Excel.Type.EXPORT,sort = 1,needMerge = true),
	})
	@TableField(exist=false)
	private School school;
	@Excels({
			@Excel(name = "分校", targetAttr = "name", type = Excel.Type.EXPORT,sort = 2,needMerge = true),
	})
	@TableField(exist=false)
	private SchoolBranch branch;
	@Excels({
			@Excel(name = "报名点", targetAttr = "name", type = Excel.Type.EXPORT,sort = 3,needMerge = true),
	})
	@TableField(exist=false)
	private SchoolRegistration registration;
	@TableField(exist=false)
	@Excels({
			@Excel(name = "学员", targetAttr = "name", type = Excel.Type.EXPORT,sort = 4,needMerge = true),
			@Excel(name = "身份证号", targetAttr = "identity", type = Excel.Type.EXPORT,sort = 5,needMerge = true),
	})
	private SchoolStudent schoolStudent;
	@TableField(exist=false)
	private SchoolStudentStudyTimeSta studyTimeSta;

	/** 请求参数 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	@TableField(exist=false)
	private Map<String, Object> params;

	@TableField(exist=false)
	@Excel(name = "课时")
	private List<StudyTimeStaExportVo> timeStaExportVoList;
	@TableField(exist=false)
	private String checkValidStudyTimeStr;

}
