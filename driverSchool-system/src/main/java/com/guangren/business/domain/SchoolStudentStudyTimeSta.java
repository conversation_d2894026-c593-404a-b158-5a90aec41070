package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guangren.business.vo.StudyTimeStaExportVo;
import com.guangren.common.annotation.Excel;
import com.guangren.common.annotation.Excels;
import com.guangren.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 学时统计，定时任务统计，有达标时通知银行释放资金对象 t_school_student_study_time_sta
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
@TableName("t_school_student_study_time_sta")
public class SchoolStudentStudyTimeSta extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    @TableId(type=IdType.INPUT)
    private String studentId;

    /** 课目1 */
    private String subject1;

    /** 课目1总的审核通过的有效的学习时长 */
    private Long subject1CheckValidStudyTime;

    /** 课目1考核的学习时长 */
    private Long subject1ExamineStudyTime;

    /** 课目1是否通过 0-未达标 ，1-达标 */
    private Integer subject1IsPass;

    /** 课目2 */
    private String subject2;

    /** 课目2总的审核通过的有效的学习时长 */
    private Long subject2CheckValidStudyTime;

    /** 课目2考核的学习时长 */
    private Long subject2ExamineStudyTime;

    /** 课目3是否通过0-未达标 ，1-达标 */
    private Integer subject2IsPass;

    /** 课目2 */
    private String subject3;

    /** 课目2总的审核通过的有效的学习时长 */
    private Long subject3CheckValidStudyTime;

    /** 课目2考核的学习时长 */
    private Long subject3ExamineStudyTime;

    /** 课目3是否通过0-未达标 ，1-达标 */
    private Integer subject3IsPass;

    /** 课目2 */
    private String subject4;

    /** 课目4总的审核通过的有效的学习时长 */
    private Long subject4CheckValidStudyTime;

    /** 课目4考核的学习时长 */
    private Long subject4ExamineStudyTime;

    /** 课目4是否通过0-未达标 ，1-达标 */
    private Integer subject4IsPass;


    private Integer subject1IsReleaseSupervise;
    private Integer subject2IsReleaseSupervise;
    private Integer subject3IsReleaseSupervise;
    private Integer subject4IsReleaseSupervise;
    
    


    @Excels({
            @Excel(name = "驾校", targetAttr = "name", type = Excel.Type.EXPORT,sort = 1,needMerge = true),
    })
    @TableField(exist=false)
    private School school;
    @Excels({
            @Excel(name = "分校", targetAttr = "name", type = Excel.Type.EXPORT,sort = 2,needMerge = true),
    })
    @TableField(exist=false)
    private SchoolBranch branch;
    @Excels({
            @Excel(name = "报名点", targetAttr = "name", type = Excel.Type.EXPORT,sort = 3,needMerge = true),
    })
    @TableField(exist=false)
    private SchoolRegistration registration;

    @TableField(exist=false)
    @Excels({
            @Excel(name = "学员", targetAttr = "name", type = Excel.Type.EXPORT,sort = 4,needMerge = true),
            @Excel(name = "身份证号", targetAttr = "identity", type = Excel.Type.EXPORT,sort = 5,needMerge = true),
    })
    private SchoolStudent schoolStudent;

    @Excel(name = "业务类型",sort = 6,needMerge = true)
    @TableField(exist=false)
    private String businessType;

    @Excel(name = "驾驶证",sort = 7,needMerge = true)
    @TableField(exist=false)
    private String licenseType;

    @TableField(exist=false)
    @Excel(name = "课时")
    private List<StudyTimeStaExportVo> timeStaExportVoList;

    @TableField(exist=false)
    private String schoolId;
    @TableField(exist=false)
    private String branchId;
    @TableField(exist=false)
    private String registrationId;

    /**
     * 学员姓名
     */
    @TableField(exist=false)
    private String name;

    /**
     * 身份证号
     */
    @TableField(exist=false)
    private String identity;

    /**
     * 身份证列表
     */
    @TableField(exist=false)
    private List<String> identityList;

    @TableField(exist = false)
    private List<String> ids;

    public List<String> getIdentityList() {
        return identityList;
    }

    public void setIdentityList(List<String> identityList) {
        this.identityList = identityList;
    }

    public void setStudentId(String studentId)
    {
        this.studentId = studentId;
    }

    public String getStudentId() 
    {
        return studentId;
    }
    public void setSubject1(String subject1) 
    {
        this.subject1 = subject1;
    }

    public String getSubject1() 
    {
        return subject1;
    }
    public void setSubject1CheckValidStudyTime(Long subject1CheckValidStudyTime) 
    {
        this.subject1CheckValidStudyTime = subject1CheckValidStudyTime;
    }

    public Long getSubject1CheckValidStudyTime() 
    {
        return subject1CheckValidStudyTime;
    }
    public void setSubject1ExamineStudyTime(Long subject1ExamineStudyTime) 
    {
        this.subject1ExamineStudyTime = subject1ExamineStudyTime;
    }

    public Long getSubject1ExamineStudyTime() 
    {
        return subject1ExamineStudyTime;
    }
    public void setSubject1IsPass(Integer subject1IsPass) 
    {
        this.subject1IsPass = subject1IsPass;
    }

    public Integer getSubject1IsPass() 
    {
        return subject1IsPass;
    }
    public void setSubject2(String subject2) 
    {
        this.subject2 = subject2;
    }

    public String getSubject2() 
    {
        return subject2;
    }
    public void setSubject2CheckValidStudyTime(Long subject2CheckValidStudyTime) 
    {
        this.subject2CheckValidStudyTime = subject2CheckValidStudyTime;
    }

    public Long getSubject2CheckValidStudyTime() 
    {
        return subject2CheckValidStudyTime;
    }
    public void setSubject2ExamineStudyTime(Long subject2ExamineStudyTime) 
    {
        this.subject2ExamineStudyTime = subject2ExamineStudyTime;
    }

    public Long getSubject2ExamineStudyTime() 
    {
        return subject2ExamineStudyTime;
    }
    public void setSubject2IsPass(Integer subject2IsPass) 
    {
        this.subject2IsPass = subject2IsPass;
    }

    public Integer getSubject2IsPass() 
    {
        return subject2IsPass;
    }
    public void setSubject3(String subject3) 
    {
        this.subject3 = subject3;
    }

    public String getSubject3() 
    {
        return subject3;
    }
    public void setSubject3CheckValidStudyTime(Long subject3CheckValidStudyTime) 
    {
        this.subject3CheckValidStudyTime = subject3CheckValidStudyTime;
    }

    public Long getSubject3CheckValidStudyTime() 
    {
        return subject3CheckValidStudyTime;
    }
    public void setSubject3ExamineStudyTime(Long subject3ExamineStudyTime) 
    {
        this.subject3ExamineStudyTime = subject3ExamineStudyTime;
    }

    public Long getSubject3ExamineStudyTime() 
    {
        return subject3ExamineStudyTime;
    }
    public void setSubject3IsPass(Integer subject3IsPass) 
    {
        this.subject3IsPass = subject3IsPass;
    }

    public Integer getSubject3IsPass() 
    {
        return subject3IsPass;
    }
    public void setSubject4(String subject4) 
    {
        this.subject4 = subject4;
    }

    public String getSubject4() 
    {
        return subject4;
    }
    public void setSubject4CheckValidStudyTime(Long subject4CheckValidStudyTime) 
    {
        this.subject4CheckValidStudyTime = subject4CheckValidStudyTime;
    }

    public Long getSubject4CheckValidStudyTime() 
    {
        return subject4CheckValidStudyTime;
    }
    public void setSubject4ExamineStudyTime(Long subject4ExamineStudyTime) 
    {
        this.subject4ExamineStudyTime = subject4ExamineStudyTime;
    }

    public Long getSubject4ExamineStudyTime() 
    {
        return subject4ExamineStudyTime;
    }
    public void setSubject4IsPass(Integer subject4IsPass) 
    {
        this.subject4IsPass = subject4IsPass;
    }

    public Integer getSubject4IsPass() 
    {
        return subject4IsPass;
    }

    public Integer getSubject1IsReleaseSupervise() {
		return subject1IsReleaseSupervise;
	}

	public void setSubject1IsReleaseSupervise(Integer subject1IsReleaseSupervise) {
		this.subject1IsReleaseSupervise = subject1IsReleaseSupervise;
	}

	public Integer getSubject2IsReleaseSupervise() {
		return subject2IsReleaseSupervise;
	}

	public void setSubject2IsReleaseSupervise(Integer subject2IsReleaseSupervise) {
		this.subject2IsReleaseSupervise = subject2IsReleaseSupervise;
	}

	public Integer getSubject3IsReleaseSupervise() {
		return subject3IsReleaseSupervise;
	}

	public void setSubject3IsReleaseSupervise(Integer subject3IsReleaseSupervise) {
		this.subject3IsReleaseSupervise = subject3IsReleaseSupervise;
	}

	public Integer getSubject4IsReleaseSupervise() {
		return subject4IsReleaseSupervise;
	}

	public void setSubject4IsReleaseSupervise(Integer subject4IsReleaseSupervise) {
		this.subject4IsReleaseSupervise = subject4IsReleaseSupervise;
	}

    public SchoolStudent getSchoolStudent() {
        return schoolStudent;
    }

    public void setSchoolStudent(SchoolStudent schoolStudent) {
        this.schoolStudent = schoolStudent;
    }

    public School getSchool() {
        return school;
    }

    public void setSchool(School school) {
        this.school = school;
    }

    public SchoolBranch getBranch() {
        return branch;
    }

    public void setBranch(SchoolBranch branch) {
        this.branch = branch;
    }

    public SchoolRegistration getRegistration() {
        return registration;
    }

    public void setRegistration(SchoolRegistration registration) {
        this.registration = registration;
    }

    public List<StudyTimeStaExportVo> getTimeStaExportVoList() {
        return timeStaExportVoList;
    }

    public void setTimeStaExportVoList(List<StudyTimeStaExportVo> timeStaExportVoList) {
        this.timeStaExportVoList = timeStaExportVoList;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }

    public String getSchoolId() {
        return schoolId;
    }

    public void setSchoolId(String schoolId) {
        this.schoolId = schoolId;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getRegistrationId() {
        return registrationId;
    }

    public void setRegistrationId(String registrationId) {
        this.registrationId = registrationId;
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("studentId", getStudentId())
            .append("subject1", getSubject1())
            .append("subject1CheckValidStudyTime", getSubject1CheckValidStudyTime())
            .append("subject1ExamineStudyTime", getSubject1ExamineStudyTime())
            .append("subject1IsPass", getSubject1IsPass())
            .append("subject2", getSubject2())
            .append("subject2CheckValidStudyTime", getSubject2CheckValidStudyTime())
            .append("subject2ExamineStudyTime", getSubject2ExamineStudyTime())
            .append("subject2IsPass", getSubject2IsPass())
            .append("subject3", getSubject3())
            .append("subject3CheckValidStudyTime", getSubject3CheckValidStudyTime())
            .append("subject3ExamineStudyTime", getSubject3ExamineStudyTime())
            .append("subject3IsPass", getSubject3IsPass())
            .append("subject4", getSubject4())
            .append("subject4CheckValidStudyTime", getSubject4CheckValidStudyTime())
            .append("subject4ExamineStudyTime", getSubject4ExamineStudyTime())
            .append("subject4IsPass", getSubject4IsPass())
            .toString();
    }
}
