package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@TableName("t_test_bank_student_virtual_account")
@Data
public class TestBankStudentVirtualAccount {
	@TableId(type=IdType.ASSIGN_UUID)
	private String id;
	private String virtualAccountNo;
	private String studentId;
	private String studentName;
	private String studentVirtualAccountName;
	private String studentVirtualAccountNo;
	private Float studentAmt;
	private Float studentBalance;
	
}
