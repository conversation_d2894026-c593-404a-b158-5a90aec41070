package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.util.Date;

@Data
@TableName(value="t_driver_train_sso_sync_info")
public class DriverTrainSsoSyncInfo {
    /** 主键ID*/
    @Id
    private String id;
    /** 本系统业务ID */
    private String systemId;
    /** 本系统业务名称 */
    private String systemName;
    /** 本系统业务唯一标识 */
    private String uniqueIdentifies;
    /** 同步状态(0未同步、1同步完成、2同步失败) */
    private int isSync;
    /** 同步次数  */
    private int syncCount;
    /** 同步标志0添加、修改用户、1删除用户 */
    private int syncFlag;
    /**  同步时间 */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date syncDate;
    /** 交通平台同步接口返回内容  */
    private String resMsg;
    /** 创建时间  */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createdTime;
    /**  更新时间 */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updatedTime;
    /**  创建人 */
    private String createdBy;
    /**  更新人 */
    private String updatedBy;
}
