package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guangren.common.annotation.Excel;
import com.guangren.common.annotation.Excels;
import com.guangren.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 学员跳过合同签署记录表
 */
@Data
@TableName(value = "t_student_skip_contract")
public class StudentSkipContract extends BaseEntity {
    private String id;
    //学员ID
    private String studentId;

    //学员姓名
    private String name;

    //学员证件号码
    private String identity;
    //学员手机号码
    private String mobile;
    //跳过签署合同时间
    @Excel(name = "跳过签署合同时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date triggerSkipDate;
    //驾校ID
    private String schoolId;
    //分校ID
    private String branchId;
    //报名点ID
    private String registrationId;
    // 发送通知次数
    private Integer sendNoticeCount;
    // 最近一次发送通知时间
    @Excel(name = "最近一次发送通知时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recentNoticeDate;
    // 旧合同电子合同id
    private String oldContractId;
    // 旧合同电子合同访问地址
    private String oldContractPath;
    // 新合同电子合同id
    private String newContractId;
    //新合同电子合同访问地址
    private String newContractPath;
    //原合同电子印章id
    private String oldSealId;
    //新合同电子印章id
    private String newSealId;
    //是否已完成合同重签,0未完成、1完成
    @TableField(exist = false)
    private Integer isFinishResign;

    @TableField(exist = false)
    private List<String> ids;

    @TableField(exist = false)
    private List<String> identityList;

    @Excels({
            @Excel(name = "驾校", targetAttr = "name", type = Excel.Type.EXPORT, sort = 1),
    })
    @TableField(exist = false)
    private School school;
    @Excels({
            @Excel(name = "分校", targetAttr = "name", type = Excel.Type.EXPORT, sort = 2),
    })
    @TableField(exist = false)
    private SchoolBranch branch;

    @Excels({
            @Excel(name = "报名点", targetAttr = "name", type = Excel.Type.EXPORT, sort = 3),
    })
    @TableField(exist = false)
    private SchoolRegistration registration;
}
