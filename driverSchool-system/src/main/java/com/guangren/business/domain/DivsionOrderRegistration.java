package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guangren.common.annotation.Excel;
import com.guangren.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 分账门店订单对象 t_divsion_order_registration
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
@Data
@TableName("t_divsion_order_registration")
@Accessors(chain = true)
public class DivsionOrderRegistration extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    @TableId(type= IdType.AUTO)
    private Long id;

    /**  */
    private Long orderId;

    /** 学校ID */
    private String schoolId;

    /** 学校名称 */
    @Excel(name = "驾校")
    private String schoolName;

    /** 分校ID */
    private String branchId;

    /** 分校名字 */
    private String branchName;

    /** 门店ID */
    private String registrationId;

    /** 门店名称 */
    @Excel(name = "门店")
    private String registrationName;

    /** 学生ID */
    private String studentId;

    /** 学生报名时姓名 */
    @Excel(name = "学员姓名")
    private String studentName;

    /** 学生报名时身份证号 */
    @Excel(name = "身份证号")
    private String studentIdcard;

    /** 学生报名时电话 */
    @Excel(name = "手机号")
    private String studentMobile;

    /** 银行单号 */
    @Excel(name = "银行单号")
    private String bankOrderNo;

    /** 订单号 */
    @Excel(name = "系统单号")
    private String orderNo;

    /** 平台费 */
    @Excel(name = "交易金额")
    private BigDecimal registrationFee;

    /** 订单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /** 0-未释放，1-已释放，2=释放失败 */
    @Excel(name = "交易状态", readConverterExp = "0=未释放,1=已释放,2=释放失败")
    private Integer status;

    /** 释放时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "释放时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date releaseTime;

    /** 释放单号 */
    @Excel(name = "清算单号")
    private String releaseOrderNo;

    /** 释放错误消息 */
    private String releaseMsg;

    /** 释放账号 */
    private String releaseAccountNo;

    /** 释放账户名 */
    private String releaseAccountName;

    /** 释放者 */
    private String releaser;

    /**  是否银行已核对  */
    private Integer isBankCheck;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdTime;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updatedTime;

    @TableField(exist = false)
    private List<String> ids;

    @TableField(exist = false)
    private String beginOrderTime;

    @TableField(exist = false)
    private String endOrderTime;

    @TableField(exist = false)
    private String beginReleaseTime;

    @TableField(exist = false)
    private String endReleaseTime;

    /**
     * 身份证列表
     */
    @TableField(exist = false)
    private List<String> identityList;
}
