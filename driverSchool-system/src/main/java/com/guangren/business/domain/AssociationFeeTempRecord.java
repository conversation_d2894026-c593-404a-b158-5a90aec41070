package com.guangren.business.domain;


import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guangren.common.core.domain.BaseEntity;

/**
 * 监管服务费临时记录表
 */
@TableName("association_fee_temp_record")
public class AssociationFeeTempRecord extends BaseEntity {
    /**
     * 业务ID
     */
    private String id;

    /**
     * 缴费学员ID
     */
    private String studentId;

    /**
     * 学校ID
     */
    private String schoolId;

    /**
     * 缴费学员身份证
     */
    private String identity;

    /**
     * 监管服务费（单位/分）
     */
    private BigDecimal associationFee;

    /**
     * 主商户ID
     */
    private String mid;
    /**
     * 主商户订单号
     */
    private String merOrderId;

    /**
     * 子订单分账的商户号
     */
    private String subMid;

    /**
     * 子订单分账账号
     */
    private String subMerOrderId;

    /**
     * 监管服务费创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /**
     * 监管服务费更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedTime;

    /**
     * 监管服务费提现时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date withdrawTime;

    /**
     * 是否存在驾协监管账号(0未转出，1转出成功，2转出失败)
     */
    private int status;

    /**
     * 提现银行单据
     */
    private String withdrawBankOrderNo;

    /**
     * 身份证列表
     */
    @TableField(exist = false)
    private List<String> identityList;

    public List<String> getIdentityList() {
        return identityList;
    }

    public void setIdentityList(List<String> identityList) {
        this.identityList = identityList;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getWithdrawBankOrderNo() {
        return withdrawBankOrderNo;
    }

    public void setWithdrawBankOrderNo(String withdrawBankOrderNo) {
        this.withdrawBankOrderNo = withdrawBankOrderNo;
    }

    public Date getWithdrawTime() {
        return withdrawTime;
    }

    public void setWithdrawTime(Date withdrawTime) {
        this.withdrawTime = withdrawTime;
    }

    public String getSubMid() {
        return subMid;
    }

    public void setSubMid(String subMid) {
        this.subMid = subMid;
    }

    public String getSubMerOrderId() {
        return subMerOrderId;
    }

    public void setSubMerOrderId(String subMerOrderId) {
        this.subMerOrderId = subMerOrderId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStudentId() {
        return studentId;
    }

    public void setStudentId(String studentId) {
        this.studentId = studentId;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public BigDecimal getAssociationFee() {
        return associationFee;
    }

    public void setAssociationFee(BigDecimal associationFee) {
        this.associationFee = associationFee;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getMerOrderId() {
        return merOrderId;
    }

    public void setMerOrderId(String merOrderId) {
        this.merOrderId = merOrderId;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getSchoolId() {
        return schoolId;
    }

    public void setSchoolId(String schoolId) {
        this.schoolId = schoolId;
    }
}
