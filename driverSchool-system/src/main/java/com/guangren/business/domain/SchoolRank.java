package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guangren.common.core.domain.BaseEntity;
import lombok.Data;

@Data
@TableName(value = "t_school_rank")
public class SchoolRank extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Integer id;
    //排名
    private int schoolRank;
    //驾校id
    private String schoolId;
    //驾校名称
    private String schoolName;
    //驾校状态(0 正常展示、1隐藏、3异常)
    private Integer status;
}
