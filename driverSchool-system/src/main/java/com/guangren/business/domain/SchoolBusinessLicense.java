package com.guangren.business.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.guangren.common.annotation.Excel;
import com.guangren.common.core.domain.BaseEntity;

/**
 * 学校相关执照对象 t_school_business_license
 * 
 * <AUTHOR>
 * @date 2023-02-15
 */
@TableName(value="t_school_business_license")
public class SchoolBusinessLicense extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    @TableId(type=IdType.ASSIGN_UUID)
    private String id;

    /** 1-营业执照，2-道路运输许可证，其他暂未定 */
    private Integer type;

    /** 图片 */
    private String image;

    /** 编号 */
    @Excel(name = "编号")
    private String no;

    /** 过期时间,永久为-1,其余为yyyy-MM-dd */
    @Excel(name = "有效期")
    private String expiration;

    /** 报名点ID */
    private String registrationId;

    /** 分校ID */
    private String branchId;

    /** 学校ID */
    private String schoolId;


    /**
     *营业执照有效期是否 永久
     */
    @Excel(name = "营业执照状态",readConverterExp = "0=无效,1=有效")
    @TableField(exist=false)
    private Integer isExpiration;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdTime;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updatedTime;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setType(Integer type) 
    {
        this.type = type;
    }

    public Integer getType() 
    {
        return type;
    }
    public void setImage(String image) 
    {
        this.image = image;
    }

    public String getImage() 
    {
        return image;
    }
    public void setNo(String no) 
    {
        this.no = no;
    }

    public String getNo() 
    {
        return no;
    }
    public void setExpiration(String expiration) 
    {
        this.expiration = expiration;
    }

    public String getExpiration() 
    {
        return expiration;
    }
    public void setRegistrationId(String registrationId) 
    {
        this.registrationId = registrationId;
    }

    public String getRegistrationId() 
    {
        return registrationId;
    }
    public void setBranchId(String branchId) 
    {
        this.branchId = branchId;
    }

    public String getBranchId() 
    {
        return branchId;
    }
    public void setSchoolId(String schoolId) 
    {
        this.schoolId = schoolId;
    }

    public String getSchoolId() 
    {
        return schoolId;
    }
    public void setCreatedTime(Date createdTime) 
    {
        this.createdTime = createdTime;
    }

    public Date getCreatedTime() 
    {
        return createdTime;
    }
    public void setUpdatedTime(Date updatedTime) 
    {
        this.updatedTime = updatedTime;
    }

    public Date getUpdatedTime() 
    {
        return updatedTime;
    }

    public Integer getIsExpiration() {
        return isExpiration;
    }

    public void setIsExpiration(Integer isExpiration) {
        this.isExpiration = isExpiration;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("type", getType())
            .append("image", getImage())
            .append("no", getNo())
            .append("expiration", getExpiration())
            .append("registrationId", getRegistrationId())
            .append("branchId", getBranchId())
            .append("schoolId", getSchoolId())
            .append("createdTime", getCreatedTime())
            .append("updatedTime", getUpdatedTime())
            .toString();
    }
}
