package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.guangren.common.core.domain.BaseEntity;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 学员退学对象 t_school_student_drop_out
 * 
 * <AUTHOR>
 * @date 2023-08-09
 */
@Data
@TableName(value = "t_school_student_drop_out")
public class SchoolStudentDropOut extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type=IdType.ASSIGN_UUID)
    private String id;

    /** 学员id */
    private String studentId;

    /** 驾校名称 */
    private String schoolName;

    /** 分校名称 */
    private String branchName;

    /** 门店名称 */
    private String registrationName;

    /** 学员姓名 */
    private String studentName;

    /** 性别 */
    private Integer gender;

    /** 身份证 */
    private String identity;

    /** 考取驾照类型 C1C2... */
    private String licenseType;

    /** 手机号 */
    private String mobile;

    /** 预登记时间 */
    private Date prepareRegisteDate;

    /** 报名时间 */
    private Date registeDate;

    /** 监管资金是否到账：0=未到账，1=已到账 */
    private Integer superviseFeeIsOk;

    /** 学习状态1-预登记 2- 已报名，3-课目2，4--课目3，5-课目1，6-课目4，99完成学业 */
    private Integer studentStatus;

    /** 是否监管：0=不监管，1=监管 */
    private Integer isSupervise;

    /** 退学状态：0=等待学员确认，1=学员已确认，2=等待驾协确认，3=驾协退回，4=驾协已确认，5=学员已确认，6=驾校撤销，7=审核确认，8=审核退回 */
    private Integer status;

    /** 是否初审：0=未初审，1=初审通过待终审，2=初审未通过、3=审核完毕、4终审未通过*/
    private Integer isFirstTrial;

    /** 初审时间 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstTrialTime;

    /** 业务处理明细 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String firstTrialRejectReason;

    /** 退学状态：0=进行中,1=流程结束 */
    private Integer isDone;

    /** 退学凭证 */
    private String dropOutImages;

    /** 退学原因 */
    private String dropOutReason;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /** 确认退学时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dropoutDoneTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedTime;

    /** 退回原因 */
    private String rejectReason;

    /** 撤销原因 */
    private String revokeReason;

    /** 驾校id */
    private String schoolId;

    /** 分校id */
    private String branchId;

    /** 门店id */
    private String registrationId;

    /** 是否归属驾协：0=非驾协成员，1=驾协成员 */
    private Integer isMember;

    /** 审核退回原因 */
    private String reviewerRejectReason;

    /** 学号 */
    private String stunum;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;

    @TableField(exist = false)
    private List<String> ids;

    @TableField(exist = false)
    private String beginCreatedTime;

    @TableField(exist = false)
    private String endCreatedTime;

    @TableField(exist = false)
    private MultipartFile[] images;

    /** 图片ID */
    @TableField(exist = false)
    private String imageIds;

    /** 是否重新发起 */
    private Boolean isReissue;

    /** 身份证号集合 */
    @TableField(exist = false)
    private List<String> identityList;
}
