package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guangren.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 学员合同重签实体类
 */
@Data
@TableName("t_student_resign_contract")
public class StudentResignContract {
    private static final long serialVersionUID = 1L;

    /**
     * 学员ID
     */
    @TableId(type = IdType.INPUT)
    private String studentId;

    /**
     * 学员姓名
     */
    private String studentName;

    /**
     * 学校ID
     */
    private String schoolId;

    /** 学校名称 */
    private String schoolName;

    /** 移动随e签系统的用户Id，添加用户之后返回 */
    private String userId;

    /** 移动随e签原签章ID */
    private String originSealId;

    /** 移动随e签系统中的新签章ID */
    private String newSealId;

    /** 创建合同的编号 */
    private String originContractNumber;

    /** 新合同编号 */
    private String newContractNumber;

    /** 原合同本地Url */
    private String originContractUrl;

    /** 新重签签合同Url */
    private String newContractUrl;

    /** 是否重签，0未重签，1已重签 */
    private Integer isResign;

    /** 学员手机号 */
    private String mobile;

    /** 新合同生成时间 */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date newContractCreateTime;

    /** 新合同签订时间 */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date newContractSignTime;

    /**
     * 以下从Excel导入的新费用设置
     */

    /**理论培训费*/
    private BigDecimal textTrainFee;
    /**综合服务费*/
    private BigDecimal serviceFee;
    /**科二实际操作培训费*/
    private BigDecimal operateFee;
    /**科三实际操作培训费*/
    private BigDecimal operateFee2;
    /** 科目二学时单价 */
    private BigDecimal studyTimeFee;
    /** 科目三学时单价 */
    private BigDecimal studyTimeFee2;
    /** 科目二补训费(按次) */
    private BigDecimal phase2Fee1;
    /** 科目三补训费(按次) */
    private BigDecimal phase3Fee1;
    /** 接送服务费 */
    private BigDecimal jiesongFee;
    /** 合同金额 */
    private BigDecimal contractFee;

}
