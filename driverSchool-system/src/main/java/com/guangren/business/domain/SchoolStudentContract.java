package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guangren.common.annotation.Excel;
import com.guangren.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 学员合同相关对象 t_school_student_contract
 * 
 * <AUTHOR>
 * @date 2024-02-24
 */
@Data
@TableName("t_school_student_contract")
@Accessors(chain = true)
public class SchoolStudentContract extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    @TableId(type = IdType.INPUT)
    private String studentId;

    /** 移动随e签系统的用户Id，添加用户之后返回 */
    @Excel(name = "移动随e签系统的用户Id，添加用户之后返回")
    private String userId;

    /** 移动随e签系统中的章ID，通过添加章接口之后返回 */
    @Excel(name = "移动随e签系统中的章ID，通过添加章接口之后返回")
    private String sealId;

    /** 创建合同的编号 */
    @Excel(name = "创建合同的编号")
    private String contractNumber;

    /** 合同本地url */
    @Excel(name = "合同本地url")
    private String contractUrl;

    /**
     * 是否已经签署合同
     * */
    public Integer isSign;

    /** 合同是否已被查看：0=未查看，1=已查看 */
    private Integer hasView;

    @TableField(exist = false)
    private List<String> pdfStrList;

    /**
     * 自定义合同编号
     * */
    private String customNumber;

    /**
     * 手机号
     * */
    private String mobile;
    /**  合同更新时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateContractTime;

    /** 学员最后一次查看合同时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date viewContractTime;

    /** 学员最后一次签署合同的时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signContractTime;

}
