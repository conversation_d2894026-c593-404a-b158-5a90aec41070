package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guangren.common.annotation.Excel;
import com.guangren.common.annotation.Excels;
import com.guangren.common.core.domain.BaseEntity;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 驾校结息流水（留存金额占比）对象 t_school_calculate_flow_amount
 * 
 * <AUTHOR>
 * @date 2024-01-06
 */
@Data
@TableName("t_school_calculate_flow_amount")
public class SchoolCalculateFlowAmount extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 驾校id */
    private String schoolId;

    @Excel(name = "统计周期")
    @TableField(exist = false)
    private String countCycle;

    /** 统计周期_开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date beginCountTime;

    /** 统计周期_结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endCountTime;

    /** 利息总数 */
    @Excel(name = "利息总数")
    private BigDecimal interestCount;

    /** 所有驾校留存金额 */
    @Excel(name = "所有驾校留存金额")
    private BigDecimal allSchoolRetainedAmount;

    /** 有效留存金额 */
    @Excel(name = "有效留存金额")
    private BigDecimal schoolRetainedAmount;

    /** 占比 */
    @Excel(name = "所占比例")
    private String ratio;

    /** 结算金额 */
    @Excel(name = "结算金额")
    private BigDecimal calculateAmount;

    /** 结息统计时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结息统计时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date countTime;

    /** 系统发起结算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "系统发起结算时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startCalculateTime;

    /** 结算系统单号 */
    @Excel(name = "结算系统单号")
    private String sysCalculateNo;

    /** 结算银行单号 */
    @Excel(name = "结算银行单号")
    private String calculateBankNo;

    /** 结算状态：0=未发起，1=成功，2=失败 */
    @Excel(name = "结算状态", readConverterExp = "0=未发起,1=成功,2=失败")
    private Integer calculateStatus;

    /** 结算批次 */
    @Excel(name = "结算批次")
    private String calculateBatchNo;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdTime;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updatedTime;

    @Excels({
            @Excel(name = "驾校", targetAttr = "name", type = Excel.Type.EXPORT, sort = 1),
    })
    @TableField(exist = false)
    private School school;

    @TableField(exist = false)
    private List<String> ids;

    @TableField(exist = false)
    private MultipartFile[] images;
}
