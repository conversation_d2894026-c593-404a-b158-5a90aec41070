package com.guangren.business.domain;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.guangren.common.annotation.Excel;
import com.guangren.common.annotation.Excels;
import com.guangren.common.core.domain.BaseEntity;

/**
 * 训练场地对象 t_school_training_ground
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
@TableName("t_school_training_ground")
public class SchoolTrainingGround extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    @TableId(type=IdType.ASSIGN_UUID)
    private String id;

    private String customerId;

    @Excel(name = "场地简称")
    private String simpleName;

    /** 训练场名称 */
    @Excel(name = "训练场名称")
    private String name;

    @Excel(name = "培训车型",prompt = "填写C1,C2、多个车型用英文逗号隔开")
    private String licenseTypes;

    private String images;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contact;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String tel;

    @Excel(name = "地址")
    private String address;

    /**  */
    private String province;

    /**  */
    private String city;

    /**  */
    private String town;


    /** 坐标经度  */
    @Excel(name = "训练场地图定位经度")
    private String longitude;

    /** 坐标纬度 */
    @Excel(name = "训练场地图定位纬度")
    private String latitude;

    /** 总部ID */
    private String schoolId;

    /** 分校ID */
    private String branchId;

    /** 报名点ID */
    private String registrationId;

    /** 审批状态 0待申请、1待审核、2审核通过、3审核失败 */
    @Excel(name = "二维码审批状态",readConverterExp = "0=待申请,1=待审核,2=审核通过,3=审核失败",type = Excel.Type.EXPORT)
    private Integer qrcodeAuditStatus;

    /** 审批信息 */
    private String qrcodeAuditReason;

    /** 最近一次的审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最近的审核时间",type = Excel.Type.EXPORT,dateFormat="yyyy-MM-dd")
    private Date lastAuditTime;

    /** 状态 */
    @Excel(name = "状态",readConverterExp = "0=撤消,1=正常")
    private Integer status;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdTime;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updatedTime;

    @Excels({
            @Excel(name = "所属驾校", targetAttr = "name", type = Excel.Type.EXPORT),
    })
    @TableField(exist=false)
    private School school;
    @Excels({
            @Excel(name = "所属分校", targetAttr = "name", type = Excel.Type.EXPORT),
    })
    @TableField(exist=false)
    private SchoolBranch branch;
    
    @TableField(exist=false)
    private List<PublicFile> imageFileList;

    // 图片ID
    @TableField(exist=false)
    private String imageIds;
    // 图片
    @JsonIgnore
    @JSONField(serialize = false)
    @TableField(exist=false)
    private MultipartFile[] schoolImages;

    /** 备案号码 */
    @Excel(name = "备案号码")
    private String recNo;

    /** 备案时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "备案时间", width = 30, dateFormat = "yyyy-MM-dd",prompt="格式如:2012-12-31")
    private Date recDate;

    public String getSimpleName() {
        return simpleName;
    }

    public void setSimpleName(String simpleName) {
        this.simpleName = simpleName;
    }

    public String getLicenseTypes() {
        return licenseTypes;
    }

    public void setLicenseTypes(String licenseTypes) {
        this.licenseTypes = licenseTypes;
    }

    public String getRecNo() {
        return recNo;
    }

    public void setRecNo(String recNo) {
        this.recNo = recNo;
    }

    public Date getRecDate() {
        return recDate;
    }

    public void setRecDate(Date recDate) {
        this.recDate = recDate;
    }

    public Integer getQrcodeAuditStatus() {
        return qrcodeAuditStatus;
    }

    public void setQrcodeAuditStatus(Integer qrcodeAuditStatus) {
        this.qrcodeAuditStatus = qrcodeAuditStatus;
    }

    public String getQrcodeAuditReason() {
        return qrcodeAuditReason;
    }

    public void setQrcodeAuditReason(String qrcodeAuditReason) {
        this.qrcodeAuditReason = qrcodeAuditReason;
    }

    public Date getLastAuditTime() {
        return lastAuditTime;
    }

    public void setLastAuditTime(Date lastAuditTime) {
        this.lastAuditTime = lastAuditTime;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setCustomerId(String customerId) 
    {
        this.customerId = customerId;
    }

    public String getCustomerId() 
    {
        return customerId;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setImages(String images) 
    {
        this.images = images;
    }

    public String getImages() 
    {
        return images;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setProvince(String province) 
    {
        this.province = province;
    }

    public String getProvince() 
    {
        return province;
    }
    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setContact(String contact) 
    {
        this.contact = contact;
    }

    public String getContact() 
    {
        return contact;
    }
    public void setTel(String tel) 
    {
        this.tel = tel;
    }

    public String getTel() 
    {
        return tel;
    }
    public void setLongitude(String longitude) 
    {
        this.longitude = longitude;
    }

    public String getLongitude() 
    {
        return longitude;
    }
    public void setLatitude(String latitude) 
    {
        this.latitude = latitude;
    }

    public String getLatitude() 
    {
        return latitude;
    }
    public void setSchoolId(String schoolId) 
    {
        this.schoolId = schoolId;
    }

    public String getSchoolId() 
    {
        return schoolId;
    }
    public void setBranchId(String branchId) 
    {
        this.branchId = branchId;
    }

    public String getBranchId() 
    {
        return branchId;
    }
    public void setRegistrationId(String registrationId) 
    {
        this.registrationId = registrationId;
    }

    public String getRegistrationId() 
    {
        return registrationId;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setCreatedTime(Date createdTime) 
    {
        this.createdTime = createdTime;
    }

    public Date getCreatedTime() 
    {
        return createdTime;
    }
    public void setUpdatedTime(Date updatedTime) 
    {
        this.updatedTime = updatedTime;
    }

    public Date getUpdatedTime() 
    {
        return updatedTime;
    }

    public School getSchool() {
        return school;
    }

    public void setSchool(School school) {
        this.school = school;
    }

    public SchoolBranch getBranch() {
        return branch;
    }

    public void setBranch(SchoolBranch branch) {
        this.branch = branch;
    }

    public List<PublicFile> getImageFileList() {
        return imageFileList;
    }

    public void setImageFileList(List<PublicFile> imageFileList) {
        this.imageFileList = imageFileList;
    }

    public String getImageIds() {
        return imageIds;
    }

    public void setImageIds(String imageIds) {
        this.imageIds = imageIds;
    }

    public MultipartFile[] getSchoolImages() {
        return schoolImages;
    }

    public void setSchoolImages(MultipartFile[] schoolImages) {
        this.schoolImages = schoolImages;
    }

    @Override
    public String toString() {
        return "SchoolTrainingGround{" +
                "id='" + id + '\'' +
                ", customerId='" + customerId + '\'' +
                ", simpleName='" + simpleName + '\'' +
                ", name='" + name + '\'' +
                ", licenseTypes='" + licenseTypes + '\'' +
                ", contact='" + contact + '\'' +
                ", tel='" + tel + '\'' +
                ", address='" + address + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", town='" + town + '\'' +
                ", longitude='" + longitude + '\'' +
                ", latitude='" + latitude + '\'' +
                ", schoolId='" + schoolId + '\'' +
                ", branchId='" + branchId + '\'' +
                ", qrcodeAuditStatus=" + qrcodeAuditStatus +
                ", qrcodeAuditReason='" + qrcodeAuditReason + '\'' +
                ", lastAuditTime=" + lastAuditTime +
                ", status=" + status +
                ", createdTime=" + createdTime +
                ", updatedTime=" + updatedTime +
                ", school=" + school +
                ", branch=" + branch +
                ", recNo='" + recNo + '\'' +
                ", recDate=" + recDate +
                '}';
    }
}
