package com.guangren.business.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@TableName("t_test_study_time_center_data")
@Data
public class TestStudyTimeCenterData {
	@TableId(type=IdType.AUTO)
	private Integer id;
	private String orgCode;
	private String studentName;
	private String teacherName;
	private String idCard;
	private String carNumber;
	private String licenseType;
	private String businessType;
	private String subjectType;
	private String periodType;
	private String periodSubType;
	private Date beginTime;
	private Date endTime;
	private Integer validStudyTime;
	private Float mileage;
	private String stage;
	private Integer validTime;
	private Integer invalidTime;
	private String reason;
	private Date auditTime;
	private Integer isSyn;
	
}
