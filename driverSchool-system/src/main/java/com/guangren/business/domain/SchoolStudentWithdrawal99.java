package com.guangren.business.domain;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.guangren.common.annotation.Excel;
import com.guangren.common.annotation.Excels;
import com.guangren.common.annotation.FieldCopy;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@TableName(value = "t_school_student_withdrawal_99")
public class SchoolStudentWithdrawal99 {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @FieldCopy(name = "studentName")
    @Excel(name = "学员", sort = 4)
    private String name;

    @Excel(name = "性别", readConverterExp = "0=女,1=男", sort = 5)
    private Integer gender;

    private String headImage;

    @FieldCopy(name = "idCardType")
    private String identityType;

    @FieldCopy(name = "idCard")
    @Excel(name = "身份证号", sort = 6)
    private String identity;

    @Excel(name = "手机号", sort = 7)
    private String mobile;

    @Excel(name = "学车类型", sort = 8)
    private String licenseType;

    @Excel(name = "来源", readConverterExp = "0=报名点,1=小程序", sort = 9)
    private Integer originData;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private String birthday;

    /** 民族 */
    @FieldCopy(name = "nationality")
    private String nation;

    private String education;

    private String address;

    private String province;

    private String city;

    private String town;

    private String hujiAddress;

    private String hujiProvince;

    private String hujiCity;

    private String hujiTown;

    private String schoolId;

    private String branchId;

    private String registrationId;

    private String businessType;

    private String oldLicenseNo;

    @FieldCopy(name = "oldDriveCarType")
    private String oldLicenseType;

    private Date oldLicenseDate;

    private String remark;

    @Excel(name = "预登记时间", dateFormat = "yyyy-MM-dd HH:mm:ss", sort = 10)
    private Date prepareRegisteDate;

    /**
     * 报名时间
     */
    @Excel(name = "报名审核时间", dateFormat = "yyyy-MM-dd", sort = 11)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registeDate;

    /**
     * 1-预登记 2- 已报名，3-课目2，4--课目3，5-课目1，6-课目4，99完成学业
     */
    @Excel(name = "学习状态", readConverterExp = "1=预登记,2=已报名,3=课目2,4=课目3,5=课目1,6=课目4,99=完成学业", sort = 12)
    private Integer status;

    private Integer isPay;

    private String payCode;

    private String studyCenterId;

    @Excel(name = "上报计时平台", readConverterExp = "0=否,1=是", sort = 13)
    private Integer studyCenterIsSyn;

    private Date studyCenterSynDate;

    //是否审核
    @Excel(name = "是否审核", readConverterExp = "0=未审核,1=已审核", sort = 14)
    private Integer isCheck;

    private Date firstPayDate;

    private String chargeMode;

    private String customerId;

    private Date updatedTime;

    private Date createdTime;

    //@Excel(name = "监管状态", readConverterExp = "0=未监管,1=已监管")
    private Integer isSupervise;

    @Excel(name = "监管资金状态", readConverterExp = "0=未到账,1=已到账", sort = 15)
    private Integer superviseFeeIsOk;

    private BigDecimal superviseFee;

    private Date superviseDate;
    @Excel(name = "是否退学", readConverterExp = "0=否,1=是", sort = 16)
    private Integer isQuit;

    private Date quitDate;

    private Integer quitIsSyn;

    @Excel(name = "微信id", sort = 18)
    private String openId;

    private String virtualAccountNo;

    private String virtualAccountName;

    private String realProvince;

    private String realCity;

    private String realTown;

    private String realAddress;

    @TableField(exist = false)
    private BigDecimal balance;

    @Excels({
            @Excel(name = "驾校", targetAttr = "name", type = Excel.Type.EXPORT, sort = 1),
    })
    @TableField(exist = false)
    private School school;
    @Excels({
            @Excel(name = "分校", targetAttr = "name", type = Excel.Type.EXPORT, sort = 2),
    })
    @TableField(exist = false)
    private SchoolBranch branch;

    @Excels({
            @Excel(name = "报名点", targetAttr = "name", type = Excel.Type.EXPORT, sort = 3),
    })
    @TableField(exist = false)
    private SchoolRegistration registration;

    /**
     * 图片
     */
    @TableField(exist = false)
    private MultipartFile[] images;

    /**
     * 原驾驶证电子版图片
     */
    private String oldLicenseImage;

    /**
     * 报名审核开始时间
     */
    @TableField(exist = false)
    private String beginTime;

    /**
     * 报名审核结束时间
     */
    @TableField(exist = false)
    private String endTime;

    /**
     * 预登记开始时间
     */
    @TableField(exist = false)
    private String beginPrepareRegisteDate;

    /**
     * 预登记结束时间
     */
    @TableField(exist = false)
    private String endPrepareRegisteDate;

    /**
     * 剩余待释放监管金额
     */
    private BigDecimal residueSuperviseAmt;

    /**
     * 退学信息
     */
    @TableField(exist = false)
    private SchoolStudentDropOut schoolStudentDropOut;

    /** 退学凭证图片 */
    @TableField(exist = false)
    private List<PublicFile> dropOutImgList;

    /** 图片ID */
    @TableField(exist = false)
    private String imageIds;

    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params;

    /** 学号 */
    @Excel(name = "学号", sort = 17)
    private String stunum;

    @TableField(exist = false)
    private List<String> ids;

    @TableField(exist = false)
    private List<String> dropOutIds;

    @TableField(exist = false)
    private List<String> identityList;

    @TableField(exist = false)
    private Integer limit;
    
    private String trainphase;
    
    @Version
    private Integer version;
}
