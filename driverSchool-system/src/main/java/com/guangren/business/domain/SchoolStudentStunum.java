package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guangren.common.core.domain.BaseEntity;

@TableName("t_school_student_stunum")
public class SchoolStudentStunum extends BaseEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = 565813090343320094L;
	@TableId(type=IdType.ASSIGN_UUID)
	private String id;
	private String studentId;
	private String name;
	private String identity;
	private String stunum;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getStudentId() {
		return studentId;
	}
	public void setStudentId(String studentId) {
		this.studentId = studentId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getIdentity() {
		return identity;
	}
	public void setIdentity(String identity) {
		this.identity = identity;
	}
	public String getStunum() {
		return stunum;
	}
	public void setStunum(String stunum) {
		this.stunum = stunum;
	}

	
}
