package com.guangren.business.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class UnionBankWithdrawal {
    private String clientNo;
    private String withdrawalNo;
    private BigDecimal withdrawAmount;
    private String payAcctNo;
    private String payAcctName;
    private String bankType;
    private String remark;
    private String sign;
}
