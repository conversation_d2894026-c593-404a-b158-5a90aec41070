package com.guangren.business.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guangren.common.annotation.Excel;

import lombok.Data;

@TableName("t_sta_month_school_branch")
@Data
public class StaMonthSchoolBranch {
	@TableId(type=IdType.AUTO)
	private Long id;
	
	private String schoolId;
	private String schoolBranchId;
	private String staMonth;
	 /** 注册点总数 */
    @Excel(name = "注册点总数")
    private Long registrationCount;

    /** 训练场总数 */
    @Excel(name = "训练场总数")
    private Long trainingGroundCount;

    /** 教练总数 */
    @Excel(name = "教练总数")
    private Long teacherCount;

    /** 车辆总数 */
    @Excel(name = "车辆总数")
    private Long carCount;

    /** 总监管资金 */
    @Excel(name = "总监管资金")
    private BigDecimal superviseTotalMoney;

    /** 已释放资金 */
    @Excel(name = "已释放资金")
    private BigDecimal superviseReleaseMoney;

    /** 剩余多少资金 */
    @Excel(name = "剩余多少资金")
    private BigDecimal superviseRestMoney;

    /** 总学生人员 */
    @Excel(name = "总学生人员")
    private Long studentCount;

    /** 审核通过学员数 */
    @Excel(name = "审核通过学员数")
    private Long checkedStudentCount;

    /** 监管学员总数 */
    @Excel(name = "监管学员总数")
    private Long superviseStudentCount;

    /** 未监管学员总数 */
    @Excel(name = "未监管学员总数")
    private Long noSuperviseStudentCount;

    /** 已完成学业人员总数 */
    @Excel(name = "已完成学业人员总数")
    private Long finishStudentCount;

    /** 退学学员总数 */
    @Excel(name = "退学学员总数")
    private Long quitStudentCount;

    /** 科目1学员数 */
    @Excel(name = "科目1学员数")
    private Long subject1StudentCount;

    /** 科目2学员数 */
    @Excel(name = "科目2学员数")
    private Long subject2StudentCount;

    /** 科目3学员数 */
    @Excel(name = "科目3学员数")
    private Long subject3StudentCount;

    /** 科目4学员数 */
    @Excel(name = "科目4学员数")
    private Long subject4StudentCount;

    /** 课目1监管人数 */
    @Excel(name = "课目1监管人数")
    private Long subject1SuperviseStudentCount;

    /** 课目1监管金额 */
    @Excel(name = "课目1监管金额")
    private BigDecimal suject1SuperviseMoney;

    /** 课目2监管人数 */
    @Excel(name = "课目2监管人数")
    private Long subject2SuperviseStudentCount;

    /** 课目2监管金额 */
    @Excel(name = "课目2监管金额")
    private BigDecimal suject2SuperviseMoney;

    /** 课目3监管人数 */
    @Excel(name = "课目3监管人数")
    private Long subject3SuperviseStudentCount;

    /** 课目3监管金额 */
    @Excel(name = "课目3监管金额")
    private BigDecimal suject3SuperviseMoney;

    /** 课目1监管人数 */
    @Excel(name = "课目1监管人数")
    private Long subject4SuperviseStudentCount;

    /** 课目4监管金额 */
    @Excel(name = "课目4监管金额")
    private BigDecimal suject4SuperviseMoney;

    /** 开卡成功数 */
    @Excel(name = "开卡成功数")
    private Long simSuccessCount;

    /** 开卡失败数 */
    @Excel(name = "开卡失败数")
    private Long simFailCount;

    /** 补贴已发放数 */
    @Excel(name = "补贴已发放数")
    private Long simAllowanceSendedCount;

    /** 补贴未发放数 */
    @Excel(name = "补贴未发放数")
    private Long simAllowanceUnsendCount;

    /** 未开卡已停止人数数 */
    @Excel(name = "未开卡已停止人数数")
    private Long simNoneStopCount;

    /** 未开卡在途人员数 */
    @Excel(name = "未开卡在途人员数")
    private Long simNoneDoingCount;

    /** 开卡已外呼数 */
    @Excel(name = "开卡已外呼数")
    private Long simCalledCount;

    /** 开卡未外呼人数 */
    @Excel(name = "开卡未外呼人数")
    private Long simUncallCount;
}
