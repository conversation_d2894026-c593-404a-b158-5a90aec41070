package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.guangren.common.annotation.Excel;
import com.guangren.common.annotation.Excels;
import com.guangren.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Data
@TableName("t_supervise_batch_submit")
public class SuperviseBatchSubmit extends BaseEntity {

	private static final long serialVersionUID = 7011317206483062222L;

	@Excels({
			@Excel(name = "驾校", targetAttr = "name", type = Excel.Type.EXPORT, sort = 1),
	})
	@TableField(exist = false)
	private School school;

	@TableId(type=IdType.INPUT)
	@Excel(name = "订单号")
	private String orderId;

	@Excel(name = "时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
	private Date createdTime;

	@Excel(name = "学员数")
	private Integer studentCount;

	@Excel(name = "转账总金额")
	private BigDecimal totalFee;

	private String virtualAccountNo;

	private String virtualAccountName;

	@Excel(name = "支付状态", readConverterExp = "0=未支付,1=成功")
	private Integer isPay;

	private String schoolId;

	private String branchId;

	private String registrationId;

	private BigDecimal superviseFee;

	private Date updatedTime;

	private Integer isSynBank;
	
	/**
	 * 手续费
	 */
	private BigDecimal commission;

	@TableField(exist=false)
	private SchoolBranch branch;

	/** 请求参数 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	@TableField(exist=false)
	private Map<String, Object> params;

	@TableField(exist = false)
	private List<String> ids;
}
