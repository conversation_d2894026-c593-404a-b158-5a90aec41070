package com.guangren.business.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.guangren.common.core.domain.BaseEntity;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * 报名点对象 t_school_registration
 * 
 * <AUTHOR>
 * @date 2023-03-02
 */
@Data
@TableName(value="t_school_registration")
public class SchoolRegistration extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    @TableId(type=IdType.ASSIGN_UUID)
    private String id;

    /** 学校名称 */
    private String name;

    /** 具体地址 */
    private String address;

    /** 所属省份 */
    private String province;

    /** 所属城市 */
    private String city;

    /** 所属县/镇/区 */
    private String town;

    /** 经度 */
    private String longitude;

    /** 纬度 */
    private String latitude;

    /** GPS解析地址 */
    private String gpsAddress;

    /** 驾照类型[’C1’,’C3’,’C2’] */
    private String licenseTypes;

    /** 收费模式[’一次性收费’,’分段收费’] */
    private String chargeModes;

    /** 图片["564jkk.jpg","6551.jpg"] */
    private String images;

    /** 0-撤消，1-正常 */
    private Integer status;

    /** 对接驾校自定义ID */
    private String customerId;

    /** 所属学校ID */
    private String schoolId;

    private String complaintTel;

    /** 营业时间 */
    private String businessTime;

    /** 审批状态 0待申请、1待审核、2审核通过、3审核失败 4审核已过期*/
    private Integer qrcodeAuditStatus;

    /** 审批信息 */
    private String qrcodeAuditReason;

    /** 最近一次的审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date lastAuditTime;


    /** 分校ID */
    private String branchId;
    @TableField(exist=false)
    private School school;
    @TableField(exist=false)
    private SchoolBranch branch;

    /** 学校相关执照信息 */
    @TableField(exist=false)
    private List<SchoolBusinessLicense> schoolBusinessLicenseList;

    /** 联系人信息*/
    @TableField(exist=false)
    private List<SchoolContact> schoolContactList;

    @TableField(exist=false)
    private List<PublicFile> imageFileList;

    // 图片ID
    @TableField(exist=false)
    private String imageIds;
    // 图片
    @JsonIgnore
    @JSONField(serialize = false)
    @TableField(exist=false)
    private MultipartFile[] schoolImages;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdTime;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updatedTime;
    
    private Integer level;

    /** 审核学员类型：0=总店审核，1=门店审核，2=自动审核 */
    private Integer reviewStudentType;
}
