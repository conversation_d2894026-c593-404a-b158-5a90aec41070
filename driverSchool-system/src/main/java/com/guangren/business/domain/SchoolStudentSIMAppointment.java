package com.guangren.business.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@TableName("t_school_student_sim_appointment")
@Data
public class SchoolStudentSIMAppointment {
	@TableId(type=IdType.ASSIGN_UUID)
	private Integer id;
	private String simMobile;
	private String studentId;
	private Date createdTime;
	private Date updatedTime;
	
}
