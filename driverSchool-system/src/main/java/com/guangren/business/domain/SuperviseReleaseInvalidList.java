package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 资讯内容对象 t_basic_article
 * 
 * <AUTHOR>
 * @date 2023-03-17
 */
@TableName(value="t_supervise_release_invalid_list")
@Data
@Accessors(chain = true)
public class SuperviseReleaseInvalidList {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String schoolId;
    private String schoolName;
    private String StudentId;
    private String studentName;
    private String studentIdentity;
    private String studentMobile;
    private Integer studentSex;
    private String studentTraintype;
    private String branchId;
    private String branchName;
    private String registrationId;
    private String registrationName;
    /** 归属科目：0=其他，1=科目一，2=科目二，3=科目三，4=科目四，5=退学 */
    private Integer subjectName;
    private String failReason;
    private Date pushtime;
    private String studentStunum;
    private Date createdTime;
}
