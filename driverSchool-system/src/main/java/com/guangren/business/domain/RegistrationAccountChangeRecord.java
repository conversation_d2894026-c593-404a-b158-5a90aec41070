package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guangren.common.annotation.Excel;
import com.guangren.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 门店收款账户变更记录对象 t_registration_account_change_record
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
@Data
@TableName("t_registration_account_change_record")
public class RegistrationAccountChangeRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 操作人id */
    private Long organUserId;

    /** 驾校id */
    private String schoolId;

    /** 驾校名称 */
    @Excel(name = "驾校")
    private String schoolName;

    /** 分校id */
    private String branchId;

    /** 分校名称 */
    @Excel(name = "分校")
    private String branchName;

    /** 门店id */
    private String registrationId;

    /** 门店名称 */
    @Excel(name = "门店")
    private String registrationName;

    /** 操作人 */
    @Excel(name = "操作人")
    private String organUsername;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "变更时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updatedTime;

    @TableField(exist = false)
    private String beginCreateTime;

    @TableField(exist = false)
    private String endCreateTime;

    @TableField(exist = false)
    private List<Long> ids;
}
