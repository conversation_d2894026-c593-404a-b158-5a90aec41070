package com.guangren.business.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.guangren.business.enumration.BankTransactionRecordType;
import com.guangren.common.annotation.Excel;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 银行交易流水
 */
@TableName("t_bank_transaction_record_backup")
@Accessors(chain = true)
@Data
public class BankTransactionRecordBackup {
	
	@TableId(type=IdType.ASSIGN_UUID)
	private String id;
	
	
    /**
     * 学员ID
     */
    @Excel(name = "学员ID")
    private String studentId;
    
    
    private Integer isQuit;
    
    private Integer isDikou;

    /**
     * 驾校id
     */
    @Excel(name = "驾校id")
    private String schoolId;

    /**
     * 分校id
     */
    @Excel(name = "分校id")
    private String branchId;

    /**
     * 报名点id
     */
    @Excel(name = "报名点id")
    private String registrationId;

    /**
     * 交易金额
     */
    @Excel(name = "交易金额")
    private BigDecimal amount;

    /**
     * 类型(0=支付,1=提现)
     */
    @Excel(name = "类型", readConverterExp = "PAY=支付,WITHDRAW=提现")
    private BankTransactionRecordType type;

    /**
     * 交易状态
     */
    @Excel(name = "交易状态", readConverterExp = "true=成功,false=失败")
    private Boolean status;

    /**
     * 银行错误消息
     */
    @Excel(name = "银行错误消息")
    private String errMsg;

    /**
     * 银行状态
     */
    @Excel(name = "银行状态", readConverterExp = "0=成功,1=失败,2-处理中")
    private String bankStatus;


    /**
     * 提交的json表单
     */
    @Excel(name = "提交的json表单")
    private String fromJson;
    
    private Integer transType;
    
    private String b2bmid;
    private String b2btid;
    private String subb2bmid;
    
    private String b2cmid;
    private String b2ctid;
    private String subb2cmid;
    
    private String scancodemid;
    private String scancodetid;
    private String subscancodemid;

    /**
     * 银行订单号
     */
    @Excel(name = "交易流水号")
    private String bankOrderId;
    
    /**
     * 支付银行信息
     * */
    @Excel(name = "支付银行信息")
    private String bankInfo;
    /**
     * 支付卡信息号
     * */
    @Excel(name = "支付卡信息号")
    private String bankCardNo;	
    
    /**
     * 目标平台单号
     * */
    @Excel(name = "目标平台单号")
    private String targetOrderId;

    /**
     * 自己的订单号
     */
    @Excel(name = "自己的订单号")
    private String orderId;

    /**
     * 子订单号
     */
    @Excel(name = "子订单号")
    private String subOrderId;

    /**
     * 交易时间
     */
    @Excel(name = "交易时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 拓展数据
     */
    @Excel(name = "拓展数据")
    private String extendData;

    // -----------association--------------
    /**
     * 学员姓名
     */
    @Excel(name = "学员姓名")
    @TableField(exist = false)
    private String studentName;
    /**
     * 学校名称
     */
    @Excel(name = "学校名称")
    @TableField(exist = false)
    private String schoolName;
    /**
     * 分校名称
     */
    @Excel(name = "分校名称")
    @TableField(exist = false)
    private String branchName;
    /**
     * 注册点名称
     */
    @Excel(name = "注册点名称")
    @TableField(exist = false)
    private String registrationName;
    
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist=false)
    private Map<String, Object> params;
    
    
}