package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@TableName("t_test_bank_supervise_account")
@Data
public class TestBankSuperviseAccount {
	@TableId(type=IdType.INPUT)
	private String customerNo;
	private String commonAccountName;
	private String commonAccountNo;
	private String superviseAccountName;
	private String superviseAccountNo;
	private Float superviseAccountAmt;
	private Float commonAccountAmt;
}
