package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.guangren.common.annotation.Excel;
import com.guangren.common.annotation.Excels;
import lombok.Data;

import java.util.Date;
import java.util.List;

@TableName("t_school_student_center_exception")
@Data
public class SchoolStudentCenterException {

	@TableId(type=IdType.ASSIGN_UUID)
	private String id;

	private String studentId;

	@Excel(name = "学员姓名")
	private String studentName;

	@Excel(name = "证件号码")
	private String studentIdentity;

	@Excels({
			@Excel(name = "学校名称", targetAttr = "name", type = Excel.Type.EXPORT),
	})
	@TableField(exist = false)
	private School school;

	@Excel(name = "学时中心")
	private String centerName;

	@Excel(name = "提交地址")
	private String requestUrl;

	@Excel(name = "是否成功", readConverterExp = "0=否,1=是")
	private Integer isSuccess;

	private String submitParams;

	@Excel(name = "返回数据")
	@TableField(exist = false)
	private String resultData;

	private String failReason;

	private String successReason;

	@Excel(name = "提交时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
	private Date synDate;

	private String schoolId;

	private String branchId;

	private String registrationId;

	private Date createdTime;

	private Date updatedTime;

	@TableField(exist = false)
	private List<String> ids;

	@TableField(exist = false)
	private String beginSynDate;

	@TableField(exist = false)
	private String endSynDate;

	/**
     * 身份证列表
     */
    @TableField(exist = false)
    private List<String> identityList;
}
