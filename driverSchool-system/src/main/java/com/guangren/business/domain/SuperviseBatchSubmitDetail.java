package com.guangren.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guangren.common.annotation.Excel;
import com.guangren.common.core.domain.BaseEntity;

import lombok.Data;

/**
 * 批量提交学员明细对象 t_supervise_batch_submit_detail
 * 
 * <AUTHOR>
 * @date 2023-03-22
 */
@Data
@TableName("t_supervise_batch_submit_detail")
public class SuperviseBatchSubmitDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    @TableId(type=IdType.ASSIGN_UUID)
    private String id;

    /**  */
    @Excel(name = "")
    private String orderId;

    /**  */
    @Excel(name = "")
    private String studentId;
    
    //总费用
    private BigDecimal totalFee;
    
    //手续费
    private BigDecimal commission;

    /** 交费金额 */
    @Excel(name = "交费金额")
    private BigDecimal superviseFee;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedTime;

    /** 虚假账号 */
    @Excel(name = "虚假账号")
    private String virtualAccountNo;

    /** 虚假账号名 */
    @Excel(name = "虚假账号名")
    private String virtualAccountName;

    @TableField(exist=false)
    private SchoolStudent schoolStudent;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setOrderId(String orderId) 
    {
        this.orderId = orderId;
    }

    public String getOrderId() 
    {
        return orderId;
    }
    public void setStudentId(String studentId) 
    {
        this.studentId = studentId;
    }

    public String getStudentId() 
    {
        return studentId;
    }
    public void setSuperviseFee(BigDecimal superviseFee) 
    {
        this.superviseFee = superviseFee;
    }

    public BigDecimal getSuperviseFee() 
    {
        return superviseFee;
    }
    public void setCreatedTime(Date createdTime) 
    {
        this.createdTime = createdTime;
    }

    public Date getCreatedTime() 
    {
        return createdTime;
    }
    public void setUpdatedTime(Date updatedTime) 
    {
        this.updatedTime = updatedTime;
    }

    public Date getUpdatedTime() 
    {
        return updatedTime;
    }
    public void setVirtualAccountNo(String virtualAccountNo) 
    {
        this.virtualAccountNo = virtualAccountNo;
    }

    public String getVirtualAccountNo() 
    {
        return virtualAccountNo;
    }
    public void setVirtualAccountName(String virtualAccountName) 
    {
        this.virtualAccountName = virtualAccountName;
    }

    public String getVirtualAccountName() 
    {
        return virtualAccountName;
    }


    public SchoolStudent getSchoolStudent() {
        return schoolStudent;
    }

    public void setSchoolStudent(SchoolStudent schoolStudent) {
        this.schoolStudent = schoolStudent;
    }
    
    

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("studentId", getStudentId())
            .append("superviseFee", getSuperviseFee())
            .append("createdTime", getCreatedTime())
            .append("updatedTime", getUpdatedTime())
            .append("virtualAccountNo", getVirtualAccountNo())
            .append("virtualAccountName", getVirtualAccountName())
            .toString();
    }
}
