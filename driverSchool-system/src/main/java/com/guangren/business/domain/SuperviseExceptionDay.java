package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guangren.business.vo.SuperviseQueryVo;
import com.guangren.common.annotation.Excel;
import com.guangren.common.annotation.Excels;
import com.guangren.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 资金监管异常记录对象 t_supervise_exception_day
 * 
 * <AUTHOR>
 * @date 2023-04-12
 */
@Data
@TableName(value="t_supervise_exception_day")
public class SuperviseExceptionDay extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /** 学员ID */
    private String studentId;

    /** 监管金额到帐时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    //@Excel(name = "监管金额到帐时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date superviseDate;

    /** 释放间隔天 */
    //@Excel(name = "释放间隔天")
    private Integer releaseDay;

    /** 释放间隔天阀值 */
    //@Excel(name = "释放间隔天阀值")
    private Integer day;


    /** 释放关联记录t_supervise_release_record表中ID */
    private String releaseRecordId;

    private String schoolId;

    private String branchId;

    private String registrationId;

    @Excels({
            @Excel(name = "驾校", targetAttr = "name", type = Excel.Type.EXPORT,sort = 1),
            @Excel(name = "释放账号", targetAttr = "payAcctNo", type = Excel.Type.EXPORT,sort = 9),
            @Excel(name = "释放账户", targetAttr = "bankAcctName", type = Excel.Type.EXPORT,sort = 10),
            @Excel(name = "关联银行", targetAttr = "bankName", type = Excel.Type.EXPORT,sort = 11),
    })
    @TableField(exist=false)
    private School school;

    @Excels({
            @Excel(name = "分校", targetAttr = "name", type = Excel.Type.EXPORT,sort = 2),
    })
    @TableField(exist=false)
    private SchoolBranch branch;

    @Excels({
            @Excel(name = "报名点", targetAttr = "name", type = Excel.Type.EXPORT,sort = 3),
    })
    @TableField(exist=false)
    private SchoolRegistration registration;


    @TableField(exist=false)
    @Excels({
            @Excel(name = "学员", targetAttr = "name", type = Excel.Type.EXPORT,sort = 4),
            @Excel(name = "身份证号", targetAttr = "identity", type = Excel.Type.EXPORT,sort = 5),
            @Excel(name = "监管金额", targetAttr = "superviseFee", type = Excel.Type.EXPORT,sort = 6),
    })
    private SchoolStudent schoolStudent;

    /** 释放金额 */
    @Excel(name = "释放金额", type = Excel.Type.EXPORT,sort = 7)
    private BigDecimal releaseFee;

    /** 释放金额时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "释放金额时间", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT,sort = 8)
    private Date releaseDate;

    /** 异常原因 */
    @Excel(name = "异常原因", type = Excel.Type.EXPORT, sort = 12)
    private String exceptionReason;

    /** 查询拓展 */
    @TableField(exist = false)
    private SuperviseQueryVo superviseQueryVo;

    @TableField(exist = false)
    private String studentName;

    @TableField(exist = false)
    private String identity;

    @TableField(exist = false)
    private String beginTime;

    @TableField(exist = false)
    private String endTime;

    @TableField(exist = false)
    private List<String> ids;

    /**
     * 身份证列表
     */
    @TableField(exist = false)
    private List<String> identityList;
}
