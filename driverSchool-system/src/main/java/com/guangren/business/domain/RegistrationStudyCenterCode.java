package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guangren.common.annotation.Excel;
import com.guangren.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 门店与学时平台签约代码对象 t_registration_study_center_code
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@Data
@TableName("t_registration_study_center_code")
public class RegistrationStudyCenterCode extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 门店id */
    @Excel(name = "门店id")
    private String registrationId;

    /** 学时平台的用户id */
    @Excel(name = "学时平台的用户id")
    private Long organId;

    /** 学时平台的用户名 */
    @Excel(name = "学时平台的用户名")
    private String organName;

    /** 签约学校/签约代码 */
    @Excel(name = "签约学校/签约代码")
    private String registrationCode;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updatedTime;

    @TableField(exist = false)
    private String schoolId;
}
