package com.guangren.business.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.guangren.common.annotation.Excel;
import com.guangren.common.annotation.Excels;
import com.guangren.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 资金监管释放记录对象 t_supervise_release_record
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
@Data
@TableName("t_supervise_release_record")
@Accessors(chain = true)
public class SuperviseReleaseRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    @TableId(type=IdType.ASSIGN_UUID)
    private String id;

    /** 学员ID */
    private String studentId;

    /** 监管金额 */
    @Excel(name = "监管金额")
    private BigDecimal superviseFee;

    /** 释放金额 */
    @Excel(name = "释放金额")
    private BigDecimal releaseFee;

    /** 释放时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "释放时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date releaseDate;

    /** 1-释放成功，0-未成功， */
    //@Excel(name = "释放状态",readConverterExp = "0=未成功,1=释放成功")
    private Integer isSuccess;

    /** 失败次数，失败一次加一次，同时在t_supervise_exception中增加记录 */
    private Integer failCount;

    /** 释放账号 */
    private String accountNo;

    /** 释放账户名 */
    private String accountName;

    /** 总的审核通过的有效的学习时长 */
    private Long checkValidStudyTime;

    /** 考核的学习时长 */
    private Long examineStudyTime;

    /** 0-未达标 ，1-达标 */
    private Integer isPass;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdTime;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updatedTime;

    /**  */
    private String schoolId;

    /**  */
    private String branchId;

    /**  */
    private String registrationId;

    /** 释放收款银行卡 */
    @Excel(name = "释放账号")
    private String payAcctNo;

    /** 释放收款银行账户名 */
    @Excel(name = "释放账户名")
    private String bankAcctName;

    /** 开户行 */
    @Excel(name = "关联银行")
    private String bankName;
    
    /** 订单号*/
    private String orderNo;

    /** 省平台达标时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    @Excels({
            @Excel(name = "驾校", targetAttr = "name", type = Excel.Type.EXPORT,sort = 1),
    })
    @TableField(exist=false)
    private School school;

    @Excels({
            @Excel(name = "分校", targetAttr = "name", type = Excel.Type.EXPORT,sort = 2),
    })
    @TableField(exist=false)
    private SchoolBranch branch;

    @Excels({
            @Excel(name = "报名点", targetAttr = "name", type = Excel.Type.EXPORT,sort = 3),
    })
    @TableField(exist=false)
    private SchoolRegistration registration;

    @TableField(exist=false)
    @Excels({
            @Excel(name = "学员", targetAttr = "name", type = Excel.Type.EXPORT,sort = 4),
            @Excel(name = "身份证号", targetAttr = "identity", type = Excel.Type.EXPORT,sort = 5),
            @Excel(name ="监管时间",targetAttr = "superviseDate", type = Excel.Type.EXPORT,sort = 6,dateFormat="yyyy-MM-dd")
    })
    private SchoolStudent schoolStudent;

    /** 课目名称 */
    @Excel(name = "归属科目")
    private String subjectName;

    @TableField(exist=false)
    private List<SuperviseException> superviseExceptionList;

    @TableField(exist=false)
    private ProvinceStudyTime provinceStudyTime;

    @TableField(exist = false)
    private String studentName;

    @TableField(exist = false)
    private String identity;

    @TableField(exist = false)
    private String beginTime;

    @TableField(exist = false)
    private String endTime;

    @TableField(exist = false)
    private SupervisePay supervisePay;

    @TableField(exist = false)
    private String beginReleaseDate;

    @TableField(exist = false)
    private String endReleaseDate;

    @TableField(exist = false)
    private BigDecimal beginReleaseFee;

    @TableField(exist = false)
    private BigDecimal endReleaseFee;

    @TableField(exist = false)
    private boolean excludeNullReleaseDate;

    /**
     * 身份证列表
     */
    @TableField(exist = false)
    private List<String> identityList;
}
