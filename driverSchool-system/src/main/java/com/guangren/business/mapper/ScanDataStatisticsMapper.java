package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.ScanDataStatistics;

import java.util.List;

public interface ScanDataStatisticsMapper extends BaseMapper<ScanDataStatistics> {
    /**
     * 查询扫码数据统计列表
     *
     * @param scanDataStatistics 扫码数据统计信息
     * @return 扫码数据统计集合
     */
    List<ScanDataStatistics> selectScanDataStatisticsList(ScanDataStatistics scanDataStatistics);

    /**
     * 新增扫码数据统计
     *
     * @param scanDataStatistics 扫码数据统计信息
     * @return 影响行数
     */
    int insertScanDataStatistics(ScanDataStatistics scanDataStatistics);

    /**
     * 通过ID删除扫码数据统计
     *
     * @param id 扫码数据统计ID
     * @return 影响行数
     */
    int deleteScanDataStatisticsById(String id);

    /**
     * 更新扫码数据统计
     *
     * @param scanDataStatistics 扫码数据统计信息
     * @return 影响行数
     */
    int updateScanDataStatistics(ScanDataStatistics scanDataStatistics);

    /**
     * 通过ID查询单条扫码数据统计信息
     *
     * @param id 扫码数据统计ID
     * @return 扫码数据统计信息
     */
    ScanDataStatistics selectScanDataStatisticsById(String id);
}
