package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.StudentSkipContract;

import java.util.List;

public interface StudentSkipContractMapper extends BaseMapper<StudentSkipContract> {
    //查询列表
    List<StudentSkipContract> selectStudentSkipContractList(StudentSkipContract studentSkipContract);
    //查询
    StudentSkipContract selectStudentSkipContractById(String id);
    //新增
    int insertStudentSkipContract(StudentSkipContract studentSkipContract);
    //修改
    int updateStudentSkipContract(StudentSkipContract studentSkipContract);
    //删除
    int deleteStudentSkipContractById(String id);
    //批量删除
    int deleteStudentSkipContractByIds(String[] ids);
    //根据学员ID查询信息
    StudentSkipContract selectStudentSkipContractByStudentId(String studentId);
}
