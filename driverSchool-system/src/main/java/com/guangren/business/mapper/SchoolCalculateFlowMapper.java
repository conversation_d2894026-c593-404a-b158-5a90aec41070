package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolCalculateFlow;

import java.util.List;

/**
 * 驾校结息流水Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-16
 */
public interface SchoolCalculateFlowMapper extends BaseMapper<SchoolCalculateFlow>
{
    /**
     * 查询驾校结息流水
     * 
     * @param id 驾校结息流水主键
     * @return 驾校结息流水
     */
    public SchoolCalculateFlow selectSchoolCalculateFlowById(String id);

    /**
     * 查询驾校结息流水列表
     * 
     * @param schoolCalculateFlow 驾校结息流水
     * @return 驾校结息流水集合
     */
    public List<SchoolCalculateFlow> selectSchoolCalculateFlowList(SchoolCalculateFlow schoolCalculateFlow);

    /**
     * 新增驾校结息流水
     * 
     * @param schoolCalculateFlow 驾校结息流水
     * @return 结果
     */
    public int insertSchoolCalculateFlow(SchoolCalculateFlow schoolCalculateFlow);

    /**
     * 修改驾校结息流水
     * 
     * @param schoolCalculateFlow 驾校结息流水
     * @return 结果
     */
    public int updateSchoolCalculateFlow(SchoolCalculateFlow schoolCalculateFlow);


    /**
    * 验证参数唯一性
    * @return
    */
    public SchoolCalculateFlow checkUnique(SchoolCalculateFlow schoolCalculateFlow);

    /**
     * 删除驾校结息流水
     * 
     * @param id 驾校结息流水主键
     * @return 结果
     */
    public int deleteSchoolCalculateFlowById(String id);

    /**
     * 批量删除驾校结息流水
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolCalculateFlowByIds(String[] ids);
}
