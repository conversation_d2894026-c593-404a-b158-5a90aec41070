package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.vo.StudentCountVo;

import java.util.List;


public interface SchoolStudentMapper extends BaseMapper<SchoolStudent>{
    /**
     * 查询学员
     *
     * @param id 学员主键
     * @return 学员
     */
    public SchoolStudent selectSchoolStudentById(String id);

    /**
     * 查询学员列表
     *
     * @param schoolStudent 学员
     * @return 学员集合
     */
    public List<SchoolStudent> selectSchoolStudentList(SchoolStudent schoolStudent);
    
    
    /**
     * 查询学员列表
     *
     * @param schoolStudent 学员
     * @return 学员集合
     */
    public List<SchoolStudent> customSelectSchoolStudentList(SchoolStudent schoolStudent);

    /**
     * 新增学员
     *
     * @param schoolStudent 学员
     * @return 结果
     */
    public int insertSchoolStudent(SchoolStudent schoolStudent);

    /**
     * 修改学员
     *
     * @param schoolStudent 学员
     * @return 结果
     */
    public int updateSchoolStudent(SchoolStudent schoolStudent);


    /**
     * 验证参数唯一性
     * @return
     */
    public SchoolStudent checkUnique(SchoolStudent schoolStudent);

    /**
     * 删除学员
     *
     * @param id 学员主键
     * @return 结果
     */
    public int deleteSchoolStudentById(String id);

    /**
     * 批量删除学员
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolStudentByIds(String[] ids);

    /**
     * 学员统计数据
     * @return 结果
     */
    StudentCountVo getSchoolStudentCount(SchoolStudent schoolStudent);

    /**
     * 查询退学学员列表
     */
    List<SchoolStudent> quitSelectSchoolStudentList(SchoolStudent schoolStudent);

    /**
     * 查询结业学员列表
     */
    List<SchoolStudent> selectSchoolGraduateStudentList(SchoolStudent schoolStudent);

    /**
     * 导出结业学员
     */
    List<SchoolStudent> exportSchoolGraduateStudentList(SchoolStudent schoolStudent);

    /**
     * 查询退学学员列表（驾校）
     */
    List<SchoolStudent> selectSchoolStudentDropOutList(SchoolStudent schoolStudent);

    List<SchoolStudent> getQuitReleaseStudentList();
}
