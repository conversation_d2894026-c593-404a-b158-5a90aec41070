package com.guangren.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.DivsionOrderPlatform;

/**
 * 分账平台订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface DivsionOrderPlatformMapper extends BaseMapper<DivsionOrderPlatform>
{
    /**
     * 查询分账平台订单
     * 
     * @param id 分账平台订单主键
     * @return 分账平台订单
     */
    public DivsionOrderPlatform selectDivsionOrderPlatformById(Long id);

    /**
     * 查询分账平台订单列表
     * 
     * @param divsionOrderPlatform 分账平台订单
     * @return 分账平台订单集合
     */
    public List<DivsionOrderPlatform> selectDivsionOrderPlatformList(DivsionOrderPlatform divsionOrderPlatform);

    /**
     * 新增分账平台订单
     * 
     * @param divsionOrderPlatform 分账平台订单
     * @return 结果
     */
    public int insertDivsionOrderPlatform(DivsionOrderPlatform divsionOrderPlatform);

    /**
     * 修改分账平台订单
     * 
     * @param divsionOrderPlatform 分账平台订单
     * @return 结果
     */
    public int updateDivsionOrderPlatform(DivsionOrderPlatform divsionOrderPlatform);


    /**
    * 验证参数唯一性
    * @return
    */
    public DivsionOrderPlatform checkUnique(DivsionOrderPlatform divsionOrderPlatform);

    /**
     * 删除分账平台订单
     * 
     * @param id 分账平台订单主键
     * @return 结果
     */
    public int deleteDivsionOrderPlatformById(Long id);

    /**
     * 批量删除分账平台订单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDivsionOrderPlatformByIds(String[] ids);
}
