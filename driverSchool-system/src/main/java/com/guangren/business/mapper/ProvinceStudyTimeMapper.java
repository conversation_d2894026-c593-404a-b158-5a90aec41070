package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.ProvinceStudyTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface ProvinceStudyTimeMapper extends BaseMapper<ProvinceStudyTime> {

    /**
     * 查询省厅数据
     * <AUTHOR>
     * @date 2023/9/27 9:40
     * @param provinceStudyTime
     * @return java.util.List<com.guangren.business.domain.SchoolStudentSIM>
     */
    List<ProvinceStudyTime> selectProvinceStudyTimeList(ProvinceStudyTime provinceStudyTime);


    List<Map<String,Object>> getReleaseStudentList();
}