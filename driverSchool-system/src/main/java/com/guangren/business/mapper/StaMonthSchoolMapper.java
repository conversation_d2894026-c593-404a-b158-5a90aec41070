package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.StaMonthSchool;

import java.util.List;

/**
 * 按学校日期统计所有指标Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-12
 */
public interface StaMonthSchoolMapper extends BaseMapper<StaMonthSchool>
{
    /**
     * 查询按学校日期统计所有指标
     *
     * @param id 按学校日期统计所有指标主键
     * @return 按学校日期统计所有指标
     */
    public StaMonthSchool selectStaMonthSchoolById(Long id);

    /**
     * 查询按学校日期统计所有指标列表
     *
     * @param staMonthSchool 按学校日期统计所有指标
     * @return 按学校日期统计所有指标集合
     */
    public List<StaMonthSchool> selectStaMonthSchoolList(StaMonthSchool staMonthSchool);

    /**
     * 新增按学校日期统计所有指标
     *
     * @param staMonthSchool 按学校日期统计所有指标
     * @return 结果
     */
    public int insertStaMonthSchool(StaMonthSchool staMonthSchool);

    /**
     * 修改按学校日期统计所有指标
     *
     * @param staMonthSchool 按学校日期统计所有指标
     * @return 结果
     */
    public int updateStaMonthSchool(StaMonthSchool staMonthSchool);


    /**
     * 验证参数唯一性
     * @return
     */
    public StaMonthSchool checkUnique(StaMonthSchool staMonthSchool);

    /**
     * 删除按学校日期统计所有指标
     *
     * @param id 按学校日期统计所有指标主键
     * @return 结果
     */
    public int deleteStaMonthSchoolById(Long id);

    /**
     * 批量删除按学校日期统计所有指标
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStaMonthSchoolByIds(String[] ids);
}
