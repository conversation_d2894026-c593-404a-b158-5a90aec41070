package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.DivsionOrderAssociation;

import java.util.List;

/**
 * 监管服务费订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface DivsionOrderAssociationMapper extends BaseMapper<DivsionOrderAssociation>
{
    /**
     * 查询分账总订单
     * 
     * @param id 分账总订单主键
     * @return 分账总订单
     */
    public DivsionOrderAssociation selectDivsionOrderAssociationById(Long id);

    /**
     * 查询分账总订单列表
     * 
     * @param divsionOrderAssociation 分账总订单
     * @return 分账总订单集合
     */
    public List<DivsionOrderAssociation> selectDivsionOrderAssociationList(DivsionOrderAssociation divsionOrderAssociation);

    /**
     * 新增分账总订单
     * 
     * @param divsionOrderAssociation 分账总订单
     * @return 结果
     */
    public int insertDivsionOrderAssociation(DivsionOrderAssociation divsionOrderAssociation);

    /**
     * 修改分账总订单
     * 
     * @param divsionOrderAssociation 分账总订单
     * @return 结果
     */
    public int updateDivsionOrderAssociation(DivsionOrderAssociation divsionOrderAssociation);


    /**
    * 验证参数唯一性
    * @return
    */
    public DivsionOrderAssociation checkUnique(DivsionOrderAssociation divsionOrderAssociation);

    /**
     * 删除分账总订单
     * 
     * @param id 分账总订单主键
     * @return 结果
     */
    public int deleteDivsionOrderAssociationById(Long id);

    /**
     * 批量删除分账总订单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDivsionOrderAssociationByIds(String[] ids);
}
