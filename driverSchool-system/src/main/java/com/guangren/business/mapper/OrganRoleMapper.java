package com.guangren.business.mapper;

import java.util.List;
import com.guangren.business.domain.OrganRole;
import com.guangren.business.domain.OrganRoleMenu;

/**
 * 机构角色信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-07
 */
public interface OrganRoleMapper 
{
    /**
     * 查询机构角色信息
     * 
     * @param roleId 机构角色信息主键
     * @return 机构角色信息
     */
    public OrganRole selectOrganRoleByRoleId(Long roleId);

    /**
     * 查询机构角色信息列表
     * 
     * @param organRole 机构角色信息
     * @return 机构角色信息集合
     */
    public List<OrganRole> selectOrganRoleList(OrganRole organRole);

    /**
     * 新增机构角色信息
     * 
     * @param organRole 机构角色信息
     * @return 结果
     */
    public int insertOrganRole(OrganRole organRole);

    /**
     * 修改机构角色信息
     * 
     * @param organRole 机构角色信息
     * @return 结果
     */
    public int updateOrganRole(OrganRole organRole);


    /**
     * 删除机构角色信息
     * 
     * @param roleId 机构角色信息主键
     * @return 结果
     */
    public int deleteOrganRoleByRoleId(Long roleId);

    /**
     * 批量删除机构角色信息
     * 
     * @param roleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrganRoleByRoleIds(String[] roleIds);

    /**
     * 批量新增角色菜单信息
     * @param roleMenuList 角色菜单列表
     * @return 结果
     */
    public int batchRoleMenu(List<OrganRoleMenu> roleMenuList);

    public List<OrganRole> selectRolesByUserId(Long userId);
}
