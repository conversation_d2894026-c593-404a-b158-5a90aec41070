package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.AssociationFeeTempRecord;
import com.guangren.business.domain.BankQingSuanOrder;

import java.util.List;

public interface AssociationFeeTempRecordMapper extends BaseMapper<AssociationFeeTempRecord> {
    public int insertAssociationFeeTempRecord(AssociationFeeTempRecord associationFeeTempRecord);

    public int updateAssociationFeeTempRecord(AssociationFeeTempRecord associationFeeTempRecord);

    //查询有效的手续费信息
    public List<AssociationFeeTempRecord> selectSuccessPayAssociationFeeTempRecords(AssociationFeeTempRecord associationFeeTempRecord);

    //查询待释放的手续费信息
    public List<AssociationFeeTempRecord>selectSuccessReleaseAssociationFeeRecords(AssociationFeeTempRecord associationFeeTempRecord);
}
