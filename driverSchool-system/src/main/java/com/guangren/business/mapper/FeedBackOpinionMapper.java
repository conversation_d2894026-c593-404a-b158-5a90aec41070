package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.FeedBackOpinion;

import java.util.List;

/**
 * 反馈意见Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface FeedBackOpinionMapper extends BaseMapper<FeedBackOpinion>
{
    /**
     * 查询反馈意见
     * 
     * @param id 反馈意见主键
     * @return 反馈意见
     */
    public FeedBackOpinion selectFeedBackOpinionById(Long id);

    /**
     * 查询反馈意见列表
     * 
     * @param feedBackOpinion 反馈意见
     * @return 反馈意见集合
     */
    public List<FeedBackOpinion> selectFeedBackOpinionList(FeedBackOpinion feedBackOpinion);

    /**
     * 新增反馈意见
     * 
     * @param feedBackOpinion 反馈意见
     * @return 结果
     */
    public int insertFeedBackOpinion(FeedBackOpinion feedBackOpinion);

    /**
     * 修改反馈意见
     * 
     * @param feedBackOpinion 反馈意见
     * @return 结果
     */
    public int updateFeedBackOpinion(FeedBackOpinion feedBackOpinion);


    /**
    * 验证参数唯一性
    * @return
    */
    public FeedBackOpinion checkUnique(FeedBackOpinion feedBackOpinion);

    /**
     * 删除反馈意见
     * 
     * @param id 反馈意见主键
     * @return 结果
     */
    public int deleteFeedBackOpinionById(Long id);

    /**
     * 批量删除反馈意见
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFeedBackOpinionByIds(String[] ids);
}
