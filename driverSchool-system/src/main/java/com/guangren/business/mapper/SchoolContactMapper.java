package com.guangren.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolContact;
import org.apache.ibatis.annotations.Param;

/**
 * 联系人Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-02-15
 */
public interface SchoolContactMapper extends BaseMapper<SchoolContact>
{
    /**
     * 查询联系人
     * 
     * @param id 联系人主键
     * @return 联系人
     */
    public SchoolContact selectSchoolContactById(String id);

    /**
     * 查询联系人列表
     * 
     * @param schoolContact 联系人
     * @return 联系人集合
     */
    public List<SchoolContact> selectSchoolContactList(SchoolContact schoolContact);

    public List<SchoolContact> selectSchoolContactBySchoolIdList(String schoolId);

    public List<SchoolContact> selectSchoolContactByBranchIdList(String branchId);

    public List<SchoolContact> selectSchoolContactByRegistrationIdList(String registrationId);

    /**
     * 新增联系人
     * 
     * @param schoolContact 联系人
     * @return 结果
     */
    public int insertSchoolContact(SchoolContact schoolContact);

    /**
     * 修改联系人
     * 
     * @param schoolContact 联系人
     * @return 结果
     */
    public int updateSchoolContact(SchoolContact schoolContact);


    /**
    * 验证参数唯一性
    * @return
    */
    public SchoolContact checkUnique(SchoolContact schoolContact);

    /**
     * 删除联系人
     * 
     * @param id 联系人主键
     * @return 结果
     */
    public int deleteSchoolContactById(String id);

    /**
     * 批量删除联系人
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolContactByIds(String[] ids);

    public int deleteSchoolContactBySchoolId(String schoolId);

    public int deleteSchoolContactBySchoolBranchId(@Param("schoolId") String schoolId,@Param("branchId")String branchId);

    public int deleteSchoolContactBySchoolRegistrationId(@Param("schoolId") String schoolId,@Param("branchId")String branchId,@Param("registrationId")String registrationId);

}
