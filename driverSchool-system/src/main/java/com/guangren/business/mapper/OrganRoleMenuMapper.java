package com.guangren.business.mapper;

import java.util.List;
import com.guangren.business.domain.OrganRoleMenu;

/**
 * 机构角色和菜单关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-08
 */
public interface OrganRoleMenuMapper 
{
    /**
     * 查询机构角色和菜单关联
     * 
     * @param roleId 机构角色和菜单关联主键
     * @return 机构角色和菜单关联
     */
    public OrganRoleMenu selectOrganRoleMenuByRoleId(Long roleId);

    /**
     * 查询机构角色和菜单关联列表
     * 
     * @param organRoleMenu 机构角色和菜单关联
     * @return 机构角色和菜单关联集合
     */
    public List<OrganRoleMenu> selectOrganRoleMenuList(OrganRoleMenu organRoleMenu);

    /**
     * 新增机构角色和菜单关联
     * 
     * @param organRoleMenu 机构角色和菜单关联
     * @return 结果
     */
    public int insertOrganRoleMenu(OrganRoleMenu organRoleMenu);

    /**
     * 修改机构角色和菜单关联
     * 
     * @param organRoleMenu 机构角色和菜单关联
     * @return 结果
     */
    public int updateOrganRoleMenu(OrganRoleMenu organRoleMenu);



    /**
     * 删除机构角色和菜单关联
     * 
     * @param roleId 机构角色和菜单关联主键
     * @return 结果
     */
    public int deleteOrganRoleMenuByRoleId(Long roleId);

    /**
     * 批量删除机构角色和菜单关联
     * 
     * @param roleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrganRoleMenuByRoleIds(String[] roleIds);




}
