package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolStudentSIM;
import com.guangren.business.vo.PendingSchoolStudentSimImportVo;
import com.guangren.business.vo.SchoolStudentSimQueryVo;
import com.guangren.business.vo.StudentSimCountVo;
import com.guangren.business.vo.StudentSubsidyImportVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface SchoolStudentSIMMapper extends BaseMapper<SchoolStudentSIM>{
    /**
     * 查询运营数据
     *
     * @param studentId 运营数据主键
     * @return 运营数据
     */
    public SchoolStudentSIM selectSchoolStudentSimByStudentId(String studentId);

    /**
     * 查询运营数据列表
     *
     * @param schoolStudentSim 运营数据
     * @return 运营数据集合
     */
    public List<SchoolStudentSIM> selectSchoolStudentSimList(SchoolStudentSIM schoolStudentSim);
    
    
    @Select("select a.* from t_school_student_sim a left join t_school_student b on a.student_id=b.id ${ew.customSqlSegment}")
    public List<SchoolStudentSIM> customList(@Param("ew") QueryWrapper<SchoolStudentSIM> query);
    
    
    @Select("select count(*) from t_school_student_sim a left join t_school_student b on a.student_id=b.id ${ew.customSqlSegment}")
    public Long customCount(@Param("ew") QueryWrapper<SchoolStudentSIM> query);

    /**
     * 开卡统计数据
     */
    StudentSimCountVo getSchoolStudentSimCount(SchoolStudentSimQueryVo schoolStudentSimQueryVo);

    /**
     * 查询补贴数据
     */
    List<SchoolStudentSIM> selectSchoolStudentSubsidyList(SchoolStudentSIM schoolStudentSim);

    /**
     * 查询待处理数据列表
     */
    List<SchoolStudentSIM> selectPendingStudentSimList(SchoolStudentSIM schoolStudentSim);

    /**
     * 导出运营数据列表
     */
    List<SchoolStudentSIM> exportSchoolStudentSimList(SchoolStudentSimQueryVo schoolStudentSimQueryVo);

    /**
     * 导出补贴数据列表
     */
    List<SchoolStudentSIM> exportSchoolStudentSubsidyList(SchoolStudentSimQueryVo schoolStudentSimQueryVo);

    /**
     * 导出补贴数据列表
     */
    List<SchoolStudentSIM> exportPendingStudentSimList(SchoolStudentSimQueryVo schoolStudentSimQueryVo);

    /**
     * 导入补贴数据
     */
    int importStudentSubsidyData(List<StudentSubsidyImportVo> subsidyImportVoList);

    /**
     * 导入待处理数据
     */
    void importPendingStudentSimData(List<PendingSchoolStudentSimImportVo> pendingSchoolStudentSimImportVos);
}
