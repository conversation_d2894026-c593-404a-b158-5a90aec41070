package com.guangren.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SuperviseExceptionDay;

/**
 * 资金监管异常记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-04-12
 */
public interface SuperviseExceptionDayMapper extends BaseMapper<SuperviseExceptionDay>
{
    /**
     * 查询资金监管异常记录
     * 
     * @param id 资金监管异常记录主键
     * @return 资金监管异常记录
     */
    public SuperviseExceptionDay selectSuperviseExceptionDayById(String id);

    /**
     * 查询资金监管异常记录列表
     * 
     * @param superviseExceptionDay 资金监管异常记录
     * @return 资金监管异常记录集合
     */
    public List<SuperviseExceptionDay> selectSuperviseExceptionDayList(SuperviseExceptionDay superviseExceptionDay);

    /**
     * 新增资金监管异常记录
     * 
     * @param superviseExceptionDay 资金监管异常记录
     * @return 结果
     */
    public int insertSuperviseExceptionDay(SuperviseExceptionDay superviseExceptionDay);

    /**
     * 修改资金监管异常记录
     * 
     * @param superviseExceptionDay 资金监管异常记录
     * @return 结果
     */
    public int updateSuperviseExceptionDay(SuperviseExceptionDay superviseExceptionDay);


    /**
    * 验证参数唯一性
    * @return
    */
    public SuperviseExceptionDay checkUnique(SuperviseExceptionDay superviseExceptionDay);

    /**
     * 删除资金监管异常记录
     * 
     * @param id 资金监管异常记录主键
     * @return 结果
     */
    public int deleteSuperviseExceptionDayById(String id);

    /**
     * 批量删除资金监管异常记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSuperviseExceptionDayByIds(String[] ids);
}
