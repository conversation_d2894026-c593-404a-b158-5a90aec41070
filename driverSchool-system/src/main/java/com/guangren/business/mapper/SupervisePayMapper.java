package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SupervisePay;

import java.util.List;

/**
 * 学员交费总Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface SupervisePayMapper extends BaseMapper<SupervisePay> 
{
    /**
     * 查询学员交费总
     * 
     * @param studentId 学员交费总主键
     * @return 学员交费总
     */
    public SupervisePay selectSupervisePayByStudentId(String studentId);

    /**
     * 查询学员交费总列表
     * 
     * @param supervisePay 学员交费总
     * @return 学员交费总集合
     */
    public List<SupervisePay> selectSupervisePayList(SupervisePay supervisePay);

    /**
     * 导出学员交费总列表
     * <AUTHOR>
     * @date 2023/10/18 11:33
     * @param supervisePay *
     * @return java.util.List<com.guangren.business.domain.SupervisePay> *
     */
    public List<SupervisePay> selectExportSupervisePayList(SupervisePay supervisePay);

    /**
     * 新增学员交费总
     * 
     * @param supervisePay 学员交费总
     * @return 结果
     */
    public int insertSupervisePay(SupervisePay supervisePay);

    /**
     * 修改学员交费总
     * 
     * @param supervisePay 学员交费总
     * @return 结果
     */
    public int updateSupervisePay(SupervisePay supervisePay);


    /**
    * 验证参数唯一性
    * @return
    */
    public SupervisePay checkUnique(SupervisePay supervisePay);

    /**
     * 删除学员交费总
     * 
     * @param studentId 学员交费总主键
     * @return 结果
     */
    public int deleteSupervisePayByStudentId(String studentId);

    /**
     * 批量删除学员交费总
     * 
     * @param studentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupervisePayByStudentIds(String[] studentIds);

    /**
     * 导出释放记录列表
     */
    List<SupervisePay> exportSupervisePayList(SupervisePay supervisePay);
}
