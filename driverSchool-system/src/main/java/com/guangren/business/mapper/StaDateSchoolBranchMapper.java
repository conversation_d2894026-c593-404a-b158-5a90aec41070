package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.StaDateSchoolBranch;

import java.util.List;

/**
 * 按学校日期统计所有指标Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-12
 */
public interface StaDateSchoolBranchMapper extends BaseMapper<StaDateSchoolBranch>
{
    /**
     * 查询按学校日期统计所有指标
     *
     * @param id 按学校日期统计所有指标主键
     * @return 按学校日期统计所有指标
     */
    public StaDateSchoolBranch selectStaDateSchoolBranchById(Long id);

    /**
     * 查询按学校日期统计所有指标列表
     *
     * @param staDateSchoolBranch 按学校日期统计所有指标
     * @return 按学校日期统计所有指标集合
     */
    public List<StaDateSchoolBranch> selectStaDateSchoolBranchList(StaDateSchoolBranch staDateSchoolBranch);

    /**
     * 新增按学校日期统计所有指标
     *
     * @param staDateSchoolBranch 按学校日期统计所有指标
     * @return 结果
     */
    public int insertStaDateSchoolBranch(StaDateSchoolBranch staDateSchoolBranch);

    /**
     * 修改按学校日期统计所有指标
     *
     * @param staDateSchoolBranch 按学校日期统计所有指标
     * @return 结果
     */
    public int updateStaDateSchoolBranch(StaDateSchoolBranch staDateSchoolBranch);


    /**
     * 验证参数唯一性
     * @return
     */
    public StaDateSchoolBranch checkUnique(StaDateSchoolBranch staDateSchoolBranch);

    /**
     * 删除按学校日期统计所有指标
     *
     * @param id 按学校日期统计所有指标主键
     * @return 结果
     */
    public int deleteStaDateSchoolBranchById(Long id);

    /**
     * 批量删除按学校日期统计所有指标
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStaDateSchoolBranchByIds(String[] ids);

    public StaDateSchoolBranch selectSchoolBranchData(String schoolBranchId);

}
