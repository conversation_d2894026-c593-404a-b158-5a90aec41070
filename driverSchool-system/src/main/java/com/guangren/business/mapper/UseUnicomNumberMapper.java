package com.guangren.business.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.UseUnicomNumber;

import java.util.List;

/**
 * 联通号码开卡Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-10-24
 */
public interface UseUnicomNumberMapper extends BaseMapper<UseUnicomNumber>
{
    /**
     * 查询联通号码开卡
     * 
     * @param id 联通号码开卡主键
     * @return 联通号码开卡
     */
    public UseUnicomNumber selectUseUnicomNumberById(String id);

    /**
     * 查询联通号码开卡列表
     * 
     * @param useUnicomNumber 联通号码开卡
     * @return 联通号码开卡集合
     */
    public List<UseUnicomNumber> selectUseUnicomNumberList(UseUnicomNumber useUnicomNumber);

    /**
     * 新增联通号码开卡
     * 
     * @param useUnicomNumber 联通号码开卡
     * @return 结果
     */
    public int insertUseUnicomNumber(UseUnicomNumber useUnicomNumber);

    /**
     * 修改联通号码开卡
     * 
     * @param useUnicomNumber 联通号码开卡
     * @return 结果
     */
    public int updateUseUnicomNumber(UseUnicomNumber useUnicomNumber);


    /**
    * 验证参数唯一性
    * @return
    */
    public UseUnicomNumber checkUnique(UseUnicomNumber useUnicomNumber);

    /**
     * 删除联通号码开卡
     * 
     * @param id 联通号码开卡主键
     * @return 结果
     */
    public int deleteUseUnicomNumberById(String id);

    /**
     * 批量删除联通号码开卡
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUseUnicomNumberByIds(String[] ids);
}
