package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolRegistration;
import com.guangren.business.domain.SchoolTrainingGround;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 报名点Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-02
 */
public interface SchoolRegistrationMapper extends BaseMapper<SchoolRegistration>
{
    /**
     * 查询报名点
     * 
     * @param id 报名点主键
     * @return 报名点
     */
    public SchoolRegistration selectSchoolRegistrationById(String id);

    /**
     * 查询报名点列表
     * 
     * @param schoolRegistration 报名点
     * @return 报名点集合
     */
    public List<SchoolRegistration> selectSchoolRegistrationList(SchoolRegistration schoolRegistration);

    /**
     * 新增报名点
     * 
     * @param schoolRegistration 报名点
     * @return 结果
     */
    public int insertSchoolRegistration(SchoolRegistration schoolRegistration);

    /**
     * 修改报名点
     * 
     * @param schoolRegistration 报名点
     * @return 结果
     */
    public int updateSchoolRegistration(SchoolRegistration schoolRegistration);


    /**
    * 验证参数唯一性
    * @return
    */
    public SchoolRegistration checkUnique(SchoolRegistration schoolRegistration);

    /**
     * 删除报名点
     * 
     * @param id 报名点主键
     * @return 结果
     */
    public int deleteSchoolRegistrationById(String id);

    /**
     * 批量删除报名点
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolRegistrationByIds(String[] ids);

    /**
     * 导出报名点列表
     */
    List<SchoolRegistration> exportSchoolRegistrationList(SchoolRegistration schoolRegistration);

    /**
     * 批量查询列表
     * @param ids ID列表
     * @return
     */
    List<SchoolRegistration>selectInfoByIds(String[] ids);

    /**
     * 批量修改二维码申请状态、申请时间、申请理由
     * @param qrcodeAuditStatus 申请状态0待提交、1待申请、2审核通过、3审核失败、4审核过期
     * @param lastAuditTime 可为null
     * @param qrcodeAuditReason 可为null
     * @param ids 需要修改的ID
     * @return
     */
    int batchUpdateQrCodeAuditStatus(@Param("qrcodeAuditStatus") Integer qrcodeAuditStatus, @Param("lastAuditTime") Date lastAuditTime, @Param("qrcodeAuditReason")String qrcodeAuditReason, @Param("array") String[] ids);
}
