package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.DriverTrainSsoSyncInfo;

import java.util.List;

public interface DriverTrainSsoSyncInfoMapper extends BaseMapper<DriverTrainSsoSyncInfo> {

    /**
     * 查询交通安全平台同步信息
     */
    public DriverTrainSsoSyncInfo selectDriverTrainSsoSyncInfoById(String id);

    /**
     * 查询交通安全平台同步信息列表
     */
    public List<DriverTrainSsoSyncInfo> selectDriverTrainSsoSyncInfoList(DriverTrainSsoSyncInfo driverTrainSsoSyncInfo);

    /**
     * 新增交通安全平台同步信息
     */
    public int insertDriverTrainSsoSyncInfo(DriverTrainSsoSyncInfo driverTrainSsoSyncInfo);

    /**
     * 修改交通安全平台同步信息
     */
    public int updateDriverTrainSsoSyncInfo(DriverTrainSsoSyncInfo driverTrainSsoSyncInfo);

    /**
     * 删除交通安全平台同步信息
     */
    public int deleteDriverTrainSsoSyncInfoById(String id);

    /**
     * 批量删除交通安全平台同步信息
     */
    public int deleteDriverTrainSsoSyncInfoByIds(String[] ids);
}
