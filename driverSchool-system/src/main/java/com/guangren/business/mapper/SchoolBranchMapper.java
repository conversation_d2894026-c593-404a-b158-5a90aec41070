package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolBranch;

import java.util.List;

/**
 * 分校Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-01
 */
public interface SchoolBranchMapper extends BaseMapper<SchoolBranch>
{
    /**
     * 查询分校
     * 
     * @param id 分校主键
     * @return 分校
     */
    public SchoolBranch selectSchoolBranchById(String id);

    /**
     * 查询分校列表
     * 
     * @param schoolBranch 分校
     * @return 分校集合
     */
    public List<SchoolBranch> selectSchoolBranchList(SchoolBranch schoolBranch);

    /**
     * 新增分校
     * 
     * @param schoolBranch 分校
     * @return 结果
     */
    public int insertSchoolBranch(SchoolBranch schoolBranch);

    /**
     * 修改分校
     * 
     * @param schoolBranch 分校
     * @return 结果
     */
    public int updateSchoolBranch(SchoolBranch schoolBranch);


    /**
    * 验证参数唯一性
    * @return
    */
    public SchoolBranch checkUnique(SchoolBranch schoolBranch);

    /**
     * 删除分校
     * 
     * @param id 分校主键
     * @return 结果
     */
    public int deleteSchoolBranchById(String id);

    /**
     * 批量删除分校
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolBranchByIds(String[] ids);

    /**
     * 导出分校列表
     */
    List<SchoolBranch> exportSchoolBranchList(SchoolBranch schoolBranch);
}
