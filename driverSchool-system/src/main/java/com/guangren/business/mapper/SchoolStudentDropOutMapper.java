package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolStudentDropOut;

import java.util.List;

/**
 * 学员退学Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-09
 */
public interface SchoolStudentDropOutMapper extends BaseMapper<SchoolStudentDropOut>
{
    /**
     * 查询学员退学
     * 
     * @param id 学员退学主键
     * @return 学员退学
     */
    public SchoolStudentDropOut selectSchoolStudentDropOutById(String id);

    /**
     * 查询学员退学列表
     * 
     * @param schoolStudentDropOut 学员退学
     * @return 学员退学集合
     */
    public List<SchoolStudentDropOut> selectSchoolStudentDropOutList(SchoolStudentDropOut schoolStudentDropOut);

    /**
     * 新增学员退学
     * 
     * @param schoolStudentDropOut 学员退学
     * @return 结果
     */
    public int insertSchoolStudentDropOut(SchoolStudentDropOut schoolStudentDropOut);

    /**
     * 修改学员退学
     * 
     * @param schoolStudentDropOut 学员退学
     * @return 结果
     */
    public int updateSchoolStudentDropOut(SchoolStudentDropOut schoolStudentDropOut);


    /**
    * 验证参数唯一性
    * @return
    */
    public SchoolStudentDropOut checkUnique(SchoolStudentDropOut schoolStudentDropOut);

    /**
     * 删除学员退学
     * 
     * @param id 学员退学主键
     * @return 结果
     */
    public int deleteSchoolStudentDropOutById(String id);

    /**
     * 批量删除学员退学
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolStudentDropOutByIds(String[] ids);

    /**
     * 查询学员退学列（7天退学学员）
     *
     * @param schoolStudentDropOut 学员退学
     * @return 学员退学集合
     */
    public List<SchoolStudentDropOut> selectSevenDayDropoutSchoolStudentDropOutList(SchoolStudentDropOut schoolStudentDropOut);
}
