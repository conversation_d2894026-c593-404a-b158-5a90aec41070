package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.UnicomNumber;

import java.util.List;

/**
 * 联通号码Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-10-24
 */
public interface UnicomNumberMapper extends BaseMapper<UnicomNumber>
{
    /**
     * 查询联通号码
     * 
     * @param id 联通号码主键
     * @return 联通号码
     */
    public UnicomNumber selectUnicomNumberById(String id);

    /**
     * 查询联通号码列表
     * 
     * @param unicomNumber 联通号码
     * @return 联通号码集合
     */
    public List<UnicomNumber> selectUnicomNumberList(UnicomNumber unicomNumber);

    /**
     * 新增联通号码
     * 
     * @param unicomNumber 联通号码
     * @return 结果
     */
    public int insertUnicomNumber(UnicomNumber unicomNumber);

    /**
     * 修改联通号码
     * 
     * @param unicomNumber 联通号码
     * @return 结果
     */
    public int updateUnicomNumber(UnicomNumber unicomNumber);


    /**
    * 验证参数唯一性
    * @return
    */
    public UnicomNumber checkUnique(UnicomNumber unicomNumber);

    /**
     * 删除联通号码
     * 
     * @param id 联通号码主键
     * @return 结果
     */
    public int deleteUnicomNumberById(String id);

    /**
     * 批量删除联通号码
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUnicomNumberByIds(String[] ids);
}
