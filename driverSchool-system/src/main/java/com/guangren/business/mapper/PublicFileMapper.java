package com.guangren.business.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.PublicFile;

/**
 * 公共文件Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-02-25
 */
public interface PublicFileMapper extends BaseMapper<PublicFile>
{
    /**
     * 查询公共文件
     * 
     * @param id 公共文件主键
     * @return 公共文件
     */
    public PublicFile selectPublicFileById(Long id);

    /**
     * 查询公共文件列表
     * 
     * @param publicFile 公共文件
     * @return 公共文件集合
     */
    public List<PublicFile> selectPublicFileList(PublicFile publicFile);


    /**
     * 根据业务ID和类型查询文件列表
     * @param refrence
     */
    public List<PublicFile> selectListByRefrenceAndType(@Param("refrence") String refrence,@Param("refrenceType") Long refrenceType);


    /**
     * 新增公共文件
     * 
     * @param publicFile 公共文件
     * @return 结果
     */
    public int insertPublicFile(PublicFile publicFile);

    /**
     * 修改公共文件
     * 
     * @param publicFile 公共文件
     * @return 结果
     */
    public int updatePublicFile(PublicFile publicFile);


    /**
    * 验证参数唯一性
    * @return
    */
    public PublicFile checkUnique(PublicFile publicFile);

    /**
     * 删除公共文件
     * 
     * @param id 公共文件主键
     * @return 结果
     */
    public int deletePublicFileById(Long id);

    /**
     * 批量删除公共文件
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePublicFileByIds(String[] ids);

    public PublicFile selectOneByRefrenceAndType(@Param("refrence")String refrence,@Param("refrenceType")Long refrenceType);

    public int clearRefrence(@Param("refrence")String refrence,@Param("fileIds")String fileIds,@Param("refrenceType")Long refrenceType);

    public int batchClearRefrence(@Param("refrence")String refrence,@Param("fileIds")String fileIds,@Param("refrenceType")Long refrenceType);

    public int batchInsert(List<PublicFile> list);
}
