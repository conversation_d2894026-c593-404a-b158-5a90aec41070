package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.RetainedNumber;

import java.util.List;

/**
 * 每日留存人数Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-15
 */
public interface RetainedNumberMapper extends BaseMapper<RetainedNumber>
{
    /**
     * 查询每日留存人数
     * 
     * @param id 每日留存人数主键
     * @return 每日留存人数
     */
    public RetainedNumber selectRetainedNumberById(String id);

    /**
     * 查询每日留存人数列表
     * 
     * @param retainedNumber 每日留存人数
     * @return 每日留存人数集合
     */
    public List<RetainedNumber> selectRetainedNumberList(RetainedNumber retainedNumber);

    /**
     * 新增每日留存人数
     * 
     * @param retainedNumber 每日留存人数
     * @return 结果
     */
    public int insertRetainedNumber(RetainedNumber retainedNumber);

    /**
     * 修改每日留存人数
     * 
     * @param retainedNumber 每日留存人数
     * @return 结果
     */
    public int updateRetainedNumber(RetainedNumber retainedNumber);


    /**
    * 验证参数唯一性
    * @return
    */
    public RetainedNumber checkUnique(RetainedNumber retainedNumber);

    /**
     * 删除每日留存人数
     * 
     * @param id 每日留存人数主键
     * @return 结果
     */
    public int deleteRetainedNumberById(String id);

    /**
     * 批量删除每日留存人数
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRetainedNumberByIds(String[] ids);
}
