package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.RegistrationStudyCenterCode;

import java.util.List;

/**
 * 门店与学时平台签约代码Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
public interface RegistrationStudyCenterCodeMapper extends BaseMapper<RegistrationStudyCenterCode>
{
    /**
     * 查询门店与学时平台签约代码
     * 
     * @param id 门店与学时平台签约代码主键
     * @return 门店与学时平台签约代码
     */
    public RegistrationStudyCenterCode selectRegistrationStudyCenterCodeById(Long id);

    /**
     * 查询门店与学时平台签约代码列表
     * 
     * @param registrationStudyCenterCode 门店与学时平台签约代码
     * @return 门店与学时平台签约代码集合
     */
    public List<RegistrationStudyCenterCode> selectRegistrationStudyCenterCodeList(RegistrationStudyCenterCode registrationStudyCenterCode);

    /**
     * 新增门店与学时平台签约代码
     * 
     * @param registrationStudyCenterCode 门店与学时平台签约代码
     * @return 结果
     */
    public int insertRegistrationStudyCenterCode(RegistrationStudyCenterCode registrationStudyCenterCode);

    /**
     * 修改门店与学时平台签约代码
     * 
     * @param registrationStudyCenterCode 门店与学时平台签约代码
     * @return 结果
     */
    public int updateRegistrationStudyCenterCode(RegistrationStudyCenterCode registrationStudyCenterCode);


    /**
    * 验证参数唯一性
    * @return
    */
    public RegistrationStudyCenterCode checkUnique(RegistrationStudyCenterCode registrationStudyCenterCode);

    /**
     * 删除门店与学时平台签约代码
     * 
     * @param id 门店与学时平台签约代码主键
     * @return 结果
     */
    public int deleteRegistrationStudyCenterCodeById(Long id);

    /**
     * 批量删除门店与学时平台签约代码
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRegistrationStudyCenterCodeByIds(String[] ids);
}
