package com.guangren.business.mapper;

import com.guangren.business.vo.SuperviseFeeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 类描述：
 *
 * @ClassName StaticticsMapper
 * <AUTHOR>
 * @Date 2023/4/3 0003 下午 3:39
 */
public interface StaticticsMapper {
    public List<Integer> getBasicData();
    public List<String> getSuperviseFeeData();

    public List<Map<String, Object>> getStudentBigData();
    public List<Map<String, Object>> getSubjectProportionData();
    public List<Map<String, Object>> getSuperviseStatData();

    public List<Map<String, Object>> getSuperviseBigData();

    /**
     * 平均释放周期
     */
    public Integer getReleaseCycleAverage();

    /**
     * 全市受监管总数据
     */
    public List<Map<String, Object>> getSchoolStudent();

    /**
     * 驾校学员资金监管TOP5
     * @return
     */
    public List<SuperviseFeeVo> getSuperviseByDescList();
    public List<SuperviseFeeVo> getSuperviseByAscList();


    /**
     * 镇街驾校分布情况
     */
    public List<SuperviseFeeVo> getSuperviseByTownDescList(@Param("town")String town);

    public List<SuperviseFeeVo> getSuperviseByTownAscList(@Param("town")String town);


    /**
     * 每个驾校平均释放周期
     */
    public Integer getReleaseCycleAverageBySchoolId(@Param("schoolId")String schoolId);

    /**
        * 镇街资金监管总数
     */
    public Map<String,Object>getSuperviseFeeTotalByTown(@Param("town")String town);

}
