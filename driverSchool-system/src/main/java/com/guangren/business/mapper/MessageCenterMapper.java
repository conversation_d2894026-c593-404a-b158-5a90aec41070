package com.guangren.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.MessageCenter;

/**
 * 留言中心Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-26
 */
public interface MessageCenterMapper extends BaseMapper<MessageCenter>
{
    /**
     * 查询留言中心
     * 
     * @param id 留言中心主键
     * @return 留言中心
     */
    public MessageCenter selectMessageCenterById(String id);

    /**
     * 查询留言中心列表
     * 
     * @param messageCenter 留言中心
     * @return 留言中心集合
     */
    public List<MessageCenter> selectMessageCenterList(MessageCenter messageCenter);

    /**
     * 新增留言中心
     * 
     * @param messageCenter 留言中心
     * @return 结果
     */
    public int insertMessageCenter(MessageCenter messageCenter);

    /**
     * 修改留言中心
     * 
     * @param messageCenter 留言中心
     * @return 结果
     */
    public int updateMessageCenter(MessageCenter messageCenter);


    /**
    * 验证参数唯一性
    * @return
    */
    public MessageCenter checkUnique(MessageCenter messageCenter);

    /**
     * 删除留言中心
     * 
     * @param id 留言中心主键
     * @return 结果
     */
    public int deleteMessageCenterById(String id);

    /**
     * 批量删除留言中心
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMessageCenterByIds(String[] ids);
}
