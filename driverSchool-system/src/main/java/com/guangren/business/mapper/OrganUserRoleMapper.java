package com.guangren.business.mapper;

import java.util.List;
import com.guangren.business.domain.OrganUserRole;

/**
 * 机构用户和角色关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-08
 */
public interface OrganUserRoleMapper 
{
    /**
     * 查询机构用户和角色关联
     * 
     * @param userId 机构用户和角色关联主键
     * @return 机构用户和角色关联
     */
    public OrganUserRole selectOrganUserRoleByUserId(Long userId);

    /**
     * 查询机构用户和角色关联列表
     * 
     * @param organUserRole 机构用户和角色关联
     * @return 机构用户和角色关联集合
     */
    public List<OrganUserRole> selectOrganUserRoleList(OrganUserRole organUserRole);

    /**
     * 新增机构用户和角色关联
     * 
     * @param organUserRole 机构用户和角色关联
     * @return 结果
     */
    public int insertOrganUserRole(OrganUserRole organUserRole);

    /**
     * 修改机构用户和角色关联
     * 
     * @param organUserRole 机构用户和角色关联
     * @return 结果
     */
    public int updateOrganUserRole(OrganUserRole organUserRole);



    /**
     * 删除机构用户和角色关联
     * 
     * @param userId 机构用户和角色关联主键
     * @return 结果
     */
    public int deleteOrganUserRoleByUserId(Long userId);

    /**
     * 批量删除机构用户和角色关联
     * 
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrganUserRoleByUserIds(String[] userIds);

    public int batchUserRole(List<OrganUserRole> list);
}
