package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolStudentStudyTime;

import java.util.List;


public interface SchoolStudentStudyTimeMapper extends BaseMapper<SchoolStudentStudyTime>{
    /**
     * 查询学时数据
     *
     * @param id 学时数据主键
     * @return 学时数据
     */
    public SchoolStudentStudyTime selectSchoolStudentStudyTimeById(String id);

    /**
     * 查询学时数据列表
     *
     * @param schoolStudentStudyTime 学时数据
     * @return 学时数据集合
     */
    public List<SchoolStudentStudyTime> selectSchoolStudentStudyTimeList(SchoolStudentStudyTime schoolStudentStudyTime);

    /**
     * 新增学时数据
     *
     * @param schoolStudentStudyTime 学时数据
     * @return 结果
     */
    public int insertSchoolStudentStudyTime(SchoolStudentStudyTime schoolStudentStudyTime);

    /**
     * 修改学时数据
     *
     * @param schoolStudentStudyTime 学时数据
     * @return 结果
     */
    public int updateSchoolStudentStudyTime(SchoolStudentStudyTime schoolStudentStudyTime);


    /**
     * 验证参数唯一性
     * @return
     */
    public SchoolStudentStudyTime checkUnique(SchoolStudentStudyTime schoolStudentStudyTime);

    /**
     * 删除学时数据
     *
     * @param id 学时数据主键
     * @return 结果
     */
    public int deleteSchoolStudentStudyTimeById(String id);

    /**
     * 批量删除学时数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolStudentStudyTimeByIds(String[] ids);
}
