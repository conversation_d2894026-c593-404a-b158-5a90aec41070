package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.OrganUser;
import com.guangren.common.core.domain.entity.SysUser;

import java.util.List;


public interface OrganUserMapper extends BaseMapper<OrganUser> {
    /**
     * 查询机构用户
     *
     * @param id 机构用户主键
     * @return 机构用户
     */
    public OrganUser selectOrganUserById(Long id);

    /**
     * 查询机构用户列表
     *
     * @param organUser 机构用户
     * @return 机构用户集合
     */
    public List<OrganUser> selectOrganUserList(OrganUser organUser);

    /**
     * 新增机构用户
     *
     * @param organUser 机构用户
     * @return 结果
     */
    public int insertOrganUser(OrganUser organUser);

    /**
     * 修改机构用户
     *
     * @param organUser 机构用户
     * @return 结果
     */
    public int updateOrganUser(OrganUser organUser);


    /**
     * 验证参数唯一性
     * @return
     */
    public OrganUser checkUnique(OrganUser organUser);

    /**
     * 删除机构用户
     *
     * @param id 机构用户主键
     * @return 结果
     */
    public int deleteOrganUserById(String id);

    /**
     * 批量删除机构用户
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrganUserByIds(String[] ids);

    public OrganUser selectUserByLoginName(OrganUser organUser);

    public OrganUser selectUserByTel(OrganUser organUser);

    public List<OrganUser> selectOperateUserList(OrganUser organUser);

    /**
     * 校验手机号码是否唯一
     *
     * @param tel 手机号码
     * @return 结果
     */
    public OrganUser checkPhoneUnique(String tel);

}
