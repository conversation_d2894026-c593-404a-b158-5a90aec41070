package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolContract;

import java.util.List;

/**
 * 驾校合同Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-24
 */
public interface SchoolContractMapper extends BaseMapper<SchoolContract>
{
    /**
     * 查询驾校合同
     * 
     * @param schoolId 驾校合同主键
     * @return 驾校合同
     */
    public SchoolContract selectSchoolContractBySchoolId(String schoolId);

    /**
     * 查询驾校合同列表
     * 
     * @param schoolContract 驾校合同
     * @return 驾校合同集合
     */
    public List<SchoolContract> selectSchoolContractList(SchoolContract schoolContract);

    /**
     * 新增驾校合同
     * 
     * @param schoolContract 驾校合同
     * @return 结果
     */
    public int insertSchoolContract(SchoolContract schoolContract);

    /**
     * 修改驾校合同
     * 
     * @param schoolContract 驾校合同
     * @return 结果
     */
    public int updateSchoolContract(SchoolContract schoolContract);


    /**
    * 验证参数唯一性
    * @return
    */
    public SchoolContract checkUnique(SchoolContract schoolContract);

    /**
     * 删除驾校合同
     * 
     * @param schoolId 驾校合同主键
     * @return 结果
     */
    public int deleteSchoolContractBySchoolId(String schoolId);

    /**
     * 批量删除驾校合同
     * 
     * @param schoolIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolContractBySchoolIds(String[] schoolIds);
}
