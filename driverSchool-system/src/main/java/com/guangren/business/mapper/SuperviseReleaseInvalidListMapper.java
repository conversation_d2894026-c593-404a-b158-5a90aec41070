package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SuperviseReleaseInvalidList;
import com.guangren.business.domain.SuperviseReleaseValidList;
import org.apache.ibatis.annotations.Select;


public interface SuperviseReleaseInvalidListMapper extends BaseMapper<SuperviseReleaseInvalidList> {
    @Select("delete from t_supervise_release_invalid_list")
    public int removeAll();
}
