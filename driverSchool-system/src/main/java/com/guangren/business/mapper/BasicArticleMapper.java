package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.BasicArticle;

import java.util.List;

/**
 * 文章内容Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface BasicArticleMapper extends BaseMapper<BasicArticle>
{
    /**
     * 查询文章内容
     * 
     * @param id 文章内容主键
     * @return 文章内容
     */
    public BasicArticle selectBasicArticleById(String id);

    /**
     * 查询文章内容列表
     * 
     * @param basicArticle 文章内容
     * @return 文章内容集合
     */
    public List<BasicArticle> selectBasicArticleList(BasicArticle basicArticle);

    /**
     * 新增文章内容
     * 
     * @param basicArticle 文章内容
     * @return 结果
     */
    public int insertBasicArticle(BasicArticle basicArticle);

    /**
     * 修改文章内容
     * 
     * @param basicArticle 文章内容
     * @return 结果
     */
    public int updateBasicArticle(BasicArticle basicArticle);


    /**
    * 验证参数唯一性
    * @return
    */
    public BasicArticle checkUnique(BasicArticle basicArticle);

    /**
     * 删除文章内容
     * 
     * @param id 文章内容主键
     * @return 结果
     */
    public int deleteBasicArticleById(String id);

    /**
     * 批量删除文章内容
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBasicArticleByIds(String[] ids);

    /**
     * 查询驾校的文章内容
     * @param schoolId 驾校id
     * @return 结果
     */
    List<BasicArticle> selectBasicArticleBySchoolId(String schoolId);
}
