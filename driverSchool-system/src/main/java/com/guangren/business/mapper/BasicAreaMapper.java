package com.guangren.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.BasicArea;

/**
 * 标准行政区域Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-02-15
 */
public interface BasicAreaMapper extends BaseMapper<BasicArea>
{
    /**
     * 查询标准行政区域
     * 
     * @param id 标准行政区域主键
     * @return 标准行政区域
     */
    public BasicArea selectBasicAreaById(Long id);

    /**
     * 查询标准行政区域列表
     * 
     * @param basicArea 标准行政区域
     * @return 标准行政区域集合
     */
    public List<BasicArea> selectBasicAreaList(BasicArea basicArea);

    /**
     * 新增标准行政区域
     * 
     * @param basicArea 标准行政区域
     * @return 结果
     */
    public int insertBasicArea(BasicArea basicArea);

    /**
     * 修改标准行政区域
     * 
     * @param basicArea 标准行政区域
     * @return 结果
     */
    public int updateBasicArea(BasicArea basicArea);


    /**
    * 验证参数唯一性
    * @return
    */
    public BasicArea checkUnique(BasicArea basicArea);

    /**
     * 删除标准行政区域
     * 
     * @param id 标准行政区域主键
     * @return 结果
     */
    public int deleteBasicAreaById(Long id);

    /**
     * 批量删除标准行政区域
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBasicAreaByIds(String[] ids);
}
