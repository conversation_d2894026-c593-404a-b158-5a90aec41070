package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SuperviseFlow;

import java.util.List;

/**
 * 监管账户流水Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-03
 */
public interface SuperviseFlowMapper extends BaseMapper<SuperviseFlow>
{
    /**
     * 查询监管账户流水
     * 
     * @param id 监管账户流水主键
     * @return 监管账户流水
     */
    public SuperviseFlow selectSuperviseFlowById(String id);

    /**
     * 查询监管账户流水列表
     * 
     * @param superviseFlow 监管账户流水
     * @return 监管账户流水集合
     */
    public List<SuperviseFlow> selectSuperviseFlowList(SuperviseFlow superviseFlow);

    /**
     * 新增监管账户流水
     * 
     * @param superviseFlow 监管账户流水
     * @return 结果
     */
    public int insertSuperviseFlow(SuperviseFlow superviseFlow);

    /**
     * 修改监管账户流水
     * 
     * @param superviseFlow 监管账户流水
     * @return 结果
     */
    public int updateSuperviseFlow(SuperviseFlow superviseFlow);


    /**
    * 验证参数唯一性
    * @return
    */
    public SuperviseFlow checkUnique(SuperviseFlow superviseFlow);

    /**
     * 删除监管账户流水
     * 
     * @param id 监管账户流水主键
     * @return 结果
     */
    public int deleteSuperviseFlowById(String id);

    /**
     * 批量删除监管账户流水
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSuperviseFlowByIds(String[] ids);
}
