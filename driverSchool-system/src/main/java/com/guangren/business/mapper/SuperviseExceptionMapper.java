package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SuperviseException;

import java.util.List;

/**
 * 资金监管异常记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface SuperviseExceptionMapper extends BaseMapper<SuperviseException>
{
    /**
     * 查询资金监管异常记录
     * 
     * @param id 资金监管异常记录主键
     * @return 资金监管异常记录
     */
    public SuperviseException selectSuperviseExceptionById(String id);

    /**
     * 查询资金监管异常记录列表
     * 
     * @param superviseException 资金监管异常记录
     * @return 资金监管异常记录集合
     */
    public List<SuperviseException> selectSuperviseExceptionList(SuperviseException superviseException);

    public List<SuperviseException> selectSuperviseExceptionByReleaseRecordIdList(String releaseRecordId);

    /**
     * 新增资金监管异常记录
     * 
     * @param superviseException 资金监管异常记录
     * @return 结果
     */
    public int insertSuperviseException(SuperviseException superviseException);

    /**
     * 修改资金监管异常记录
     * 
     * @param superviseException 资金监管异常记录
     * @return 结果
     */
    public int updateSuperviseException(SuperviseException superviseException);


    /**
    * 验证参数唯一性
    * @return
    */
    public SuperviseException checkUnique(SuperviseException superviseException);

    /**
     * 删除资金监管异常记录
     * 
     * @param id 资金监管异常记录主键
     * @return 结果
     */
    public int deleteSuperviseExceptionById(String id);

    /**
     * 批量删除资金监管异常记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSuperviseExceptionByIds(String[] ids);
}
