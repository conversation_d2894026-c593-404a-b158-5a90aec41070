package com.guangren.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.StaDate;

/**
 * 按日期统计所有指标Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-12
 */
public interface StaDateMapper extends BaseMapper<StaDate>
{
    /**
     * 查询按日期统计所有指标
     * 
     * @param id 按日期统计所有指标主键
     * @return 按日期统计所有指标
     */
    public StaDate selectStaDateById(Long id);

    /**
     * 查询按日期统计所有指标列表
     * 
     * @param staDate 按日期统计所有指标
     * @return 按日期统计所有指标集合
     */
    public List<StaDate> selectStaDateList(StaDate staDate);

    /**
     * 新增按日期统计所有指标
     * 
     * @param staDate 按日期统计所有指标
     * @return 结果
     */
    public int insertStaDate(StaDate staDate);

    /**
     * 修改按日期统计所有指标
     * 
     * @param staDate 按日期统计所有指标
     * @return 结果
     */
    public int updateStaDate(StaDate staDate);



    /**
     * 删除按日期统计所有指标
     * 
     * @param id 按日期统计所有指标主键
     * @return 结果
     */
    public int deleteStaDateById(Long id);

    /**
     * 批量删除按日期统计所有指标
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStaDateByIds(String[] ids);

    public StaDate getTodayStaDate();

}
