package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.StaDateSchool;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 按学校日期统计所有指标Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-12
 */
public interface StaDateSchoolMapper extends BaseMapper<StaDateSchool>
{
    /**
     * 查询按学校日期统计所有指标
     * 
     * @param id 按学校日期统计所有指标主键
     * @return 按学校日期统计所有指标
     */
    public StaDateSchool selectStaDateSchoolById(Long id);

    /**
     * 查询按学校日期统计所有指标列表
     * 
     * @param staDateSchool 按学校日期统计所有指标
     * @return 按学校日期统计所有指标集合
     */
    public List<StaDateSchool> selectStaDateSchoolList(StaDateSchool staDateSchool);

    /**
     * 新增按学校日期统计所有指标
     * 
     * @param staDateSchool 按学校日期统计所有指标
     * @return 结果
     */
    public int insertStaDateSchool(StaDateSchool staDateSchool);

    /**
     * 修改按学校日期统计所有指标
     * 
     * @param staDateSchool 按学校日期统计所有指标
     * @return 结果
     */
    public int updateStaDateSchool(StaDateSchool staDateSchool);

    /**
     * 删除按学校日期统计所有指标
     * 
     * @param id 按学校日期统计所有指标主键
     * @return 结果
     */
    public int deleteStaDateSchoolById(Long id);

    /**
     * 批量删除按学校日期统计所有指标
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStaDateSchoolByIds(String[] ids);

    public StaDateSchool selectSchoolData(@Param("schoolId")String schoolId,@Param("staDate")String staDate);

    /**
     * 查询驾校监管数据（查询昨天的数据）
     * <AUTHOR>
     * @date 2023/10/16 16:35
     * @return com.guangren.common.core.domain.AjaxResult *
     */
    List<StaDateSchool> selectSchoolSuperviseDataForYesterday(StaDateSchool staDateSchool);

    /**
     * 查询驾校监管数据（根据搜索条件查询）
     * <AUTHOR>
     * @date 2023/10/17 9:09
     * @param staDateSchool
     * @return java.util.List<com.guangren.business.domain.StaDateSchool> *
     */
    List<StaDateSchool> selectSchoolSuperviseData(StaDateSchool staDateSchool);
}
