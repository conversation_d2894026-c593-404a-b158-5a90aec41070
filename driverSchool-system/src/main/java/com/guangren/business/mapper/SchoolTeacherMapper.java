package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolTeacher;

import java.util.List;

/**
 * 教练Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface SchoolTeacherMapper extends BaseMapper<SchoolTeacher>
{
    /**
     * 查询教练
     * 
     * @param id 教练主键
     * @return 教练
     */
    public SchoolTeacher selectSchoolTeacherById(String id);

    /**
     * 查询教练列表
     * 
     * @param schoolTeacher 教练
     * @return 教练集合
     */
    public List<SchoolTeacher> selectSchoolTeacherList(SchoolTeacher schoolTeacher);

    /**
     * 新增教练
     * 
     * @param schoolTeacher 教练
     * @return 结果
     */
    public int insertSchoolTeacher(SchoolTeacher schoolTeacher);

    /**
     * 修改教练
     * 
     * @param schoolTeacher 教练
     * @return 结果
     */
    public int updateSchoolTeacher(SchoolTeacher schoolTeacher);


    /**
    * 验证参数唯一性
    * @return
    */
    public SchoolTeacher checkUnique(SchoolTeacher schoolTeacher);

    /**
     * 删除教练
     * 
     * @param id 教练主键
     * @return 结果
     */
    public int deleteSchoolTeacherById(String id);

    /**
     * 批量删除教练
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolTeacherByIds(String[] ids);

    /**
     * 导出教练列表
     */
    List<SchoolTeacher> exportSchoolTeacherList(SchoolTeacher schoolTeacher);
}
