package com.guangren.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolBusinessLicense;

/**
 * 学校相关执照Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-02-15
 */
public interface SchoolBusinessLicenseMapper extends BaseMapper<SchoolBusinessLicense>
{
    /**
     * 查询学校相关执照
     * 
     * @param id 学校相关执照主键
     * @return 学校相关执照
     */
    public SchoolBusinessLicense selectSchoolBusinessLicenseById(String id);

    /**
     * 查询学校相关执照列表
     * 
     * @param schoolBusinessLicense 学校相关执照
     * @return 学校相关执照集合
     */
    public List<SchoolBusinessLicense> selectSchoolBusinessLicenseList(SchoolBusinessLicense schoolBusinessLicense);

    /**
     * 新增学校相关执照
     * 
     * @param schoolBusinessLicense 学校相关执照
     * @return 结果
     */
    public int insertSchoolBusinessLicense(SchoolBusinessLicense schoolBusinessLicense);

    /**
     * 修改学校相关执照
     * 
     * @param schoolBusinessLicense 学校相关执照
     * @return 结果
     */
    public int updateSchoolBusinessLicense(SchoolBusinessLicense schoolBusinessLicense);


    /**
    * 验证参数唯一性
    * @return
    */
    public SchoolBusinessLicense checkUnique(SchoolBusinessLicense schoolBusinessLicense);

    /**
     * 删除学校相关执照
     * 
     * @param id 学校相关执照主键
     * @return 结果
     */
    public int deleteSchoolBusinessLicenseById(String id);

    /**
     * 批量删除学校相关执照
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolBusinessLicenseByIds(String[] ids);


    public List<SchoolBusinessLicense> selectSchoolBusinessLicenseBySchoolId(String schoolId);

    public List<SchoolBusinessLicense> selectSchoolBusinessLicenseByBranchId(String branchId);

    public List<SchoolBusinessLicense> selectSchoolBusinessLicenseByRegistrationId(String registrationId);


}
