package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolDealFlow;

import java.util.List;

/**
 * 驾校交易流水Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-03
 */
public interface SchoolDealFlowMapper extends BaseMapper<SchoolDealFlow>
{
    /**
     * 查询驾校交易流水
     * 
     * @param id 驾校交易流水主键
     * @return 驾校交易流水
     */
    public SchoolDealFlow selectSchoolDealFlowById(String id);

    /**
     * 查询驾校交易流水列表
     * 
     * @param schoolDealFlow 驾校交易流水
     * @return 驾校交易流水集合
     */
    public List<SchoolDealFlow> selectSchoolDealFlowList(SchoolDealFlow schoolDealFlow);

    /**
     * 新增驾校交易流水
     * 
     * @param schoolDealFlow 驾校交易流水
     * @return 结果
     */
    public int insertSchoolDealFlow(SchoolDealFlow schoolDealFlow);

    /**
     * 修改驾校交易流水
     * 
     * @param schoolDealFlow 驾校交易流水
     * @return 结果
     */
    public int updateSchoolDealFlow(SchoolDealFlow schoolDealFlow);


    /**
    * 验证参数唯一性
    * @return
    */
    public SchoolDealFlow checkUnique(SchoolDealFlow schoolDealFlow);

    /**
     * 删除驾校交易流水
     * 
     * @param id 驾校交易流水主键
     * @return 结果
     */
    public int deleteSchoolDealFlowById(String id);

    /**
     * 批量删除驾校交易流水
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolDealFlowByIds(String[] ids);
}
