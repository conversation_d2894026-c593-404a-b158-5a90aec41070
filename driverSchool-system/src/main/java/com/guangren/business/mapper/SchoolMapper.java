package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.School;
import com.guangren.business.domain.SchoolBusinessLicense;
import com.guangren.business.dto.SchoolTreeDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 驾校管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-02-15
 */
public interface SchoolMapper extends BaseMapper<School>
{
    /**
     * 查询驾校管理
     * 
     * @param id 驾校管理主键
     * @return 驾校管理
     */
    public School selectSchoolById(String id);

    /**
     * 查询驾校管理列表
     * 
     * @param school 驾校管理
     * @return 驾校管理集合
     */
    public List<School> selectSchoolList(School school);

    /**
     * 新增驾校管理
     * 
     * @param school 驾校管理
     * @return 结果
     */
    public int insertSchool(School school);

    /**
     * 修改驾校管理
     * 
     * @param school 驾校管理
     * @return 结果
     */
    public int updateSchool(School school);


    /**
    * 验证参数唯一性
    * @return
    */
    public School checkUnique(School school);

    /**
     * 删除驾校管理
     * 
     * @param id 驾校管理主键
     * @return 结果
     */
    public int deleteSchoolById(String id);

    /**
     * 批量删除驾校管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolByIds(String[] ids);

    /**
     * 批量删除学校相关执照
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolBusinessLicenseBySchoolIds(String[] ids);
    
    /**
     * 批量新增学校相关执照
     * 
     * @param schoolBusinessLicenseList 学校相关执照列表
     * @return 结果
     */
    public int batchSchoolBusinessLicense(List<SchoolBusinessLicense> schoolBusinessLicenseList);
    

    /**
     * 通过驾校管理主键删除学校相关执照信息
     * 
     * @param id 驾校管理ID
     * @return 结果
     */
    public int deleteSchoolBusinessLicenseBySchoolId(String schoolId);

    public int deleteSchoolBusinessLicenseBySchoolBranchId(@Param("schoolId") String schoolId,@Param("branchId")String branchId);

    public int deleteSchoolBusinessLicenseBySchoolRegistrationId(@Param("schoolId") String schoolId,@Param("branchId")String branchId,@Param("registrationId")String registrationId);

    /**
     * 驾校、分校、报名点联动（弃用）
     */
    List<SchoolTreeDTO> schoolThreeData();

    /**
     * 导出驾校列表
     */
    List<School> exportSchoolList(School school);
}
