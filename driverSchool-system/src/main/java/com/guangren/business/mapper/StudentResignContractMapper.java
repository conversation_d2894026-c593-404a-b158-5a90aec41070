package com.guangren.business.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.StudentResignContract;

import java.util.List;

/**
 * 学生重签合同Mapper接口
 */
public interface StudentResignContractMapper extends BaseMapper<StudentResignContract> {


    /**
     * 通过学生id查询学生重签合同信息
     * @param studentId
     * @return
     */
    public StudentResignContract selectStudentResignContractByStudentId(String studentId);


    /**
     * 查询学生重签合同列表
     * @param studentResignContract
     * @return
     */
    public List<StudentResignContract> selectStudentResignContractList(StudentResignContract studentResignContract);

    /**
     * 新增学生重签合同
     * @param studentResignContract
     * @return
     */
    public int insertStudentResignContract(StudentResignContract studentResignContract);

    /**
     * 修改学生重签合同
     * @param studentResignContract
     * @return
     */
    public int updateStudentResignContract(StudentResignContract studentResignContract);

    /**
     * 删除学生重签合同信息
     * @param studentId
     * @return
     */
    public int deletStudentResignContractByStudentId(String studentId);

    /**
     * 批量删除学生重签合同信息
     * @param studentIds
     * @return
     */
    public int deletStudentResignContractByStudentIds(String[] studentIds);
}
