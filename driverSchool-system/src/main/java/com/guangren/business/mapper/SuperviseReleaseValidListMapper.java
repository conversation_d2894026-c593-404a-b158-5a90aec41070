package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SuperviseReleaseValidList;

import java.util.List;

public interface SuperviseReleaseValidListMapper extends BaseMapper<SuperviseReleaseValidList> {

    /**
     * 查询释放明单
     *
     * @param id 释放明单主键
     * @return 释放明单
     */
    public SuperviseReleaseValidList selectSuperviseReleaseValidListById(Long id);

    /**
     * 查询释放明单列表
     *
     * @param superviseReleaseValidList 释放明单
     * @return 释放明单集合
     */
    public List<SuperviseReleaseValidList> selectSuperviseReleaseValidListList(SuperviseReleaseValidList superviseReleaseValidList);

    /**
     * 新增释放明单
     *
     * @param superviseReleaseValidList 释放明单
     * @return 结果
     */
    public int insertSuperviseReleaseValidList(SuperviseReleaseValidList superviseReleaseValidList);

    /**
     * 修改释放明单
     *
     * @param superviseReleaseValidList 释放明单
     * @return 结果
     */
    public int updateSuperviseReleaseValidList(SuperviseReleaseValidList superviseReleaseValidList);


    /**
     * 验证参数唯一性
     * @return
     */
    public SuperviseReleaseValidList checkUnique(SuperviseReleaseValidList superviseReleaseValidList);

    /**
     * 删除释放明单
     *
     * @param id 释放明单主键
     * @return 结果
     */
    public int deleteSuperviseReleaseValidListById(Long id);

    /**
     * 批量删除释放明单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSuperviseReleaseValidListByIds(String[] ids);

}
