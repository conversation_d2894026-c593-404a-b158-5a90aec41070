package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.StaMonthSchoolBranch;

import java.util.List;

/**
 * 按学校日期统计所有指标Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-12
 */
public interface StaMonthSchoolBranchMapper extends BaseMapper<StaMonthSchoolBranch>
{
    /**
     * 查询按学校日期统计所有指标
     *
     * @param id 按学校日期统计所有指标主键
     * @return 按学校日期统计所有指标
     */
    public StaMonthSchoolBranch selectStaMonthSchoolBranchById(Long id);

    /**
     * 查询按学校日期统计所有指标列表
     *
     * @param staMonthSchoolBranch 按学校日期统计所有指标
     * @return 按学校日期统计所有指标集合
     */
    public List<StaMonthSchoolBranch> selectStaMonthSchoolBranchList(StaMonthSchoolBranch staMonthSchoolBranch);

    /**
     * 新增按学校日期统计所有指标
     *
     * @param staMonthSchoolBranch 按学校日期统计所有指标
     * @return 结果
     */
    public int insertStaMonthSchoolBranch(StaMonthSchoolBranch staMonthSchoolBranch);

    /**
     * 修改按学校日期统计所有指标
     *
     * @param staMonthSchoolBranch 按学校日期统计所有指标
     * @return 结果
     */
    public int updateStaMonthSchoolBranch(StaMonthSchoolBranch staMonthSchoolBranch);


    /**
     * 验证参数唯一性
     * @return
     */
    public StaMonthSchoolBranch checkUnique(StaMonthSchoolBranch staMonthSchoolBranch);

    /**
     * 删除按学校日期统计所有指标
     *
     * @param id 按学校日期统计所有指标主键
     * @return 结果
     */
    public int deleteStaMonthSchoolBranchById(Long id);

    /**
     * 批量删除按学校日期统计所有指标
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStaMonthSchoolBranchByIds(String[] ids);
}
