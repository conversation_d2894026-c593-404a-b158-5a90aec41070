package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.WhitelistStudents;

import java.util.List;

/**
 * 白名单学员Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-04
 */
public interface WhitelistStudentsMapper extends BaseMapper<WhitelistStudents>
{
    /**
     * 查询白名单学员
     * 
     * @param id 白名单学员主键
     * @return 白名单学员
     */
    public WhitelistStudents selectWhitelistStudentsById(String id);

    /**
     * 查询白名单学员列表
     * 
     * @param whitelistStudents 白名单学员
     * @return 白名单学员集合
     */
    public List<WhitelistStudents> selectWhitelistStudentsList(WhitelistStudents whitelistStudents);

    /**
     * 新增白名单学员
     * 
     * @param whitelistStudents 白名单学员
     * @return 结果
     */
    public int insertWhitelistStudents(WhitelistStudents whitelistStudents);

    /**
     * 修改白名单学员
     * 
     * @param whitelistStudents 白名单学员
     * @return 结果
     */
    public int updateWhitelistStudents(WhitelistStudents whitelistStudents);


    /**
    * 验证参数唯一性
    * @return
    */
    public WhitelistStudents checkUnique(WhitelistStudents whitelistStudents);

    /**
     * 删除白名单学员
     * 
     * @param id 白名单学员主键
     * @return 结果
     */
    public int deleteWhitelistStudentsById(String id);

    /**
     * 批量删除白名单学员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWhitelistStudentsByIds(String[] ids);
}
