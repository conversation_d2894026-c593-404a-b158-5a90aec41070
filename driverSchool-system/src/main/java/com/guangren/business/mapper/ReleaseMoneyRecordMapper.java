package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.ReleaseMoneyRecord;

import java.util.List;

/**
 * 手动释放资金记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-10-10
 */
public interface ReleaseMoneyRecordMapper extends BaseMapper<ReleaseMoneyRecord>
{
    /**
     * 查询手动释放资金记录
     * 
     * @param id 手动释放资金记录主键
     * @return 手动释放资金记录
     */
    public ReleaseMoneyRecord selectReleaseMoneyRecordById(Long id);

    /**
     * 查询手动释放资金记录列表
     * 
     * @param releaseMoneyRecord 手动释放资金记录
     * @return 手动释放资金记录集合
     */
    public List<ReleaseMoneyRecord> selectReleaseMoneyRecordList(ReleaseMoneyRecord releaseMoneyRecord);

    /**
     * 新增手动释放资金记录
     * 
     * @param releaseMoneyRecord 手动释放资金记录
     * @return 结果
     */
    public int insertReleaseMoneyRecord(ReleaseMoneyRecord releaseMoneyRecord);

    /**
     * 修改手动释放资金记录
     * 
     * @param releaseMoneyRecord 手动释放资金记录
     * @return 结果
     */
    public int updateReleaseMoneyRecord(ReleaseMoneyRecord releaseMoneyRecord);


    /**
    * 验证参数唯一性
    * @return
    */
    public ReleaseMoneyRecord checkUnique(ReleaseMoneyRecord releaseMoneyRecord);

    /**
     * 删除手动释放资金记录
     * 
     * @param id 手动释放资金记录主键
     * @return 结果
     */
    public int deleteReleaseMoneyRecordById(Long id);

    /**
     * 批量删除手动释放资金记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteReleaseMoneyRecordByIds(String[] ids);
}
