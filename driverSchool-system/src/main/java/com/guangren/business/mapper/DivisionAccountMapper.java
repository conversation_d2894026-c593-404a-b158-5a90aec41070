package com.guangren.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.DivisionAccount;

/**
 * 分账账户Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface DivisionAccountMapper extends BaseMapper<DivisionAccount>
{
    /**
     * 查询分账账户
     * 
     * @param id 分账账户主键
     * @return 分账账户
     */
    public DivisionAccount selectDivisionAccountById(Long id);

    /**
     * 查询分账账户列表
     * 
     * @param divisionAccount 分账账户
     * @return 分账账户集合
     */
    public List<DivisionAccount> selectDivisionAccountList(DivisionAccount divisionAccount);

    /**
     * 新增分账账户
     * 
     * @param divisionAccount 分账账户
     * @return 结果
     */
    public int insertDivisionAccount(DivisionAccount divisionAccount);

    /**
     * 修改分账账户
     * 
     * @param divisionAccount 分账账户
     * @return 结果
     */
    public int updateDivisionAccount(DivisionAccount divisionAccount);


    /**
    * 验证参数唯一性
    * @return
    */
    public DivisionAccount checkUnique(DivisionAccount divisionAccount);

    /**
     * 删除分账账户
     * 
     * @param id 分账账户主键
     * @return 结果
     */
    public int deleteDivisionAccountById(Long id);

    /**
     * 批量删除分账账户
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDivisionAccountByIds(String[] ids);
}
