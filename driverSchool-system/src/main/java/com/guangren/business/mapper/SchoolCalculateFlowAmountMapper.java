package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolCalculateFlowAmount;

import java.util.List;

/**
 * 驾校结息流水（留存金额占比）Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-06
 */
public interface SchoolCalculateFlowAmountMapper extends BaseMapper<SchoolCalculateFlowAmount>
{
    /**
     * 查询驾校结息流水（留存金额占比）
     * 
     * @param id 驾校结息流水（留存金额占比）主键
     * @return 驾校结息流水（留存金额占比）
     */
    public SchoolCalculateFlowAmount selectSchoolCalculateFlowAmountById(String id);

    /**
     * 查询驾校结息流水（留存金额占比）列表
     * 
     * @param schoolCalculateFlowAmount 驾校结息流水（留存金额占比）
     * @return 驾校结息流水（留存金额占比）集合
     */
    public List<SchoolCalculateFlowAmount> selectSchoolCalculateFlowAmountList(SchoolCalculateFlowAmount schoolCalculateFlowAmount);

    /**
     * 新增驾校结息流水（留存金额占比）
     * 
     * @param schoolCalculateFlowAmount 驾校结息流水（留存金额占比）
     * @return 结果
     */
    public int insertSchoolCalculateFlowAmount(SchoolCalculateFlowAmount schoolCalculateFlowAmount);

    /**
     * 修改驾校结息流水（留存金额占比）
     * 
     * @param schoolCalculateFlowAmount 驾校结息流水（留存金额占比）
     * @return 结果
     */
    public int updateSchoolCalculateFlowAmount(SchoolCalculateFlowAmount schoolCalculateFlowAmount);


    /**
    * 验证参数唯一性
    * @return
    */
    public SchoolCalculateFlowAmount checkUnique(SchoolCalculateFlowAmount schoolCalculateFlowAmount);

    /**
     * 删除驾校结息流水（留存金额占比）
     * 
     * @param id 驾校结息流水（留存金额占比）主键
     * @return 结果
     */
    public int deleteSchoolCalculateFlowAmountById(String id);

    /**
     * 批量删除驾校结息流水（留存金额占比）
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolCalculateFlowAmountByIds(String[] ids);
}
