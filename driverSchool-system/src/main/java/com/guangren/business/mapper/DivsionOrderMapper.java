package com.guangren.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.DivsionOrder;

/**
 * 分账总订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface DivsionOrderMapper extends BaseMapper<DivsionOrder>
{
    /**
     * 查询分账总订单
     * 
     * @param id 分账总订单主键
     * @return 分账总订单
     */
    public DivsionOrder selectDivsionOrderById(Long id);

    /**
     * 查询分账总订单列表
     * 
     * @param divsionOrder 分账总订单
     * @return 分账总订单集合
     */
    public List<DivsionOrder> selectDivsionOrderList(DivsionOrder divsionOrder);

    /**
     * 新增分账总订单
     * 
     * @param divsionOrder 分账总订单
     * @return 结果
     */
    public int insertDivsionOrder(DivsionOrder divsionOrder);

    /**
     * 修改分账总订单
     * 
     * @param divsionOrder 分账总订单
     * @return 结果
     */
    public int updateDivsionOrder(DivsionOrder divsionOrder);


    /**
    * 验证参数唯一性
    * @return
    */
    public DivsionOrder checkUnique(DivsionOrder divsionOrder);

    /**
     * 删除分账总订单
     * 
     * @param id 分账总订单主键
     * @return 结果
     */
    public int deleteDivsionOrderById(Long id);

    /**
     * 批量删除分账总订单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDivsionOrderByIds(String[] ids);
}
