package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.CalculateFlow;

import java.util.List;

/**
 * 结息统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-17
 */
public interface CalculateFlowMapper extends BaseMapper<CalculateFlow>
{
    /**
     * 查询结息统计
     * 
     * @param id 结息统计主键
     * @return 结息统计
     */
    public CalculateFlow selectCalculateFlowById(String id);

    /**
     * 查询结息统计列表
     * 
     * @param calculateFlow 结息统计
     * @return 结息统计集合
     */
    public List<CalculateFlow> selectCalculateFlowList(CalculateFlow calculateFlow);

    /**
     * 新增结息统计
     * 
     * @param calculateFlow 结息统计
     * @return 结果
     */
    public int insertCalculateFlow(CalculateFlow calculateFlow);

    /**
     * 修改结息统计
     * 
     * @param calculateFlow 结息统计
     * @return 结果
     */
    public int updateCalculateFlow(CalculateFlow calculateFlow);


    /**
    * 验证参数唯一性
    * @return
    */
    public CalculateFlow checkUnique(CalculateFlow calculateFlow);

    /**
     * 删除结息统计
     * 
     * @param id 结息统计主键
     * @return 结果
     */
    public int deleteCalculateFlowById(String id);

    /**
     * 批量删除结息统计
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCalculateFlowByIds(String[] ids);
}
