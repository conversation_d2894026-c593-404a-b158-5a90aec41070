package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolTrainingGround;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 训练场地Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface SchoolTrainingGroundMapper extends BaseMapper<SchoolTrainingGround>
{
    /**
     * 查询训练场地
     * 
     * @param id 训练场地主键
     * @return 训练场地
     */
    public SchoolTrainingGround selectSchoolTrainingGroundById(String id);

    /**
     * 查询训练场地列表
     * 
     * @param schoolTrainingGround 训练场地
     * @return 训练场地集合
     */
    public List<SchoolTrainingGround> selectSchoolTrainingGroundList(SchoolTrainingGround schoolTrainingGround);

    /**
     * 新增训练场地
     * 
     * @param schoolTrainingGround 训练场地
     * @return 结果
     */
    public int insertSchoolTrainingGround(SchoolTrainingGround schoolTrainingGround);

    /**
     * 修改训练场地
     * 
     * @param schoolTrainingGround 训练场地
     * @return 结果
     */
    public int updateSchoolTrainingGround(SchoolTrainingGround schoolTrainingGround);


    /**
    * 验证参数唯一性
    * @return
    */
    public SchoolTrainingGround checkUnique(SchoolTrainingGround schoolTrainingGround);

    /**
     * 删除训练场地
     * 
     * @param id 训练场地主键
     * @return 结果
     */
    public int deleteSchoolTrainingGroundById(String id);

    /**
     * 批量删除训练场地
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolTrainingGroundByIds(String[] ids);

    /**
     * 导出训练场地列表
     */
    List<SchoolTrainingGround> exportSchoolTrainingGroundList(SchoolTrainingGround schoolTrainingGround);

    /**
     * 批量查询训练场地列表
     * @param ids 场地ID列表
     * @return
     */
    List<SchoolTrainingGround>selectInfoByIds(String[] ids);

    /**
     * 批量修改二维码申请状态、申请时间、申请理由
     * @param qrcodeAuditStatus 申请状态0待提交、1待申请、2审核通过、3审核失败、4审核过期
     * @param lastAuditTime 可为null
     * @param qrcodeAuditReason 可为null
     * @param ids 需要修改的ID
     * @return
     */
    int batchUpdateQrCodeAuditStatus(@Param("qrcodeAuditStatus") Integer qrcodeAuditStatus,@Param("lastAuditTime")Date lastAuditTime,@Param("qrcodeAuditReason")String qrcodeAuditReason, @Param("array") String[] ids);
}
