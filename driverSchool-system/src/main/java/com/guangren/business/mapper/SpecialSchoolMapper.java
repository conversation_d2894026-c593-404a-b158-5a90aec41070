package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SpecialSchool;

import java.util.List;

/**
 * 好方向驾校入金参数Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-07
 */
public interface SpecialSchoolMapper extends BaseMapper<SpecialSchool>
{
    /**
     * 查询好方向驾校入金参数
     * 
     * @param id 好方向驾校入金参数主键
     * @return 好方向驾校入金参数
     */
    public SpecialSchool selectHfxConfigById(String id);

    /**
     * 查询好方向驾校入金参数列表
     * 
     * @param hfxConfig 好方向驾校入金参数
     * @return 好方向驾校入金参数集合
     */
    public List<SpecialSchool> selectHfxConfigList(SpecialSchool hfxConfig);

    /**
     * 新增好方向驾校入金参数
     * 
     * @param hfxConfig 好方向驾校入金参数
     * @return 结果
     */
    public int insertHfxConfig(SpecialSchool hfxConfig);

    /**
     * 修改好方向驾校入金参数
     * 
     * @param hfxConfig 好方向驾校入金参数
     * @return 结果
     */
    public int updateHfxConfig(SpecialSchool hfxConfig);


    /**
    * 验证参数唯一性
    * @return
    */
    public SpecialSchool checkUnique(SpecialSchool hfxConfig);

    /**
     * 删除好方向驾校入金参数
     * 
     * @param id 好方向驾校入金参数主键
     * @return 结果
     */
    public int deleteHfxConfigById(String id);

    /**
     * 批量删除好方向驾校入金参数
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHfxConfigByIds(String[] ids);
}
