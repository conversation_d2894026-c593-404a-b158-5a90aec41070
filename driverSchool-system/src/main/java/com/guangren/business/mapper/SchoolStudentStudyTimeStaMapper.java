package com.guangren.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolStudentStudyTimeSta;

/**
 * 学时统计，定时任务统计，有达标时通知银行释放资金Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface SchoolStudentStudyTimeStaMapper extends BaseMapper<SchoolStudentStudyTimeSta>
{
    /**
     * 查询学时统计，定时任务统计，有达标时通知银行释放资金
     * 
     * @param studentId 学时统计，定时任务统计，有达标时通知银行释放资金主键
     * @return 学时统计，定时任务统计，有达标时通知银行释放资金
     */
    public SchoolStudentStudyTimeSta selectSchoolStudentStudyTimeStaByStudentId(String studentId);

    /**
     * 查询学时统计，定时任务统计，有达标时通知银行释放资金列表
     * 
     * @param schoolStudentStudyTimeSta 学时统计，定时任务统计，有达标时通知银行释放资金
     * @return 学时统计，定时任务统计，有达标时通知银行释放资金集合
     */
    public List<SchoolStudentStudyTimeSta> selectSchoolStudentStudyTimeStaList(SchoolStudentStudyTimeSta schoolStudentStudyTimeSta);

    public List<SchoolStudentStudyTimeSta> selectSchoolStudentStudyTimeStaListByStudentId(String studentId);

    /**
     * 新增学时统计，定时任务统计，有达标时通知银行释放资金
     * 
     * @param schoolStudentStudyTimeSta 学时统计，定时任务统计，有达标时通知银行释放资金
     * @return 结果
     */
    public int insertSchoolStudentStudyTimeSta(SchoolStudentStudyTimeSta schoolStudentStudyTimeSta);

    /**
     * 修改学时统计，定时任务统计，有达标时通知银行释放资金
     * 
     * @param schoolStudentStudyTimeSta 学时统计，定时任务统计，有达标时通知银行释放资金
     * @return 结果
     */
    public int updateSchoolStudentStudyTimeSta(SchoolStudentStudyTimeSta schoolStudentStudyTimeSta);


    /**
    * 验证参数唯一性
    * @return
    */
    public SchoolStudentStudyTimeSta checkUnique(SchoolStudentStudyTimeSta schoolStudentStudyTimeSta);

    /**
     * 删除学时统计，定时任务统计，有达标时通知银行释放资金
     * 
     * @param studentId 学时统计，定时任务统计，有达标时通知银行释放资金主键
     * @return 结果
     */
    public int deleteSchoolStudentStudyTimeStaByStudentId(String studentId);

    /**
     * 批量删除学时统计，定时任务统计，有达标时通知银行释放资金
     * 
     * @param studentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolStudentStudyTimeStaByStudentIds(String[] studentIds);
}
