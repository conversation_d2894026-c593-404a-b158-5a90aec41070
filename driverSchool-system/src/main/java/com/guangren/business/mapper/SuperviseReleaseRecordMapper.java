package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SuperviseReleaseRecord;

import java.util.List;

/**
 * 资金监管释放记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface SuperviseReleaseRecordMapper extends BaseMapper<SuperviseReleaseRecord>
{
    /**
     * 查询资金监管释放记录
     * 
     * @param id 资金监管释放记录主键
     * @return 资金监管释放记录
     */
    public SuperviseReleaseRecord selectSuperviseReleaseRecordById(String id);

    /**
     * 查询资金监管释放记录列表
     * 
     * @param superviseReleaseRecord 资金监管释放记录
     * @return 资金监管释放记录集合
     */
    public List<SuperviseReleaseRecord> selectSuperviseReleaseRecordList(SuperviseReleaseRecord superviseReleaseRecord);

    /**
     * 新增资金监管释放记录
     * 
     * @param superviseReleaseRecord 资金监管释放记录
     * @return 结果
     */
    public int insertSuperviseReleaseRecord(SuperviseReleaseRecord superviseReleaseRecord);

    /**
     * 修改资金监管释放记录
     * 
     * @param superviseReleaseRecord 资金监管释放记录
     * @return 结果
     */
    public int updateSuperviseReleaseRecord(SuperviseReleaseRecord superviseReleaseRecord);


    /**
    * 验证参数唯一性
    * @return
    */
    public SuperviseReleaseRecord checkUnique(SuperviseReleaseRecord superviseReleaseRecord);

    /**
     * 删除资金监管释放记录
     * 
     * @param id 资金监管释放记录主键
     * @return 结果
     */
    public int deleteSuperviseReleaseRecordById(String id);

    /**
     * 批量删除资金监管释放记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSuperviseReleaseRecordByIds(String[] ids);

    /**
     * 查询总释放监管资金
     * <AUTHOR>
     * @date 2023/10/16 10:21
     * @return java.lang.String *
     */
    Double selectTotalReleaseFee();
}
