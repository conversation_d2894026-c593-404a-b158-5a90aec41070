package com.guangren.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.DivsionOrderSchool;

/**
 * 分账学校订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface DivsionOrderSchoolMapper extends BaseMapper<DivsionOrderSchool>
{
    /**
     * 查询分账学校订单
     * 
     * @param id 分账学校订单主键
     * @return 分账学校订单
     */
    public DivsionOrderSchool selectDivsionOrderSchoolById(Long id);

    /**
     * 查询分账学校订单列表
     * 
     * @param divsionOrderSchool 分账学校订单
     * @return 分账学校订单集合
     */
    public List<DivsionOrderSchool> selectDivsionOrderSchoolList(DivsionOrderSchool divsionOrderSchool);

    /**
     * 新增分账学校订单
     * 
     * @param divsionOrderSchool 分账学校订单
     * @return 结果
     */
    public int insertDivsionOrderSchool(DivsionOrderSchool divsionOrderSchool);

    /**
     * 修改分账学校订单
     * 
     * @param divsionOrderSchool 分账学校订单
     * @return 结果
     */
    public int updateDivsionOrderSchool(DivsionOrderSchool divsionOrderSchool);


    /**
    * 验证参数唯一性
    * @return
    */
    public DivsionOrderSchool checkUnique(DivsionOrderSchool divsionOrderSchool);

    /**
     * 删除分账学校订单
     * 
     * @param id 分账学校订单主键
     * @return 结果
     */
    public int deleteDivsionOrderSchoolById(Long id);

    /**
     * 批量删除分账学校订单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDivsionOrderSchoolByIds(String[] ids);
}
