package com.guangren.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SuperviseBatchSubmitDetail;

/**
 * 批量提交学员明细Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-22
 */
public interface SuperviseBatchSubmitDetailMapper extends BaseMapper<SuperviseBatchSubmitDetail> {
    /**
     * 查询批量提交学员明细
     *
     * @param id 批量提交学员明细主键
     * @return 批量提交学员明细
     */
    public SuperviseBatchSubmitDetail selectSuperviseBatchSubmitDetailById(String id);

    /**
     * 查询批量提交学员明细列表
     *
     * @param superviseBatchSubmitDetail 批量提交学员明细
     * @return 批量提交学员明细集合
     */
    public List<SuperviseBatchSubmitDetail> selectSuperviseBatchSubmitDetailList(SuperviseBatchSubmitDetail superviseBatchSubmitDetail);

    /**
     * 新增批量提交学员明细
     *
     * @param superviseBatchSubmitDetail 批量提交学员明细
     * @return 结果
     */
    public int insertSuperviseBatchSubmitDetail(SuperviseBatchSubmitDetail superviseBatchSubmitDetail);

    /**
     * 修改批量提交学员明细
     *
     * @param superviseBatchSubmitDetail 批量提交学员明细
     * @return 结果
     */
    public int updateSuperviseBatchSubmitDetail(SuperviseBatchSubmitDetail superviseBatchSubmitDetail);


    /**
     * 验证参数唯一性
     *
     * @return
     */
    public SuperviseBatchSubmitDetail checkUnique(SuperviseBatchSubmitDetail superviseBatchSubmitDetail);

    /**
     * 删除批量提交学员明细
     *
     * @param id 批量提交学员明细主键
     * @return 结果
     */
    public int deleteSuperviseBatchSubmitDetailById(String id);

    /**
     * 批量删除批量提交学员明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSuperviseBatchSubmitDetailByIds(String[] ids);
}
