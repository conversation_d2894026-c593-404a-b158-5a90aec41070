package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolCar;
import com.guangren.business.domain.SchoolRegistration;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 车辆管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-03
 */
public interface SchoolCarMapper extends BaseMapper<SchoolCar>
{
    /**
     * 查询车辆管理
     * 
     * @param id 车辆管理主键
     * @return 车辆管理
     */
    public SchoolCar selectSchoolCarById(String id);

    /**
     * 查询车辆管理列表
     * 
     * @param schoolCar 车辆管理
     * @return 车辆管理集合
     */
    public List<SchoolCar> selectSchoolCarList(SchoolCar schoolCar);

    /**
     * 新增车辆管理
     * 
     * @param schoolCar 车辆管理
     * @return 结果
     */
    public int insertSchoolCar(SchoolCar schoolCar);

    /**
     * 修改车辆管理
     * 
     * @param schoolCar 车辆管理
     * @return 结果
     */
    public int updateSchoolCar(SchoolCar schoolCar);


    /**
    * 验证参数唯一性
    * @return
    */
    public SchoolCar checkUnique(SchoolCar schoolCar);

    /**
     * 删除车辆管理
     * 
     * @param id 车辆管理主键
     * @return 结果
     */
    public int deleteSchoolCarById(String id);

    /**
     * 批量删除车辆管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolCarByIds(String[] ids);

    /**
     * 导出车辆管理列表
     */
    List<SchoolCar> exportSchoolCarList(SchoolCar schoolCar);
    /**
     * 批量查询列表
     * @param ids ID列表
     * @return
     */
    List<SchoolCar>selectInfoByIds(String[] ids);

    /**
     * 批量修改二维码申请状态、申请时间、申请理由
     * @param qrcodeAuditStatus 申请状态0待提交、1待申请、2审核通过、3审核失败、4审核过期
     * @param lastAuditTime 可为null
     * @param qrcodeAuditReason 可为null
     * @param ids 需要修改的ID
     * @return
     */
    int batchUpdateQrCodeAuditStatus(@Param("qrcodeAuditStatus") Integer qrcodeAuditStatus, @Param("lastAuditTime") Date lastAuditTime, @Param("qrcodeAuditReason")String qrcodeAuditReason, @Param("array") String[] ids);

}
