package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolStudentDel;

import java.util.List;

/**
 * 学员删除Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-20
 */
public interface SchoolStudentDelMapper extends BaseMapper<SchoolStudentDel>
{
    /**
     * 查询学员删除
     * 
     * @param id 学员删除主键
     * @return 学员删除
     */
    public SchoolStudentDel selectSchoolStudentDelById(String id);

    /**
     * 查询学员删除列表
     * 
     * @param schoolStudentDel 学员删除
     * @return 学员删除集合
     */
    public List<SchoolStudentDel> selectSchoolStudentDelList(SchoolStudentDel schoolStudentDel);

    /**
     * 新增学员删除
     * 
     * @param schoolStudentDel 学员删除
     * @return 结果
     */
    public int insertSchoolStudentDel(SchoolStudentDel schoolStudentDel);

    /**
     * 修改学员删除
     * 
     * @param schoolStudentDel 学员删除
     * @return 结果
     */
    public int updateSchoolStudentDel(SchoolStudentDel schoolStudentDel);


    /**
    * 验证参数唯一性
    * @return
    */
    public SchoolStudentDel checkUnique(SchoolStudentDel schoolStudentDel);

    /**
     * 删除学员删除
     * 
     * @param id 学员删除主键
     * @return 结果
     */
    public int deleteSchoolStudentDelById(String id);

    /**
     * 批量删除学员删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolStudentDelByIds(String[] ids);
}
