package com.guangren.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SuperviseBatchSubmit;

/**
 * 批量提交Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-22
 */
public interface SuperviseBatchSubmitMapper extends BaseMapper<SuperviseBatchSubmit>
{
    /**
     * 查询批量提交
     *
     * @param orderId 批量提交主键
     * @return 批量提交
     */
    public SuperviseBatchSubmit selectSuperviseBatchSubmitByOrderId(String orderId);

    /**
     * 查询批量提交列表
     *
     * @param superviseBatchSubmit 批量提交
     * @return 批量提交集合
     */
    public List<SuperviseBatchSubmit> selectSuperviseBatchSubmitList(SuperviseBatchSubmit superviseBatchSubmit);

    /**
     * 新增批量提交
     *
     * @param superviseBatchSubmit 批量提交
     * @return 结果
     */
    public int insertSuperviseBatchSubmit(SuperviseBatchSubmit superviseBatchSubmit);

    /**
     * 修改批量提交
     *
     * @param superviseBatchSubmit 批量提交
     * @return 结果
     */
    public int updateSuperviseBatchSubmit(SuperviseBatchSubmit superviseBatchSubmit);

    /**
     * 删除批量提交
     *
     * @param orderId 批量提交主键
     * @return 结果
     */
    public int deleteSuperviseBatchSubmitByOrderId(String orderId);

    /**
     * 批量删除批量提交
     *
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSuperviseBatchSubmitByOrderIds(String[] orderIds);

    public String getTodayMaxNo();

}
