package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolStudyCenter;

import java.util.List;


public interface SchoolStudyCenterMapper extends BaseMapper<SchoolStudyCenter> {
    /**
     * 查询学时签约中心
     *
     * @param schoolId 学时签约中心主键
     * @return 学时签约中心
     */
    public SchoolStudyCenter selectSchoolStudyCenterBySchoolId(String schoolId);

    /**
     * 查询学时签约中心列表
     *
     * @param schoolStudyCenter 学时签约中心
     * @return 学时签约中心集合
     */
    public List<SchoolStudyCenter> selectSchoolStudyCenterList(SchoolStudyCenter schoolStudyCenter);

    /**
     * 新增学时签约中心
     *
     * @param schoolStudyCenter 学时签约中心
     * @return 结果
     */
    public int insertSchoolStudyCenter(SchoolStudyCenter schoolStudyCenter);

    /**
     * 修改学时签约中心
     *
     * @param schoolStudyCenter 学时签约中心
     * @return 结果
     */
    public int updateSchoolStudyCenter(SchoolStudyCenter schoolStudyCenter);


    /**
     * 验证参数唯一性
     * @return
     */
    public SchoolStudyCenter checkUnique(SchoolStudyCenter schoolStudyCenter);

    /**
     * 删除学时签约中心
     *
     * @param schoolId 学时签约中心主键
     * @return 结果
     */
    public int deleteSchoolStudyCenterBySchoolId(String schoolId);

    /**
     * 批量删除学时签约中心
     *
     * @param schoolIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchoolStudyCenterBySchoolIds(String[] schoolIds);

    public int batchInsert(List<SchoolStudyCenter> studyCenterList);
}
