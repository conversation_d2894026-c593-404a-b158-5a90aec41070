package com.guangren.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guangren.business.domain.SchoolRank;

import java.util.List;

public interface SchoolRankMapper extends BaseMapper<SchoolRank> {

    public SchoolRank selectSchoolRankById(String id);

    public List<SchoolRank> selectSchoolRankList(SchoolRank schoolRank);

    public int insertSchoolRank(SchoolRank schoolRank);

    public int updateSchoolRank(SchoolRank schoolRank);

    public int deleteSchoolRankById(String id);

    public int deleteSchoolRankByIds(String[] ids);

}
