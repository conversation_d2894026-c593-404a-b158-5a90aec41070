<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guangren.business.mapper.DivsionOrderSchoolMapper">
    
    <resultMap type="DivsionOrderSchool" id="DivsionOrderSchoolResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="orderTime"    column="order_time"    />
        <result property="schoolId"    column="school_id"    />
        <result property="schoolName"    column="school_name"    />
        <result property="studentId"    column="student_id"    />
        <result property="studentName"    column="student_name"    />
        <result property="studentMobile"    column="student_mobile"    />
        <result property="studentIdcard"    column="student_idcard"    />
        <result property="bankOrderNo"    column="bank_order_no"    />
        <result property="schoolFee"    column="school_fee"    />
        <result property="schoolFactFee"    column="school_fact_fee"    />
        <result property="status"    column="status"    />
        <result property="releaseTime"    column="release_time"    />
        <result property="releaseOrderNo"    column="release_order_no"    />
        <result property="releaseMsg"    column="release_msg"    />
        <result property="releaseAccountNo"    column="release_account_no"    />
        <result property="releaseAccountName"    column="release_account_name"    />
        <result property="releaser"    column="releaser"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updatedTime"    column="updated_time"    />
    </resultMap>

    <sql id="selectDivsionOrderSchoolVo">
        select id, order_id, order_no, order_time, school_id, school_name, student_id, student_name, student_mobile, student_idcard, bank_order_no, school_fee,school_fact_fee, status, release_time, release_order_no, release_msg, release_account_no, release_account_name,releaser, created_time, updated_time from t_divsion_order_school
    </sql>

    <select id="selectDivsionOrderSchoolList" parameterType="DivsionOrderSchool" resultMap="DivsionOrderSchoolResult">
        <include refid="selectDivsionOrderSchoolVo"/>
        <where>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="params.schoolId != null  and params.schoolId != ''">
                and school_id = #{params.schoolId}
            </if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="orderTime != null "> and order_time = #{orderTime}</if>
            <if test="schoolId != null  and schoolId != ''"> and school_id = #{schoolId}</if>
            <if test="schoolName != null  and schoolName != ''"> and school_name like concat('%', #{schoolName}, '%')</if>
            <if test="studentId != null  and studentId != ''"> and student_id = #{studentId}</if>
            <if test="studentName != null  and studentName != ''"> and student_name like concat('%', #{studentName}, '%')</if>
            <if test="studentMobile != null  and studentMobile != ''"> and student_mobile = #{studentMobile}</if>
<!--            <if test="studentIdcard != null  and studentIdcard != ''"> and student_idcard = #{studentIdcard}</if>-->
            <if test="identityList != null and identityList.size() > 0">
                and (
                <foreach collection="identityList" item="identity" separator="or">
                   student_idcard = #{identity}
                </foreach>
                )
            </if>
            <if test="bankOrderNo != null  and bankOrderNo != ''"> and bank_order_no = #{bankOrderNo}</if>
            <if test="schoolFee != null "> and school_fee = #{schoolFee}</if>
            <if test="schoolFactFee != null "> and school_fact_fee = #{schoolFactFee}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="releaseTime != null "> and release_time = #{releaseTime}</if>
            <if test="releaseOrderNo != null  and releaseOrderNo != ''"> and release_order_no = #{releaseOrderNo}</if>
            <if test="releaseMsg != null  and releaseMsg != ''"> and release_msg = #{releaseMsg}</if>
            <if test="releaseAccountNo != null  and releaseAccountNo != ''"> and release_account_no = #{releaseAccountNo}</if>
            <if test="releaseAccountName != null  and releaseAccountName != ''"> and release_account_name like concat('%', #{releaseAccountName}, '%')</if>
            <if test="releaser != null  and releaser != ''"> and releaser = #{releaser}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="updatedTime != null "> and updated_time = #{updatedTime}</if>
            <if test="beginOrderTime != null and beginOrderTime != ''">
                and date_format(order_time,'%Y-%m-%d %H:%i:%s') &gt;= date_format(#{beginOrderTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test="endOrderTime != null and endOrderTime != ''">
                and date_format(order_time,'%Y-%m-%d %H:%i:%s') &lt;= date_format(#{endOrderTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test="beginReleaseTime != null and beginReleaseTime != ''">
                and date_format(release_time,'%Y-%m-%d %H:%i:%s') &gt;= date_format(#{beginReleaseTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test="endReleaseTime != null and endReleaseTime != ''">
                and date_format(release_time,'%Y-%m-%d %H:%i:%s') &lt;= date_format(#{endReleaseTime},'%Y-%m-%d %H:%i:%s')
            </if>
        </where>
        ORDER BY id DESC
    </select>
    
    <select id="selectDivsionOrderSchoolById" parameterType="Long" resultMap="DivsionOrderSchoolResult">
        <include refid="selectDivsionOrderSchoolVo"/>
        where id = #{id}
    </select>

    <select id="checkUnique" parameterType="DivsionOrderSchool" resultMap="DivsionOrderSchoolResult">
        <include refid="selectDivsionOrderSchoolVo"/>
        <where>
                        <if test="orderId != null "> and order_id = #{orderId}</if>
                        <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
                        <if test="orderTime != null "> and order_time = #{orderTime}</if>
                        <if test="schoolId != null  and schoolId != ''"> and school_id = #{schoolId}</if>
                        <if test="schoolName != null  and schoolName != ''"> and school_name like concat('%', #{schoolName}, '%')</if>
                        <if test="studentId != null  and studentId != ''"> and student_id = #{studentId}</if>
                        <if test="studentName != null  and studentName != ''"> and student_name like concat('%', #{studentName}, '%')</if>
                        <if test="studentMobile != null  and studentMobile != ''"> and student_mobile = #{studentMobile}</if>
                        <if test="studentIdcard != null  and studentIdcard != ''"> and student_idcard = #{studentIdcard}</if>
                        <if test="bankOrderNo != null  and bankOrderNo != ''"> and bank_order_no = #{bankOrderNo}</if>
                        <if test="schoolFee != null "> and school_fee = #{schoolFee}</if>
                        <if test="status != null "> and status = #{status}</if>
                        <if test="releaseTime != null "> and release_time = #{releaseTime}</if>
                        <if test="releaseOrderNo != null  and releaseOrderNo != ''"> and release_order_no = #{releaseOrderNo}</if>
                        <if test="releaseMsg != null  and releaseMsg != ''"> and release_msg = #{releaseMsg}</if>
                        <if test="releaseAccountNo != null  and releaseAccountNo != ''"> and release_account_no = #{releaseAccountNo}</if>
                        <if test="releaseAccountName != null  and releaseAccountName != ''"> and release_account_name like concat('%', #{releaseAccountName}, '%')</if>
                        <if test="createdTime != null "> and created_time = #{createdTime}</if>
                        <if test="updatedTime != null "> and updated_time = #{updatedTime}</if>
        </where>
        limit 1
    </select>


        
    <insert id="insertDivsionOrderSchool" parameterType="DivsionOrderSchool" useGeneratedKeys="true" keyProperty="id">
        insert into t_divsion_order_school
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="orderTime != null">order_time,</if>
            <if test="schoolId != null and schoolId != ''">school_id,</if>
            <if test="schoolName != null and schoolName != ''">school_name,</if>
            <if test="studentId != null and studentId != ''">student_id,</if>
            <if test="studentName != null and studentName != ''">student_name,</if>
            <if test="studentMobile != null and studentMobile != ''">student_mobile,</if>
            <if test="studentIdcard != null and studentIdcard != ''">student_idcard,</if>
            <if test="bankOrderNo != null">bank_order_no,</if>
            <if test="schoolFee != null">school_fee,</if>
            <if test="schoolFactFee != null">school_fact_fee,</if>
            <if test="status != null">status,</if>
            <if test="releaseTime != null">release_time,</if>
            <if test="releaseOrderNo != null">release_order_no,</if>
            <if test="releaseMsg != null">release_msg,</if>
            <if test="releaseAccountNo != null">release_account_no,</if>
            <if test="releaseAccountName != null">release_account_name,</if>
            <if test="releaser != null">releaser,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="orderTime != null">#{orderTime},</if>
            <if test="schoolId != null and schoolId != ''">#{schoolId},</if>
            <if test="schoolName != null and schoolName != ''">#{schoolName},</if>
            <if test="studentId != null and studentId != ''">#{studentId},</if>
            <if test="studentName != null and studentName != ''">#{studentName},</if>
            <if test="studentMobile != null and studentMobile != ''">#{studentMobile},</if>
            <if test="studentIdcard != null and studentIdcard != ''">#{studentIdcard},</if>
            <if test="bankOrderNo != null">#{bankOrderNo},</if>
            <if test="schoolFee != null">#{schoolFee},</if>
            <if test="schoolFactFee != null">#{schoolFactFee},</if>
            <if test="status != null">#{status},</if>
            <if test="releaseTime != null">#{releaseTime},</if>
            <if test="releaseOrderNo != null">#{releaseOrderNo},</if>
            <if test="releaseMsg != null">#{releaseMsg},</if>
            <if test="releaseAccountNo != null">#{releaseAccountNo},</if>
            <if test="releaseAccountName != null">#{releaseAccountName},</if>
            <if test="releaser != null">#{releaser},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
         </trim>
    </insert>

    <update id="updateDivsionOrderSchool" parameterType="DivsionOrderSchool">
        update t_divsion_order_school
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="orderTime != null">order_time = #{orderTime},</if>
            <if test="schoolId != null and schoolId != ''">school_id = #{schoolId},</if>
            <if test="schoolName != null and schoolName != ''">school_name = #{schoolName},</if>
            <if test="studentId != null and studentId != ''">student_id = #{studentId},</if>
            <if test="studentName != null and studentName != ''">student_name = #{studentName},</if>
            <if test="studentMobile != null and studentMobile != ''">student_mobile = #{studentMobile},</if>
            <if test="studentIdcard != null and studentIdcard != ''">student_idcard = #{studentIdcard},</if>
            <if test="bankOrderNo != null">bank_order_no = #{bankOrderNo},</if>
            <if test="schoolFee != null">school_fee = #{schoolFee},</if>
            <if test="schoolFactFee != null">school_fact_fee = #{schoolFactFee},</if>
            <if test="status != null">status = #{status},</if>
            <if test="releaseTime != null">release_time = #{releaseTime},</if>
            <if test="releaseOrderNo != null">release_order_no = #{releaseOrderNo},</if>
            <if test="releaseMsg != null">release_msg = #{releaseMsg},</if>
            <if test="releaseAccountNo != null">release_account_no = #{releaseAccountNo},</if>
            <if test="releaseAccountName != null">release_account_name = #{releaseAccountName},</if>
            <if test="releaser != null">releaser = #{releaser},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDivsionOrderSchoolById" parameterType="Long">
        delete from t_divsion_order_school where id = #{id}
    </delete>

    <delete id="deleteDivsionOrderSchoolByIds" parameterType="String">
        delete from t_divsion_order_school where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>