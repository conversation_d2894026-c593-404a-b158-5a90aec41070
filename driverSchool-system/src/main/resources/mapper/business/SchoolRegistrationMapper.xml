<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guangren.business.mapper.SchoolRegistrationMapper">
    
    <resultMap type="SchoolRegistration" id="SchoolRegistrationResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="address"    column="address"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="town"    column="town"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="licenseTypes"    column="license_types"    />
        <result property="chargeModes"    column="charge_modes"    />
        <result property="images"    column="images"    />
        <result property="status"    column="status"    />
        <result property="customerId"    column="customer_id"    />
        <result property="remark"    column="remark"    />
        <result property="schoolId"    column="school_id"    />
        <result property="branchId"    column="branch_id"    />
        <result property="qrcodeAuditStatus" column="qrcode_audit_status" />
        <result property="qrcodeAuditReason" column="qrcode_audit_reason" />
        <result property="lastAuditTime" column="last_audit_time" />
        <result property="createdTime"    column="created_time"    />
        <result property="updatedTime"    column="updated_time"    />
        <result property="level"    column="level"    />
        <result property="complaintTel"    column="complaint_tel"    />
        <result property="businessTime"    column="business_time"    />
        <result property="reviewStudentType"    column="review_student_type"    />
        <result property="gpsAddress" column="gps_address" />
        <association property="school" javaType="com.guangren.business.domain.School">
         	<id property="id" column="school_id"></id>
         	<result property="name" column="school_name" />
         </association>
         
         <association property="branch" javaType="com.guangren.business.domain.SchoolBranch">
         	<id property="id" column="branch_id"></id>
         	<result property="name" column="branch_name" />
         </association>
        
        <collection property="schoolBusinessLicenseList" ofType="com.guangren.business.domain.SchoolBusinessLicense">
         	<id property="id" column="license_id"></id>
         	<result property="no" column="license_no" />
         	<result property="expiration" column="license_expiration" />
         	<result property="isExpiration" column="license_is_expiration" />
         </collection>
         
         <collection property="schoolContactList" ofType="com.guangren.business.domain.SchoolContact">
         	<id property="id" column="contact_id"></id>
         	<result property="name" column="contact_name" />
         	<result property="tel" column="contact_tel" />
         </collection>

        <!-- <association property="school" column="school_id" select="com.guangren.business.mapper.SchoolMapper.selectSchoolById" />
        <association property="branch" column="branch_id" select="com.guangren.business.mapper.SchoolBranchMapper.selectSchoolBranchById"/>
        <collection property="schoolBusinessLicenseList" column="id" select="com.guangren.business.mapper.SchoolBusinessLicenseMapper.selectSchoolBusinessLicenseByRegistrationId"/>
        <collection property="schoolContactList" column="id" select="com.guangren.business.mapper.SchoolContactMapper.selectSchoolContactByRegistrationIdList"/> -->

    </resultMap>


    <resultMap id="SchoolRegistrationDetailResult" type="SchoolRegistration" extends="SchoolRegistrationResult">
        <collection property="imageFileList" column="{refrence=id,refrenceType=image_type}"
                    select="com.guangren.business.mapper.PublicFileMapper.selectListByRefrenceAndType"/>
    </resultMap>

    <sql id="selectSchoolRegistrationVo">
    
    	select a.*,b.id as license_id,b.no as license_no,b.expiration as license_expiration,
        if(datediff(date_format(now(),'%Y-%m-%d'),b.expiration)  <![CDATA[ <= ]]> 0,'1','0') as license_is_expiration,
        c.id as contact_id,c.name as contact_name,c.tel as contact_tel,d.id as school_id,d.name as school_name, e.id as branch_id,e.name as branch_name
        from t_school_registration a
        left join t_school_business_license b on a.id = b.registration_id
        left join t_school_contact c on a.id = c.registration_id
        left join t_school d on a.school_id=d.id
    	left join t_school_branch e on a.branch_id=e.id
    </sql>

    <select id="selectSchoolRegistrationList" parameterType="SchoolRegistration" resultMap="SchoolRegistrationResult">
        <include refid="selectSchoolRegistrationVo"/>
        <where>  
            <if test="name != null  and name != ''"> and a.name like concat('%', #{name}, '%')</if>
            <if test="address != null  and address != ''"> and a.address = #{address}</if>
            <if test="province != null  and province != ''"> and a.province = #{province}</if>
            <if test="city != null  and city != ''"> and a.city = #{city}</if>
            <if test="town != null  and town != ''"> and a.town = #{town}</if>
            <if test="longitude != null  and longitude != ''"> and a.longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''"> and a.latitude = #{latitude}</if>
            <if test="licenseTypes != null  and licenseTypes != ''"> and a.license_types = #{licenseTypes}</if>
            <if test="chargeModes != null  and chargeModes != ''"> and a.charge_modes = #{chargeModes}</if>
            <if test="images != null  and images != ''"> and a.images = #{images}</if>
            <if test="status != null "> and a.status = #{status}</if>
            <if test="customerId != null  and customerId != ''"> and a.customer_id = #{customerId}</if>
            <if test="schoolId != null  and schoolId != ''"> and a.school_id = #{schoolId}</if>
            <if test="branchId != null  and branchId != ''"> and a.branch_id = #{branchId}</if>
            <if test="createdTime != null "> and a.created_time = #{createdTime}</if>
            <if test="updatedTime != null "> and a.updated_time = #{updatedTime}</if>
            <if test="qrcodeAuditStatus != null">
                and a.qrcode_audit_status = #{qrcodeAuditStatus}
            </if>
            <if test="params.beginLastAuditTime != null and params.beginLastAuditTime != ''"><!-- 最近审核开始时间检索 -->
                and date_format(a.last_audit_time,'%y%m%d') &gt;= date_format(#{params.beginLastAuditTime},'%y%m%d')
            </if>
            <if test="params.endLastAuditTime != null and params.endLastAuditTime != ''"><!-- 最近审核结束时间检索 -->
                and date_format(a.last_audit_time,'%y%m%d') &lt;= date_format(#{params.endLastAuditTime},'%y%m%d')
            </if>
            <!--查询二维码已过期（不在当前季度）的数据-->
            <if test="params.isExpired != null and params.isExpired == 1">
                and (a.last_audit_time &lt; DATE_FORMAT(NOW(), '%Y-%m-01') + INTERVAL QUARTER(NOW()) QUARTER - INTERVAL 1 QUARTER
                OR a.last_audit_time &gt; LAST_DAY(DATE_FORMAT(NOW(), '%Y-%m-01') + INTERVAL QUARTER(NOW()) QUARTER - INTERVAL 1 QUARTER))
                OR a.last_audit_time IS NULL;
            </if>

            <if test="params.registrationName != null  and params.registrationName != ''"> and a.name = #{params.registrationName}</if>

            <if test="params.schoolId != null  and params.schoolId != ''">
                and a.school_id = #{params.schoolId}
            </if>
            <if test="params.banchId != null  and params.banchId != ''">
                and a.branch_id = #{params.banchId}
            </if>
        </where>
    </select>
    
    <select id="selectSchoolRegistrationById" parameterType="String" resultMap="SchoolRegistrationDetailResult">
 		select a.*,b.id as license_id,b.no as license_no,b.expiration as license_expiration,
        if(datediff(date_format(now(),'%Y-%m-%d'),b.expiration)  <![CDATA[ <= ]]> 0,'1','0') as license_is_expiration,
        c.id as contact_id,c.name as contact_name,c.tel as contact_tel,d.id as school_id,d.name as school_name, e.id as branch_id,e.name as branch_name,
        ${@com.guangren.common.constant.DictConst@FILE_TYPE_SCHOOL_REGISTRATION_IMG.dict} as image_type
        from t_school_registration a
        left join t_school_business_license b on a.id = b.registration_id
        left join t_school_contact c on a.id = c.registration_id
        left join t_school d on a.school_id=d.id
    	left join t_school_branch e on a.branch_id=e.id
        where a.id = #{id}
    </select>

    <select id="checkUnique" parameterType="SchoolRegistration" resultMap="SchoolRegistrationResult">
        <include refid="selectSchoolRegistrationVo"/>
        <where>
            <if test="name != null  and name != ''"> and a.name = #{name}</if>
            <if test="address != null  and address != ''"> and a.address = #{address}</if>
            <if test="province != null  and province != ''"> and a.province = #{province}</if>
            <if test="city != null  and city != ''"> and a.city = #{city}</if>
            <if test="town != null  and town != ''"> and a.town = #{town}</if>
            <if test="licenseTypes != null  and licenseTypes != ''"> and a.license_types = #{licenseTypes}</if>
            <if test="chargeModes != null  and chargeModes != ''"> and a.charge_modes = #{chargeModes}</if>
            <if test="status != null "> and a.status = #{status}</if>
            <if test="schoolId != null  and schoolId != ''"> and a.school_id = #{schoolId}</if>
            <if test="branchId != null  and branchId != ''"> and a.branch_id = #{branchId}</if>
        </where>
        limit 1
    </select>

    <insert id="insertSchoolRegistration" parameterType="SchoolRegistration">
        insert into t_school_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="gpsAddress != null and gpsAddress != ''">gps_address,</if>
            <if test="province != null and province != ''">province,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="town != null and town != ''">town,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="licenseTypes != null and licenseTypes != ''">license_types,</if>
            <if test="chargeModes != null and chargeModes != ''">charge_modes,</if>
            <if test="images != null">images,</if>
            <if test="status != null">status,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="remark != null">remark,</if>
            <if test="schoolId != null and schoolId != ''">school_id,</if>
            <if test="branchId != null">branch_id,</if>
            <if test="qrcodeAuditStatus != null">qrcode_audit_status,</if>
            <if test="qrcodeAuditReason != null and qrcodeAuditReason != ''">qrcode_audit_reason,</if>
            <if test="lastAuditTime != null">last_audit_time,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="level != null">level,</if>
            <if test="complaintTel != null">complaint_tel,</if>
            <if test="businessTime != null">business_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="gpsAddress != null and gpsAddress != ''">#{gpsAddress},</if>
            <if test="province != null and province != ''">#{province},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="town != null and town != ''">#{town},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="licenseTypes != null and licenseTypes != ''">#{licenseTypes},</if>
            <if test="chargeModes != null and chargeModes != ''">#{chargeModes},</if>
            <if test="images != null">#{images},</if>
            <if test="status != null">#{status},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="schoolId != null and schoolId != ''">#{schoolId},</if>
            <if test="branchId != null">#{branchId},</if>
            <if test="qrcodeAuditStatus != null">#{qrcodeAuditStatus},</if>
            <if test="qrcodeAuditReason != null and qrcodeAuditReason != ''">#{qrcodeAuditReason},</if>
            <if test="lastAuditTime != null">#{lastAuditTime},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="level != null">#{level},</if>
            <if test="complaintTel != null">#{complaintTel},</if>
            <if test="businessTime != null">#{businessTime},</if>
         </trim>
    </insert>

    <update id="updateSchoolRegistration" parameterType="SchoolRegistration">
        update t_school_registration
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="gpsAddress != null and gpsAddress != ''">gps_address = #{gpsAddress},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="town != null and town != ''">town = #{town},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="licenseTypes != null and licenseTypes != ''">license_types = #{licenseTypes},</if>
            <if test="chargeModes != null and chargeModes != ''">charge_modes = #{chargeModes},</if>
            <if test="images != null">images = #{images},</if>
            <if test="status != null">status = #{status},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="schoolId != null and schoolId != ''">school_id = #{schoolId},</if>
            <if test="branchId != null">branch_id = #{branchId},</if>
            <if test="qrcodeAuditStatus != null">qrcode_audit_status=#{qrcodeAuditStatus},</if>
            <if test="qrcodeAuditReason != null and qrcodeAuditReason != ''">qrcode_audit_reason=#{qrcodeAuditReason},</if>
            <if test="lastAuditTime != null">last_audit_time=#{lastAuditTime},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="level != null">level = #{level},</if>
            <if test="complaintTel != null">complaint_tel = #{complaintTel},</if>
            <if test="businessTime != null">business_time = #{businessTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSchoolRegistrationById" parameterType="String">
        delete from t_school_registration where id = #{id}
    </delete>

    <delete id="deleteSchoolRegistrationByIds" parameterType="String">
        delete from t_school_registration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="exportSchoolRegistrationList" resultType="com.guangren.business.domain.SchoolRegistration" resultMap="SchoolRegistrationResult">
        <include refid="selectSchoolRegistrationVo"/>
        <where>
            <if test="name != null  and name != ''"> and a.name like concat('%', #{name}, '%')</if>
            <if test="address != null  and address != ''"> and a.address = #{address}</if>
             <if test="gpsAddress != null  and gpsAddress != ''"> and a.gps_address = #{gpsAddress}</if>
            <if test="province != null  and province != ''"> and a.province = #{province}</if>
            <if test="city != null  and city != ''"> and a.city = #{city}</if>
            <if test="town != null  and town != ''"> and a.town = #{town}</if>
            <if test="longitude != null  and longitude != ''"> and a.longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''"> and a.latitude = #{latitude}</if>
            <if test="licenseTypes != null  and licenseTypes != ''"> and a.license_types = #{licenseTypes}</if>
            <if test="chargeModes != null  and chargeModes != ''"> and a.charge_modes = #{chargeModes}</if>
            <if test="images != null  and images != ''"> and a.images = #{images}</if>
            <if test="status != null "> and a.status = #{status}</if>
            <if test="customerId != null  and customerId != ''"> and a.customer_id = #{customerId}</if>
            <if test="schoolId != null  and schoolId != ''"> and a.school_id = #{schoolId}</if>
            <if test="branchId != null  and branchId != ''"> and a.branch_id = #{branchId}</if>
            <if test="createdTime != null "> and a.created_time = #{createdTime}</if>
            <if test="updatedTime != null "> and a.updated_time = #{updatedTime}</if>

            <if test="params.registrationName != null  and params.registrationName != ''"> and a.name = #{params.registrationName}</if>

            <if test="params.schoolId != null  and params.schoolId != ''">
                and a.school_id = #{params.schoolId}
            </if>
            <if test="params.banchId != null  and params.banchId != ''">
                and a.branch_id = #{params.banchId}
            </if>
        </where>
    </select>
    <!--根据id批量查询信息-->
    <select id="selectInfoByIds" parameterType="String" resultMap="SchoolRegistrationResult">
        select * from t_school_registration where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!--批量更新二维码审核状态-->
    <update id="batchUpdateQrCodeAuditStatus">
        update t_school_registration
        <set>
            qrcode_audit_status = #{qrcodeAuditStatus}
            <if test="lastAuditTime!=null">
                , last_audit_time =#{lastAuditTime}
            </if>
            <if test="qrcodeAuditReason !=null and qrcodeAuditReason != ''">
                , qrcode_audit_reason = #{qrcodeAuditReason}
            </if>
        </set>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>