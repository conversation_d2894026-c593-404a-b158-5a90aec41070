<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guangren.business.mapper.OrganRoleMapper">
    
    <resultMap type="OrganRole" id="OrganRoleResult">
        <result property="roleId"    column="role_id"    />
        <result property="roleName"    column="role_name"    />
        <result property="roleSort"    column="role_sort"    />
        <result property="dataScope"    column="data_scope"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectRoleContactVo">
        select distinct r.* from t_organ_role r
	        left join t_organ_user_role ur on ur.role_id = r.role_id
	        left join t_organ_user u on u.id = ur.user_id
    </sql>


    <sql id="selectOrganRoleVo">
        select role_id, role_name, role_sort, data_scope, status, create_by, create_time, update_by, update_time, remark from t_organ_role
    </sql>

    <select id="selectOrganRoleList" parameterType="OrganRole" resultMap="OrganRoleResult">
        <include refid="selectOrganRoleVo"/>
        <where>  
            <if test="roleName != null  and roleName != ''"> and role_name like concat('%', #{roleName}, '%')</if>
            <if test="roleSort != null "> and role_sort = #{roleSort}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectOrganRoleByRoleId" parameterType="Long" resultMap="OrganRoleResult">
        <include refid="selectOrganRoleVo"/>
        where role_id = #{roleId}
    </select>


    <select id="selectRolesByUserId" parameterType="Long" resultMap="OrganRoleResult">
        <include refid="selectRoleContactVo"/>
        WHERE  ur.user_id = #{userId}
    </select>


    <insert id="insertOrganRole" parameterType="OrganRole" useGeneratedKeys="true" keyProperty="roleId">
        insert into t_organ_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleName != null and roleName != ''">role_name,</if>
            <if test="roleSort != null">role_sort,</if>
            <if test="dataScope != null">data_scope,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleName != null and roleName != ''">#{roleName},</if>
            <if test="roleSort != null">#{roleSort},</if>
            <if test="dataScope != null">#{dataScope},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOrganRole" parameterType="OrganRole">
        update t_organ_role
        <trim prefix="SET" suffixOverrides=",">
            <if test="roleName != null and roleName != ''">role_name = #{roleName},</if>
            <if test="roleSort != null">role_sort = #{roleSort},</if>
            <if test="dataScope != null">data_scope = #{dataScope},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where role_id = #{roleId}
    </update>

    <delete id="deleteOrganRoleByRoleId" parameterType="Long">
        delete from t_organ_role where role_id = #{roleId}
    </delete>

    <delete id="deleteOrganRoleByRoleIds" parameterType="String">
        delete from t_organ_role where role_id in 
        <foreach item="roleId" collection="array" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <insert id="batchRoleMenu">
        insert into t_organ_role_menu(role_id, menu_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.roleId},#{item.menuId})
        </foreach>
    </insert>

</mapper>