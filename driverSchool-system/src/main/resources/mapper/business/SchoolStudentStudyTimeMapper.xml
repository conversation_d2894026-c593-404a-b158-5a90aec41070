<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guangren.business.mapper.SchoolStudentStudyTimeMapper">
    
    <resultMap type="SchoolStudentStudyTime" id="SchoolStudentStudyTimeResult">
        <result property="id"    column="id"    />
        <result property="studentId"    column="student_id"    />
        <result property="teacherName"    column="teacher_name"    />
        <result property="carNo"    column="car_no"    />
        <result property="businessType"    column="business_type"    />
        <result property="licenseType"    column="license_type"    />
        <result property="subjectName"    column="subject_name"    />
        <result property="classType"    column="class_type"    />
        <result property="classSubType"    column="class_sub_type"    />
        <result property="beginTime"    column="begin_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="studyTime"    column="study_time"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="checkValidStudyTime"    column="check_valid_study_time"    />
        <result property="checkInvalidStudyTime"    column="check_invalid_study_time"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updatedTime"    column="updated_time"    />
        <result property="schoolId"    column="school_id"    />
        <result property="branchId"    column="branch_id"    />
        <result property="registrationId"    column="registration_id"    />
        <result property="reason"    column="reason"    />
        <result property="checkTime"    column="check_time"    />
        <result property="legend"    column="legend"    />

        <association property="school" column="school_id" select="com.guangren.business.mapper.SchoolMapper.selectSchoolById"/>
        <association property="branch" column="branch_id" select="com.guangren.business.mapper.SchoolBranchMapper.selectSchoolBranchById"/>
        <association property="registration" column="registration_id" select="com.guangren.business.mapper.SchoolRegistrationMapper.selectSchoolRegistrationById"/>
        <association property="schoolStudent" column="student_id" select="com.guangren.business.mapper.SchoolStudentMapper.selectSchoolStudentById"/>
        <association property="studyTimeSta" column="student_id" select="com.guangren.business.mapper.SchoolStudentStudyTimeStaMapper.selectSchoolStudentStudyTimeStaByStudentId"/>

    </resultMap>

    <sql id="selectSchoolStudentStudyTimeVo">
        select id, student_id, teacher_name, car_no, business_type, license_type, subject_name, class_type, class_sub_type, begin_time, end_time, study_time, check_status, check_valid_study_time, check_invalid_study_time, created_time, updated_time, school_id, branch_id, registration_id, reason, check_time,legend from t_school_student_study_time
    </sql>

    <select id="selectSchoolStudentStudyTimeList" parameterType="SchoolStudentStudyTime" resultMap="SchoolStudentStudyTimeResult">
        select a.* from t_school_student_study_time a
        left join t_school_student b on a.student_id= b.id
        <where>
            <if test="ids != null and ids.size() > 0">
                and a.id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="teacherName != null  and teacherName != ''"> and a.teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="studentId != null  and studentId != ''"> and a.student_id = #{studentId}</if>
            <if test="carNo != null  and carNo != ''"> and a.car_no = #{carNo}</if>
            <if test="businessType != null  and businessType != ''"> and a.business_type = #{businessType}</if>
            <if test="licenseType != null  and licenseType != ''"> and a.license_type = #{licenseType}</if>

            <if test="subjectName != null  and subjectName != ''"> and a.subject_name =#{subjectName}</if>
            <if test="classType != null  and classType != ''"> and a.class_type = #{classType}</if>
            <if test="classSubType != null  and classSubType != ''"> and a.class_sub_type = #{classSubType}</if>
            <if test="beginTime != null "> and a.begin_time = #{beginTime}</if>
            <if test="endTime != null "> and a.end_time = #{endTime}</if>
            <if test="studyTime != null "> and a.study_time = #{studyTime}</if>
            <if test="checkStatus != null "> and a.check_status = #{checkStatus}</if>
            <if test="schoolId != null  and schoolId != ''"> and a.school_id = #{schoolId}</if>
            <if test="branchId != null  and branchId != ''"> and a.branch_id = #{branchId}</if>
            <if test="registrationId != null  and registrationId != ''"> and a.registration_id = #{registrationId}</if>
            <if test="reason != null  and reason != ''"> and a.reason = #{reason}</if>
            <if test="checkTime != null "> and a.check_time = #{checkTime}</if>


            <if test="params.schoolId != null  and params.schoolId != ''">
                and a.school_id = #{params.schoolId}
            </if>
            <if test="params.banchId != null  and params.banchId != ''">
                and a.branch_id = #{params.banchId}
            </if>
            <if test="params.registrationId != null  and params.registrationId != ''">
                and a.registration_id = #{params.registrationId}
            </if>

            <if test="params.identity != null and params.identity != ''">
                and b.identity like concat('%', #{params.identity}, '%')
            </if>

            <if test="params.studentName != null and params.studentName != ''">
                and b.name= #{params.studentName}
            </if>
        </where>
    </select>
    
    <select id="selectSchoolStudentStudyTimeById" parameterType="String" resultMap="SchoolStudentStudyTimeResult">
        <include refid="selectSchoolStudentStudyTimeVo"/>
        where id = #{id}
    </select>

    <select id="checkUnique" parameterType="SchoolStudentStudyTime" resultMap="SchoolStudentStudyTimeResult">
        <include refid="selectSchoolStudentStudyTimeVo"/>
        <where>
                        <if test="studentId != null  and studentId != ''"> and student_id = #{studentId}</if>
                        <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
                        <if test="carNo != null  and carNo != ''"> and car_no = #{carNo}</if>
                        <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
                        <if test="licenseType != null  and licenseType != ''"> and license_type = #{licenseType}</if>
                        <if test="subjectName != null  and subjectName != ''"> and subject_name like concat('%', #{subjectName}, '%')</if>
                        <if test="classType != null  and classType != ''"> and class_type = #{classType}</if>
                        <if test="classSubType != null  and classSubType != ''"> and class_sub_type = #{classSubType}</if>
                        <if test="beginTime != null "> and begin_time = #{beginTime}</if>
                        <if test="endTime != null "> and end_time = #{endTime}</if>
                        <if test="studyTime != null "> and study_time = #{studyTime}</if>
                        <if test="checkStatus != null "> and check_status = #{checkStatus}</if>
                        <if test="checkValidStudyTime != null "> and check_valid_study_time = #{checkValidStudyTime}</if>
                        <if test="checkInvalidStudyTime != null "> and check_invalid_study_time = #{checkInvalidStudyTime}</if>
                        <if test="createdTime != null "> and created_time = #{createdTime}</if>
                        <if test="updatedTime != null "> and updated_time = #{updatedTime}</if>
                        <if test="schoolId != null  and schoolId != ''"> and school_id = #{schoolId}</if>
                        <if test="branchId != null  and branchId != ''"> and branch_id = #{branchId}</if>
                        <if test="registrationId != null  and registrationId != ''"> and registration_id = #{registrationId}</if>
                        <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
                        <if test="checkTime != null "> and check_time = #{checkTime}</if>
        </where>
        limit 1
    </select>


        
    <insert id="insertSchoolStudentStudyTime" parameterType="SchoolStudentStudyTime">
        insert into t_school_student_study_time
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="studentId != null and studentId != ''">student_id,</if>
            <if test="teacherName != null and teacherName != ''">teacher_name,</if>
            <if test="carNo != null and carNo != ''">car_no,</if>
            <if test="businessType != null and businessType != ''">business_type,</if>
            <if test="licenseType != null and licenseType != ''">license_type,</if>
            <if test="subjectName != null">subject_name,</if>
            <if test="classType != null">class_type,</if>
            <if test="classSubType != null">class_sub_type,</if>
            <if test="beginTime != null">begin_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="studyTime != null">study_time,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="checkValidStudyTime != null">check_valid_study_time,</if>
            <if test="checkInvalidStudyTime != null">check_invalid_study_time,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="schoolId != null and schoolId != ''">school_id,</if>
            <if test="branchId != null">branch_id,</if>
            <if test="registrationId != null">registration_id,</if>
            <if test="reason != null">reason,</if>
            <if test="checkTime != null">check_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="studentId != null and studentId != ''">#{studentId},</if>
            <if test="teacherName != null and teacherName != ''">#{teacherName},</if>
            <if test="carNo != null and carNo != ''">#{carNo},</if>
            <if test="businessType != null and businessType != ''">#{businessType},</if>
            <if test="licenseType != null and licenseType != ''">#{licenseType},</if>
            <if test="subjectName != null">#{subjectName},</if>
            <if test="classType != null">#{classType},</if>
            <if test="classSubType != null">#{classSubType},</if>
            <if test="beginTime != null">#{beginTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="studyTime != null">#{studyTime},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="checkValidStudyTime != null">#{checkValidStudyTime},</if>
            <if test="checkInvalidStudyTime != null">#{checkInvalidStudyTime},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="schoolId != null and schoolId != ''">#{schoolId},</if>
            <if test="branchId != null">#{branchId},</if>
            <if test="registrationId != null">#{registrationId},</if>
            <if test="reason != null">#{reason},</if>
            <if test="checkTime != null">#{checkTime},</if>
         </trim>
    </insert>

    <update id="updateSchoolStudentStudyTime" parameterType="SchoolStudentStudyTime">
        update t_school_student_study_time
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null and studentId != ''">student_id = #{studentId},</if>
            <if test="teacherName != null and teacherName != ''">teacher_name = #{teacherName},</if>
            <if test="carNo != null and carNo != ''">car_no = #{carNo},</if>
            <if test="businessType != null and businessType != ''">business_type = #{businessType},</if>
            <if test="licenseType != null and licenseType != ''">license_type = #{licenseType},</if>
            <if test="subjectName != null">subject_name = #{subjectName},</if>
            <if test="classType != null">class_type = #{classType},</if>
            <if test="classSubType != null">class_sub_type = #{classSubType},</if>
            <if test="beginTime != null">begin_time = #{beginTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="studyTime != null">study_time = #{studyTime},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="checkValidStudyTime != null">check_valid_study_time = #{checkValidStudyTime},</if>
            <if test="checkInvalidStudyTime != null">check_invalid_study_time = #{checkInvalidStudyTime},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="schoolId != null and schoolId != ''">school_id = #{schoolId},</if>
            <if test="branchId != null">branch_id = #{branchId},</if>
            <if test="registrationId != null">registration_id = #{registrationId},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSchoolStudentStudyTimeById" parameterType="String">
        delete from t_school_student_study_time where id = #{id}
    </delete>

    <delete id="deleteSchoolStudentStudyTimeByIds" parameterType="String">
        delete from t_school_student_study_time where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>