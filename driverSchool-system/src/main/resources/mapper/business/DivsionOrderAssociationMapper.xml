<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guangren.business.mapper.DivsionOrderAssociationMapper">
    
    <resultMap type="DivsionOrderAssociation" id="DivsionOrderAssociationResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="subOrderNo"    column="sub_order_no"    />
        <result property="orderTime"    column="order_time"    />
        <result property="schoolId"    column="school_id"    />
        <result property="schoolName"    column="school_name"    />
        <result property="branchId"    column="branch_id"    />
        <result property="branchName"    column="branch_name"    />
        <result property="registrationId"    column="registration_id"    />
        <result property="registrationName"    column="registration_name"    />
        <result property="studentId"    column="student_id"    />
        <result property="studentName"    column="student_name"    />
        <result property="studentMobile"    column="student_mobile"    />
        <result property="studentIdcard"    column="student_idcard"    />
        <result property="bankOrderNo"    column="bank_order_no"    />
        <result property="associationFee"    column="association_fee"    />
        <result property="status"    column="status"    />
        <result property="checkTime"    column="check_time"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updatedTime"    column="updated_time"    />
    </resultMap>

    <sql id="selectDivsionOrderAssociationVo">
        select id, order_id, order_no,sub_order_no, order_time, school_id, school_name, branch_id, branch_name, registration_id,
               registration_name, student_id, student_name, student_mobile, student_idcard, bank_order_no, association_fee, status,
               check_time, created_time, updated_time from t_divsion_order_association
    </sql>

    <select id="selectDivsionOrderAssociationList" parameterType="DivsionOrderAssociation" resultMap="DivsionOrderAssociationResult">
        <include refid="selectDivsionOrderAssociationVo"/>
        <where>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="params.schoolId != null  and params.schoolId != ''">
                and school_id = #{params.schoolId}
            </if>
            <if test="params.banchId != null  and params.banchId != ''">
                and branch_id = #{params.banchId}
            </if>
            <if test="params.registrationId != null  and params.registrationId != ''">
                and registration_id = #{params.registrationId}
            </if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="subOrderNo != null  and subOrderNo != ''"> and sub_order_no = #{subOrderNo}</if>
            <if test="orderTime != null "> and order_time = #{orderTime}</if>
            <if test="schoolId != null  and schoolId != ''"> and school_id = #{schoolId}</if>
            <if test="schoolName != null  and schoolName != ''"> and school_name like concat('%', #{schoolName}, '%')</if>
            <if test="branchId != null  and branchId != ''"> and branch_id = #{branchId}</if>
            <if test="branchName != null  and branchName != ''"> and branch_name like concat('%', #{branchName}, '%')</if>
            <if test="registrationId != null  and registrationId != ''"> and registration_id = #{registrationId}</if>
            <if test="registrationName != null  and registrationName != ''"> and registration_name like concat('%', #{registrationName}, '%')</if>
            <if test="studentId != null  and studentId != ''"> and student_id = #{studentId}</if>
            <if test="studentName != null  and studentName != ''"> and student_name like concat('%', #{studentName}, '%')</if>
            <if test="studentMobile != null  and studentMobile != ''"> and student_mobile = #{studentMobile}</if>
            <if test="studentIdcard != null  and studentIdcard != ''"> and student_idcard = #{studentIdcard}</if>
            <if test="bankOrderNo != null  and bankOrderNo != ''"> and bank_order_no = #{bankOrderNo}</if>
            <if test="associationFee != null "> and association_fee = #{associationFee}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="checkTime != null "> and check_time = #{checkTime}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="updatedTime != null "> and updated_time = #{updatedTime}</if>
            <if test="beginOrderTime != null and beginOrderTime != ''">
                and date_format(order_time,'%Y-%m-%d %H:%i:%s') &gt;= date_format(#{beginOrderTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test="endOrderTime != null and endOrderTime != ''">
                and date_format(order_time,'%Y-%m-%d %H:%i:%s') &lt;= date_format(#{endOrderTime},'%Y-%m-%d %H:%i:%s')
            </if>
        </where>
        ORDER BY id DESC
    </select>
    
    <select id="selectDivsionOrderAssociationById" parameterType="Long" resultMap="DivsionOrderAssociationResult">
        <include refid="selectDivsionOrderAssociationVo"/>
        where id = #{id}
    </select>

    <select id="checkUnique" parameterType="DivsionOrderAssociation" resultMap="DivsionOrderAssociationResult">
        <include refid="selectDivsionOrderAssociationVo"/>
        <where>
                        <if test="orderId != null "> and order_id = #{orderId}</if>
                        <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
                        <if test="orderTime != null "> and order_time = #{orderTime}</if>
                        <if test="schoolId != null  and schoolId != ''"> and school_id = #{schoolId}</if>
                        <if test="schoolName != null  and schoolName != ''"> and school_name like concat('%', #{schoolName}, '%')</if>
                        <if test="branchId != null  and branchId != ''"> and branch_id = #{branchId}</if>
                        <if test="branchName != null  and branchName != ''"> and branch_name like concat('%', #{branchName}, '%')</if>
                        <if test="registrationId != null  and registrationId != ''"> and registration_id = #{registrationId}</if>
                        <if test="registrationName != null  and registrationName != ''"> and registration_name like concat('%', #{registrationName}, '%')</if>
                        <if test="studentId != null  and studentId != ''"> and student_id = #{studentId}</if>
                        <if test="studentName != null  and studentName != ''"> and student_name like concat('%', #{studentName}, '%')</if>
                        <if test="studentMobile != null  and studentMobile != ''"> and student_mobile = #{studentMobile}</if>
                        <if test="studentIdcard != null  and studentIdcard != ''"> and student_idcard = #{studentIdcard}</if>
                        <if test="bankOrderNo != null  and bankOrderNo != ''"> and bank_order_no = #{bankOrderNo}</if>
                        <if test="associationFee != null "> and association_fee = #{associationFee}</if>
                        <if test="status != null "> and status = #{status}</if>
                        <if test="checkTime != null "> and check_time = #{checkTime}</if>
                        <if test="createdTime != null "> and created_time = #{createdTime}</if>
                        <if test="updatedTime != null "> and updated_time = #{updatedTime}</if>
        </where>
        limit 1
    </select>


        
    <insert id="insertDivsionOrderAssociation" parameterType="DivsionOrderAssociation" useGeneratedKeys="true" keyProperty="id">
        insert into t_divsion_order_association
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="subOrderNo != null and subOrderNo != ''">sub_order_no,</if>
            <if test="orderTime != null">order_time,</if>
            <if test="schoolId != null and schoolId != ''">school_id,</if>
            <if test="schoolName != null and schoolName != ''">school_name,</if>
            <if test="branchId != null">branch_id,</if>
            <if test="branchName != null">branch_name,</if>
            <if test="registrationId != null and registrationId != ''">registration_id,</if>
            <if test="registrationName != null and registrationName != ''">registration_name,</if>
            <if test="studentId != null and studentId != ''">student_id,</if>
            <if test="studentName != null and studentName != ''">student_name,</if>
            <if test="studentMobile != null and studentMobile != ''">student_mobile,</if>
            <if test="studentIdcard != null and studentIdcard != ''">student_idcard,</if>
            <if test="bankOrderNo != null">bank_order_no,</if>
            <if test="associationFee != null">association_fee,</if>
            <if test="status != null">status,</if>
            <if test="checkTime != null">check_time,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="subOrderNo != null and subOrderNo != ''">#{subOrderNo},</if>
            <if test="orderTime != null">#{orderTime},</if>
            <if test="schoolId != null and schoolId != ''">#{schoolId},</if>
            <if test="schoolName != null and schoolName != ''">#{schoolName},</if>
            <if test="branchId != null">#{branchId},</if>
            <if test="branchName != null">#{branchName},</if>
            <if test="registrationId != null and registrationId != ''">#{registrationId},</if>
            <if test="registrationName != null and registrationName != ''">#{registrationName},</if>
            <if test="studentId != null and studentId != ''">#{studentId},</if>
            <if test="studentName != null and studentName != ''">#{studentName},</if>
            <if test="studentMobile != null and studentMobile != ''">#{studentMobile},</if>
            <if test="studentIdcard != null and studentIdcard != ''">#{studentIdcard},</if>
            <if test="bankOrderNo != null">#{bankOrderNo},</if>
            <if test="associationFee != null">#{associationFee},</if>
            <if test="status != null">#{status},</if>
            <if test="checkTime != null">#{checkTime},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
         </trim>
    </insert>

    <update id="updateDivsionOrderAssociation" parameterType="DivsionOrderAssociation">
        update t_divsion_order_association
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="subOrderNo != null and subOrderNo != ''">sub_order_no = #{subOrderNo},</if>
            <if test="orderTime != null">order_time = #{orderTime},</if>
            <if test="schoolId != null and schoolId != ''">school_id = #{schoolId},</if>
            <if test="schoolName != null and schoolName != ''">school_name = #{schoolName},</if>
            <if test="branchId != null">branch_id = #{branchId},</if>
            <if test="branchName != null">branch_name = #{branchName},</if>
            <if test="registrationId != null and registrationId != ''">registration_id = #{registrationId},</if>
            <if test="registrationName != null and registrationName != ''">registration_name = #{registrationName},</if>
            <if test="studentId != null and studentId != ''">student_id = #{studentId},</if>
            <if test="studentName != null and studentName != ''">student_name = #{studentName},</if>
            <if test="studentMobile != null and studentMobile != ''">student_mobile = #{studentMobile},</if>
            <if test="studentIdcard != null and studentIdcard != ''">student_idcard = #{studentIdcard},</if>
            <if test="bankOrderNo != null">bank_order_no = #{bankOrderNo},</if>
            <if test="associationFee != null">association_fee = #{associationFee},</if>
            <if test="status != null">status = #{status},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDivsionOrderAssociationById" parameterType="Long">
        delete from t_divsion_order_association where id = #{id}
    </delete>

    <delete id="deleteDivsionOrderAssociationByIds" parameterType="String">
        delete from t_divsion_order_association where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>