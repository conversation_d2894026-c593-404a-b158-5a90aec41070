<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guangren.business.mapper.SchoolRankMapper">
    <resultMap id="SchoolRankResult" type="com.guangren.business.domain.SchoolRank">
        <result property="id" column="id"/>
        <result property="schoolRank" column="school_rank"/>
        <result property="schoolId" column="school_id"/>
        <result property="schoolName" column="school_name"/>
        <result property="status" column="status"/>
        <result property="schoolDesc" column="school_desc"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="createBy" column="create_by"/>
    </resultMap>
    <!--构建sql-->
    <sql id="selectSchoolRankVo">
        select
        b.id,
        b.school_rank,
        b.school_id,
        c.school_name,
        b.status,
        b.school_desc,
        b.create_time,
        b.update_time,
        b.update_by,
        b.create_by
            from t_school_rank b
            left join t_school c on b.school_id = c.id and b.status = 1
        from t_school_rank
    </sql>

    <select id="selectSchoolRankList" parameterType="com.guangren.business.domain.SchoolRank"
            resultMap="SchoolRankResult">
        <include refid="selectSchoolRankVo"/>
        <where>
            <if test="schoolId != null and schoolId != ''">
                and b.school_id = #{schoolId}
            </if>
            <if test="schoolName != null and schoolName != ''">
                and c.school_name like concat('%',#{schoolName},'%')
            </if>
            <if test="status != null ">
                and b.status = #{status}
            </if>
        </where>
    </select>

    <select id="selectSchoolRankById" parameterType="String" resultMap="SchoolRankResult">
        <include refid="selectSchoolRankVo"/>
        where b.id = #{id}
    </select>

    <insert id="insertSchoolRank" parameterType="com.guangren.business.domain.SchoolRank">
        insert into t_school_rank
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="schoolRank != null">
                school_rank,
            </if>
            <if test="schoolId != null">
                school_id,
            </if>
            <if test="schoolName != null">
                school_name,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="schoolDesc != null">
                school_desc,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="schoolRank != null">#{schoolRank},</if>
            <if test="schoolId != null">#{schoolId},</if>
            <if test="schoolName != null">#{schoolName},</if>
            <if test="status != null">#{status},</if>
            <if test="schoolDesc != null">#{schoolDesc},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createBy != null">#{createBy},</if>
        </trim>
    </insert>

    <update id="updateSchoolRank" parameterType="com.guangren.business.domain.SchoolRank">
        update t_school_rank
        <set>
            <if test="schoolRank != null">
                school_rank = #{schoolRank},
            </if>
            <if test="schoolId != null">
                school_id = #{schoolId},
            </if>
            <if test="schoolName != null">
                school_name = #{schoolName},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="schoolDesc != null">
                school_desc = #{schoolDesc},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
        </set>
    </update>

</mapper>