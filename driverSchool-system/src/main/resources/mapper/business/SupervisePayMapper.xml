<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guangren.business.mapper.SupervisePayMapper">
    
    <resultMap type="SupervisePay" id="SupervisePayResult">
        <result property="studentId"    column="student_id"    />
        <result property="studyFee"    column="study_fee"    />
        <result property="superviseFee"    column="supervise_fee"    />
        <result property="totalRealFee"    column="total_real_fee"    />
        <result property="restStudyFee"    column="rest_study_fee"    />
        <result property="totalReleaseFee"    column="total_release_fee"    />
        <result property="commissionFee"    column="commission_fee"    />
        <result property="restSuperviseFee"    column="rest_supervise_fee"    />
        <result property="releaseAccountNo"    column="release_account_no"    />
        <result property="releaseAccountName"    column="release_account_name"    />
        <result property="releaseBankName"    column="release_bank_name"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updatedTime"    column="updated_time"    />
        <result property="schoolId"    column="school_id"    />
        <result property="branchId"    column="branch_id"    />
        <result property="registrationId"    column="registration_id"    />
        <result property="subject1"    column="subject1"    />
        <result property="subject1ReleaseFee"    column="subject1ReleaseFee"    />
        <result property="subject1ReleaseDate"    column="subject1ReleaseDate"    />
        <result property="subject2"    column="subject2"    />
        <result property="subject2ReleaseFee"    column="subject2ReleaseFee"    />
        <result property="subject2ReleaseDate"    column="subject2ReleaseDate"    />
        <result property="subject3"    column="subject3"    />
        <result property="subject3ReleaseFee"    column="subject3ReleaseFee"    />
        <result property="subject3ReleaseDate"    column="subject3ReleaseDate"    />
        <result property="subject4"    column="subject4"    />
        <result property="subject4ReleaseFee"    column="subject4ReleaseFee"    />
        <result property="subject4ReleaseDate"    column="subject4ReleaseDate"    />
        <result property="subject5"    column="subject5"    />
        <result property="subject5ReleaseFee"    column="subject5ReleaseFee"    />
        <result property="subject5ReleaseDate"    column="subject5ReleaseDate"    />
        <result property="subject6"    column="subject6"    />
        <result property="subject6ReleaseFee"    column="subject6ReleaseFee"    />
        <result property="subject6ReleaseDate"    column="subject6ReleaseDate"    />

        <association property="schoolStudent" javaType="com.guangren.business.domain.SchoolStudent">
            <result property="id"    column="student_id"    />
            <result property="name"    column="student_name"    />
            <result property="identity"    column="identity"    />
        </association>

        <association property="school" javaType="com.guangren.business.domain.School" >
            <result property="id"    column="school_id"    />
            <result property="name"    column="school_name"    />
        </association>

        <association property="branch" javaType="com.guangren.business.domain.SchoolBranch">
            <result property="id"    column="branch_id"    />
            <result property="name"    column="branch_name"    />
        </association>

        <association property="registration" javaType="com.guangren.business.domain.SchoolRegistration" >
            <result property="id"    column="registration_id"    />
            <result property="name"    column="registration_name"    />
        </association>
    </resultMap>

    <sql id="selectSupervisePayVo">
        select * from t_supervise_pay
    </sql>

    <select id="selectSupervisePayList" parameterType="SupervisePay" resultMap="SupervisePayResult">
        SELECT
        a.*,
        b.subject1,
        b.subject1ReleaseFee,
        b.subject1ReleaseDate,
        b.subject2,
        b.subject2ReleaseFee,
        b.subject2ReleaseDate,
        b.subject3,
        b.subject3ReleaseFee,
        b.subject3ReleaseDate,
        b.subject4,
        b.subject4ReleaseFee,
        b.subject4ReleaseDate,
        b.subject5,
        b.subject5ReleaseFee,
        b.subject5ReleaseDate,
        b.subject6,
        b.subject6ReleaseFee,
        b.subject6ReleaseDate,
        c.name AS student_name,
        c.identity,
        d.name AS school_name,
        e.name AS branch_name,
        f.name AS registration_name
        FROM t_supervise_pay a left join (
        SELECT student_id,
        sum(case `subject_name` when '科目1' then release_fee else 0 end) as subject1ReleaseFee,
        max(case `subject_name` when '科目1' then release_date else '' end) as subject1ReleaseDate,
        max(case `subject_name` when '科目1' then subject_name else '' end) as subject1,
        sum(case `subject_name` when '科目2' then release_fee else 0 end) as subject2ReleaseFee,
        max(case `subject_name` when '科目2' then release_date else '' end) as subject2ReleaseDate,
        max(case `subject_name` when '科目2' then subject_name else '' end) as subject2,
        sum(case `subject_name` when '科目3' then release_fee else 0 end) as subject3ReleaseFee,
        max(case `subject_name` when '科目3' then release_date else '' end) as subject3ReleaseDate,
        max(case `subject_name` when '科目3' then subject_name else '' end) as subject3,
        sum(case `subject_name` when '科目4' then release_fee else 0 end) as subject4ReleaseFee,
        max(case `subject_name` when '科目4' then release_date else '' end) as subject4ReleaseDate,
        max(case `subject_name` when '科目4' then subject_name else '' end) as subject4,
        sum(case `subject_name` when '退学' then release_fee else 0 end) as subject5ReleaseFee,
        max(case `subject_name` when '退学' then release_date else '' end) as subject5ReleaseDate,
        max(case `subject_name` when '退学' then subject_name else '' end) as subject5,
        sum(case `subject_name` when '其他' then release_fee else 0 end) as subject6ReleaseFee,
        max(case `subject_name` when '其他' then release_date else '' end) as subject6ReleaseDate,
        max(case `subject_name` when '其他' then subject_name else '' end) as subject6
        from t_supervise_release_record WHERE is_success = 1
        GROUP BY student_id) b on a.student_id = b.student_id
        left join t_school_student AS c on a.student_id = c.id
        LEFT JOIN t_school AS d ON c.school_id = d.id
        LEFT JOIN t_school_branch AS e ON c.branch_id = e.id
        LEFT JOIN t_school_registration AS f ON c.registration_id = f.id
        <where>
           <if test="ids != null and ids.size() > 0">
               and a.student_id in
               <foreach collection="ids" item="id" open="(" separator="," close=")">
                   #{id}
               </foreach>
           </if>
           <if test="studentId != null  and studentId != ''"> and a.student_id = #{studentId}</if>
            <if test="schoolId != null  and schoolId != ''"> and d.id = #{schoolId}</if>
            <if test="branchId != null  and branchId != ''"> and e.id = #{branchId}</if>
           <if test="registrationId != null  and registrationId != ''"> and f.id = #{registrationId}</if>
            <if test="params.schoolId != null  and params.schoolId != ''">
                and d.id = #{params.schoolId}
            </if>
            <if test="params.banchId != null  and params.banchId != ''">
                and c.branch_id = #{params.banchId}
            </if>
            <if test="params.registrationId != null  and params.registrationId != ''">
                and f.registration_id = #{params.registrationId}
            </if>

<!--            <if test="identity != null and identity != ''">-->
<!--                and c.identity like concat('%', #{identity}, '%')-->
<!--            </if>-->
            <if test="identityList != null and identityList.size() > 0">
                and (
                <foreach collection="identityList" item="identity" separator="or">
                    c.identity like CONCAT('%', #{identity}, '%')
                </foreach>
                )
            </if>

            <if test="studentName != null and studentName != ''">
                and c.name= #{studentName}
            </if>
            <if test="excludeSubject1">
                and b.subject1 IS NOT NULL AND b.subject1 != ''
            </if>
            <if test="excludeSubject2">
                and b.subject2 IS NOT NULL AND b.subject2 != ''
            </if>
            <if test="excludeSubject3">
                and b.subject3 IS NOT NULL AND b.subject3 != ''
            </if>
            <if test="excludeSubject4">
                and b.subject4 IS NOT NULL AND b.subject4 != ''
            </if>
            <if test="excludeSubject5">
                and b.subject5 IS NOT NULL AND b.subject5 != ''
            </if>
            <if test="excludeSubject6">
                and b.subject6 IS NOT NULL AND b.subject6 != ''
            </if>
           <if test="beginSuperviseTime != null and beginSuperviseTime != ''"><!-- 开始时间检索 -->
               AND date_format(a.created_time,'%Y-%m-%d %H:%i:%s') &gt;= date_format(#{beginSuperviseTime},'%Y-%m-%d %H:%i:%s')
           </if>
           <if test="endSuperviseTime != null and endSuperviseTime != ''"><!-- 结束时间检索 -->
               AND date_format(a.created_time,'%Y-%m-%d %H:%i:%s') &lt;= date_format(#{endSuperviseTime},'%Y-%m-%d %H:%i:%s')
           </if>
            <if test="beginReleaseDate != null and beginReleaseDate != ''">
                AND (
                DATE_FORMAT(b.subject1ReleaseDate,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{beginReleaseDate},'%Y-%m-%d %H:%i:%s')
                OR DATE_FORMAT(b.subject2ReleaseDate,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{beginReleaseDate},'%Y-%m-%d %H:%i:%s')
                OR DATE_FORMAT(b.subject3ReleaseDate,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{beginReleaseDate},'%Y-%m-%d %H:%i:%s')
                OR DATE_FORMAT(b.subject4ReleaseDate,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{beginReleaseDate},'%Y-%m-%d %H:%i:%s')
                OR DATE_FORMAT(b.subject5ReleaseDate,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{beginReleaseDate},'%Y-%m-%d %H:%i:%s')
                OR DATE_FORMAT(b.subject6ReleaseDate,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{beginReleaseDate},'%Y-%m-%d %H:%i:%s')
                )
            </if>
            <if test="endReleaseDate != null and endReleaseDate != ''">
                AND (
                DATE_FORMAT(b.subject1ReleaseDate,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endReleaseDate},'%Y-%m-%d %H:%i:%s')
                OR DATE_FORMAT(b.subject2ReleaseDate,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endReleaseDate},'%Y-%m-%d %H:%i:%s')
                OR DATE_FORMAT(b.subject3ReleaseDate,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endReleaseDate},'%Y-%m-%d %H:%i:%s')
                OR DATE_FORMAT(b.subject4ReleaseDate,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endReleaseDate},'%Y-%m-%d %H:%i:%s')
                OR DATE_FORMAT(b.subject5ReleaseDate,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endReleaseDate},'%Y-%m-%d %H:%i:%s')
                OR DATE_FORMAT(b.subject6ReleaseDate,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endReleaseDate},'%Y-%m-%d %H:%i:%s')
                )
            </if>
            <choose>
                <when test="beginReleaseFee != null and endReleaseFee != null">
                    AND (b.subject1ReleaseFee BETWEEN #{beginReleaseFee} AND #{endReleaseFee}
                    OR b.subject2ReleaseFee BETWEEN #{beginReleaseFee} AND #{endReleaseFee}
                    OR b.subject3ReleaseFee BETWEEN #{beginReleaseFee} AND #{endReleaseFee}
                    OR b.subject4ReleaseFee BETWEEN #{beginReleaseFee} AND #{endReleaseFee}
                    OR b.subject5ReleaseFee BETWEEN #{beginReleaseFee} AND #{endReleaseFee}
                    OR b.subject6ReleaseFee BETWEEN #{beginReleaseFee} AND #{endReleaseFee})
                </when>
                <when test="beginReleaseFee != null">
                    AND (b.subject1ReleaseFee &gt;= #{beginReleaseFee}
                    OR b.subject2ReleaseFee &gt;= #{beginReleaseFee}
                    OR b.subject3ReleaseFee &gt;= #{beginReleaseFee}
                    OR b.subject4ReleaseFee &gt;= #{beginReleaseFee}
                    OR b.subject5ReleaseFee &gt;= #{beginReleaseFee}
                    OR b.subject6ReleaseFee &gt;= #{beginReleaseFee})
                </when>
                <when test="endReleaseFee != null">
                    AND (b.subject1ReleaseFee &lt;= #{endReleaseFee}
                    OR b.subject2ReleaseFee &lt;= #{endReleaseFee}
                    OR b.subject3ReleaseFee &lt;= #{endReleaseFee}
                    OR b.subject4ReleaseFee &lt;= #{endReleaseFee}
                    OR b.subject5ReleaseFee &lt;= #{endReleaseFee}
                    OR b.subject6ReleaseFee &lt;= #{endReleaseFee})
                </when>
            </choose>
        </where>
        order by a.created_time desc
    </select>
    
    <select id="selectSupervisePayByStudentId" parameterType="String" resultMap="SupervisePayResult">
        <include refid="selectSupervisePayVo"/>
        where student_id = #{studentId}
    </select>

    <select id="checkUnique" parameterType="SupervisePay" resultMap="SupervisePayResult">
        <include refid="selectSupervisePayVo"/>
        <where>
                        <if test="studyFee != null "> and study_fee = #{studyFee}</if>
                        <if test="superviseFee != null "> and supervise_fee = #{superviseFee}</if>
                        <if test="totalRealFee != null "> and total_real_fee = #{totalRealFee}</if>
                        <if test="restStudyFee != null "> and rest_study_fee = #{restStudyFee}</if>
                        <if test="totalReleaseFee != null "> and total_release_fee = #{totalReleaseFee}</if>
                        <if test="restSuperviseFee != null "> and rest_supervise_fee = #{restSuperviseFee}</if>
                        <if test="releaseAccountNo != null  and releaseAccountNo != ''"> and release_account_no = #{releaseAccountNo}</if>
                        <if test="releaseAccountName != null  and releaseAccountName != ''"> and release_account_name like concat('%', #{releaseAccountName}, '%')</if>
                        <if test="releaseBankName != null  and releaseBankName != ''"> and release_bank_name like concat('%', #{releaseBankName}, '%')</if>
                        <if test="createdTime != null "> and created_time = #{createdTime}</if>
                        <if test="updatedTime != null "> and updated_time = #{updatedTime}</if>
                        <if test="schoolId != null  and schoolId != ''"> and school_id = #{schoolId}</if>
                        <if test="branchId != null  and branchId != ''"> and branch_id = #{branchId}</if>
                        <if test="registrationId != null  and registrationId != ''"> and registration_id = #{registrationId}</if>
        </where>
        limit 1
    </select>

    <insert id="insertSupervisePay" parameterType="SupervisePay">
        insert into t_supervise_pay
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null">student_id,</if>
            <if test="studyFee != null">study_fee,</if>
            <if test="superviseFee != null">supervise_fee,</if>
            <if test="totalRealFee != null">total_real_fee,</if>
            <if test="restStudyFee != null">rest_study_fee,</if>
            <if test="totalReleaseFee != null">total_release_fee,</if>
            <if test="restSuperviseFee != null">rest_supervise_fee,</if>
            <if test="releaseAccountNo != null">release_account_no,</if>
            <if test="releaseAccountName != null">release_account_name,</if>
            <if test="releaseBankName != null">release_bank_name,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="schoolId != null and schoolId != ''">school_id,</if>
            <if test="branchId != null">branch_id,</if>
            <if test="registrationId != null">registration_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null">#{studentId},</if>
            <if test="studyFee != null">#{studyFee},</if>
            <if test="superviseFee != null">#{superviseFee},</if>
            <if test="totalRealFee != null">#{totalRealFee},</if>
            <if test="restStudyFee != null">#{restStudyFee},</if>
            <if test="totalReleaseFee != null">#{totalReleaseFee},</if>
            <if test="restSuperviseFee != null">#{restSuperviseFee},</if>
            <if test="releaseAccountNo != null">#{releaseAccountNo},</if>
            <if test="releaseAccountName != null">#{releaseAccountName},</if>
            <if test="releaseBankName != null">#{releaseBankName},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="schoolId != null and schoolId != ''">#{schoolId},</if>
            <if test="branchId != null">#{branchId},</if>
            <if test="registrationId != null">#{registrationId},</if>
         </trim>
    </insert>

    <update id="updateSupervisePay" parameterType="SupervisePay">
        update t_supervise_pay
        <trim prefix="SET" suffixOverrides=",">
            <if test="studyFee != null">study_fee = #{studyFee},</if>
            <if test="superviseFee != null">supervise_fee = #{superviseFee},</if>
            <if test="totalRealFee != null">total_real_fee = #{totalRealFee},</if>
            <if test="restStudyFee != null">rest_study_fee = #{restStudyFee},</if>
            <if test="totalReleaseFee != null">total_release_fee = #{totalReleaseFee},</if>
            <if test="restSuperviseFee != null">rest_supervise_fee = #{restSuperviseFee},</if>
            <if test="releaseAccountNo != null">release_account_no = #{releaseAccountNo},</if>
            <if test="releaseAccountName != null">release_account_name = #{releaseAccountName},</if>
            <if test="releaseBankName != null">release_bank_name = #{releaseBankName},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="schoolId != null and schoolId != ''">school_id = #{schoolId},</if>
            <if test="branchId != null">branch_id = #{branchId},</if>
            <if test="registrationId != null">registration_id = #{registrationId},</if>
        </trim>
        where student_id = #{studentId}
    </update>

    <delete id="deleteSupervisePayByStudentId" parameterType="String">
        delete from t_supervise_pay where student_id = #{studentId}
    </delete>

    <delete id="deleteSupervisePayByStudentIds" parameterType="String">
        delete from t_supervise_pay where student_id in 
        <foreach item="studentId" collection="array" open="(" separator="," close=")">
            #{studentId}
        </foreach>
    </delete>

    <select id="exportSupervisePayList" resultType="com.guangren.business.domain.SupervisePay" resultMap="SupervisePayResult">
        select a.* from t_supervise_pay a
        left join t_school_student b on a.student_id= b.id
        <where>

            <if test="registrationId != null  and registrationId != ''"> and a.registration_id = #{registrationId}</if>
            <if test="schoolId != null  and schoolId != ''"> and a.school_id = #{schoolId}</if>
            <if test="branchId != null  and branchId != ''"> and a.branch_id = #{branchId}</if>
            <if test="name != null  and name != ''"> and b.name = #{name}</if>
            <if test="identity != null  and identity != ''"> and b.identity like concat('%', #{identity}, '%')</if>
            <if test="params.schoolId != null  and params.schoolId != ''">
                and a.school_id = #{params.schoolId}
            </if>
            <if test="params.banchId != null  and params.banchId != ''">
                and a.branch_id = #{params.banchId}
            </if>
            <if test="params.registrationId != null  and params.registrationId != ''">
                and a.registration_id = #{params.registrationId}
            </if>

            <if test="params.identity != null and params.identity != ''">
                and b.identity like concat('%', #{params.identity}, '%')
            </if>

            <if test="params.studentName != null and params.studentName != ''">
                and b.name= #{params.studentName}
            </if>

            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(a.created_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(a.created_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by created_time desc
    </select>

    <select id="selectExportSupervisePayList" parameterType="SupervisePay" resultMap="SupervisePayResult">
        select a.*,b.id as student_id,b.name as student_name,b.identity as identity,
        c.id as school_id,c.name as school_name,
        d.id as branch_id,d.name as branch_name,
        e.id as registration_id,e.name as registration_name
        from t_supervise_pay a
        left join t_school_student b on a.student_id= b.id
        left join t_school c on a.school_id =c.id
        left join t_school_branch d on a.branch_id=d.id
        left join t_school_registration e on a.registration_id=e.id
        <where>
            <if test="studentId != null  and studentId != ''"> and a.student_id = #{studentId}</if>
            <if test="registrationId != null  and registrationId != ''"> and a.registration_id = #{registrationId}</if>
            <if test="schoolId != null  and schoolId != ''"> and a.school_id = #{schoolId}</if>
            <if test="branchId != null  and branchId != ''"> and a.branch_id = #{branchId}</if>
            <if test="params.schoolId != null  and params.schoolId != ''">
                and a.school_id = #{params.schoolId}
            </if>
            <if test="params.banchId != null  and params.banchId != ''">
                and a.branch_id = #{params.banchId}
            </if>
            <if test="params.registrationId != null  and params.registrationId != ''">
                and a.registration_id = #{params.registrationId}
            </if>

            <if test="params.identity != null and params.identity != ''">
                and b.identity like concat('%', #{params.identity}, '%')
            </if>

            <if test="params.studentName != null and params.studentName != ''">
                and b.name= #{params.studentName}
            </if>

            <if test="params.beginSuperviseTime != null and params.beginSuperviseTime != ''"><!-- 开始时间检索 -->
                AND date_format(a.created_time,'%Y-%m-%d %H:%i:%s') &gt;= date_format(#{params.beginSuperviseTime},'%Y-%m-%d %H:%i:%s')
            </if>
            <if test="params.endSuperviseTime != null and params.endSuperviseTime != ''"><!-- 结束时间检索 -->
                AND date_format(a.created_time,'%Y-%m-%d %H:%i:%s') &lt;= date_format(#{params.endSuperviseTime},'%Y-%m-%d %H:%i:%s')
            </if>
        </where>
        order by created_time desc limit 1000
    </select>

</mapper>