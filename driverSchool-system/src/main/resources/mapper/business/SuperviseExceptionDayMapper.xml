<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guangren.business.mapper.SuperviseExceptionDayMapper">
    
    <resultMap type="SuperviseExceptionDay" id="SuperviseExceptionDayResult">
        <result property="id"    column="id"    />
        <result property="studentId"    column="student_id"    />
        <result property="releaseFee"    column="release_fee"    />
        <result property="releaseDate"    column="release_date"    />
        <result property="superviseDate"    column="supervise_date"    />
        <result property="releaseDay"    column="release_day"    />
        <result property="day"    column="day"    />
        <result property="releaseRecordId"    column="release_record_id"    />
        <result property="exceptionReason"    column="exception_reason"    />
        <result property="schoolId"    column="school_id"    />
        <result property="branchId"    column="branch_id"    />
        <result property="registrationId"    column="registration_id"    />
        <association property="school" javaType="school" autoMapping="true" >
            <result property="name" column="school_name" />
        </association>
        <association property="branch"  javaType="schoolBranch">
            <result property="name" column="branch_name" />
        </association>
        <association property="registration"  javaType="schoolRegistration">
            <result property="name" column="registration_name" />
        </association>
        <association property="schoolStudent" javaType="schoolStudent" autoMapping="true">
            <result property="name" column="name" />
            <result property="identity" column="identity" />
        </association>
    </resultMap>

    <sql id="selectSuperviseExceptionDayVo">
        select id, student_id, release_fee, release_date, supervise_date, release_day, day, release_record_id, exception_reason, school_id, branch_id, registration_id from t_supervise_exception_day
    </sql>

    <select id="selectSuperviseExceptionDayList" parameterType="SuperviseExceptionDay" resultMap="SuperviseExceptionDayResult">
        select
        a.*,
        b.name,
        b.identity,
        c.name AS school_name,
        d.name AS branch_name,
        e.name AS registration_name
        from t_supervise_exception_day a
        left join t_school_student b on a.student_id= b.id
        left join t_school as c on b.school_id = c.id
        left join t_school_branch as d ON b.branch_id = d.id
        left join t_school_registration as e ON b.registration_id = e.id
        <where>
            <if test="ids != null and ids.size() > 0">
                and a.id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="studentId != null  and studentId != ''"> and student_id = #{studentId}</if>
            <if test="releaseFee != null "> and release_fee = #{releaseFee}</if>
            <if test="releaseDate != null "> and release_date = #{releaseDate}</if>
            <if test="releaseDay != null "> and release_day = #{releaseDay}</if>
            <if test="releaseRecordId != null  and releaseRecordId != ''"> and release_record_id = #{releaseRecordId}</if>
            <if test="exceptionReason != null  and exceptionReason != ''"> and exception_reason = #{exceptionReason}</if>
            <if test="schoolId != null  and schoolId != ''"> and a.school_id = #{schoolId}</if>
            <if test="branchId != null  and branchId != ''"> and a.branch_id = #{branchId}</if>
            <if test="registrationId != null  and registrationId != ''"> and a.registration_id = #{registrationId}</if>

            <if test="params.schoolId != null  and params.schoolId != ''">
                and a.school_id = #{params.schoolId}
            </if>
            <if test="params.banchId != null  and params.banchId != ''">
                and a.branch_id = #{params.banchId}
            </if>
            <if test="params.registrationId != null  and params.registrationId != ''">
                and a.registration_id = #{params.registrationId}
            </if>

<!--            <if test="identity != null and identity != ''">-->
<!--                and b.identity like concat('%', #{identity}, '%')-->
<!--            </if>-->
            <if test="identityList != null and identityList.size() > 0">
                and (
                <foreach collection="identityList" item="identity" separator="or">
                    b.identity like CONCAT('%', #{identity}, '%')
                </foreach>
                )
            </if>

            <if test="studentName != null and studentName != ''">
                and b.name= #{studentName}
            </if>

            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(a.release_date,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                AND date_format(a.release_date,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
        order by a.release_date desc
    </select>
    
    <select id="selectSuperviseExceptionDayById" parameterType="String" resultMap="SuperviseExceptionDayResult">
        <include refid="selectSuperviseExceptionDayVo"/>
        where id = #{id}
    </select>

    <select id="checkUnique" parameterType="SuperviseExceptionDay" resultMap="SuperviseExceptionDayResult">
        <include refid="selectSuperviseExceptionDayVo"/>
        <where>
            <if test="studentId != null  and studentId != ''"> and student_id = #{studentId}</if>
            <if test="releaseFee != null "> and release_fee = #{releaseFee}</if>
            <if test="releaseDate != null "> and release_date = #{releaseDate}</if>
            <if test="releaseDay != null "> and release_day = #{releaseDay}</if>
            <if test="releaseRecordId != null  and releaseRecordId != ''"> and release_record_id = #{releaseRecordId}</if>
            <if test="exceptionReason != null  and exceptionReason != ''"> and exception_reason = #{exceptionReason}</if>
            <if test="schoolId != null  and schoolId != ''"> and school_id = #{schoolId}</if>
            <if test="branchId != null  and branchId != ''"> and branch_id = #{branchId}</if>
            <if test="registrationId != null  and registrationId != ''"> and registration_id = #{registrationId}</if>
        </where>
        limit 1
    </select>


        
    <insert id="insertSuperviseExceptionDay" parameterType="SuperviseExceptionDay">
        insert into t_supervise_exception_day
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="studentId != null and studentId != ''">student_id,</if>
            <if test="releaseFee != null">release_fee,</if>
            <if test="releaseDate != null">release_date,</if>

            <if test="superviseDate != null">supervise_date,</if>
            <if test="releaseDay != null">release_day,</if>
            <if test="day != null">day,</if>

            <if test="releaseRecordId != null and releaseRecordId != ''">release_record_id,</if>
            <if test="exceptionReason != null">exception_reason,</if>
            <if test="schoolId != null and schoolId != ''">school_id,</if>
            <if test="branchId != null">branch_id,</if>
            <if test="registrationId != null">registration_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="studentId != null and studentId != ''">#{studentId},</if>
            <if test="releaseFee != null">#{releaseFee},</if>
            <if test="releaseDate != null">#{releaseDate},</if>

            <if test="superviseDate != null">#{superviseDate},</if>
            <if test="releaseDay != null">#{releaseDay},</if>
            <if test="day != null">#{day},</if>


            <if test="releaseRecordId != null and releaseRecordId != ''">#{releaseRecordId},</if>
            <if test="exceptionReason != null">#{exceptionReason},</if>
            <if test="schoolId != null and schoolId != ''">#{schoolId},</if>
            <if test="branchId != null">#{branchId},</if>
            <if test="registrationId != null">#{registrationId},</if>
         </trim>
    </insert>

    <update id="updateSuperviseExceptionDay" parameterType="SuperviseExceptionDay">
        update t_supervise_exception_day
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null and studentId != ''">student_id = #{studentId},</if>
            <if test="releaseFee != null">release_fee = #{releaseFee},</if>
            <if test="releaseDate != null">release_date = #{releaseDate},</if>

            <if test="superviseDate != null">supervise_date = #{superviseDate},</if>
            <if test="releaseDay != null">release_day = #{releaseDay},</if>
            <if test="day != null">day = #{day},</if>

            <if test="releaseRecordId != null and releaseRecordId != ''">release_record_id = #{releaseRecordId},</if>
            <if test="exceptionReason != null">exception_reason = #{exceptionReason},</if>
            <if test="schoolId != null and schoolId != ''">school_id = #{schoolId},</if>
            <if test="branchId != null">branch_id = #{branchId},</if>
            <if test="registrationId != null">registration_id = #{registrationId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSuperviseExceptionDayById" parameterType="String">
        delete from t_supervise_exception_day where id = #{id}
    </delete>

    <delete id="deleteSuperviseExceptionDayByIds" parameterType="String">
        delete from t_supervise_exception_day where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>