import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.guangren.business.domain.AssociationFeeTempRecord;
import com.guangren.business.domain.SchoolContract;
import com.guangren.business.domain.SchoolRegistration;
import com.guangren.business.domain.SchoolStudent;
import com.guangren.business.helper.QrCodeGenerateHelper;
import com.guangren.business.service.IAssociationFeeTempRecordService;
import com.guangren.business.service.ISchoolContractService;
import com.guangren.business.service.ISchoolRegistrationService;
import com.guangren.business.service.ISchoolStudentService;
import com.guangren.business.service.impl.SchoolStudentServiceImpl;
import com.guangren.common.config.RuoYiConfig;
import com.guangren.common.exception.BusinessException;
import com.guangren.common.utils.StringUtils;
import com.guangren.common.utils.uuid.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@SpringBootTest(classes = TestCode.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class TestCode {
    private final static  String appKey = "5b8c5017381f4df0a45d45a81d443738";
    private final static String prefix_url = "https://esign.it.10086.cn/saas/api/api-openapi/openApi";
    @Autowired
    private IAssociationFeeTempRecordService associationFeeTempRecordService;

    @Autowired
    private ISchoolStudentService schoolStudentService;

    @Test
    public void testAssociationFeeTempRecord() {
        BigDecimal associationFee = new BigDecimal(10);
        String associationSubOrderId = "ASSOCIATION" + DateUtil.format(new Date(), "yyyyMMddHHmmssSSS") + RandomStringUtils.randomNumeric(7);

        AssociationFeeTempRecord associationFeeTempRecord = new AssociationFeeTempRecord();
        associationFeeTempRecord.setId(IdUtils.fastSimpleUUID());
        associationFeeTempRecord.setAssociationFee(associationFee.multiply(new BigDecimal(100)));
        associationFeeTempRecord.setMerOrderId("order0001");
        associationFeeTempRecord.setMid("mid9999");
        associationFeeTempRecord.setSubMerOrderId(associationSubOrderId);
        associationFeeTempRecord.setSubMid("sub-mid888");
        associationFeeTempRecord.setCreatedTime(new Date());
        associationFeeTempRecord.setStudentId("stu00009");
        associationFeeTempRecord.setIdentity("******************");
        associationFeeTempRecord.setStatus(0); //未转出
        associationFeeTempRecordService.save(associationFeeTempRecord);

    }

    public void testaddPerson() throws Exception {
        JSONObject jsonObject = this.addPersonUser("",0,"******************","15602617676");
    }
    public JSONObject addPersonUser(String name, int cardType, String idcard, String mobile){
        String url = prefix_url+"/user/createUser";
        JSONObject object = new JSONObject();
        object.put("userType",1);
        object.put("userName",name);
        object.put("cardType",cardType);
        object.put("cardNumber",idcard);
        object.put("phone",mobile);
        object.put("applyNo",mobile);
        String token = getToken();
        if(StringUtils.isEmpty(token)){
            throw new BusinessException("未获取token");
        }
        HttpRequest request = HttpUtil.createPost(url)
                .body(object.toJSONString())
                .header("access_token",token)
                .header("Content-Type", "application/json;charset=utf-8");
        try (HttpResponse response = request.execute()){
            log.info("request:"+request.toString());
            log.info("response:"+response.toString());
            object = JSON.parseObject(response.body());
            int code = object.getIntValue("code");
            if(code == 200){
                return object.getJSONObject("data");
            }else{
                throw new BusinessException(object.getString("message"));
            }
        }
    }
    public String getToken(){
//        Object tokenStr = CacheUtils.get(cacheKey);
//        if(tokenStr != null){
//            String [] strs = tokenStr.toString().split("@");
//            log.info("token expires:"+strs);
//            Date expireDate = DateUtil.parse(strs[1],"yyyyMMddHHmmss");
//            if(expireDate.after(new Date())){
//                return strs[0];
//            }
//        }

        JSONObject object = new JSONObject();
        object.put("appKey",appKey);
        String url = prefix_url+"/auth/getToken?appKey="+appKey;
        HttpRequest request = HttpUtil.createGet(url)
                .header("Content-Type", "application/json;charset=utf-8");

        try (HttpResponse response = request.execute()) {
            log.info("response:"+response.body());
            JSONObject json = JSON.parseObject(response.body());
            int code = json.getIntValue("code");
            if(code == 200){
                JSONObject data = json.getJSONObject("data");
                String token = data.getString("access_token");
                int expireIn = data.getIntValue("expires_in");
                log.info("currentDate:"+DateUtil.format(new Date(),"yyyyMMddHHmmss"));
                log.info("get token expireId:"+expireIn);
                Calendar cal = Calendar.getInstance();
                cal.add(Calendar.SECOND,expireIn-60);
                String expireDate = DateUtil.format(cal.getTime(),"yyyyMMddHHmmss");
                //CacheUtils.put(cacheKey,token+"@"+expireDate);
                return token;
            }
        }
        return null;
    }
}
