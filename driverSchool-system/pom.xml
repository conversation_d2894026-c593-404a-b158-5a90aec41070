<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>driverSchool</artifactId>
        <groupId>com.guangren</groupId>
        <version>4.7.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>driverSchool-system</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
		</dependency>

		<dependency>
	        <groupId>com.baomidou</groupId>
	        <artifactId>mybatis-plus-boot-starter</artifactId>
	    </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
        </dependency>

        <dependency>
		   <groupId>org.projectlombok</groupId>
		   <artifactId>lombok</artifactId>
		   <optional>true</optional>
		</dependency>
		
		<dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.guangren</groupId>
            <artifactId>driverSchool-common</artifactId>
        </dependency>
        
        <dependency>
            <groupId>com.guangren</groupId>
            <artifactId>yidong</artifactId>
        </dependency>
        
        <dependency>
		   <groupId>yidong</groupId>
		   <artifactId>masmgc.sdk.mms</artifactId>
		   <version>1.0.3</version>
		</dependency>
		
		<dependency>
		   <groupId>yidong</groupId>
		   <artifactId>masmgc.sdk.sms</artifactId>
		   <version>1.0.3</version>
		</dependency>
		
		<dependency>
		   <groupId>org.bouncycastle</groupId>
		   <artifactId>bcprov-jdk15on</artifactId>
		</dependency>
		
		<dependency>
			<groupId>bank</groupId>
			<artifactId>MicrodoneSMF</artifactId>
		</dependency>
		<!-- 条码，二维码生成 -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.5.1</version>
		</dependency>

		<dependency>
			<groupId>bank</groupId>
			<artifactId>ocxdgnsbwjm</artifactId>
		</dependency>
		
		<dependency>
			<groupId>bank</groupId>
			<artifactId>open-sdk</artifactId>
		</dependency>
		<dependency>
			<groupId>xuanwu</groupId>
			<artifactId>mos</artifactId>
			<version>1.0</version>
		</dependency>

		<!-- 外部渠道插码支持 -->
		<dependency>
			<groupId>io.growing.sdk.java</groupId>
			<artifactId>growingio-java-sdk</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
    </dependencies>
</project>